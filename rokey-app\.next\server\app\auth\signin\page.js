(()=>{var e={};e.id=4680,e.ids=[4680],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43476:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(60687),a=r(43210),i=r(85814),n=r.n(i),l=r(30474),o=r(94257),d=r(66524),c=r(81836),u=r(79481),p=r(16189),m=r(34374);function x(){let[e,t]=(0,a.useState)(""),[r,i]=(0,a.useState)(""),[x,h]=(0,a.useState)(!1),[g,b]=(0,a.useState)(!1),[f,y]=(0,a.useState)(""),[v,w]=(0,a.useState)(!1),[j,N]=(0,a.useState)(""),[k,_]=(0,a.useState)(!1),[C,P]=(0,a.useState)(""),q=(0,p.useRouter)(),R=(0,p.useSearchParams)(),S=(0,u.createSupabaseBrowserClient)(),{success:E,error:A}=(0,m.dj)(),K=async t=>{t.preventDefault(),b(!0),y("");try{let{data:t,error:s}=await S.auth.signInWithPassword({email:e,password:r});if(s)throw s;if(t.user){await new Promise(e=>setTimeout(e,500));let e=R.get("redirectTo"),r=R.get("plan"),s=R.get("email"),a=R.get("checkout_user_id");try{let{data:r}=await S.from("user_profiles").select("subscription_tier, subscription_status").eq("id",t.user.id).single();if(r&&"active"===r.subscription_status)return void(e?q.push(e):q.push("/dashboard"));let s=t.user.user_metadata,a=s?.payment_status,i=s?.plan;if("pending"===a&&i&&["starter","professional","enterprise"].includes(i))return void q.push("/pricing")}catch(e){}if(a&&r&&["starter","professional","enterprise"].includes(r)){let e=`/checkout?plan=${r}&user_id=${t.user.id}${s?`&email=${encodeURIComponent(s)}`:""}`;q.push(e)}else if(r&&["starter","professional","enterprise"].includes(r))try{let{data:e}=await S.from("user_profiles").select("subscription_tier, subscription_status").eq("id",t.user.id).single();if(e&&"free"===e.subscription_tier&&"active"===e.subscription_status)q.push("/dashboard");else{let e=`/checkout?plan=${r}&user_id=${t.user.id}${s?`&email=${encodeURIComponent(s)}`:""}`;q.push(e)}}catch(e){q.push("/dashboard")}else e?q.push(e):q.push("/dashboard")}}catch(e){y(e.message||"Invalid email or password. Please try again.")}finally{b(!1)}},L=async e=>{if(e.preventDefault(),_(!0),P(""),!j.trim()){P("Please enter your email address"),_(!1);return}try{let{error:e}=await S.auth.resetPasswordForEmail(j,{redirectTo:`${window.location.origin}/auth/reset-password`});if(e)throw e;E("Password reset email sent!","Check your inbox for instructions to reset your password."),w(!1),N("")}catch(e){P(e.message||"Failed to send reset email. Please try again."),A("Failed to send reset email",e.message||"Please try again.")}finally{_(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen flex bg-gradient-to-br from-[#1C051C] to-[#040716]",children:[(0,s.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 items-center justify-center p-12 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.15]",style:{backgroundImage:"linear-gradient(rgba(255, 255, 255, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.3) 1px, transparent 1px)",backgroundSize:"40px 40px",maskImage:"radial-gradient(ellipse at center, black 30%, transparent 80%)",WebkitMaskImage:"radial-gradient(ellipse at center, black 30%, transparent 80%)"}}),(0,s.jsxs)("div",{className:"max-w-lg relative z-10",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)(n(),{href:"/",className:"inline-flex items-center space-x-3",children:[(0,s.jsx)(l.default,{src:"/RouKey_Logo_GLOW.png",alt:"RouKey",width:48,height:48,className:"w-12 h-12"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-white",children:"RouKey"})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-white leading-tight",children:"Welcome back!"}),(0,s.jsx)("p",{className:"text-xl text-white/80 leading-relaxed",children:"Sign in to your RouKey account and continue building with our powerful routing platform."}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsxs)("p",{className:"text-white/60 text-sm",children:["Don't have an account?"," ",(0,s.jsx)(n(),{href:"/auth/signup",className:"text-white font-medium hover:text-white/80 transition-colors",children:"Sign up here"})]})})]})]})]}),(0,s.jsx)("div",{className:"flex-1 flex items-center justify-center p-8 bg-white",children:(0,s.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[(0,s.jsx)("div",{className:"lg:hidden text-center",children:(0,s.jsxs)(n(),{href:"/",className:"inline-flex items-center space-x-2",children:[(0,s.jsx)(l.default,{src:"/RouKey_Logo_NOGLOW.png",alt:"RouKey",width:32,height:32,className:"w-8 h-8"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"RouKey"})]})}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"Sign in"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600",children:"Welcome back to RouKey"})]}),f&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-600 text-sm",children:f})}),(0,s.jsxs)("form",{onSubmit:K,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>t(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your email"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"password",name:"password",type:x?"text":"password",autoComplete:"current-password",required:!0,value:r,onChange:e=>i(e.target.value),className:"w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your password"}),(0,s.jsx)("button",{type:"button",onClick:()=>h(!x),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors",children:x?(0,s.jsx)(o.A,{className:"h-5 w-5"}):(0,s.jsx)(d.A,{className:"h-5 w-5"})})]})]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("button",{type:"button",onClick:()=>{N(e),P(""),w(!0)},className:"text-sm text-blue-600 hover:text-blue-500 font-medium transition-colors",children:"Forgot your password?"})}),(0,s.jsx)("button",{type:"submit",disabled:g,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:g?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Signing in..."]}):"Sign in"})]}),(0,s.jsx)("div",{className:"lg:hidden text-center",children:(0,s.jsxs)("p",{className:"text-gray-600 text-sm",children:["Don't have an account?"," ",(0,s.jsx)(n(),{href:"/auth/signup",className:"text-blue-600 hover:text-blue-500 font-medium transition-colors",children:"Sign up"})]})})]})}),v&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Reset Password"}),(0,s.jsx)("button",{onClick:()=>w(!1),className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(c.A,{className:"h-6 w-6"})})]}),(0,s.jsxs)("form",{onSubmit:L,className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"resetEmail",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email address"}),(0,s.jsx)("input",{id:"resetEmail",type:"email",required:!0,value:j,onChange:e=>N(e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your email"})]}),C&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,s.jsx)("p",{className:"text-red-600 text-sm",children:C})}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[(0,s.jsx)("button",{type:"button",onClick:()=>w(!1),className:"flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:k,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:k?"Sending...":"Send Reset Email"})]})]})]})})]})}function h(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,s.jsx)(x,{})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66524:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},71963:(e,t,r)=>{Promise.resolve().then(r.bind(r,87578))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82579:(e,t,r)=>{Promise.resolve().then(r.bind(r,43476))},85443:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,87578)),"C:\\RoKey App\\rokey-app\\src\\app\\auth\\signin\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\auth\\signin\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},87578:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\auth\\signin\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94257:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5449,4912],()=>r(85443));module.exports=s})();