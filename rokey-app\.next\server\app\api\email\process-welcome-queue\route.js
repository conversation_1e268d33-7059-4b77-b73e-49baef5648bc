(()=>{var e={};e.id=2025,e.ids=[1489,2025],e.modules={2502:e=>{"use strict";e.exports=import("prettier/plugins/html")},2507:(e,r,t)=>{"use strict";t.d(r,{H:()=>n,Q:()=>u,createSupabaseServerClientOnRequest:()=>o});var s=t(34386),i=t(39398),a=t(44999);async function o(){let e=await (0,a.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:r=>e.get(r)?.value,set(r,t,s){try{e.set({name:r,value:t,...s})}catch(e){}},remove(r,t){try{e.set({name:r,value:"",...t})}catch(e){}}}})}function u(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:r=>e.cookies.get(r)?.value,set(e,r,t){},remove(e,r){}}})}function n(){return(0,i.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7660:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>m,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>l,POST:()=>p});var i=t(96559),a=t(48088),o=t(37719),u=t(32190),n=t(2507),c=t(99176);async function p(e){try{let r=(0,n.Q)(e),t=e.headers.get("authorization"),s=process.env.ROKEY_API_ACCESS_TOKEN;if(s&&t!==`Bearer ${s}`)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{data:i,error:a}=await r.from("welcome_email_queue").select("*").eq("sent",!1).order("created_at",{ascending:!0}).limit(10);if(a)return u.NextResponse.json({error:"Database error"},{status:500});if(!i||0===i.length)return u.NextResponse.json({success:!0,message:"No pending emails",processed:0});let o=0,p=0;for(let e of i){try{await (0,c.v)({userEmail:e.user_email,userName:e.user_name,userTier:e.user_tier})?(await r.from("welcome_email_queue").update({sent:!0,sent_at:new Date().toISOString(),error_message:null}).eq("id",e.id),o++):(await r.from("welcome_email_queue").update({error_message:"Failed to send email via EmailJS",sent:!1}).eq("id",e.id),p++)}catch(t){await r.from("welcome_email_queue").update({error_message:t instanceof Error?t.message:"Unknown error",sent:!1}).eq("id",e.id),p++}await new Promise(e=>setTimeout(e,1e3))}return u.NextResponse.json({success:!0,processed:o+p,successful:o,errors:p})}catch(e){return u.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e){try{let r=(0,n.Q)(e),{data:t,error:s}=await r.from("welcome_email_queue").select("sent, created_at").order("created_at",{ascending:!1}).limit(100);if(s)return u.NextResponse.json({error:"Database error"},{status:500});let i=t?.filter(e=>!e.sent).length||0,a=t?.filter(e=>e.sent).length||0,o=t?.length||0;return u.NextResponse.json({queue_status:{pending:i,sent:a,total:o,last_24h:t?.filter(e=>new Date(e.created_at)>new Date(Date.now()-864e5)).length||0}})}catch(e){return u.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/email/process-welcome-queue/route",pathname:"/api/email/process-welcome-queue",filename:"route",bundlePath:"app/api/email/process-welcome-queue/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\email\\process-welcome-queue\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:x}=m;function q(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57075:e=>{"use strict";e.exports=require("node:stream")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83505:e=>{"use strict";e.exports=import("prettier/standalone")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398,3410,6588],()=>t(7660));module.exports=s})();