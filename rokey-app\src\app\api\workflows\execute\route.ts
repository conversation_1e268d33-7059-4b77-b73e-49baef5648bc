/**
 * Workflow Execution API
 * Allows executing workflows via persistent API keys (similar to model API)
 */

import { NextRequest, NextResponse } from 'next/server';
import { workflowPersistence } from '@/lib/workflow/WorkflowPersistence';
import { WorkflowExecutor } from '@/lib/workflow/WorkflowExecutor';
import { workflowMonitor } from '@/lib/workflow/WorkflowExecutionMonitor';
import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

/**
 * Log workflow execution to request_logs table for analytics and monitoring
 */
async function logWorkflowExecution(
  workflowId: string,
  userId: string,
  apiKeyId: string,
  request: NextRequest,
  executionData: {
    status_code: number;
    request_timestamp: Date;
    response_timestamp: Date;
    processing_duration_ms: number;
    input_text?: string;
    output_text?: string;
    error_message?: string;
    models_used?: string[];
    providers_used?: string[];
    total_cost?: number;
    total_input_tokens?: number;
    total_output_tokens?: number;
    execution_id?: string;
  }
): Promise<void> {
  try {
    // Use service role client for logging to bypass RLS
    const serviceSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    const logEntry = {
      workflow_id: workflowId,
      api_key_id: apiKeyId,
      user_id: userId,
      predefined_model_id: executionData.models_used?.join(', ') || null,
      role_requested: null, // Workflows don't use role-based routing
      role_used: 'workflow_execution',
      ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || null,
      request_timestamp: executionData.request_timestamp.toISOString(),
      response_timestamp: executionData.response_timestamp.toISOString(),
      status_code: executionData.status_code,
      request_payload_summary: {
        input_length: executionData.input_text?.length || 0,
        execution_id: executionData.execution_id,
        workflow_type: 'manual_build'
      },
      response_payload_summary: {
        output_length: executionData.output_text?.length || 0,
        models_used: executionData.models_used || [],
        providers_used: executionData.providers_used || []
      },
      error_message: executionData.error_message || null,
      error_source: executionData.error_message ? 'workflow_execution' : null,
      llm_model_name: executionData.models_used?.join(', ') || null,
      llm_provider_name: executionData.providers_used?.join(', ') || null,
      llm_provider_status_code: executionData.status_code,
      processing_duration_ms: executionData.processing_duration_ms,
      cost: executionData.total_cost || null,
      input_tokens: executionData.total_input_tokens || null,
      output_tokens: executionData.total_output_tokens || null,
      is_multimodal: false, // TODO: Detect multimodal input in workflows
    };

    const { error: logError } = await serviceSupabase.from('request_logs').insert(logEntry);
    if (logError) {
      console.error('[Workflow Log Error]', logError.message, logError.details);
    } else {
      console.log(`✅ [Workflow Logged] ${workflowId} - ${executionData.status_code} - ${executionData.processing_duration_ms}ms`);
    }
  } catch (logCatchError: any) {
    console.error('[Workflow Log System Error]', logCatchError.message, logCatchError.stack);
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get API key from header
    const apiKey = request.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key required in X-API-Key header' },
        { status: 401 }
      );
    }

    // Validate API key and get workflow info
    const validation = await workflowPersistence.validateWorkflowAPIKey(apiKey);
    if (!validation.valid || !validation.workflowId || !validation.userId) {
      return NextResponse.json(
        { error: 'Invalid or expired API key' },
        { status: 401 }
      );
    }

    // Get request body
    const body = await request.json();
    const { input, options = {} } = body;

    if (!input) {
      return NextResponse.json(
        { error: 'Input is required' },
        { status: 400 }
      );
    }

    // Get workflow
    const workflow = await workflowPersistence.getWorkflow(validation.workflowId, validation.userId);
    if (!workflow || !workflow.is_active) {
      return NextResponse.json(
        { error: 'Workflow not found or inactive' },
        { status: 404 }
      );
    }

    // Generate execution ID and track timing
    const executionId = crypto.randomUUID();
    const requestTimestamp = new Date();
    const startTime = Date.now();

    // Start execution monitoring
    await workflowMonitor.startExecution(
      executionId,
      workflow.id,
      validation.userId,
      workflow.nodes.length
    );

    let responseTimestamp: Date;
    let processingDurationMs: number;
    let executionResult: any;
    let executionError: Error | null = null;

    try {
      // Execute workflow
      const executor = WorkflowExecutor.getInstance();
      executionResult = await executor.executeWorkflow(
        workflow.id,
        validation.userId,
        workflow.nodes,
        workflow.edges,
        input
      );

      responseTimestamp = new Date();
      processingDurationMs = Date.now() - startTime;

      // Complete monitoring
      await workflowMonitor.completeExecution(executionId, executionResult, Date.now());

      // Log successful execution
      setImmediate(async () => {
        await logWorkflowExecution(
          workflow.id,
          validation.userId!,
          '', // API key ID not available from validation
          request,
          {
            status_code: 200,
            request_timestamp: requestTimestamp,
            response_timestamp: responseTimestamp,
            processing_duration_ms: processingDurationMs,
            input_text: typeof input === 'string' ? input : JSON.stringify(input),
            output_text: typeof executionResult === 'string' ? executionResult : JSON.stringify(executionResult),
            execution_id: executionId,
            // TODO: Extract actual models and providers used from execution result
            models_used: [], // Will be populated when we extract from execution
            providers_used: [], // Will be populated when we extract from execution
            total_cost: 0, // Will be calculated from individual node costs
            total_input_tokens: 0, // Will be summed from individual nodes
            total_output_tokens: 0, // Will be summed from individual nodes
          }
        );
      });

      return NextResponse.json({
        success: true,
        execution_id: executionId,
        workflow_id: workflow.id,
        workflow_name: workflow.name,
        result: executionResult,
        executed_at: new Date().toISOString(),
        execution_time_ms: processingDurationMs
      });

    } catch (error) {
      executionError = error instanceof Error ? error : new Error('Unknown execution error');
      responseTimestamp = new Date();
      processingDurationMs = Date.now() - startTime;

      // Fail monitoring
      await workflowMonitor.failExecution(
        executionId,
        executionError.message,
        { error: executionError }
      );

      // Log failed execution
      setImmediate(async () => {
        await logWorkflowExecution(
          workflow.id,
          validation.userId!,
          '', // API key ID not available from validation
          request,
          {
            status_code: 500,
            request_timestamp: requestTimestamp,
            response_timestamp: responseTimestamp,
            processing_duration_ms: processingDurationMs,
            input_text: typeof input === 'string' ? input : JSON.stringify(input),
            error_message: executionError?.message || 'Unknown execution error',
            execution_id: executionId,
            models_used: [],
            providers_used: [],
          }
        );
      });

      throw executionError;
    }

  } catch (error) {
    console.error('Workflow execution API error:', error);

    // Log early errors (validation, setup failures, etc.)
    setImmediate(async () => {
      try {
        // Use service role client for logging to bypass RLS
        const serviceSupabase = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.SUPABASE_SERVICE_ROLE_KEY!,
          {
            auth: {
              autoRefreshToken: false,
              persistSession: false
            }
          }
        );

        const errorLogEntry = {
          workflow_id: null, // Unknown at this point
          api_key_id: null,
          user_id: null,
          predefined_model_id: null,
          role_requested: null,
          role_used: 'workflow_execution_failed',
          ip_address: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || null,
          request_timestamp: new Date().toISOString(),
          response_timestamp: new Date().toISOString(),
          status_code: 500,
          request_payload_summary: { note: "Early workflow execution error" },
          response_payload_summary: null,
          error_message: error instanceof Error ? error.message : 'Unknown workflow execution error',
          error_source: 'workflow_api',
          processing_duration_ms: 0,
        };

        const { error: logError } = await serviceSupabase.from('request_logs').insert(errorLogEntry);
        if (logError) console.error('[Workflow Early Error Log Error]', logError.message);
      } catch (logCatchError: any) {
        console.error('[Workflow Early Error Log System Error]', logCatchError.message);
      }
    });

    return NextResponse.json({
      error: 'Workflow execution failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get API key from header
    const apiKey = request.headers.get('X-API-Key');
    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key required in X-API-Key header' },
        { status: 401 }
      );
    }

    // Validate API key
    const validation = await workflowPersistence.validateWorkflowAPIKey(apiKey);
    if (!validation.valid || !validation.workflowId || !validation.userId) {
      return NextResponse.json(
        { error: 'Invalid or expired API key' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('execution_id');

    if (executionId) {
      // Get specific execution status
      const execution = workflowMonitor.getExecutionStatus(executionId);
      if (!execution) {
        return NextResponse.json(
          { error: 'Execution not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        execution_id: executionId,
        status: execution.status,
        progress: execution.progress,
        current_node: execution.currentNodeId,
        logs: execution.logs,
        result: execution.result,
        error: execution.error,
        timestamp: execution.timestamp
      });
    } else {
      // Get workflow info and recent executions
      const workflow = await workflowPersistence.getWorkflow(validation.workflowId, validation.userId);
      if (!workflow) {
        return NextResponse.json(
          { error: 'Workflow not found' },
          { status: 404 }
        );
      }

      const recentExecutions = await workflowMonitor.getExecutionHistory(
        workflow.id,
        validation.userId,
        10
      );

      return NextResponse.json({
        workflow: {
          id: workflow.id,
          name: workflow.name,
          description: workflow.description,
          is_active: workflow.is_active,
          version: workflow.version,
          created_at: workflow.created_at,
          updated_at: workflow.updated_at
        },
        recent_executions: recentExecutions.map(exec => ({
          id: exec.id,
          status: exec.status,
          started_at: exec.started_at,
          completed_at: exec.completed_at,
          execution_time_ms: exec.execution_time_ms,
          nodes_executed: exec.nodes_executed,
          nodes_total: exec.nodes_total
        }))
      });
    }

  } catch (error) {
    console.error('Workflow status API error:', error);
    
    return NextResponse.json({
      error: 'Failed to get workflow status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
