{"name": "parseley", "version": "0.12.1", "description": "CSS selectors parser", "keywords": ["CSS", "selectors", "parser", "AST", "serializer", "specificity"], "repository": {"type": "git", "url": "git+https://github.com/mxxii/parseley.git"}, "bugs": {"url": "https://github.com/mxxii/parseley/issues"}, "homepage": "https://github.com/mxxii/parseley", "author": "KillyMXI", "funding": "https://ko-fi.com/killymxi", "license": "MIT", "exports": {"import": "./lib/parseley.mjs", "require": "./lib/parseley.cjs"}, "type": "module", "main": "./lib/parseley.cjs", "module": "./lib/parseley.mjs", "types": "./lib/parseley.d.ts", "files": ["lib"], "sideEffects": false, "scripts": {"build:deno": "denoify", "build:docs": "typedoc --plugin typedoc-plugin-markdown", "build:rollup": "rollup -c", "build:types": "tsc --declaration --emitDeclarationOnly", "build": "npm run clean && npm run build:rollup && npm run build:types && npm run build:docs && npm run build:deno", "checkAll": "npm run lint && npm test", "clean": "<PERSON><PERSON><PERSON> lib", "example": "node ./example/example.mjs", "lint:eslint": "eslint .", "lint:md": "markdownlint-cli2", "lint": "npm run lint:eslint && npm run lint:md", "prepublishOnly": "npm run build && npm run checkAll", "test": "ava --timeout=20s"}, "dependencies": {"leac": "^0.6.0", "peberminta": "^0.9.0"}, "devDependencies": {"@rollup/plugin-typescript": "^11.1.0", "@tsconfig/node14": "^1.0.3", "@types/node": "^14.18.42", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "ava": "^5.2.0", "denoify": "^1.5.3", "eslint": "^8.39.0", "eslint-plugin-jsonc": "^2.7.0", "eslint-plugin-tsdoc": "^0.2.17", "markdownlint-cli2": "^0.7.0", "rimraf": "^5.0.0", "rollup": "^2.79.1", "rollup-plugin-cleanup": "^3.2.1", "ts-node": "^10.9.1", "tslib": "^2.5.0", "typedoc": "~0.23.28", "typedoc-plugin-markdown": "~3.14.0", "typescript": "~4.9.5"}, "ava": {"extensions": {"ts": "module"}, "files": ["test/**/*"], "nodeArguments": ["--loader=ts-node/esm", "--experimental-specifier-resolution=node"], "verbose": true}, "denoify": {"out": "./deno"}}