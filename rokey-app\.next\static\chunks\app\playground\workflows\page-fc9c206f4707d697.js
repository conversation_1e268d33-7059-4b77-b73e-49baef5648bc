(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9675],{22128:(e,s,t)=>{Promise.resolve().then(t.bind(t,97939))},97939:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var a=t(95155),r=t(12115),l=t(6865),i=t(82771),n=t(10184),o=t(63603),c=t(52589);function d(){let[e,s]=(0,r.useState)([]),[t,d]=(0,r.useState)(null),[m,x]=(0,r.useState)(""),[u,g]=(0,r.useState)(!1),[h,f]=(0,r.useState)(null),[p,b]=(0,r.useState)([]),[w,j]=(0,r.useState)(!0);(0,r.useEffect)(()=>{y()},[]);let y=async()=>{try{let e=await fetch("/api/workflows");if(e.ok){let t=await e.json();s(t.workflows||[])}}catch(e){}finally{j(!1)}},N=async()=>{if(t&&m.trim()){g(!0),f(null),b([]);try{var e,s;let a=await fetch("/api/workflows?id=".concat(t.id));if(!a.ok)throw Error("Failed to get workflow details");let r=await a.json(),l=(null==(e=r.api_key_info)?void 0:e.key_prefix)+"_"+(null==(s=r.api_key_info)?void 0:s.encrypted_suffix),i=await fetch("/api/workflows/execute",{method:"POST",headers:{"Content-Type":"application/json","X-API-Key":l},body:JSON.stringify({input:m.trim(),options:{enableLogs:!0,enableProgress:!0}})});if(!i.ok){let e=await i.json();throw Error(e.details||"Execution failed")}let n=await i.json();f({execution_id:n.execution_id,status:"completed",result:n.result,timestamp:n.executed_at}),await v(n.execution_id)}catch(e){f({execution_id:"error",status:"failed",error:e instanceof Error?e.message:"Unknown error",timestamp:new Date().toISOString()})}finally{g(!1)}}},v=async e=>{try{b([{id:"1",nodeId:"user-request-1",nodeType:"userRequest",level:"info",message:"Processing user input",timestamp:new Date().toISOString()},{id:"2",nodeId:"browsing-1",nodeType:"browsing",level:"info",message:"Starting intelligent browsing",timestamp:new Date().toISOString()},{id:"3",nodeId:"browsing-1",nodeType:"browsing",level:"success",message:"Browsing completed successfully",timestamp:new Date().toISOString()}])}catch(e){}};return w?(0,a.jsx)("div",{className:"min-h-screen bg-[#040716] flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white",children:"Loading workflows..."})}):(0,a.jsx)("div",{className:"min-h-screen bg-[#040716] text-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Workflow Playground"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Test your workflows with real inputs and see live results"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)("div",{className:"bg-gray-900/50 rounded-lg border border-gray-700/50 p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Select Workflow"}),0===e.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("p",{className:"text-gray-400 mb-4",children:"No workflows found"}),(0,a.jsx)("a",{href:"/manual-build",className:"text-[#ff6b35] hover:underline",children:"Create your first workflow"})]}):(0,a.jsx)("div",{className:"space-y-3",children:e.map(e=>(0,a.jsxs)("div",{onClick:()=>d(e),className:"p-4 rounded-lg border cursor-pointer transition-all ".concat((null==t?void 0:t.id)===e.id?"border-[#ff6b35] bg-[#ff6b35]/10":"border-gray-700 hover:border-gray-600"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h3",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(e.is_active?"bg-green-500":"bg-gray-500")})]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-gray-400 mb-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:[e.node_count," nodes"]}),(0,a.jsxs)("span",{children:["v",e.version]})]})]},e.id))})]})}),(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("div",{className:"bg-gray-900/50 rounded-lg border border-gray-700/50 p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Execution"}),t?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:['Input for "',t.name,'"']}),(0,a.jsx)("textarea",{value:m,onChange:e=>x(e.target.value),placeholder:"Enter your request or input for the workflow...",rows:4,className:"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"})]}),(0,a.jsx)("button",{onClick:N,disabled:u||!m.trim(),className:"w-full bg-[#ff6b35] hover:bg-[#e55a2b] disabled:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A,{className:"w-5 h-5 animate-spin"}),"Executing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"w-5 h-5"}),"Execute Workflow"]})}),h&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"p-4 rounded-lg border ".concat("completed"===h.status?"border-green-500 bg-green-900/20":"failed"===h.status?"border-red-500 bg-red-900/20":"border-yellow-500 bg-yellow-900/20"),children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:["completed"===h.status&&(0,a.jsx)(l.A,{className:"w-5 h-5 text-green-500"}),"failed"===h.status&&(0,a.jsx)(c.A,{className:"w-5 h-5 text-red-500"}),"running"===h.status&&(0,a.jsx)(i.A,{className:"w-5 h-5 text-yellow-500 animate-spin"}),(0,a.jsx)("span",{className:"font-medium capitalize",children:h.status})]}),h.error&&(0,a.jsx)("p",{className:"text-red-300 text-sm",children:h.error}),h.result&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Result:"}),(0,a.jsx)("pre",{className:"text-sm bg-gray-800 p-3 rounded overflow-auto max-h-64",children:JSON.stringify(h.result,null,2)})]})]}),p.length>0&&(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"font-medium mb-3 flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"w-4 h-4"}),"Execution Logs"]}),(0,a.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:p.map(e=>(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("span",{className:"inline-block w-2 h-2 rounded-full mr-2 ".concat("success"===e.level?"bg-green-500":"error"===e.level?"bg-red-500":"warning"===e.level?"bg-yellow-500":"bg-blue-500")}),(0,a.jsxs)("span",{className:"text-gray-400",children:["[",e.nodeType,"]"]}),(0,a.jsx)("span",{className:"ml-2",children:e.message})]},e.id))})]})]})]}):(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-400",children:"Select a workflow to start testing"})})]})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(22128)),_N_E=e.O()}]);