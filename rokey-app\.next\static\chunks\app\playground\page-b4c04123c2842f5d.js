(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3882],{11485:(e,t,a)=>{"use strict";a.d(t,{B:()=>n.A,v:()=>r.A});var r=a(30192),n=a(86474)},24766:(e,t,a)=>{Promise.resolve().then(a.bind(a,26225))},26203:(e,t,a)=>{"use strict";a(5279),a(5500),a(6865),a(82771),a(92975),a(65529),a(78046),a(52589)},26225:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var r=a(95155),n=a(12115),s=a(46172);let i=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{d:"M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"}))}),l=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M18.97 3.659a2.25 2.25 0 0 0-3.182 0l-10.94 10.94a3.75 3.75 0 1 0 5.304 5.303l7.693-7.693a.75.75 0 0 1 1.06 1.06l-7.693 7.693a5.25 5.25 0 1 1-7.424-7.424l10.939-10.94a3.75 3.75 0 1 1 5.303 5.304L9.097 18.835l-.008.008-.007.007-.002.002-.003.002A2.25 2.25 0 0 1 5.91 15.66l7.81-7.81a.75.75 0 0 1 1.061 1.06l-7.81 7.81a.75.75 0 0 0 1.054 1.068L18.97 6.84a2.25 2.25 0 0 0 0-3.182Z",clipRule:"evenodd"}))}),o=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z",clipRule:"evenodd"}))}),c=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))}),d=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"}))});var u=a(79112),h=a(95803),m=a(8413),x=a(43456);a(46197);var g=a(4654),p=a(22261),f=a(24403),y=a(41e3),w=a(83298);a(96121);let v=(0,n.lazy)(()=>Promise.all([a.e(3466),a.e(5738),a.e(2396),a.e(4842),a.e(6419),a.e(6210),a.e(1826),a.e(721),a.e(7069),a.e(5313),a.e(6585),a.e(9299),a.e(9420),a.e(3362),a.e(2662),a.e(8669),a.e(4703),a.e(622),a.e(2432),a.e(408),a.e(8925),a.e(7068),a.e(5721)]).then(a.bind(a,35079)).then(e=>({default:e.OrchestrationCanvas}))),b=(e,t)=>{},j=n.memo(e=>{let{chat:t,currentConversation:a,onLoadChat:n,onDeleteChat:s}=e,i=(null==a?void 0:a.id)===t.id;return(0,r.jsxs)("div",{className:"relative group p-3 hover:bg-white/10 rounded-xl transition-all duration-200 ".concat(i?"bg-blue-500/20 border border-blue-400/30":""),children:[(0,r.jsx)("button",{onClick:()=>n(t),className:"w-full text-left",children:(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-white truncate mb-1",children:t.title}),t.last_message_preview&&(0,r.jsx)("p",{className:"text-xs text-gray-400 line-clamp-2 mb-2",children:t.last_message_preview}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,r.jsxs)("span",{children:[t.message_count," messages"]}),(0,r.jsx)("span",{children:new Date(t.updated_at).toLocaleDateString()})]})]})})}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),s(t.id)},className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-400 hover:bg-red-500/20 rounded transition-all duration-200",title:"Delete conversation",children:(0,r.jsx)(o,{className:"w-4 h-4"})})]})});function N(){var e,t,a;let{isCollapsed:o,isHovered:N,setHoverDisabled:S}=(0,p.c)(),{user:k}=(0,w.R)(),T=!o||N?"256px":"64px",C=(null==k||null==(e=k.user_metadata)?void 0:e.first_name)||(null==k||null==(a=k.user_metadata)||null==(t=a.full_name)?void 0:t.split(" ")[0])||"",[E,A]=(0,n.useState)([]),[_,O]=(0,n.useState)(""),[L,D]=(0,n.useState)(!0),I=(0,n.useCallback)(async e=>{if(e)try{let t=await fetch("/api/keys?custom_config_id=".concat(e),{cache:"force-cache",headers:{"Cache-Control":"max-age=300"}});t.ok&&await t.json()}catch(e){}},[]);(0,n.useEffect)(()=>{if(_){let e=setTimeout(()=>{I(_)},1e3);return()=>clearTimeout(e)}},[_,I]);let[M,H]=(0,n.useState)(""),[R,P]=(0,n.useState)([]),[z,W]=(0,n.useState)(!1),[B,F]=(0,n.useState)(null),[Y,q]=(0,n.useState)(!0),[U,J]=(0,n.useState)(!1),[Z,K]=(0,n.useState)(null),[V,X]=(0,n.useState)([]),[G,Q]=(0,n.useState)([]),$=(0,n.useRef)(null),ee=(0,n.useRef)(null),et=(0,n.useRef)(null),[ea,er]=(0,n.useState)(!1),[en,es]=(0,n.useState)(null),[ei,el]=(0,n.useState)(null),[eo,ec]=(0,n.useState)(""),[ed,eu]=(0,n.useState)(!1),[eh,em]=(0,n.useState)(null),[ex,eg]=(0,n.useState)(!1),[ep,ef]=(0,n.useState)(!1),[ey,ew]=(0,n.useState)(!1),[ev,eb]=(0,n.useState)(!1),[ej,eN]=(0,n.useState)(!1);(0,n.useEffect)(()=>{S(ey&&!ev)},[ey,ev,S]),(0,n.useEffect)(()=>{},[ex,eh,ey,ev]);let eS=(0,y.w6)({enableAutoProgression:!0,onStageChange:void 0}),[ek,eT]=(0,n.useState)(""),[eC,eE]=(0,n.useState)(null),[eA,e_]=(0,n.useState)(!1),eO=(e,t)=>{let a="";if(e.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**"))a="Multi-Role AI Orchestration Started";else if(e.includes("\uD83D\uDCCB **Orchestration Plan:**"))a="Planning specialist assignments";else if(e.includes("\uD83E\uDD16 **Moderator:**"))a="Moderator coordinating specialists";else if(e.includes("Specialist:")&&e.includes("Working...")){let t=e.match(/(\w+)\s+Specialist:/);a=t?"".concat(t[1]," Specialist working"):"Specialist working on your request"}else e.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")?a="Synthesizing specialist responses":e.includes("Analyzing and processing")&&(a="Analyzing and processing with specialized expertise");a&&a!==ek&&(eT(a),t.updateOrchestrationStatus(a))},eL=async()=>{if(_&&en){W(!0),eT("Continuing synthesis automatically..."),eS.startProcessing();try{let a,r={id:Date.now().toString()+"-continue",role:"user",content:[{type:"text",text:"continue"}]};P(e=>[...e,r]),await eQ(en.id,r),a={custom_api_config_id:_,messages:[...R.map(e=>({role:e.role,content:1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content})),{role:"user",content:"continue"}],stream:Y,...(null==k?void 0:k.id)&&{_internal_user_id:k.id}};let n=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(a),cache:"no-store"});if(n.ok){let a,r=await n.text();try{a=JSON.parse(r)}catch(e){a=null}if((null==a?void 0:a.error)==="synthesis_complete"){P(e=>e.slice(0,-1)),W(!1),eT(""),eS.markComplete(),H("continue"),setTimeout(()=>{e7()},100);return}let s=new Response(r,{status:n.status,statusText:n.statusText,headers:n.headers});if(Y&&s.body){let a=s.body.getReader(),r=new TextDecoder,n=Date.now().toString()+"-assistant-continue",i={id:n,role:"assistant",content:[{type:"text",text:""}]};P(e=>[...e,i]);let l="",o=!1,c=null,d=s.headers.get("X-Synthesis-Progress"),u=s.headers.get("X-Synthesis-Complete"),h=null!==d;for(h?(eS.markStreaming(),eT("")):(eS.markOrchestrationStarted(),eT("Continuing synthesis..."),c=setTimeout(()=>{o||(eS.markStreaming(),eT(""))},800));;){let{done:s,value:d}=await a.read();if(s)break;for(let a of r.decode(d,{stream:!0}).split("\n"))if(a.startsWith("data: ")){let r=a.substring(6);if("[DONE]"===r.trim())break;try{var e,t;let a=JSON.parse(r);if(a.choices&&(null==(t=a.choices[0])||null==(e=t.delta)?void 0:e.content)){let e=a.choices[0].delta.content;l+=e,!h&&!o&&(e.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||e.includes("\uD83D\uDCCB **Orchestration Plan:**")||e.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||e.includes("\uD83E\uDD16 **Moderator:**")||e.includes("Specialist:"))?(o=!0,c&&(clearTimeout(c),c=null),eO(e,eS)):!h&&o&&eO(e,eS);let t=i.content[0];t.text=l,P(e=>e.map(e=>e.id===n?{...e,content:[t]}:e))}}catch(e){}}}if(c&&clearTimeout(c),l){let e={...i,content:[{type:"text",text:l}]};h&&"true"!==u&&l.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")?(await eQ(en.id,e),setTimeout(()=>{eL()},1e3)):await eQ(en.id,e)}}}else throw Error("Auto-continuation failed: ".concat(n.status))}catch(t){let e={id:Date.now().toString()+"-error-continue",role:"error",content:[{type:"text",text:"Auto-continuation failed: ".concat(t instanceof Error?t.message:"Unknown error")}]};P(t=>[...t,e])}finally{W(!1),eT(""),eS.markComplete()}}},{chatHistory:eD,isLoading:eI,isStale:eM,error:eH,refetch:eR,prefetch:eP,invalidateCache:ez}=(0,f.mx)({configId:_,enablePrefetch:!0,cacheTimeout:3e5,staleTimeout:3e4}),{prefetchChatHistory:eW}=(0,f.l2)(),eB=(0,n.useMemo)(()=>[{id:"explain-concept",title:"Explain a concept",description:"Get clear explanations on any topic",icon:"\uD83D\uDCA1",color:"bg-blue-100 text-blue-700",prompt:"Explain how machine learning works in simple terms"},{id:"write-code",title:"Write code",description:"Generate code snippets and solutions",icon:"\uD83D\uDCBB",color:"bg-green-100 text-green-700",prompt:"Write a Python function that calculates the fibonacci sequence"},{id:"brainstorm-ideas",title:"Brainstorm ideas",description:"Generate creative solutions and ideas",icon:"\uD83E\uDDE0",color:"bg-purple-100 text-purple-700",prompt:"Help me brainstorm innovative features for a mobile app"},{id:"analyze-data",title:"Analyze data",description:"Get insights from data and trends",icon:"\uD83D\uDCCA",color:"bg-amber-100 text-amber-700",prompt:"What are the key trends in artificial intelligence for 2025?"}],[]);(0,n.useEffect)(()=>{let e=async()=>{let e=performance.now();try{L&&await new Promise(e=>setTimeout(e,50));let t=await fetch("/api/custom-configs");if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to fetch configurations")}let a=await t.json();A(a),a.length>0&&O(a[0].id),D(!1),b("Config fetch",e)}catch(t){F("Failed to load configurations: ".concat(t.message)),A([]),D(!1),b("Config fetch (failed)",e)}};k&&L&&e()},[L,k]);let eF=e=>new Promise((t,a)=>{let r=new FileReader;r.readAsDataURL(e),r.onload=()=>t(r.result),r.onerror=e=>a(e)}),eY=async e=>{let t=Array.from(e.target.files||[]);if(0===t.length)return;let a=V.length,r=t.slice(0,10-a);r.length<t.length&&F("You can only upload up to 10 images. ".concat(t.length-r.length," images were not added."));try{let e=[];for(let t of r){let a=await eF(t);e.push(a)}X(e=>[...e,...r]),Q(t=>[...t,...e])}catch(e){F("Failed to process one or more images. Please try again.")}$.current&&($.current.value="")},eq=e=>{void 0!==e?(X(t=>t.filter((t,a)=>a!==e)),Q(t=>t.filter((t,a)=>a!==e))):(X([]),Q([])),$.current&&($.current.value="")},eU=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];et.current&&et.current.scrollTo({top:et.current.scrollHeight,behavior:e?"smooth":"auto"})},eJ=function(){if(arguments.length>0&&void 0!==arguments[0]&&arguments[0],et.current){let e=et.current;setTimeout(()=>{let t=e.clientHeight,a=e.scrollHeight-.8*t;e.scrollTo({top:a,behavior:"smooth"}),setTimeout(()=>{Math.abs(e.scrollTop-a)>50&&(e.scrollTop=a)},100),setTimeout(()=>{let t=e.querySelectorAll("[data-message-id]"),a=t[t.length-1];a&&a.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})},200)},100)}},eZ=(0,n.useMemo)(()=>R.length>100?R.slice(-50):R,[R]);(0,n.useEffect)(()=>{if(R.length>0){let e=R[R.length-1];requestAnimationFrame(()=>{(null==e?void 0:e.role)==="user"?(eJ(),setTimeout(()=>{if(et.current){let e=et.current;e.scrollHeight-e.scrollTop-e.clientHeight<200||eU(!0)}},300)):eU()})}},[R.length]),(0,n.useEffect)(()=>{z&&R.length>0&&requestAnimationFrame(()=>{eU()})},[R,z]),(0,n.useEffect)(()=>{if(z&&R.length>0){let e=R[R.length-1];e&&"assistant"===e.role&&requestAnimationFrame(()=>{eU()})}},[R,z]),(0,n.useEffect)(()=>{let e=setTimeout(()=>{R.length>0&&requestAnimationFrame(()=>{if(et.current){let e=et.current;e.scrollHeight-e.scrollTop-e.clientHeight<100&&eU()}})},200);return()=>clearTimeout(e)},[o,N,ea,R.length]),(0,n.useEffect)(()=>{if(_&&E.length>0){let e=E.filter(e=>e.id!==_).slice(0,3),t=setTimeout(()=>{e.forEach(e=>{eW(e.id)})},2e3);return()=>clearTimeout(t)}},[_,E,eW]);let eK=async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t||eu(!0);try{let a=t?R.length:0,r=Date.now(),n=await fetch("/api/chat/messages?conversation_id=".concat(e.id,"&limit=").concat(25,"&offset=").concat(a,"&latest=").concat(!t,"&_cb=").concat(r),{cache:"no-store",headers:{"Cache-Control":"no-cache"}});if(!n.ok)throw Error("Failed to load conversation messages");let s=(await n.json()).map(e=>({id:e.id,role:e.role,content:e.content.map(e=>{var t;return"text"===e.type&&e.text?{type:"text",text:e.text}:"image_url"===e.type&&(null==(t=e.image_url)?void 0:t.url)?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:""}})}));t?P(e=>[...s,...e]):(P(s),en&&en.id===e.id||es(e)),F(null)}catch(e){F("Failed to load conversation: ".concat(e.message))}finally{t||eu(!1)}},eV=async()=>{if(!_||0===R.length)return null;try{let e=null==en?void 0:en.id;if(!e){let t=R[0],a="New Chat";if(t&&t.content.length>0){let e=t.content.find(e=>"text"===e.type);e&&e.text&&(a=e.text.slice(0,50)+(e.text.length>50?"...":""))}let r={title:a};r.custom_api_config_id=_;let n=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!n.ok)throw Error("Failed to create conversation");let s=await n.json();e=s.id,es(s)}for(let t of R){if(t.id.includes("-")&&t.id.length>20)continue;let a={conversation_id:e,role:t.role,content:t.content};await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})}return en||eR(!0),e}catch(e){return F("Failed to save conversation: ".concat(e.message)),null}},eX=async e=>{try{if(!(await fetch("/api/chat/conversations?id=".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete conversation");(null==en?void 0:en.id)===e&&(es(null),P([])),eR(!0)}catch(e){F("Failed to delete conversation: ".concat(e.message))}},eG=async e=>{if(!_)return null;try{let t="New Chat";if(e.content.length>0){let a=e.content.find(e=>"text"===e.type);a&&a.text&&(t=a.text.slice(0,50)+(a.text.length>50?"...":""))}let a={title:t};a.custom_api_config_id=_;let r=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.ok)throw Error("Failed to create conversation");let n=await r.json();return es(n),n.id}catch(e){return F("Failed to create conversation: ".concat(e.message)),null}},eQ=async(e,t)=>{try{let a={conversation_id:e,role:t.role,content:t.content},r=await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.ok)throw Error("Failed to save message");return await r.json()}catch(e){}},e$=e=>{H(e),setTimeout(()=>{let e=document.querySelector('textarea[placeholder*="Type a message"]');e&&(e.focus(),e.setSelectionRange(e.value.length,e.value.length))},100)},e0=async()=>{R.length>0&&await eV(),P([]),es(null),H(""),F(null),eq(),eS.reset()},e1=(0,n.useCallback)(async e=>{if(e===_)return;R.length>0&&await e0(),O(e);let t=E.find(t=>t.id===e);t&&t.name},[_,R.length,e0,O,E]),e2=async e=>{es(e),P([]),H(""),F(null),eq();let t=(async()=>{if(R.length>0&&!en)try{await eV()}catch(e){}})();try{await eK(e)}catch(e){F("Failed to load conversation: ".concat(e.message))}await t},e5=(e,t)=>{el(e),ec(t)},e4=()=>{el(null),ec("")},e3=async()=>{if(!ei||!eo.trim()||!_)return;let e=R.findIndex(e=>e.id===ei);if(-1===e)return;let t=[...R];t[e]={...t[e],content:[{type:"text",text:eo.trim()}]};let a=t.slice(0,e+1);if(P(a),el(null),ec(""),en)try{if(R.slice(e+1).length>0){let t=R[e];if(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(t.id)){let e=await fetch("/api/chat/messages?conversation_id=".concat(en.id,"&limit=1&latest=false"));if(e.ok){let a=(await e.json()).find(e=>e.id===t.id);if(a){let e=new Date(a.created_at).getTime(),t=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:en.id,after_timestamp:e})});t.ok&&await t.json()}}}else{let e=parseInt(t.id)||Date.now(),a=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:en.id,after_timestamp:e})});a.ok&&await a.json()}}let t=a[e];if(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(t.id)){let e=await fetch("/api/chat/messages?id=".concat(t.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:t.content})});if(e.ok)await e.json();else{let t=await e.text();throw Error("Failed to update message: ".concat(t))}}else await eQ(en.id,t);eR(!0),Object.keys(localStorage).filter(e=>e.startsWith("chat_")||e.startsWith("conversation_")).forEach(e=>localStorage.removeItem(e))}catch(e){F("Failed to update conversation: ".concat(e.message))}await e8(a)},e8=async e=>{if(!_||0===e.length)return;W(!0),F(null),eS.startProcessing();let t={custom_api_config_id:_,messages:e.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),stream:Y,...(null==k?void 0:k.id)&&{_internal_user_id:k.id}};try{var a,r,n,s,i,l,o;eS.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(t),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"API Error: ".concat(e.statusText," (Status: ").concat(e.status,")"))}if(eS.analyzeResponseHeaders(e.headers),setTimeout(()=>{Y&&eS.markStreaming()},400),Y&&e.body){let t=e.body.getReader(),n=new TextDecoder,s=Date.now().toString()+"-assistant",i={id:s,role:"assistant",content:[{type:"text",text:""}]};P(e=>[...e,i]);let l="",o=!1,c=null,d="";for(c=setTimeout(()=>{o||eS.markStreaming()},400);;){let{done:e,value:u}=await t.read();if(e)break;for(let e of n.decode(u,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if("orchestration.progress"===e.object){o=!0,d=e.data.message,c&&(clearTimeout(c),c=null),eS.markOrchestrationStarted(),eT(d);return}if(e.choices&&(null==(r=e.choices[0])||null==(a=r.delta)?void 0:a.content)){let t=e.choices[0].delta.content;l+=t,!o&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(o=!0,c&&(clearTimeout(c),c=null),eS.markOrchestrationStarted()),o&&!d&&eO(t,eS);let a=i.content[0];a.text=l,P(e=>e.map(e=>e.id===s?{...e,content:[a]}:e))}}catch(e){}}}if(c&&clearTimeout(c),l&&en){let e={...i,content:[{type:"text",text:l}]};l.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||l.includes("*The response will continue automatically in a new message...*")?(await eQ(en.id,e),setTimeout(()=>{eL()},2e3)):await eQ(en.id,e)}}else{let t=await e.json(),a="Could not parse assistant's response.";(null==(i=t.choices)||null==(s=i[0])||null==(n=s.message)?void 0:n.content)?a=t.choices[0].message.content:(null==(o=t.content)||null==(l=o[0])?void 0:l.text)?a=t.content[0].text:"string"==typeof t.text&&(a=t.text);let r={id:Date.now().toString()+"-assistant",role:"assistant",content:[{type:"text",text:a}]};P(e=>[...e,r]),en&&await eQ(en.id,r)}}catch(t){let e={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred."}]};P(t=>[...t,e]),F(t.message)}finally{W(!1),eS.markComplete()}},e6=async(e,t)=>{if(!_||e<0||e>=R.length||"assistant"!==R[e].role)return;W(!0),F(null),eT(""),eS.startProcessing();let a=R.slice(0,e);if(P(a),en)try{if(R.slice(e).length>0){let t=R[e],a=parseInt(t.id)||Date.now(),r=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:en.id,from_timestamp:a})});r.ok&&await r.json()}eR(!0)}catch(e){}let r={custom_api_config_id:_,messages:a.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),stream:Y,...t&&{specific_api_key_id:t},...(null==k?void 0:k.id)&&{_internal_user_id:k.id}};try{eS.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(r),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"HTTP ".concat(e.status,": ").concat(e.statusText))}eS.analyzeResponseHeaders(e.headers);let t=e.headers.get("X-RoKey-Orchestration-ID"),a=e.headers.get("X-RoKey-Orchestration-Active");if(t&&"true"===a&&(em(t),eg(!0),ef(!1)),setTimeout(()=>{Y&&eS.markStreaming()},400),Y&&e.body){let t=e.body.getReader(),a=new TextDecoder,r="",l={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:""}]};P(e=>[...e,l]);try{for(;;){let{done:e,value:o}=await t.read();if(e)break;for(let e of a.decode(o,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{var n,s,i;let e=JSON.parse(t);if(null==(i=e.choices)||null==(s=i[0])||null==(n=s.delta)?void 0:n.content){let t=e.choices[0].delta.content;r+=t,t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:")?(eS.markOrchestrationStarted(),eO(t,eS)):ek&&eO(t,eS),P(e=>e.map(e=>e.id===l.id?{...e,content:[{type:"text",text:r}]}:e))}}catch(e){}}}}finally{t.releaseLock()}if(r&&en){let e={...l,content:[{type:"text",text:r}]};await eQ(en.id,e)}}else{let t=await e.json(),a="";t.choices&&t.choices.length>0&&t.choices[0].message?a=t.choices[0].message.content:t.content&&Array.isArray(t.content)&&t.content.length>0&&(a=t.content[0].text);let r={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:a}]};P(e=>[...e,r]),en&&await eQ(en.id,r)}}catch(t){let e={id:Date.now().toString()+"-error-retry",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred during retry."}]};P(t=>[...t,e]),F(t.message),en&&await eQ(en.id,e)}finally{W(!1),eS.markComplete()}},e7=(0,n.useCallback)(async e=>{let t;if(e&&e.preventDefault(),!M.trim()&&0===V.length||!_)return;if("continue"===M.trim().toLowerCase()&&R.length>0){H(""),await eL();return}W(!0),F(null),eT("");let a=new AbortController;K(a),eS.startProcessing(),performance.now();let r=M.trim(),n=[...V],s=[...G];H(""),eq();let i=[],l=[];if(r&&(i.push({type:"text",text:r}),l.push({type:"text",text:r})),n.length>0)try{for(let e=0;e<n.length;e++){let t=n[e],a=s[e],r=await eF(t);i.push({type:"image_url",image_url:{url:a}}),l.push({type:"image_url",image_url:{url:r}})}}catch(e){F("Failed to process one or more images. Please try again."),W(!1),H(r),X(n),Q(s);return}let o={id:Date.now().toString(),role:"user",content:i};P(e=>[...e,o]);let c=null==en?void 0:en.id,d=Promise.resolve(c||null);Promise.resolve(),c||en||(d=eG(o)),d.then(async e=>{e&&await eQ(e,o)}).catch(e=>{});let u=R.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),h={"Content-Type":"application/json"};h.Authorization="Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13"),t={custom_api_config_id:_,messages:[...u,{role:"user",content:1===l.length&&"text"===l[0].type?l[0].text:l}],stream:Y,...(null==k?void 0:k.id)&&{_internal_user_id:k.id}};try{var m,x;eS.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:h,body:JSON.stringify(t),cache:"no-store",signal:a.signal});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"API Error: ".concat(e.statusText," (Status: ").concat(e.status,")"))}eS.analyzeResponseHeaders(e.headers);let r=e.headers.get("X-RoKey-Orchestration-ID"),n=e.headers.get("X-RoKey-Orchestration-Active");if(r&&"true"===n&&(em(r),eg(!0),ef(!1)),Y&&e.body){let t=e.body.getReader(),a=new TextDecoder,r=Date.now().toString()+"-assistant",n={id:r,role:"assistant",content:[{type:"text",text:""}]};P(e=>[...e,n]);let s="",i=!1,l=null,o="";for(l=setTimeout(()=>{i||eS.markStreaming()},400);;){let{done:e,value:c}=await t.read();if(e)break;for(let e of a.decode(c,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if("orchestration.progress"===e.object){i=!0,o=e.data.message,l&&(clearTimeout(l),l=null),eS.markOrchestrationStarted(),eT(o);continue}if("browsing.progress"===e.object){e_(!0),eE(e.data);continue}if(e.result,e.choices&&(null==(x=e.choices[0])||null==(m=x.delta)?void 0:m.content)){let t=e.choices[0].delta.content;s+=t,!i&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(i=!0,l&&(clearTimeout(l),l=null),eS.markOrchestrationStarted()),i&&!o&&eO(t,eS);let a=n.content[0];a.text=s,P(e=>e.map(e=>e.id===r?{...e,content:[a]}:e))}}catch(e){}}}if(l&&clearTimeout(l),s){let t={...n,content:[{type:"text",text:s}]},a=e.headers.get("X-Synthesis-Progress");e.headers.get("X-Synthesis-Complete"),s.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||s.includes("*The response will continue automatically in a new message...*")?(d.then(async e=>{e&&await eQ(e,t)}),setTimeout(()=>{eL()},null!==a?1e3:2e3)):d.then(async e=>{e&&await eQ(e,t)})}}}catch(e){if(eS.markError(),"AbortError"===e.name){let e={id:Date.now().toString()+"-stopped",role:"system",content:[{type:"text",text:"⏹️ Request stopped by user"}]};P(t=>[...t,e]),F(null)}else{let t={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:e.message||"An unexpected error occurred."}]};P(e=>[...e,t]),F(e.message),d.then(async e=>{e&&await eQ(e,t)}).catch(e=>{})}}finally{W(!1),K(null),workflowStream&&(workflowStream.close(),setWorkflowStream(null),setTimeout(()=>setWorkflowStatus(""),3e3)),eS.markComplete(),(0,y.n4)(eS.stageHistory),performance.now(),d.then(async e=>{e&&!en&&eR(!0)}).catch(e=>{})}},[M,V,_,z,en,R,eS,Y,ek,P,F,W,es,H,X,Q,eR]);return(0,r.jsxs)("div",{className:"min-h-screen bg-[#040716] flex",children:[(0,r.jsxs)("div",{className:"flex-1 flex flex-col transition-all duration-300 ease-in-out",style:{marginLeft:T,marginRight:ey&&!ev?"50%":ea?"0px":"320px"},children:[(0,r.jsx)("div",{className:"fixed top-0 z-40 bg-[#040716]/95 backdrop-blur-sm border-b border-gray-800/50 transition-all duration-300 ease-in-out",style:{left:T,right:ey&&!ev?"50%":ea?"0px":"320px"},children:(0,r.jsx)("div",{className:"px-6 py-2",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"flex items-center space-x-2",children:_?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:"Connected"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-400",children:"Not Connected"})]})}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("select",{value:_,onChange:e=>e1(e.target.value),disabled:0===E.length,className:"appearance-none px-4 py-2.5 pr-10 bg-gray-800/50 border border-gray-700/50 rounded-xl text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-gray-600 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]",children:[(0,r.jsx)("option",{value:"",children:"Select Router Configuration"}),E.length>0&&(0,r.jsx)("optgroup",{label:"\uD83D\uDD00 Router Configurations",children:E.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))})]}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:"Streaming"}),(0,r.jsx)("button",{onClick:()=>q(!Y),className:"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20 shadow-sm ".concat(Y?"bg-blue-500 shadow-blue-200":"bg-gray-600"),children:(0,r.jsx)("span",{className:"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm ".concat(Y?"translate-x-6":"translate-x-1")})})]})]})})}),(0,r.jsx)("div",{className:"flex-1 flex flex-col pt-0 pb-32",children:0!==R.length||en?(0,r.jsxs)("div",{className:"flex-1 relative ".concat(ey&&!ev?"overflow-visible":"overflow-hidden"),children:[(0,r.jsx)("div",{className:"h-full flex ".concat(ey&&!ev?"justify-start":"justify-center"),children:(0,r.jsx)("div",{ref:et,className:"w-full h-full overflow-y-auto px-6 transition-all duration-300 ".concat(ey&&!ev?"max-w-2xl -ml-32":"max-w-3xl"),onScroll:e=>{let t=e.currentTarget;J(!(t.scrollHeight-t.scrollTop-t.clientHeight<100)&&R.length>0)},children:(0,r.jsxs)("div",{className:"pt-0 pb-80",children:[en&&R.length>=50&&(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsx)("button",{onClick:()=>eK(en,!0),disabled:eI,className:"px-4 py-2 text-sm text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 rounded-lg transition-colors duration-200 disabled:opacity-50",children:eI?"Loading...":"Load Earlier Messages"})}),ed&&0===R.length&&(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,t)=>(0,r.jsxs)("div",{className:"flex justify-start",children:[(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0"}),(0,r.jsx)("div",{className:"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]})})]},t))}),R.length>100&&(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsxs)("div",{className:"text-sm text-gray-400 bg-white/10 rounded-lg px-4 py-2 inline-block",children:["Showing last 50 of ",R.length," messages for better performance"]})}),eZ.map((e,t)=>{let a=t>0?eZ[t-1]:null,n=a&&"user"===a.role&&"assistant"===e.role;return(0,r.jsx)("div",{"data-message-id":e.id,className:"flex ".concat("user"===e.role?"justify-end":"justify-start"," group ").concat(ey&&!ev?"-ml-96":""," ").concat("assistant"===e.role&&ey&&!ev?"ml-8":""," ").concat(0===t?"pt-3":n?"mt-6":"mt-32"),children:(0,r.jsxs)("div",{className:"".concat("user"===e.role?ey&&!ev?"max-w-[60%]":"max-w-[50%]":"max-w-[100%]"," relative ").concat("user"===e.role?"bg-gray-700/60 text-white rounded-2xl rounded-br-lg shadow-sm border border-gray-600/30":"assistant"===e.role?"text-white":"system"===e.role?"bg-amber-500/20 text-amber-300 rounded-xl border border-amber-500/30":"bg-red-500/20 text-red-300 rounded-xl border border-red-500/30"," ").concat("user"===e.role?"px-4 py-3":"py-3"," transition-all duration-300"),children:["user"===e.role&&(0,r.jsxs)("div",{className:"absolute -bottom-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[(0,r.jsx)(h.A,{text:e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message",className:"text-gray-400 hover:text-white hover:bg-white/20 rounded-lg cursor-pointer"}),(0,r.jsx)("button",{onClick:()=>e5(e.id,e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n")),className:"p-1.5 text-gray-400 hover:text-white hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer",title:"Edit message",children:(0,r.jsx)(d,{className:"w-4 h-4 stroke-2"})})]}),"user"!==e.role&&(0,r.jsxs)("div",{className:"absolute -bottom-8 left-0 z-10 flex items-center space-x-2",children:[(0,r.jsx)(h.A,{text:e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message"}),"assistant"===e.role&&_&&(0,r.jsx)(m.A,{configId:_,onRetry:e=>e6(t,e),disabled:z})]}),(0,r.jsx)("div",{className:"space-y-2 chat-message-content",children:"user"===e.role&&ei===e.id?(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("textarea",{value:eo,onChange:e=>ec(e.target.value),className:"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none",placeholder:"Edit your message...",rows:3,autoFocus:!0}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:e3,disabled:!eo.trim(),className:"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200",children:[(0,r.jsx)(s.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Save & Continue"})]}),(0,r.jsxs)("button",{onClick:e4,className:"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200",children:[(0,r.jsx)(c,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Cancel"})]})]}),(0,r.jsx)("p",{className:"text-white/70 text-xs",children:"\uD83D\uDCA1 Saving will restart the conversation from this point, removing all messages that came after."})]}):e.content.map((t,a)=>{if("text"===t.type)if("assistant"===e.role)return(0,r.jsx)(u.A,{content:t.text,className:"text-[16.5px]"},a);else return(0,r.jsx)("div",{className:"whitespace-pre-wrap break-words leading-relaxed text-[16.5px]",children:t.text},a);return"image_url"===t.type?(0,r.jsx)("img",{src:t.image_url.url,alt:"uploaded content",className:"max-w-full max-h-48 rounded-xl shadow-sm"},a):null})})]})},e.id)}),ex&&eh&&ev&&(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)(g.f,{orchestrationComplete:ep,onMaximize:()=>{eN(!0),setTimeout(()=>eN(!1),100)},isCanvasOpen:ey,isCanvasMinimized:ev})}),z&&(0,r.jsxs)("div",{className:"flex justify-start group",children:[(0,r.jsx)(x.A,{currentStage:eS.currentStage,isStreaming:Y&&"typing"===eS.currentStage,orchestrationStatus:ek,onStageChange:e=>{}}),!1]}),ex&&eh&&(0,r.jsx)(n.Suspense,{fallback:(0,r.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"}),(0,r.jsx)("span",{className:"ml-3 text-gray-600",children:"Loading orchestration canvas..."})]}),children:(0,r.jsx)(v,{executionId:eh,onCanvasStateChange:(e,t)=>{ew(e),eb(t),e&&!t&&er(!0)},forceMaximize:ej,onComplete:e=>{if(null==eh?void 0:eh.startsWith("test-execution-id"))return void ef(!0);ef(!0);let t={id:Date.now().toString()+"-orchestration-final",role:"assistant",content:[{type:"text",text:e}]};P(e=>[...e,t]),eg(!1),em(null),ef(!1),(null==en?void 0:en.id)&&eQ(en.id,t).catch(e=>{})},onError:e=>{null!=eh&&eh.startsWith("test-execution-id")||(F("Orchestration error: ".concat(e)),eg(!1),em(null))}})}),(0,r.jsx)("div",{ref:ee})]})})}),U&&(0,r.jsx)("div",{className:"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10",children:(0,r.jsx)("button",{onClick:()=>eU(!0),className:"w-12 h-12 bg-gray-800/50 rounded-full shadow-lg border border-gray-700/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group","aria-label":"Scroll to bottom",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-gray-400 group-hover:text-blue-400 transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]}):(0,r.jsx)("div",{className:"fixed inset-0 flex items-center justify-center px-6",style:{top:"80px",left:T,right:ey&&!ev?"50%":ea?"0px":"320px",bottom:"120px"},children:(0,r.jsx)("div",{className:"w-full mx-auto transition-all duration-300 ".concat(ey&&!ev?"max-w-2xl":"max-w-3xl"),children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold mb-4",children:(0,r.jsxs)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400",children:["Hello",C?" ".concat(C):""]})}),(0,r.jsx)("p",{className:"text-lg text-gray-400 max-w-md mx-auto",children:"Select a router configuration and explore RouKey's intelligent routing capabilities below."})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4 w-full max-w-2xl",children:eB.map(e=>(0,r.jsxs)("button",{onClick:()=>e$(e.prompt),disabled:!_,className:"group relative p-6 bg-gray-800/30 rounded-2xl border border-gray-700/50 hover:border-gray-600/70 hover:bg-gray-800/50 transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed ".concat(_?"cursor-pointer hover:scale-[1.02]":"cursor-not-allowed"),children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-xl flex items-center justify-center text-xl bg-white/10 text-white group-hover:scale-110 transition-transform duration-200",children:e.icon}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-white mb-1 group-hover:text-blue-400 transition-colors",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-400 leading-relaxed",children:e.description})]})]}),(0,r.jsx)("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4l8 8-8 8M4 12h16"})})})]},e.id))})]})})})}),(0,r.jsx)("div",{className:"fixed bottom-0 z-50 transition-all duration-300 ease-in-out bg-gradient-to-t from-[#040716] via-[#040716]/95 to-transparent",style:{left:T,right:ey&&!ev?"50%":ea?"0px":"320px"},children:(0,r.jsx)("div",{className:"px-6 pt-24 pb-2 flex justify-center",children:(0,r.jsxs)("div",{className:"w-full transition-all duration-300 ".concat(ey&&!ev?"max-w-2xl":"max-w-3xl"),children:[B&&(0,r.jsx)("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-2xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("p",{className:"text-red-800 text-sm font-medium",children:B})]})}),(0,r.jsxs)("form",{onSubmit:e7,children:[G.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-white",children:[G.length," image",G.length>1?"s":""," attached"]})]}),(0,r.jsx)("button",{type:"button",onClick:()=>eq(),className:"text-xs text-gray-400 hover:text-red-400 transition-colors duration-200 font-medium",children:"Clear all"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3",children:G.map((e,t)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsxs)("div",{className:"relative overflow-hidden rounded-xl border-2 border-gray-700/50 bg-gray-800/30 shadow-sm hover:shadow-md transition-all duration-200 aspect-square",children:[(0,r.jsx)("img",{src:e,alt:"Preview ".concat(t+1),className:"w-full h-full object-cover"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200"}),(0,r.jsx)("button",{type:"button",onClick:()=>eq(t),className:"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100","aria-label":"Remove image ".concat(t+1),children:(0,r.jsx)(c,{className:"w-3.5 h-3.5"})})]}),(0,r.jsx)("div",{className:"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium",children:t+1})]},t))})]}),(0,r.jsx)("div",{className:"relative bg-gray-800/30 rounded-2xl border border-gray-700/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-blue-500/20 focus-within:border-gray-600",children:(0,r.jsxs)("div",{className:"flex items-end p-4 space-x-3",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",multiple:!0,onChange:eY,ref:$,className:"hidden",id:"imageUpload"}),(0,r.jsxs)("button",{type:"button",onClick:()=>{var e;return null==(e=$.current)?void 0:e.click()},disabled:V.length>=10,className:"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 ".concat(V.length>=10?"text-gray-600 cursor-not-allowed":"text-gray-400 hover:text-blue-400 hover:bg-white/10"),"aria-label":V.length>=10?"Maximum 10 images reached":"Attach images",title:V.length>=10?"Maximum 10 images reached":"Attach images (up to 10)",children:[(0,r.jsx)(l,{className:"w-5 h-5"}),V.length>0&&(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:V.length})]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("textarea",{value:M,onChange:e=>H(e.target.value),placeholder:_?"Type a message...":"Select a router configuration first",disabled:!_||z,rows:1,className:"w-full px-0 py-2 bg-transparent border-0 text-white placeholder-gray-500 focus:outline-none focus-visible:outline-none focus:border-none focus:ring-0 disabled:opacity-50 resize-none text-base leading-relaxed [&:focus]:outline-none [&:focus-visible]:outline-none [&:focus]:border-none [&:focus]:ring-0 [&:focus]:shadow-none [&:active]:outline-none [&:active]:border-none [&:active]:ring-0 [&:active]:shadow-none",onKeyDown:e=>{"Enter"===e.key&&!e.shiftKey&&(e.preventDefault(),(M.trim()||V.length>0)&&_&&!z&&e7())},style:{minHeight:"24px",maxHeight:"120px",outline:"none !important",border:"none !important",boxShadow:"none !important",WebkitAppearance:"none",MozAppearance:"none"},onInput:e=>{let t=e.target;t.style.height="auto",t.style.height=Math.min(t.scrollHeight,120)+"px"}})}),(0,r.jsx)("button",{type:z?"button":"submit",onClick:z?()=>{Z&&(Z.abort(),K(null)),W(!1),eT(""),setWorkflowStatus(""),eS.reset(),workflowStream&&(workflowStream.close(),setWorkflowStream(null))}:void 0,disabled:!_||!z&&!M.trim()&&0===V.length,className:"p-2.5 ".concat(z?"bg-red-500 hover:bg-red-600":"bg-blue-500 hover:bg-blue-600"," disabled:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0"),"aria-label":z?"Stop request":"Send message",title:z?"Stop request":"Send message",children:z?(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 6h12v12H6z"})}):(0,r.jsx)(i,{className:"w-5 h-5"})})]})})]})]})})})]}),(0,r.jsx)("div",{className:"fixed top-0 right-0 h-full bg-[#030614] border-l border-gray-800/50 shadow-xl transition-all duration-300 ease-in-out z-30 ".concat(ea?"w-0 overflow-hidden":"w-80"),style:{transform:ea?"translateX(100%)":"translateX(0)",opacity:+!ea},children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-800/50",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"font-semibold text-white",children:"History"}),(0,r.jsxs)("p",{className:"text-xs text-gray-400",children:[eD.length," conversations"]})]})]}),(0,r.jsx)("button",{onClick:()=>er(!ea),className:"p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200 hover:scale-105","aria-label":"Toggle history sidebar",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsx)("div",{className:"p-4 border-b border-gray-800/50",children:(0,r.jsxs)("button",{onClick:e0,className:"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,r.jsx)("span",{className:"font-medium",children:"New Chat"})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4 space-y-2",children:eI?(0,r.jsx)("div",{className:"space-y-2 p-4",children:Array.from({length:8}).map((e,t)=>(0,r.jsxs)("div",{className:"p-3 rounded-xl border border-gray-700/50 animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-700 h-4 w-3/4 rounded mb-2"}),(0,r.jsx)("div",{className:"bg-gray-700 h-3 w-1/2 rounded"})]},t))}):0===eD.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-white/10 rounded-xl flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"No conversations yet"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Start chatting to see your history"})]}):(0,r.jsx)(r.Fragment,{children:eD.map(e=>(0,r.jsx)(j,{chat:e,currentConversation:en,onLoadChat:e2,onDeleteChat:eX},e.id))})}),eM&&(0,r.jsx)("div",{className:"px-4 py-2 bg-orange-500/20 border-t border-orange-500/30",children:(0,r.jsxs)("div",{className:"flex items-center text-xs text-orange-600",children:[(0,r.jsx)("svg",{className:"w-3 h-3 mr-1 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Updating..."]})}),eH&&(0,r.jsx)("div",{className:"px-4 py-2 bg-red-500/20 border-t border-red-500/30",children:(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-red-600",children:[(0,r.jsx)("span",{children:"Failed to load history"}),(0,r.jsx)("button",{onClick:()=>eR(!0),className:"text-red-700 hover:text-red-800 font-medium",children:"Retry"})]})})]})}),(0,r.jsx)("div",{className:"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out ".concat(ea?"opacity-100 scale-100 translate-x-0":"opacity-0 scale-95 translate-x-4 pointer-events-none"),children:(0,r.jsx)("button",{onClick:()=>er(!1),className:"p-3 bg-gray-800/50 border border-gray-700/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-400 hover:text-blue-400 hover:scale-105","aria-label":"Show history sidebar",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})})]})}j.displayName="ChatHistoryItem"},33654:(e,t,a)=>{"use strict";a.d(t,{S:()=>r.A});var r=a(29337)},34359:(e,t,a)=>{"use strict";a.d(t,{EF:()=>r.A,DQ:()=>n.A,C1:()=>s.A,Pp:()=>i.A,DP:()=>l.A,nr:()=>c,Y3:()=>d,XL:()=>u.A,$p:()=>h.A,R2:()=>m.A,P:()=>x.A,Zu:()=>g.A,BZ:()=>p.A,Gg:()=>f.A,K6:()=>y.A});var r=a(5279),n=a(64274),s=a(6865),i=a(94830),l=a(5246),o=a(12115);let c=o.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?o.createElement("title",{id:r},a):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m6.75 7.5 3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25Z"}))}),d=o.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?o.createElement("title",{id:r},a):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z"}),o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z"}))});var u=a(48666),h=a(78046),m=a(61316),x=a(65946),g=a(8246),p=a(86474),f=a(27305),y=a(64219)},60323:(e,t,a)=>{"use strict";a.d(t,{D:()=>r.A});var r=a(63418)},64134:(e,t,a)=>{"use strict";a.d(t,{g:()=>n});var r=a(12115);let n=r.forwardRef(function(e,t){let{title:a,titleId:n,...s}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),a?r.createElement("title",{id:n},a):null,r.createElement("path",{fillRule:"evenodd",d:"M5.25 2.25a3 3 0 0 0-3 3v4.318a3 3 0 0 0 .879 2.121l9.58 9.581c.92.92 2.39 1.186 3.548.428a18.849 18.849 0 0 0 5.441-5.44c.758-1.16.492-2.629-.428-3.548l-9.58-9.581a3 3 0 0 0-2.122-.879H5.25ZM6.375 7.5a1.125 1.125 0 1 0 0-2.25 1.125 1.125 0 0 0 0 2.25Z",clipRule:"evenodd"}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[8888,1459,5738,2396,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(24766)),_N_E=e.O()}]);