"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9299],{1673:(t,e,n)=>{function r(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function i(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function o(){}n.d(e,{yW:()=>b,UB:()=>J,aq:()=>G,KI:()=>C,Qh:()=>E});var a="\\s*([+-]?\\d+)\\s*",u="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",s="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",f=/^#([0-9a-f]{3,8})$/,l=RegExp(`^rgb\\(${a},${a},${a}\\)$`),h=RegExp(`^rgb\\(${s},${s},${s}\\)$`),c=RegExp(`^rgba\\(${a},${a},${a},${u}\\)$`),p=RegExp(`^rgba\\(${s},${s},${s},${u}\\)$`),d=RegExp(`^hsl\\(${u},${s},${s}\\)$`),y=RegExp(`^hsla\\(${u},${s},${s},${u}\\)$`),g={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function m(){return this.rgb().formatHex()}function v(){return this.rgb().formatRgb()}function b(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=f.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?w(e):3===n?new M(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?_(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?_(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=l.exec(t))?new M(e[1],e[2],e[3],1):(e=h.exec(t))?new M(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=c.exec(t))?_(e[1],e[2],e[3],e[4]):(e=p.exec(t))?_(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=d.exec(t))?S(e[1],e[2]/100,e[3]/100,1):(e=y.exec(t))?S(e[1],e[2]/100,e[3]/100,e[4]):g.hasOwnProperty(t)?w(g[t]):"transparent"===t?new M(NaN,NaN,NaN,0):null}function w(t){return new M(t>>16&255,t>>8&255,255&t,1)}function _(t,e,n,r){return r<=0&&(t=e=n=NaN),new M(t,e,n,r)}function x(t){return(t instanceof o||(t=b(t)),t)?new M((t=t.rgb()).r,t.g,t.b,t.opacity):new M}function E(t,e,n,r){return 1==arguments.length?x(t):new M(t,e,n,null==r?1:r)}function M(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function A(){return`#${T(this.r)}${T(this.g)}${T(this.b)}`}function B(){let t=I(this.opacity);return`${1===t?"rgb(":"rgba("}${k(this.r)}, ${k(this.g)}, ${k(this.b)}${1===t?")":`, ${t})`}`}function I(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function k(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function T(t){return((t=k(t))<16?"0":"")+t.toString(16)}function S(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new O(t,e,n,r)}function N(t){if(t instanceof O)return new O(t.h,t.s,t.l,t.opacity);if(t instanceof o||(t=b(t)),!t)return new O;if(t instanceof O)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),a=Math.max(e,n,r),u=NaN,s=a-i,f=(a+i)/2;return s?(u=e===a?(n-r)/s+(n<r)*6:n===a?(r-e)/s+2:(e-n)/s+4,s/=f<.5?a+i:2-a-i,u*=60):s=f>0&&f<1?0:u,new O(u,s,f,t.opacity)}function C(t,e,n,r){return 1==arguments.length?N(t):new O(t,e,n,null==r?1:r)}function O(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function U(t){return(t=(t||0)%360)<0?t+360:t}function R(t){return Math.max(0,Math.min(1,t||0))}function $(t,e,n){return(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)*255}r(o,b,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:m,formatHex:m,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return N(this).formatHsl()},formatRgb:v,toString:v}),r(M,E,i(o,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new M(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new M(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new M(k(this.r),k(this.g),k(this.b),I(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:A,formatHex:A,formatHex8:function(){return`#${T(this.r)}${T(this.g)}${T(this.b)}${T((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:B,toString:B})),r(O,C,i(o,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new O(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new O(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,i=2*n-r;return new M($(t>=240?t-240:t+120,i,r),$(t,i,r),$(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new O(U(this.h),R(this.s),R(this.l),I(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=I(this.opacity);return`${1===t?"hsl(":"hsla("}${U(this.h)}, ${100*R(this.s)}%, ${100*R(this.l)}%${1===t?")":`, ${t})`}`}}));let z=Math.PI/180,P=180/Math.PI,L=4/29,j=6/29,D=6/29*3*(6/29),F=6/29*(6/29)*(6/29);function q(t){if(t instanceof X)return new X(t.l,t.a,t.b,t.opacity);if(t instanceof Z)return K(t);t instanceof M||(t=x(t));var e,n,r=H(t.r),i=H(t.g),o=H(t.b),a=Y((.2225045*r+.7168786*i+.0606169*o)/1);return r===i&&i===o?e=n=a:(e=Y((.4360747*r+.3850649*i+.1430804*o)/.96422),n=Y((.0139322*r+.0971045*i+.7141733*o)/.82521)),new X(116*a-16,500*(e-a),200*(a-n),t.opacity)}function X(t,e,n,r){this.l=+t,this.a=+e,this.b=+n,this.opacity=+r}function Y(t){return t>F?Math.pow(t,1/3):t/D+L}function V(t){return t>j?t*t*t:D*(t-L)}function W(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function H(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function G(t,e,n,r){return 1==arguments.length?function(t){if(t instanceof Z)return new Z(t.h,t.c,t.l,t.opacity);if(t instanceof X||(t=q(t)),0===t.a&&0===t.b)return new Z(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*P;return new Z(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}(t):new Z(t,e,n,null==r?1:r)}function Z(t,e,n,r){this.h=+t,this.c=+e,this.l=+n,this.opacity=+r}function K(t){if(isNaN(t.h))return new X(t.l,0,0,t.opacity);var e=t.h*z;return new X(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}r(X,function(t,e,n,r){return 1==arguments.length?q(t):new X(t,e,n,null==r?1:r)},i(o,{brighter(t){return new X(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker(t){return new X(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,n=isNaN(this.b)?t:t-this.b/200;return e=.96422*V(e),new M(W(3.1338561*e-1.6168667*(t=+V(t))-.4906146*(n=.82521*V(n))),W(-.9787684*e+1.9161415*t+.033454*n),W(.0719453*e-.2289914*t+1.4052427*n),this.opacity)}})),r(Z,G,i(o,{brighter(t){return new Z(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker(t){return new Z(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb(){return K(this).rgb()}}));var Q=-1.78277*.29227-.1347134789;function J(t,e,n,r){return 1==arguments.length?function(t){if(t instanceof tt)return new tt(t.h,t.s,t.l,t.opacity);t instanceof M||(t=x(t));var e=t.r/255,n=t.g/255,r=t.b/255,i=(Q*r+-1.7884503806*e-3.5172982438*n)/(Q+-1.7884503806-3.5172982438),o=r-i,a=-((1.97294*(n-i)- -.29227*o)/.90649),u=Math.sqrt(a*a+o*o)/(1.97294*i*(1-i)),s=u?Math.atan2(a,o)*P-120:NaN;return new tt(s<0?s+360:s,u,i,t.opacity)}(t):new tt(t,e,n,null==r?1:r)}function tt(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}r(tt,J,i(o,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tt(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*z,e=+this.l,n=isNaN(this.s)?0:this.s*e*(1-e),r=Math.cos(t),i=Math.sin(t);return new M(255*(e+n*(-.14861*r+1.78277*i)),255*(e+n*(-.29227*r+-.90649*i)),255*(e+1.97294*r*n),this.opacity)}}))},5585:(t,e,n)=>{n.d(e,{A:()=>i});var r={};!function t(e,n,r,i){var o,a,u,s,f,l,h,c,p,d,y,g=!!(e.Worker&&e.Blob&&e.Promise&&e.OffscreenCanvas&&e.OffscreenCanvasRenderingContext2D&&e.HTMLCanvasElement&&e.HTMLCanvasElement.prototype.transferControlToOffscreen&&e.URL&&e.URL.createObjectURL),m="function"==typeof Path2D&&"function"==typeof DOMMatrix;function v(){}function b(t){var r=n.exports.Promise,i=void 0!==r?r:e.Promise;return"function"==typeof i?new i(t):(t(v,v),null)}var w=(o=function(){if(!e.OffscreenCanvas)return!1;var t=new OffscreenCanvas(1,1),n=t.getContext("2d");n.fillRect(0,0,1,1);var r=t.transferToImageBitmap();try{n.createPattern(r,"no-repeat")}catch(t){return!1}return!0}(),a=new Map,{transform:function(t){if(o)return t;if(a.has(t))return a.get(t);var e=new OffscreenCanvas(t.width,t.height);return e.getContext("2d").drawImage(t,0,0),a.set(t,e),e},clear:function(){a.clear()}}),_=(f=Math.floor(1e3/60),l={},h=0,"function"==typeof requestAnimationFrame&&"function"==typeof cancelAnimationFrame?(u=function(t){var e=Math.random();return l[e]=requestAnimationFrame(function n(r){h===r||h+f-1<r?(h=r,delete l[e],t()):l[e]=requestAnimationFrame(n)}),e},s=function(t){l[t]&&cancelAnimationFrame(l[t])}):(u=function(t){return setTimeout(t,f)},s=function(t){return clearTimeout(t)}),{frame:u,cancel:s}),x=(d={},function(){if(c)return c;if(!r&&g){var e=["var CONFETTI, SIZE = {}, module = {};","("+t.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join("\n");try{c=new Worker(URL.createObjectURL(new Blob([e])))}catch(t){return"function"==typeof console.warn&&console.warn("\uD83C\uDF8A Could not load worker",t),null}var n=c;function i(t,e){n.postMessage({options:t||{},callback:e})}n.init=function(t){var e=t.transferControlToOffscreen();n.postMessage({canvas:e},[e])},n.fire=function(t,e,r){if(p)return i(t,null),p;var o=Math.random().toString(36).slice(2);return p=b(function(e){function a(t){t.data.callback===o&&(delete d[o],n.removeEventListener("message",a),p=null,w.clear(),r(),e())}n.addEventListener("message",a),i(t,o),d[o]=a.bind(null,{data:{callback:o}})})},n.reset=function(){for(var t in n.postMessage({reset:!0}),d)d[t](),delete d[t]}}return c}),E={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function M(t,e,n){var r,i;return i=t&&null!=t[e]?t[e]:E[e],n?n(i):i}function A(t){return t<0?0:Math.floor(t)}function B(t){return parseInt(t,16)}function I(t){return t.map(k)}function k(t){var e=String(t).replace(/[^0-9a-f]/gi,"");return e.length<6&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]),{r:B(e.substring(0,2)),g:B(e.substring(2,4)),b:B(e.substring(4,6))}}function T(t){t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight}function S(t){var e=t.getBoundingClientRect();t.width=e.width,t.height=e.height}function N(t,n){var o,a=!t,u=!!M(n||{},"resize"),s=!1,f=M(n,"disableForReducedMotion",Boolean),l=g&&M(n||{},"useWorker")?x():null,h=a?T:S,c=!!t&&!!l&&!!t.__confetti_initialized,p="function"==typeof matchMedia&&matchMedia("(prefers-reduced-motion)").matches;function d(n){var d,y=f||M(n,"disableForReducedMotion",Boolean),g=M(n,"zIndex",Number);if(y&&p)return b(function(t){t()});a&&o?t=o.canvas:a&&!t&&((d=document.createElement("canvas")).style.position="fixed",d.style.top="0px",d.style.left="0px",d.style.pointerEvents="none",d.style.zIndex=g,t=d,document.body.appendChild(t)),u&&!c&&h(t);var v={width:t.width,height:t.height};function x(){if(l){var e={getBoundingClientRect:function(){if(!a)return t.getBoundingClientRect()}};h(e),l.postMessage({resize:{width:e.width,height:e.height}});return}v.width=v.height=null}function E(){o=null,u&&(s=!1,e.removeEventListener("resize",x)),a&&t&&(document.body.contains(t)&&document.body.removeChild(t),t=null,c=!1)}return(l&&!c&&l.init(t),c=!0,l&&(t.__confetti_initialized=!0),u&&!s&&(s=!0,e.addEventListener("resize",x,!1)),l)?l.fire(n,v,E):function(e,n,a){for(var u,s,f,l,c,p,d,y=M(e,"particleCount",A),g=M(e,"angle",Number),v=M(e,"spread",Number),x=M(e,"startVelocity",Number),E=M(e,"decay",Number),B=M(e,"gravity",Number),k=M(e,"drift",Number),T=M(e,"colors",I),S=M(e,"ticks",Number),N=M(e,"shapes"),C=M(e,"scalar"),O=!!M(e,"flat"),U=((u=M(e,"origin",Object)).x=M(u,"x",Number),u.y=M(u,"y",Number),u),R=y,$=[],z=t.width*U.x,P=t.height*U.y;R--;)$.push(function(t){var e=t.angle*(Math.PI/180),n=t.spread*(Math.PI/180);return{x:t.x,y:t.y,wobble:10*Math.random(),wobbleSpeed:Math.min(.11,.1*Math.random()+.05),velocity:.5*t.startVelocity+Math.random()*t.startVelocity,angle2D:-e+(.5*n-Math.random()*n),tiltAngle:(.5*Math.random()+.25)*Math.PI,color:t.color,shape:t.shape,tick:0,totalTicks:t.ticks,decay:t.decay,drift:t.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:3*t.gravity,ovalScalar:.6,scalar:t.scalar,flat:t.flat}}({x:z,y:P,angle:g,spread:v,startVelocity:x,color:T[R%T.length],shape:N[Math.floor(Math.random()*(N.length-0))+0],ticks:S,decay:E,gravity:B,drift:k,scalar:C,flat:O}));return o?o.addFettis($):(s=t,c=$.slice(),p=s.getContext("2d"),d=b(function(t){function e(){f=l=null,p.clearRect(0,0,n.width,n.height),w.clear(),a(),t()}f=_.frame(function t(){r&&(n.width!==i.width||n.height!==i.height)&&(n.width=s.width=i.width,n.height=s.height=i.height),n.width||n.height||(h(s),n.width=s.width,n.height=s.height),p.clearRect(0,0,n.width,n.height),(c=c.filter(function(t){return function(t,e){e.x+=Math.cos(e.angle2D)*e.velocity+e.drift,e.y+=Math.sin(e.angle2D)*e.velocity+e.gravity,e.velocity*=e.decay,e.flat?(e.wobble=0,e.wobbleX=e.x+10*e.scalar,e.wobbleY=e.y+10*e.scalar,e.tiltSin=0,e.tiltCos=0,e.random=1):(e.wobble+=e.wobbleSpeed,e.wobbleX=e.x+10*e.scalar*Math.cos(e.wobble),e.wobbleY=e.y+10*e.scalar*Math.sin(e.wobble),e.tiltAngle+=.1,e.tiltSin=Math.sin(e.tiltAngle),e.tiltCos=Math.cos(e.tiltAngle),e.random=Math.random()+2);var n,r,i,o,a,u,s,f,l,h,c,p,d,y,g,v,b=e.tick++/e.totalTicks,_=e.x+e.random*e.tiltCos,x=e.y+e.random*e.tiltSin,E=e.wobbleX+e.random*e.tiltCos,M=e.wobbleY+e.random*e.tiltSin;if(t.fillStyle="rgba("+e.color.r+", "+e.color.g+", "+e.color.b+", "+(1-b)+")",t.beginPath(),m&&"path"===e.shape.type&&"string"==typeof e.shape.path&&Array.isArray(e.shape.matrix)){t.fill((n=e.shape.path,r=e.shape.matrix,i=e.x,o=e.y,a=.1*Math.abs(E-_),u=.1*Math.abs(M-x),s=Math.PI/10*e.wobble,f=new Path2D(n),(l=new Path2D).addPath(f,new DOMMatrix(r)),(h=new Path2D).addPath(l,new DOMMatrix([Math.cos(s)*a,Math.sin(s)*a,-Math.sin(s)*u,Math.cos(s)*u,i,o])),h))}else if("bitmap"===e.shape.type){var A=Math.PI/10*e.wobble,B=.1*Math.abs(E-_),I=.1*Math.abs(M-x),k=e.shape.bitmap.width*e.scalar,T=e.shape.bitmap.height*e.scalar,S=new DOMMatrix([Math.cos(A)*B,Math.sin(A)*B,-Math.sin(A)*I,Math.cos(A)*I,e.x,e.y]);S.multiplySelf(new DOMMatrix(e.shape.matrix));var N=t.createPattern(w.transform(e.shape.bitmap),"no-repeat");N.setTransform(S),t.globalAlpha=1-b,t.fillStyle=N,t.fillRect(e.x-k/2,e.y-T/2,k,T),t.globalAlpha=1}else if("circle"===e.shape)t.ellipse?t.ellipse(e.x,e.y,Math.abs(E-_)*e.ovalScalar,Math.abs(M-x)*e.ovalScalar,Math.PI/10*e.wobble,0,2*Math.PI):(c=e.x,p=e.y,d=Math.abs(E-_)*e.ovalScalar,y=Math.abs(M-x)*e.ovalScalar,g=Math.PI/10*e.wobble,v=2*Math.PI,t.save(),t.translate(c,p),t.rotate(g),t.scale(d,y),t.arc(0,0,1,0,v,void 0),t.restore());else if("star"===e.shape)for(var C=Math.PI/2*3,O=4*e.scalar,U=8*e.scalar,R=e.x,$=e.y,z=5,P=Math.PI/5;z--;)R=e.x+Math.cos(C)*U,$=e.y+Math.sin(C)*U,t.lineTo(R,$),C+=P,R=e.x+Math.cos(C)*O,$=e.y+Math.sin(C)*O,t.lineTo(R,$),C+=P;else t.moveTo(Math.floor(e.x),Math.floor(e.y)),t.lineTo(Math.floor(e.wobbleX),Math.floor(x)),t.lineTo(Math.floor(E),Math.floor(M)),t.lineTo(Math.floor(_),Math.floor(e.wobbleY));return t.closePath(),t.fill(),e.tick<e.totalTicks}(p,t)})).length?f=_.frame(t):e()}),l=e}),(o={addFettis:function(t){return c=c.concat(t),d},canvas:s,promise:d,reset:function(){f&&_.cancel(f),l&&l()}}).promise)}(n,v,E)}return d.reset=function(){l&&l.reset(),o&&o.reset()},d}function C(){return y||(y=N(null,{useWorker:!0,resize:!0})),y}n.exports=function(){return C().apply(this,arguments)},n.exports.reset=function(){C().reset()},n.exports.create=N,n.exports.shapeFromPath=function(t){if(!m)throw Error("path confetti are not supported in this browser");"string"==typeof t?r=t:(r=t.path,i=t.matrix);var e=new Path2D(r),n=document.createElement("canvas").getContext("2d");if(!i){for(var r,i,o,a,u=1e3,s=1e3,f=0,l=0,h=0;h<1e3;h+=2)for(var c=0;c<1e3;c+=2)n.isPointInPath(e,h,c,"nonzero")&&(u=Math.min(u,h),s=Math.min(s,c),f=Math.max(f,h),l=Math.max(l,c));o=f-u;var p=Math.min(10/o,10/(a=l-s));i=[p,0,0,p,-Math.round(o/2+u)*p,-Math.round(a/2+s)*p]}return{type:"path",path:r,matrix:i}},n.exports.shapeFromText=function(t){var e,n=1,r="#000000",i='"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "EmojiOne Color", "Android Emoji", "Twemoji Mozilla", "system emoji", sans-serif';"string"==typeof t?e=t:(e=t.text,n="scalar"in t?t.scalar:n,i="fontFamily"in t?t.fontFamily:i,r="color"in t?t.color:r);var o=10*n,a=""+o+"px "+i,u=new OffscreenCanvas(o,o),s=u.getContext("2d");s.font=a;var f=s.measureText(e),l=Math.ceil(f.actualBoundingBoxRight+f.actualBoundingBoxLeft),h=Math.ceil(f.actualBoundingBoxAscent+f.actualBoundingBoxDescent),c=f.actualBoundingBoxLeft+2,p=f.actualBoundingBoxAscent+2;l+=4,h+=4,(s=(u=new OffscreenCanvas(l,h)).getContext("2d")).font=a,s.fillStyle=r,s.fillText(e,c,p);var d=1/n;return{type:"bitmap",bitmap:u.transferToImageBitmap(),matrix:[d,0,0,d,-l*d/2,-h*d/2]}}}(function(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:this||{}}(),r,!1);let i=r.exports;r.exports.create},7609:(t,e,n)=>{n.d(e,{GW:()=>g,Dj:()=>c,Zr:()=>l,zl:()=>y,TE:()=>_,pN:()=>x,p7:()=>M});var r,i=n(1673);function o(t,e,n,r,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*n+(1+3*t+3*o-3*a)*r+a*i)/6}let a=t=>()=>t;function u(t,e){return function(n){return t+n*e}}function s(t,e){var n=e-t;return n?u(t,n>180||n<-180?n-360*Math.round(n/360):n):a(isNaN(t)?e:t)}function f(t,e){var n=e-t;return n?u(t,n):a(isNaN(t)?e:t)}let l=function t(e){var n,r=1==(n=+e)?f:function(t,e){var r,i,o;return e-t?(r=t,i=e,r=Math.pow(r,o=n),i=Math.pow(i,o)-r,o=1/o,function(t){return Math.pow(r+t*i,o)}):a(isNaN(t)?e:t)};function o(t,e){var n=r((t=(0,i.Qh)(t)).r,(e=(0,i.Qh)(e)).r),o=r(t.g,e.g),a=r(t.b,e.b),u=f(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=o(e),t.b=a(e),t.opacity=u(e),t+""}}return o.gamma=t,o}(1);function h(t){return function(e){var n,r,o=e.length,a=Array(o),u=Array(o),s=Array(o);for(n=0;n<o;++n)r=(0,i.Qh)(e[n]),a[n]=r.r||0,u[n]=r.g||0,s[n]=r.b||0;return a=t(a),u=t(u),s=t(s),r.opacity=1,function(t){return r.r=a(t),r.g=u(t),r.b=s(t),r+""}}}h(function(t){var e=t.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),i=t[r],a=t[r+1],u=r>0?t[r-1]:2*i-a,s=r<e-1?t[r+2]:2*a-i;return o((n-r/e)*e,u,i,a,s)}}),h(function(t){var e=t.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*e),i=t[(r+e-1)%e],a=t[r%e],u=t[(r+1)%e],s=t[(r+2)%e];return o((n-r/e)*e,i,a,u,s)}});function c(t,e){return t*=1,e*=1,function(n){return t*(1-n)+e*n}}var p=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,d=RegExp(p.source,"g");function y(t,e){var n,r,i,o,a,u=p.lastIndex=d.lastIndex=0,s=-1,f=[],l=[];for(t+="",e+="";(i=p.exec(t))&&(o=d.exec(e));)(a=o.index)>u&&(a=e.slice(u,a),f[s]?f[s]+=a:f[++s]=a),(i=i[0])===(o=o[0])?f[s]?f[s]+=o:f[++s]=o:(f[++s]=null,l.push({i:s,x:c(i,o)})),u=d.lastIndex;return u<e.length&&(a=e.slice(u),f[s]?f[s]+=a:f[++s]=a),f.length<2?l[0]?(n=l[0].x,function(t){return n(t)+""}):(r=e,function(){return r}):(e=l.length,function(t){for(var n,r=0;r<e;++r)f[(n=l[r]).i]=n.x(t);return f.join("")})}function g(t,e){var n,r,o=typeof e;return null==e||"boolean"===o?a(e):("number"===o?c:"string"===o?(r=(0,i.yW)(e))?(e=r,l):y:e instanceof i.yW?l:e instanceof Date?function(t,e){var n=new Date;return t*=1,e*=1,function(r){return n.setTime(t*(1-r)+e*r),n}}:!ArrayBuffer.isView(n=e)||n instanceof DataView?Array.isArray(e)?function(t,e){var n,r=e?e.length:0,i=t?Math.min(r,t.length):0,o=Array(i),a=Array(r);for(n=0;n<i;++n)o[n]=g(t[n],e[n]);for(;n<r;++n)a[n]=e[n];return function(t){for(n=0;n<i;++n)a[n]=o[n](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var n,r={},i={};for(n in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)n in t?r[n]=g(t[n],e[n]):i[n]=e[n];return function(t){for(n in r)i[n]=r[n](t);return i}}:c:function(t,e){e||(e=[]);var n,r=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(n=0;n<r;++n)i[n]=t[n]*(1-o)+e[n]*o;return i}})(t,e)}var m=180/Math.PI,v={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function b(t,e,n,r,i,o){var a,u,s;return(a=Math.sqrt(t*t+e*e))&&(t/=a,e/=a),(s=t*n+e*r)&&(n-=t*s,r-=e*s),(u=Math.sqrt(n*n+r*r))&&(n/=u,r/=u,s/=u),t*r<e*n&&(t=-t,e=-e,s=-s,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(e,t)*m,skewX:Math.atan(s)*m,scaleX:a,scaleY:u}}function w(t,e,n,r){function i(t){return t.length?t.pop()+" ":""}return function(o,a){var u,s,f,l,h=[],p=[];return o=t(o),a=t(a),!function(t,r,i,o,a,u){if(t!==i||r!==o){var s=a.push("translate(",null,e,null,n);u.push({i:s-4,x:c(t,i)},{i:s-2,x:c(r,o)})}else(i||o)&&a.push("translate("+i+e+o+n)}(o.translateX,o.translateY,a.translateX,a.translateY,h,p),u=o.rotate,s=a.rotate,u!==s?(u-s>180?s+=360:s-u>180&&(u+=360),p.push({i:h.push(i(h)+"rotate(",null,r)-2,x:c(u,s)})):s&&h.push(i(h)+"rotate("+s+r),f=o.skewX,l=a.skewX,f!==l?p.push({i:h.push(i(h)+"skewX(",null,r)-2,x:c(f,l)}):l&&h.push(i(h)+"skewX("+l+r),!function(t,e,n,r,o,a){if(t!==n||e!==r){var u=o.push(i(o)+"scale(",null,",",null,")");a.push({i:u-4,x:c(t,n)},{i:u-2,x:c(e,r)})}else(1!==n||1!==r)&&o.push(i(o)+"scale("+n+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,h,p),o=a=null,function(t){for(var e,n=-1,r=p.length;++n<r;)h[(e=p[n]).i]=e.x(t);return h.join("")}}}var _=w(function(t){let e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?v:b(e.a,e.b,e.c,e.d,e.e,e.f)},"px, ","px)","deg)"),x=w(function(t){return null==t?v:(r||(r=document.createElementNS("http://www.w3.org/2000/svg","g")),r.setAttribute("transform",t),t=r.transform.baseVal.consolidate())?b((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):v},", ",")",")");function E(t){return((t=Math.exp(t))+1/t)/2}let M=function t(e,n,r){function i(t,i){var o,a,u=t[0],s=t[1],f=t[2],l=i[0],h=i[1],c=i[2],p=l-u,d=h-s,y=p*p+d*d;if(y<1e-12)a=Math.log(c/f)/e,o=function(t){return[u+t*p,s+t*d,f*Math.exp(e*t*a)]};else{var g=Math.sqrt(y),m=(c*c-f*f+r*y)/(2*f*n*g),v=(c*c-f*f-r*y)/(2*c*n*g),b=Math.log(Math.sqrt(m*m+1)-m);a=(Math.log(Math.sqrt(v*v+1)-v)-b)/e,o=function(t){var r,i,o=t*a,l=E(b),h=f/(n*g)*(l*(((r=Math.exp(2*(r=e*o+b)))-1)/(r+1))-((i=Math.exp(i=b))-1/i)/2);return[u+h*p,s+h*d,f*l/E(e*o+b)]}}return o.duration=1e3*a*e/Math.SQRT2,o}return i.rho=function(e){var n=Math.max(.001,+e),r=n*n;return t(n,r,r*r)},i}(Math.SQRT2,2,4);function A(t){return function(e,n){var r=t((e=(0,i.KI)(e)).h,(n=(0,i.KI)(n)).h),o=f(e.s,n.s),a=f(e.l,n.l),u=f(e.opacity,n.opacity);return function(t){return e.h=r(t),e.s=o(t),e.l=a(t),e.opacity=u(t),e+""}}}function B(t){return function(e,n){var r=t((e=(0,i.aq)(e)).h,(n=(0,i.aq)(n)).h),o=f(e.c,n.c),a=f(e.l,n.l),u=f(e.opacity,n.opacity);return function(t){return e.h=r(t),e.c=o(t),e.l=a(t),e.opacity=u(t),e+""}}}function I(t){return function e(n){function r(e,r){var o=t((e=(0,i.UB)(e)).h,(r=(0,i.UB)(r)).h),a=f(e.s,r.s),u=f(e.l,r.l),s=f(e.opacity,r.opacity);return function(t){return e.h=o(t),e.s=a(t),e.l=u(Math.pow(t,n)),e.opacity=s(t),e+""}}return n*=1,r.gamma=e,r}(1)}A(s),A(f),B(s),B(f),I(s),I(f)},17359:(t,e,n)=>{n.d(e,{J:()=>u});var r={value:()=>{}};function i(){for(var t,e=0,n=arguments.length,r={};e<n;++e){if(!(t=arguments[e]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new o(r)}function o(t){this._=t}function a(t,e,n){for(var i=0,o=t.length;i<o;++i)if(t[i].name===e){t[i]=r,t=t.slice(0,i).concat(t.slice(i+1));break}return null!=n&&t.push({name:e,value:n}),t}o.prototype=i.prototype={constructor:o,on:function(t,e){var n,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");if(n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:e}}),o=-1,u=i.length;if(arguments.length<2){for(;++o<u;)if((n=(t=i[o]).type)&&(n=function(t,e){for(var n,r=0,i=t.length;r<i;++r)if((n=t[r]).name===e)return n.value}(r[n],t.name)))return n;return}if(null!=e&&"function"!=typeof e)throw Error("invalid callback: "+e);for(;++o<u;)if(n=(t=i[o]).type)r[n]=a(r[n],t.name,e);else if(null==e)for(n in r)r[n]=a(r[n],t.name,null);return this},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new o(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,r,i=Array(n),o=0;o<n;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],o=0,n=r.length;o<n;++o)r[o].value.apply(e,i)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(e,n)}};let u=i},37396:(t,e,n)=>{n.d(e,{$E:()=>m,XD:()=>f,EH:()=>l});var r=n(17359),i=n(63664);let o={passive:!1},a={capture:!0,passive:!1};function u(t){t.stopImmediatePropagation()}function s(t){t.preventDefault(),t.stopImmediatePropagation()}function f(t){var e=t.document.documentElement,n=(0,i.Lt)(t).on("dragstart.drag",s,a);"onselectstart"in e?n.on("selectstart.drag",s,a):(e.__noselect=e.style.MozUserSelect,e.style.MozUserSelect="none")}function l(t,e){var n=t.document.documentElement,r=(0,i.Lt)(t).on("dragstart.drag",null);e&&(r.on("click.drag",s,a),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}let h=t=>()=>t;function c(t,{sourceEvent:e,subject:n,target:r,identifier:i,active:o,x:a,y:u,dx:s,dy:f,dispatch:l}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:u,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:f,enumerable:!0,configurable:!0},_:{value:l}})}function p(t){return!t.ctrlKey&&!t.button}function d(){return this.parentNode}function y(t,e){return null==e?{x:t.x,y:t.y}:e}function g(){return navigator.maxTouchPoints||"ontouchstart"in this}function m(){var t,e,n,m,v=p,b=d,w=y,_=g,x={},E=(0,r.J)("start","drag","end"),M=0,A=0;function B(t){t.on("mousedown.drag",I).filter(_).on("touchstart.drag",S).on("touchmove.drag",N,o).on("touchend.drag touchcancel.drag",C).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function I(r,o){if(!m&&v.call(this,r,o)){var s=O(this,b.call(this,r,o),r,o,"mouse");s&&((0,i.Lt)(r.view).on("mousemove.drag",k,a).on("mouseup.drag",T,a),f(r.view),u(r),n=!1,t=r.clientX,e=r.clientY,s("start",r))}}function k(r){if(s(r),!n){var i=r.clientX-t,o=r.clientY-e;n=i*i+o*o>A}x.mouse("drag",r)}function T(t){(0,i.Lt)(t.view).on("mousemove.drag mouseup.drag",null),l(t.view,n),s(t),x.mouse("end",t)}function S(t,e){if(v.call(this,t,e)){var n,r,i=t.changedTouches,o=b.call(this,t,e),a=i.length;for(n=0;n<a;++n)(r=O(this,o,t,e,i[n].identifier,i[n]))&&(u(t),r("start",t,i[n]))}}function N(t){var e,n,r=t.changedTouches,i=r.length;for(e=0;e<i;++e)(n=x[r[e].identifier])&&(s(t),n("drag",t,r[e]))}function C(t){var e,n,r=t.changedTouches,i=r.length;for(m&&clearTimeout(m),m=setTimeout(function(){m=null},500),e=0;e<i;++e)(n=x[r[e].identifier])&&(u(t),n("end",t,r[e]))}function O(t,e,n,r,o,a){var u,s,f,l=E.copy(),h=(0,i.Wn)(a||n,e);if(null!=(f=w.call(t,new c("beforestart",{sourceEvent:n,target:B,identifier:o,active:M,x:h[0],y:h[1],dx:0,dy:0,dispatch:l}),r)))return u=f.x-h[0]||0,s=f.y-h[1]||0,function n(a,p,d){var y,g=h;switch(a){case"start":x[o]=n,y=M++;break;case"end":delete x[o],--M;case"drag":h=(0,i.Wn)(d||p,e),y=M}l.call(a,t,new c(a,{sourceEvent:p,subject:f,target:B,identifier:o,active:y,x:h[0]+u,y:h[1]+s,dx:h[0]-g[0],dy:h[1]-g[1],dispatch:l}),r)}}return B.filter=function(t){return arguments.length?(v="function"==typeof t?t:h(!!t),B):v},B.container=function(t){return arguments.length?(b="function"==typeof t?t:h(t),B):b},B.subject=function(t){return arguments.length?(w="function"==typeof t?t:h(t),B):w},B.touchable=function(t){return arguments.length?(_="function"==typeof t?t:h(!!t),B):_},B.on=function(){var t=E.on.apply(E,arguments);return t===E?B:t},B.clickDistance=function(t){return arguments.length?(A=(t*=1)*t,B):Math.sqrt(A)},B}c.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t}},41049:(t,e)=>{e.qg=function(t,e){let n=new u,r=t.length;if(r<2)return n;let i=e?.decode||l,o=0;do{let e=t.indexOf("=",o);if(-1===e)break;let a=t.indexOf(";",o),u=-1===a?r:a;if(e>u){o=t.lastIndexOf(";",e-1)+1;continue}let l=s(t,o,e),h=f(t,e,l),c=t.slice(l,h);if(void 0===n[c]){let r=s(t,e+1,u),o=f(t,u,r),a=i(t.slice(r,o));n[c]=a}o=u+1}while(o<r);return n},e.lK=function(t,e,u){let s=u?.encode||encodeURIComponent;if(!n.test(t))throw TypeError(`argument name is invalid: ${t}`);let f=s(e);if(!r.test(f))throw TypeError(`argument val is invalid: ${e}`);let l=t+"="+f;if(!u)return l;if(void 0!==u.maxAge){if(!Number.isInteger(u.maxAge))throw TypeError(`option maxAge is invalid: ${u.maxAge}`);l+="; Max-Age="+u.maxAge}if(u.domain){if(!i.test(u.domain))throw TypeError(`option domain is invalid: ${u.domain}`);l+="; Domain="+u.domain}if(u.path){if(!o.test(u.path))throw TypeError(`option path is invalid: ${u.path}`);l+="; Path="+u.path}if(u.expires){var h;if(h=u.expires,"[object Date]"!==a.call(h)||!Number.isFinite(u.expires.valueOf()))throw TypeError(`option expires is invalid: ${u.expires}`);l+="; Expires="+u.expires.toUTCString()}if(u.httpOnly&&(l+="; HttpOnly"),u.secure&&(l+="; Secure"),u.partitioned&&(l+="; Partitioned"),u.priority)switch("string"==typeof u.priority?u.priority.toLowerCase():void 0){case"low":l+="; Priority=Low";break;case"medium":l+="; Priority=Medium";break;case"high":l+="; Priority=High";break;default:throw TypeError(`option priority is invalid: ${u.priority}`)}if(u.sameSite)switch("string"==typeof u.sameSite?u.sameSite.toLowerCase():u.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError(`option sameSite is invalid: ${u.sameSite}`)}return l};let n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,i=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,u=(()=>{let t=function(){};return t.prototype=Object.create(null),t})();function s(t,e,n){do{let n=t.charCodeAt(e);if(32!==n&&9!==n)return e}while(++e<n);return n}function f(t,e,n){for(;e>n;){let n=t.charCodeAt(--e);if(32!==n&&9!==n)return e+1}return n}function l(t){if(-1===t.indexOf("%"))return t;try{return decodeURIComponent(t)}catch(e){return t}}},44134:(t,e,n)=>{let r=n(57719),i=n(7610),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function a(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');let e=new Uint8Array(t);return Object.setPrototypeOf(e,u.prototype),e}function u(t,e,n){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return l(t)}return s(t,e,n)}function s(t,e,n){if("string"==typeof t){var r=t,i=e;if(("string"!=typeof i||""===i)&&(i="utf8"),!u.isEncoding(i))throw TypeError("Unknown encoding: "+i);let n=0|d(r,i),o=a(n),s=o.write(r,i);return s!==n&&(o=o.slice(0,s)),o}if(ArrayBuffer.isView(t)){var o=t;if(z(o,Uint8Array)){let t=new Uint8Array(o);return c(t.buffer,t.byteOffset,t.byteLength)}return h(o)}if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(z(t,ArrayBuffer)||t&&z(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(z(t,SharedArrayBuffer)||t&&z(t.buffer,SharedArrayBuffer)))return c(t,e,n);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');let s=t.valueOf&&t.valueOf();if(null!=s&&s!==t)return u.from(s,e,n);let f=function(t){if(u.isBuffer(t)){let e=0|p(t.length),n=a(e);return 0===n.length||t.copy(n,0,0,e),n}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?a(0):h(t):"Buffer"===t.type&&Array.isArray(t.data)?h(t.data):void 0}(t);if(f)return f;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return u.from(t[Symbol.toPrimitive]("string"),e,n);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function f(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function l(t){return f(t),a(t<0?0:0|p(t))}function h(t){let e=t.length<0?0:0|p(t.length),n=a(e);for(let r=0;r<e;r+=1)n[r]=255&t[r];return n}function c(t,e,n){let r;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(n||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(r=void 0===e&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,e):new Uint8Array(t,e,n),u.prototype),r}function p(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function d(t,e){if(u.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||z(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);let n=t.length,r=arguments.length>2&&!0===arguments[2];if(!r&&0===n)return 0;let i=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return U(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return R(t).length;default:if(i)return r?-1:U(t).length;e=(""+e).toLowerCase(),i=!0}}function y(t,e,n){let i=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===n||n>this.length)&&(n=this.length),n<=0||(n>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,n){let r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);let i="";for(let r=e;r<n;++r)i+=P[t[r]];return i}(this,e,n);case"utf8":case"utf-8":return b(this,e,n);case"ascii":return function(t,e,n){let r="";n=Math.min(t.length,n);for(let i=e;i<n;++i)r+=String.fromCharCode(127&t[i]);return r}(this,e,n);case"latin1":case"binary":return function(t,e,n){let r="";n=Math.min(t.length,n);for(let i=e;i<n;++i)r+=String.fromCharCode(t[i]);return r}(this,e,n);case"base64":var o,a,u;return o=this,a=e,u=n,0===a&&u===o.length?r.fromByteArray(o):r.fromByteArray(o.slice(a,u));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,n){let r=t.slice(e,n),i="";for(let t=0;t<r.length-1;t+=2)i+=String.fromCharCode(r[t]+256*r[t+1]);return i}(this,e,n);default:if(i)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),i=!0}}function g(t,e,n){let r=t[e];t[e]=t[n],t[n]=r}function m(t,e,n,r,i){var o;if(0===t.length)return -1;if("string"==typeof n?(r=n,n=0):n>0x7fffffff?n=0x7fffffff:n<-0x80000000&&(n=-0x80000000),(o=n*=1)!=o&&(n=i?0:t.length-1),n<0&&(n=t.length+n),n>=t.length)if(i)return -1;else n=t.length-1;else if(n<0)if(!i)return -1;else n=0;if("string"==typeof e&&(e=u.from(e,r)),u.isBuffer(e))return 0===e.length?-1:v(t,e,n,r,i);if("number"==typeof e){if(e&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(t,e,n);else return Uint8Array.prototype.lastIndexOf.call(t,e,n);return v(t,[e],n,r,i)}throw TypeError("val must be string, number or Buffer")}function v(t,e,n,r,i){let o,a=1,u=t.length,s=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return -1;a=2,u/=2,s/=2,n/=2}function f(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(i){let r=-1;for(o=n;o<u;o++)if(f(t,o)===f(e,-1===r?0:o-r)){if(-1===r&&(r=o),o-r+1===s)return r*a}else -1!==r&&(o-=o-r),r=-1}else for(n+s>u&&(n=u-s),o=n;o>=0;o--){let n=!0;for(let r=0;r<s;r++)if(f(t,o+r)!==f(e,r)){n=!1;break}if(n)return o}return -1}function b(t,e,n){n=Math.min(t.length,n);let r=[],i=e;for(;i<n;){let e=t[i],o=null,a=e>239?4:e>223?3:e>191?2:1;if(i+a<=n){let n,r,u,s;switch(a){case 1:e<128&&(o=e);break;case 2:(192&(n=t[i+1]))==128&&(s=(31&e)<<6|63&n)>127&&(o=s);break;case 3:n=t[i+1],r=t[i+2],(192&n)==128&&(192&r)==128&&(s=(15&e)<<12|(63&n)<<6|63&r)>2047&&(s<55296||s>57343)&&(o=s);break;case 4:n=t[i+1],r=t[i+2],u=t[i+3],(192&n)==128&&(192&r)==128&&(192&u)==128&&(s=(15&e)<<18|(63&n)<<12|(63&r)<<6|63&u)>65535&&s<1114112&&(o=s)}}null===o?(o=65533,a=1):o>65535&&(o-=65536,r.push(o>>>10&1023|55296),o=56320|1023&o),r.push(o),i+=a}var o=r;let a=o.length;if(a<=4096)return String.fromCharCode.apply(String,o);let u="",s=0;for(;s<a;)u+=String.fromCharCode.apply(String,o.slice(s,s+=4096));return u}function w(t,e,n){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>n)throw RangeError("Trying to access beyond buffer length")}function _(t,e,n,r,i,o){if(!u.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw RangeError('"value" argument is out of bounds');if(n+r>t.length)throw RangeError("Index out of range")}function x(t,e,n,r,i){S(e,r,i,t,n,7);let o=Number(e&BigInt(0xffffffff));t[n++]=o,o>>=8,t[n++]=o,o>>=8,t[n++]=o,o>>=8,t[n++]=o;let a=Number(e>>BigInt(32)&BigInt(0xffffffff));return t[n++]=a,a>>=8,t[n++]=a,a>>=8,t[n++]=a,a>>=8,t[n++]=a,n}function E(t,e,n,r,i){S(e,r,i,t,n,7);let o=Number(e&BigInt(0xffffffff));t[n+7]=o,o>>=8,t[n+6]=o,o>>=8,t[n+5]=o,o>>=8,t[n+4]=o;let a=Number(e>>BigInt(32)&BigInt(0xffffffff));return t[n+3]=a,a>>=8,t[n+2]=a,a>>=8,t[n+1]=a,a>>=8,t[n]=a,n+8}function M(t,e,n,r,i,o){if(n+r>t.length||n<0)throw RangeError("Index out of range")}function A(t,e,n,r,o){return e*=1,n>>>=0,o||M(t,e,n,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,n,r,23,4),n+4}function B(t,e,n,r,o){return e*=1,n>>>=0,o||M(t,e,n,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,n,r,52,8),n+8}e.hp=u,e.IS=50,u.TYPED_ARRAY_SUPPORT=function(){try{let t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),u.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(u.prototype,"parent",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.buffer}}),Object.defineProperty(u.prototype,"offset",{enumerable:!0,get:function(){if(u.isBuffer(this))return this.byteOffset}}),u.poolSize=8192,u.from=function(t,e,n){return s(t,e,n)},Object.setPrototypeOf(u.prototype,Uint8Array.prototype),Object.setPrototypeOf(u,Uint8Array),u.alloc=function(t,e,n){return(f(t),t<=0)?a(t):void 0!==e?"string"==typeof n?a(t).fill(e,n):a(t).fill(e):a(t)},u.allocUnsafe=function(t){return l(t)},u.allocUnsafeSlow=function(t){return l(t)},u.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==u.prototype},u.compare=function(t,e){if(z(t,Uint8Array)&&(t=u.from(t,t.offset,t.byteLength)),z(e,Uint8Array)&&(e=u.from(e,e.offset,e.byteLength)),!u.isBuffer(t)||!u.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let n=t.length,r=e.length;for(let i=0,o=Math.min(n,r);i<o;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:+(r<n)},u.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(t,e){let n;if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return u.alloc(0);if(void 0===e)for(n=0,e=0;n<t.length;++n)e+=t[n].length;let r=u.allocUnsafe(e),i=0;for(n=0;n<t.length;++n){let e=t[n];if(z(e,Uint8Array))i+e.length>r.length?(u.isBuffer(e)||(e=u.from(e)),e.copy(r,i)):Uint8Array.prototype.set.call(r,e,i);else if(u.isBuffer(e))e.copy(r,i);else throw TypeError('"list" argument must be an Array of Buffers');i+=e.length}return r},u.byteLength=d,u.prototype._isBuffer=!0,u.prototype.swap16=function(){let t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)g(this,e,e+1);return this},u.prototype.swap32=function(){let t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)g(this,e,e+3),g(this,e+1,e+2);return this},u.prototype.swap64=function(){let t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)g(this,e,e+7),g(this,e+1,e+6),g(this,e+2,e+5),g(this,e+3,e+4);return this},u.prototype.toString=function(){let t=this.length;return 0===t?"":0==arguments.length?b(this,0,t):y.apply(this,arguments)},u.prototype.toLocaleString=u.prototype.toString,u.prototype.equals=function(t){if(!u.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===u.compare(this,t)},u.prototype.inspect=function(){let t="",n=e.IS;return t=this.toString("hex",0,n).replace(/(.{2})/g,"$1 ").trim(),this.length>n&&(t+=" ... "),"<Buffer "+t+">"},o&&(u.prototype[o]=u.prototype.inspect),u.prototype.compare=function(t,e,n,r,i){if(z(t,Uint8Array)&&(t=u.from(t,t.offset,t.byteLength)),!u.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),e<0||n>t.length||r<0||i>this.length)throw RangeError("out of range index");if(r>=i&&e>=n)return 0;if(r>=i)return -1;if(e>=n)return 1;if(e>>>=0,n>>>=0,r>>>=0,i>>>=0,this===t)return 0;let o=i-r,a=n-e,s=Math.min(o,a),f=this.slice(r,i),l=t.slice(e,n);for(let t=0;t<s;++t)if(f[t]!==l[t]){o=f[t],a=l[t];break}return o<a?-1:+(a<o)},u.prototype.includes=function(t,e,n){return -1!==this.indexOf(t,e,n)},u.prototype.indexOf=function(t,e,n){return m(this,t,e,n,!0)},u.prototype.lastIndexOf=function(t,e,n){return m(this,t,e,n,!1)},u.prototype.write=function(t,e,n,r){var i,o,a,u,s,f,l,h;if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let c=this.length-e;if((void 0===n||n>c)&&(n=c),t.length>0&&(n<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");r||(r="utf8");let p=!1;for(;;)switch(r){case"hex":return function(t,e,n,r){let i;n=Number(n)||0;let o=t.length-n;r?(r=Number(r))>o&&(r=o):r=o;let a=e.length;for(r>a/2&&(r=a/2),i=0;i<r;++i){var u;let r=parseInt(e.substr(2*i,2),16);if((u=r)!=u)break;t[n+i]=r}return i}(this,t,e,n);case"utf8":case"utf-8":return i=e,o=n,$(U(t,this.length-i),this,i,o);case"ascii":case"latin1":case"binary":return a=e,u=n,$(function(t){let e=[];for(let n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}(t),this,a,u);case"base64":return s=e,f=n,$(R(t),this,s,f);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l=e,h=n,$(function(t,e){let n,r,i=[];for(let o=0;o<t.length&&!((e-=2)<0);++o)r=(n=t.charCodeAt(o))>>8,i.push(n%256),i.push(r);return i}(t,this.length-l),this,l,h);default:if(p)throw TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),p=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},u.prototype.slice=function(t,e){let n=this.length;t=~~t,e=void 0===e?n:~~e,t<0?(t+=n)<0&&(t=0):t>n&&(t=n),e<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t);let r=this.subarray(t,e);return Object.setPrototypeOf(r,u.prototype),r},u.prototype.readUintLE=u.prototype.readUIntLE=function(t,e,n){t>>>=0,e>>>=0,n||w(t,e,this.length);let r=this[t],i=1,o=0;for(;++o<e&&(i*=256);)r+=this[t+o]*i;return r},u.prototype.readUintBE=u.prototype.readUIntBE=function(t,e,n){t>>>=0,e>>>=0,n||w(t,e,this.length);let r=this[t+--e],i=1;for(;e>0&&(i*=256);)r+=this[t+--e]*i;return r},u.prototype.readUint8=u.prototype.readUInt8=function(t,e){return t>>>=0,e||w(t,1,this.length),this[t]},u.prototype.readUint16LE=u.prototype.readUInt16LE=function(t,e){return t>>>=0,e||w(t,2,this.length),this[t]|this[t+1]<<8},u.prototype.readUint16BE=u.prototype.readUInt16BE=function(t,e){return t>>>=0,e||w(t,2,this.length),this[t]<<8|this[t+1]},u.prototype.readUint32LE=u.prototype.readUInt32LE=function(t,e){return t>>>=0,e||w(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},u.prototype.readUint32BE=u.prototype.readUInt32BE=function(t,e){return t>>>=0,e||w(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},u.prototype.readBigUInt64LE=L(function(t){N(t>>>=0,"offset");let e=this[t],n=this[t+7];(void 0===e||void 0===n)&&C(t,this.length-8);let r=e+256*this[++t]+65536*this[++t]+0x1000000*this[++t],i=this[++t]+256*this[++t]+65536*this[++t]+0x1000000*n;return BigInt(r)+(BigInt(i)<<BigInt(32))}),u.prototype.readBigUInt64BE=L(function(t){N(t>>>=0,"offset");let e=this[t],n=this[t+7];(void 0===e||void 0===n)&&C(t,this.length-8);let r=0x1000000*e+65536*this[++t]+256*this[++t]+this[++t],i=0x1000000*this[++t]+65536*this[++t]+256*this[++t]+n;return(BigInt(r)<<BigInt(32))+BigInt(i)}),u.prototype.readIntLE=function(t,e,n){t>>>=0,e>>>=0,n||w(t,e,this.length);let r=this[t],i=1,o=0;for(;++o<e&&(i*=256);)r+=this[t+o]*i;return r>=(i*=128)&&(r-=Math.pow(2,8*e)),r},u.prototype.readIntBE=function(t,e,n){t>>>=0,e>>>=0,n||w(t,e,this.length);let r=e,i=1,o=this[t+--r];for(;r>0&&(i*=256);)o+=this[t+--r]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},u.prototype.readInt8=function(t,e){return(t>>>=0,e||w(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},u.prototype.readInt16LE=function(t,e){t>>>=0,e||w(t,2,this.length);let n=this[t]|this[t+1]<<8;return 32768&n?0xffff0000|n:n},u.prototype.readInt16BE=function(t,e){t>>>=0,e||w(t,2,this.length);let n=this[t+1]|this[t]<<8;return 32768&n?0xffff0000|n:n},u.prototype.readInt32LE=function(t,e){return t>>>=0,e||w(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},u.prototype.readInt32BE=function(t,e){return t>>>=0,e||w(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},u.prototype.readBigInt64LE=L(function(t){N(t>>>=0,"offset");let e=this[t],n=this[t+7];return(void 0===e||void 0===n)&&C(t,this.length-8),(BigInt(this[t+4]+256*this[t+5]+65536*this[t+6]+(n<<24))<<BigInt(32))+BigInt(e+256*this[++t]+65536*this[++t]+0x1000000*this[++t])}),u.prototype.readBigInt64BE=L(function(t){N(t>>>=0,"offset");let e=this[t],n=this[t+7];return(void 0===e||void 0===n)&&C(t,this.length-8),(BigInt((e<<24)+65536*this[++t]+256*this[++t]+this[++t])<<BigInt(32))+BigInt(0x1000000*this[++t]+65536*this[++t]+256*this[++t]+n)}),u.prototype.readFloatLE=function(t,e){return t>>>=0,e||w(t,4,this.length),i.read(this,t,!0,23,4)},u.prototype.readFloatBE=function(t,e){return t>>>=0,e||w(t,4,this.length),i.read(this,t,!1,23,4)},u.prototype.readDoubleLE=function(t,e){return t>>>=0,e||w(t,8,this.length),i.read(this,t,!0,52,8)},u.prototype.readDoubleBE=function(t,e){return t>>>=0,e||w(t,8,this.length),i.read(this,t,!1,52,8)},u.prototype.writeUintLE=u.prototype.writeUIntLE=function(t,e,n,r){if(t*=1,e>>>=0,n>>>=0,!r){let r=Math.pow(2,8*n)-1;_(this,t,e,n,r,0)}let i=1,o=0;for(this[e]=255&t;++o<n&&(i*=256);)this[e+o]=t/i&255;return e+n},u.prototype.writeUintBE=u.prototype.writeUIntBE=function(t,e,n,r){if(t*=1,e>>>=0,n>>>=0,!r){let r=Math.pow(2,8*n)-1;_(this,t,e,n,r,0)}let i=n-1,o=1;for(this[e+i]=255&t;--i>=0&&(o*=256);)this[e+i]=t/o&255;return e+n},u.prototype.writeUint8=u.prototype.writeUInt8=function(t,e,n){return t*=1,e>>>=0,n||_(this,t,e,1,255,0),this[e]=255&t,e+1},u.prototype.writeUint16LE=u.prototype.writeUInt16LE=function(t,e,n){return t*=1,e>>>=0,n||_(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},u.prototype.writeUint16BE=u.prototype.writeUInt16BE=function(t,e,n){return t*=1,e>>>=0,n||_(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},u.prototype.writeUint32LE=u.prototype.writeUInt32LE=function(t,e,n){return t*=1,e>>>=0,n||_(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},u.prototype.writeUint32BE=u.prototype.writeUInt32BE=function(t,e,n){return t*=1,e>>>=0,n||_(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},u.prototype.writeBigUInt64LE=L(function(t,e=0){return x(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),u.prototype.writeBigUInt64BE=L(function(t,e=0){return E(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),u.prototype.writeIntLE=function(t,e,n,r){if(t*=1,e>>>=0,!r){let r=Math.pow(2,8*n-1);_(this,t,e,n,r-1,-r)}let i=0,o=1,a=0;for(this[e]=255&t;++i<n&&(o*=256);)t<0&&0===a&&0!==this[e+i-1]&&(a=1),this[e+i]=(t/o|0)-a&255;return e+n},u.prototype.writeIntBE=function(t,e,n,r){if(t*=1,e>>>=0,!r){let r=Math.pow(2,8*n-1);_(this,t,e,n,r-1,-r)}let i=n-1,o=1,a=0;for(this[e+i]=255&t;--i>=0&&(o*=256);)t<0&&0===a&&0!==this[e+i+1]&&(a=1),this[e+i]=(t/o|0)-a&255;return e+n},u.prototype.writeInt8=function(t,e,n){return t*=1,e>>>=0,n||_(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},u.prototype.writeInt16LE=function(t,e,n){return t*=1,e>>>=0,n||_(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},u.prototype.writeInt16BE=function(t,e,n){return t*=1,e>>>=0,n||_(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},u.prototype.writeInt32LE=function(t,e,n){return t*=1,e>>>=0,n||_(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},u.prototype.writeInt32BE=function(t,e,n){return t*=1,e>>>=0,n||_(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},u.prototype.writeBigInt64LE=L(function(t,e=0){return x(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),u.prototype.writeBigInt64BE=L(function(t,e=0){return E(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),u.prototype.writeFloatLE=function(t,e,n){return A(this,t,e,!0,n)},u.prototype.writeFloatBE=function(t,e,n){return A(this,t,e,!1,n)},u.prototype.writeDoubleLE=function(t,e,n){return B(this,t,e,!0,n)},u.prototype.writeDoubleBE=function(t,e,n){return B(this,t,e,!1,n)},u.prototype.copy=function(t,e,n,r){if(!u.isBuffer(t))throw TypeError("argument should be a Buffer");if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw RangeError("Index out of range");if(r<0)throw RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);let i=r-n;return this===t&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(e,n,r):Uint8Array.prototype.set.call(t,this.subarray(n,r),e),i},u.prototype.fill=function(t,e,n,r){let i;if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),void 0!==r&&"string"!=typeof r)throw TypeError("encoding must be a string");if("string"==typeof r&&!u.isEncoding(r))throw TypeError("Unknown encoding: "+r);if(1===t.length){let e=t.charCodeAt(0);("utf8"===r&&e<128||"latin1"===r)&&(t=e)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<n)throw RangeError("Out of range index");if(n<=e)return this;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"==typeof t)for(i=e;i<n;++i)this[i]=t;else{let o=u.isBuffer(t)?t:u.from(t,r),a=o.length;if(0===a)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<n-e;++i)this[i+e]=o[i%a]}return this};let I={};function k(t,e,n){I[t]=class extends n{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(t){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:t,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}function T(t){let e="",n=t.length,r=+("-"===t[0]);for(;n>=r+4;n-=3)e=`_${t.slice(n-3,n)}${e}`;return`${t.slice(0,n)}${e}`}function S(t,e,n,r,i,o){if(t>n||t<e){let r,i="bigint"==typeof e?"n":"";throw r=o>3?0===e||e===BigInt(0)?`>= 0${i} and < 2${i} ** ${(o+1)*8}${i}`:`>= -(2${i} ** ${(o+1)*8-1}${i}) and < 2 ** ${(o+1)*8-1}${i}`:`>= ${e}${i} and <= ${n}${i}`,new I.ERR_OUT_OF_RANGE("value",r,t)}N(i,"offset"),(void 0===r[i]||void 0===r[i+o])&&C(i,r.length-(o+1))}function N(t,e){if("number"!=typeof t)throw new I.ERR_INVALID_ARG_TYPE(e,"number",t)}function C(t,e,n){if(Math.floor(t)!==t)throw N(t,n),new I.ERR_OUT_OF_RANGE(n||"offset","an integer",t);if(e<0)throw new I.ERR_BUFFER_OUT_OF_BOUNDS;throw new I.ERR_OUT_OF_RANGE(n||"offset",`>= ${+!!n} and <= ${e}`,t)}k("ERR_BUFFER_OUT_OF_BOUNDS",function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),k("ERR_INVALID_ARG_TYPE",function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`},TypeError),k("ERR_OUT_OF_RANGE",function(t,e,n){let r=`The value of "${t}" is out of range.`,i=n;return Number.isInteger(n)&&Math.abs(n)>0x100000000?i=T(String(n)):"bigint"==typeof n&&(i=String(n),(n>BigInt(2)**BigInt(32)||n<-(BigInt(2)**BigInt(32)))&&(i=T(i)),i+="n"),r+=` It must be ${e}. Received ${i}`},RangeError);let O=/[^+/0-9A-Za-z-_]/g;function U(t,e){let n;e=e||1/0;let r=t.length,i=null,o=[];for(let a=0;a<r;++a){if((n=t.charCodeAt(a))>55295&&n<57344){if(!i){if(n>56319||a+1===r){(e-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(e-=3)>-1&&o.push(239,191,189),i=n;continue}n=(i-55296<<10|n-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((e-=1)<0)break;o.push(n)}else if(n<2048){if((e-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else if(n<1114112){if((e-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}else throw Error("Invalid code point")}return o}function R(t){return r.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(O,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function $(t,e,n,r){let i;for(i=0;i<r&&!(i+n>=e.length)&&!(i>=t.length);++i)e[i+n]=t[i];return i}function z(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}let P=function(){let t="0123456789abcdef",e=Array(256);for(let n=0;n<16;++n){let r=16*n;for(let i=0;i<16;++i)e[r+i]=t[n]+t[i]}return e}();function L(t){return"undefined"==typeof BigInt?j:t}function j(){throw Error("BigInt not supported")}},52596:(t,e,n)=>{n.d(e,{$:()=>r});function r(){for(var t,e,n=0,r="",i=arguments.length;n<i;n++)(t=arguments[n])&&(e=function t(e){var n,r,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(n=0;n<o;n++)e[n]&&(r=t(e[n]))&&(i&&(i+=" "),i+=r)}else for(r in e)e[r]&&(i&&(i+=" "),i+=r);return i}(t))&&(r&&(r+=" "),r+=e);return r}},57719:(t,e)=>{e.byteLength=function(t){var e=s(t),n=e[0],r=e[1];return(n+r)*3/4-r},e.toByteArray=function(t){var e,n,o=s(t),a=o[0],u=o[1],f=new i((a+u)*3/4-u),l=0,h=u>0?a-4:a;for(n=0;n<h;n+=4)e=r[t.charCodeAt(n)]<<18|r[t.charCodeAt(n+1)]<<12|r[t.charCodeAt(n+2)]<<6|r[t.charCodeAt(n+3)],f[l++]=e>>16&255,f[l++]=e>>8&255,f[l++]=255&e;return 2===u&&(e=r[t.charCodeAt(n)]<<2|r[t.charCodeAt(n+1)]>>4,f[l++]=255&e),1===u&&(e=r[t.charCodeAt(n)]<<10|r[t.charCodeAt(n+1)]<<4|r[t.charCodeAt(n+2)]>>2,f[l++]=e>>8&255,f[l++]=255&e),f},e.fromByteArray=function(t){for(var e,r=t.length,i=r%3,o=[],a=0,u=r-i;a<u;a+=16383)o.push(function(t,e,r){for(var i,o=[],a=e;a<r;a+=3)i=(t[a]<<16&0xff0000)+(t[a+1]<<8&65280)+(255&t[a+2]),o.push(n[i>>18&63]+n[i>>12&63]+n[i>>6&63]+n[63&i]);return o.join("")}(t,a,a+16383>u?u:a+16383));return 1===i?o.push(n[(e=t[r-1])>>2]+n[e<<4&63]+"=="):2===i&&o.push(n[(e=(t[r-2]<<8)+t[r-1])>>10]+n[e>>4&63]+n[e<<2&63]+"="),o.join("")};for(var n=[],r=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,u=o.length;a<u;++a)n[a]=o[a],r[o.charCodeAt(a)]=a;function s(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");-1===n&&(n=e);var r=n===e?0:4-n%4;return[n,r]}r[45]=62,r[95]=63},63664:(t,e,n)=>{n.d(e,{jN:()=>h,MF:()=>o,Wn:()=>H,Lt:()=>Y,r1:()=>X,gD:()=>s,XM:()=>l,iF:()=>M});var r="http://www.w3.org/1999/xhtml";let i={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function o(t){var e=t+="",n=e.indexOf(":");return n>=0&&"xmlns"!==(e=t.slice(0,n))&&(t=t.slice(n+1)),i.hasOwnProperty(e)?{space:i[e],local:t}:t}function a(t){var e=o(t);return(e.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var e=this.ownerDocument,n=this.namespaceURI;return n===r&&e.documentElement.namespaceURI===r?e.createElement(t):e.createElementNS(n,t)}})(e)}function u(){}function s(t){return null==t?u:function(){return this.querySelector(t)}}function f(){return[]}function l(t){return null==t?f:function(){return this.querySelectorAll(t)}}function h(t){return function(){return this.matches(t)}}function c(t){return function(e){return e.matches(t)}}var p=Array.prototype.find;function d(){return this.firstElementChild}var y=Array.prototype.filter;function g(){return Array.from(this.children)}function m(t){return Array(t.length)}function v(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}function b(t,e,n,r,i,o){for(var a,u=0,s=e.length,f=o.length;u<f;++u)(a=e[u])?(a.__data__=o[u],r[u]=a):n[u]=new v(t,o[u]);for(;u<s;++u)(a=e[u])&&(i[u]=a)}function w(t,e,n,r,i,o,a){var u,s,f,l=new Map,h=e.length,c=o.length,p=Array(h);for(u=0;u<h;++u)(s=e[u])&&(p[u]=f=a.call(s,s.__data__,u,e)+"",l.has(f)?i[u]=s:l.set(f,s));for(u=0;u<c;++u)f=a.call(t,o[u],u,o)+"",(s=l.get(f))?(r[u]=s,s.__data__=o[u],l.delete(f)):n[u]=new v(t,o[u]);for(u=0;u<h;++u)(s=e[u])&&l.get(p[u])===s&&(i[u]=s)}function _(t){return t.__data__}function x(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}function E(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function M(t,e){return t.style.getPropertyValue(e)||E(t).getComputedStyle(t,null).getPropertyValue(e)}function A(t){return t.trim().split(/^|\s+/)}function B(t){return t.classList||new I(t)}function I(t){this._node=t,this._names=A(t.getAttribute("class")||"")}function k(t,e){for(var n=B(t),r=-1,i=e.length;++r<i;)n.add(e[r])}function T(t,e){for(var n=B(t),r=-1,i=e.length;++r<i;)n.remove(e[r])}function S(){this.textContent=""}function N(){this.innerHTML=""}function C(){this.nextSibling&&this.parentNode.appendChild(this)}function O(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function U(){return null}function R(){var t=this.parentNode;t&&t.removeChild(this)}function $(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function z(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function P(t){return function(){var e=this.__on;if(e){for(var n,r=0,i=-1,o=e.length;r<o;++r)(n=e[r],t.type&&n.type!==t.type||n.name!==t.name)?e[++i]=n:this.removeEventListener(n.type,n.listener,n.options);++i?e.length=i:delete this.__on}}}function L(t,e,n){return function(){var r,i=this.__on,o=function(t){e.call(this,t,this.__data__)};if(i){for(var a=0,u=i.length;a<u;++a)if((r=i[a]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=n),r.value=e;return}}this.addEventListener(t.type,o,n),r={type:t.type,name:t.name,value:e,listener:o,options:n},i?i.push(r):this.__on=[r]}}function j(t,e,n){var r=E(t),i=r.CustomEvent;"function"==typeof i?i=new i(e,n):(i=r.document.createEvent("Event"),n?(i.initEvent(e,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(e,!1,!1)),t.dispatchEvent(i)}v.prototype={constructor:v,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}},I.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var D=[null];function F(t,e){this._groups=t,this._parents=e}function q(){return new F([[document.documentElement]],D)}F.prototype=q.prototype={constructor:F,select:function(t){"function"!=typeof t&&(t=s(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var o,a,u=e[i],f=u.length,l=r[i]=Array(f),h=0;h<f;++h)(o=u[h])&&(a=t.call(o,o.__data__,h,u))&&("__data__"in o&&(a.__data__=o.__data__),l[h]=a);return new F(r,this._parents)},selectAll:function(t){if("function"==typeof t){var e;e=t,t=function(){var t;return t=e.apply(this,arguments),null==t?[]:Array.isArray(t)?t:Array.from(t)}}else t=l(t);for(var n=this._groups,r=n.length,i=[],o=[],a=0;a<r;++a)for(var u,s=n[a],f=s.length,h=0;h<f;++h)(u=s[h])&&(i.push(t.call(u,u.__data__,h,s)),o.push(u));return new F(i,o)},selectChild:function(t){var e;return this.select(null==t?d:(e="function"==typeof t?t:c(t),function(){return p.call(this.children,e)}))},selectChildren:function(t){var e;return this.selectAll(null==t?g:(e="function"==typeof t?t:c(t),function(){return y.call(this.children,e)}))},filter:function(t){"function"!=typeof t&&(t=h(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var o,a=e[i],u=a.length,s=r[i]=[],f=0;f<u;++f)(o=a[f])&&t.call(o,o.__data__,f,a)&&s.push(o);return new F(r,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,_);var n=e?w:b,r=this._parents,i=this._groups;"function"!=typeof t&&(v=t,t=function(){return v});for(var o=i.length,a=Array(o),u=Array(o),s=Array(o),f=0;f<o;++f){var l=r[f],h=i[f],c=h.length,p="object"==typeof(m=t.call(l,l&&l.__data__,f,r))&&"length"in m?m:Array.from(m),d=p.length,y=u[f]=Array(d),g=a[f]=Array(d);n(l,h,y,g,s[f]=Array(c),p,e);for(var m,v,x,E,M=0,A=0;M<d;++M)if(x=y[M]){for(M>=A&&(A=M+1);!(E=g[A])&&++A<d;);x._next=E||null}}return(a=new F(a,r))._enter=u,a._exit=s,a},enter:function(){return new F(this._enter||this._groups.map(m),this._parents)},exit:function(){return new F(this._exit||this._groups.map(m),this._parents)},join:function(t,e,n){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=e&&(i=e(i))&&(i=i.selection()),null==n?o.remove():n(o),r&&i?r.merge(i).order():i},merge:function(t){for(var e=t.selection?t.selection():t,n=this._groups,r=e._groups,i=n.length,o=r.length,a=Math.min(i,o),u=Array(i),s=0;s<a;++s)for(var f,l=n[s],h=r[s],c=l.length,p=u[s]=Array(c),d=0;d<c;++d)(f=l[d]||h[d])&&(p[d]=f);for(;s<i;++s)u[s]=n[s];return new F(u,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,n=t.length;++e<n;)for(var r,i=t[e],o=i.length-1,a=i[o];--o>=0;)(r=i[o])&&(a&&4^r.compareDocumentPosition(a)&&a.parentNode.insertBefore(r,a),a=r);return this},sort:function(t){function e(e,n){return e&&n?t(e.__data__,n.__data__):!e-!n}t||(t=x);for(var n=this._groups,r=n.length,i=Array(r),o=0;o<r;++o){for(var a,u=n[o],s=u.length,f=i[o]=Array(s),l=0;l<s;++l)(a=u[l])&&(f[l]=a);f.sort(e)}return new F(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r=t[e],i=0,o=r.length;i<o;++i){var a=r[i];if(a)return a}return null},size:function(){let t=0;for(let e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,n=0,r=e.length;n<r;++n)for(var i,o=e[n],a=0,u=o.length;a<u;++a)(i=o[a])&&t.call(i,i.__data__,a,o);return this},attr:function(t,e){var n=o(t);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==e?n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof e?n.local?function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}}:function(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttribute(t):this.setAttribute(t,n)}}:n.local?function(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}:function(t,e){return function(){this.setAttribute(t,e)}})(n,e))},style:function(t,e,n){return arguments.length>1?this.each((null==e?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof e?function(t,e,n){return function(){var r=e.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,n)}}:function(t,e,n){return function(){this.style.setProperty(t,e,n)}})(t,e,null==n?"":n)):M(this.node(),t)},property:function(t,e){return arguments.length>1?this.each((null==e?function(t){return function(){delete this[t]}}:"function"==typeof e?function(t,e){return function(){var n=e.apply(this,arguments);null==n?delete this[t]:this[t]=n}}:function(t,e){return function(){this[t]=e}})(t,e)):this.node()[t]},classed:function(t,e){var n=A(t+"");if(arguments.length<2){for(var r=B(this.node()),i=-1,o=n.length;++i<o;)if(!r.contains(n[i]))return!1;return!0}return this.each(("function"==typeof e?function(t,e){return function(){(e.apply(this,arguments)?k:T)(this,t)}}:e?function(t){return function(){k(this,t)}}:function(t){return function(){T(this,t)}})(n,e))},text:function(t){return arguments.length?this.each(null==t?S:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?N:("function"==typeof t?function(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(C)},lower:function(){return this.each(O)},append:function(t){var e="function"==typeof t?t:a(t);return this.select(function(){return this.appendChild(e.apply(this,arguments))})},insert:function(t,e){var n="function"==typeof t?t:a(t),r=null==e?U:"function"==typeof e?e:s(e);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(R)},clone:function(t){return this.select(t?z:$)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,n){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var e="",n=t.indexOf(".");return n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),{type:t,name:e}}),a=o.length;if(arguments.length<2){var u=this.node().__on;if(u){for(var s,f=0,l=u.length;f<l;++f)for(r=0,s=u[f];r<a;++r)if((i=o[r]).type===s.type&&i.name===s.name)return s.value}return}for(r=0,u=e?L:P;r<a;++r)this.each(u(o[r],e,n));return this},dispatch:function(t,e){return this.each(("function"==typeof e?function(t,e){return function(){return j(this,t,e.apply(this,arguments))}}:function(t,e){return function(){return j(this,t,e)}})(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r,i=t[e],o=0,a=i.length;o<a;++o)(r=i[o])&&(yield r)}};let X=q;function Y(t){return"string"==typeof t?new F([[document.querySelector(t)]],[document.documentElement]):new F([[t]],D)}var V=0;function W(){this._="@"+(++V).toString(36)}function H(t,e){if(t=function(t){let e;for(;e=t.sourceEvent;)t=e;return t}(t),void 0===e&&(e=t.currentTarget),e){var n=e.ownerSVGElement||e;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(e.getScreenCTM().inverse())).x,r.y]}if(e.getBoundingClientRect){var i=e.getBoundingClientRect();return[t.clientX-i.left-e.clientLeft,t.clientY-i.top-e.clientTop]}}return[t.pageX,t.pageY]}W.prototype=(function(){return new W}).prototype={constructor:W,get:function(t){for(var e=this._;!(e in t);)if(!(t=t.parentNode))return;return t[e]},set:function(t,e){return t[this._]=e},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}}},74466:(t,e,n)=>{n.d(e,{F:()=>a});var r=n(52596);let i=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,o=r.$,a=(t,e)=>n=>{var r;if((null==e?void 0:e.variants)==null)return o(t,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:a,defaultVariants:u}=e,s=Object.keys(a).map(t=>{let e=null==n?void 0:n[t],r=null==u?void 0:u[t];if(null===e)return null;let o=i(e)||i(r);return a[t][o]}),f=n&&Object.entries(n).reduce((t,e)=>{let[n,r]=e;return void 0===r||(t[n]=r),t},{});return o(t,s,null==e||null==(r=e.compoundVariants)?void 0:r.reduce((t,e)=>{let{class:n,className:r,...i}=e;return Object.entries(i).every(t=>{let[e,n]=t;return Array.isArray(n)?n.includes({...u,...f}[e]):({...u,...f})[e]===n})?[...t,n,r]:t},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},75694:(t,e,n)=>{n.d(e,{A:()=>function t(e){if("string"==typeof e||"number"==typeof e)return""+e;let n="";if(Array.isArray(e))for(let r=0,i;r<e.length;r++)""!==(i=t(e[r]))&&(n+=(n&&" ")+i);else for(let t in e)e[t]&&(n+=(n&&" ")+t);return n}})},76528:(t,e,n)=>{n.d(e,{s_:()=>to,GS:()=>G,_V:()=>Z});var r,i,o=n(17359),a=n(37396),u=n(7609),s=n(63664),f=0,l=0,h=0,c=0,p=0,d=0,y="object"==typeof performance&&performance.now?performance:Date,g="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function m(){return p||(g(v),p=y.now()+d)}function v(){p=0}function b(){this._call=this._time=this._next=null}function w(t,e,n){var r=new b;return r.restart(t,e,n),r}function _(){p=(c=y.now())+d,f=l=0;try{m(),++f;for(var t,e=r;e;)(t=p-e._time)>=0&&e._call.call(void 0,t),e=e._next;--f}finally{f=0,function(){for(var t,e,n=r,o=1/0;n;)n._call?(o>n._time&&(o=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:r=e);i=t,E(o)}(),p=0}}function x(){var t=y.now(),e=t-c;e>1e3&&(d-=e,c=t)}function E(t){!f&&(l&&(l=clearTimeout(l)),t-p>24?(t<1/0&&(l=setTimeout(_,t-y.now()-d)),h&&(h=clearInterval(h))):(h||(c=y.now(),h=setInterval(x,1e3)),f=1,g(_)))}function M(t,e,n){var r=new b;return e=null==e?0:+e,r.restart(n=>{r.stop(),t(n+e)},e,n),r}b.prototype=w.prototype={constructor:b,restart:function(t,e,n){if("function"!=typeof t)throw TypeError("callback is not a function");n=(null==n?m():+n)+(null==e?0:+e),this._next||i===this||(i?i._next=this:r=this,i=this),this._call=t,this._time=n,E()},stop:function(){this._call&&(this._call=null,this._time=1/0,E())}};var A=(0,o.J)("start","end","cancel","interrupt"),B=[];function I(t,e,n,r,i,o){var a=t.__transition;if(a){if(n in a)return}else t.__transition={};!function(t,e,n){var r,i=t.__transition;function o(s){var f,l,h,c;if(1!==n.state)return u();for(f in i)if((c=i[f]).name===n.name){if(3===c.state)return M(o);4===c.state?(c.state=6,c.timer.stop(),c.on.call("interrupt",t,t.__data__,c.index,c.group),delete i[f]):+f<e&&(c.state=6,c.timer.stop(),c.on.call("cancel",t,t.__data__,c.index,c.group),delete i[f])}if(M(function(){3===n.state&&(n.state=4,n.timer.restart(a,n.delay,n.time),a(s))}),n.state=2,n.on.call("start",t,t.__data__,n.index,n.group),2===n.state){for(f=0,n.state=3,r=Array(h=n.tween.length),l=-1;f<h;++f)(c=n.tween[f].value.call(t,t.__data__,n.index,n.group))&&(r[++l]=c);r.length=l+1}}function a(e){for(var i=e<n.duration?n.ease.call(null,e/n.duration):(n.timer.restart(u),n.state=5,1),o=-1,a=r.length;++o<a;)r[o].call(t,i);5===n.state&&(n.on.call("end",t,t.__data__,n.index,n.group),u())}function u(){for(var r in n.state=6,n.timer.stop(),delete i[e],i)return;delete t.__transition}i[e]=n,n.timer=w(function(t){n.state=1,n.timer.restart(o,n.delay,n.time),n.delay<=t&&o(t-n.delay)},0,n.time)}(t,n,{name:e,index:r,group:i,on:A,tween:B,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function k(t,e){var n=S(t,e);if(n.state>0)throw Error("too late; already scheduled");return n}function T(t,e){var n=S(t,e);if(n.state>3)throw Error("too late; already running");return n}function S(t,e){var n=t.__transition;if(!n||!(n=n[e]))throw Error("transition not found");return n}function N(t,e){var n,r,i,o=t.__transition,a=!0;if(o){for(i in e=null==e?null:e+"",o){if((n=o[i]).name!==e){a=!1;continue}r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",t,t.__data__,n.index,n.group),delete o[i]}a&&delete t.__transition}}function C(t,e,n){var r=t._id;return t.each(function(){var t=T(this,r);(t.value||(t.value={}))[e]=n.apply(this,arguments)}),function(t){return S(t,r).value[e]}}var O=n(1673);function U(t,e){var n;return("number"==typeof e?u.Dj:e instanceof O.yW?u.Zr:(n=(0,O.yW)(e))?(e=n,u.Zr):u.zl)(t,e)}var R=s.r1.prototype.constructor;function $(t){return function(){this.style.removeProperty(t)}}var z=0;function P(t,e,n,r){this._groups=t,this._parents=e,this._name=n,this._id=r}var L=s.r1.prototype;P.prototype=(function(t){return(0,s.r1)().transition(t)}).prototype={constructor:P,select:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=(0,s.gD)(t));for(var r=this._groups,i=r.length,o=Array(i),a=0;a<i;++a)for(var u,f,l=r[a],h=l.length,c=o[a]=Array(h),p=0;p<h;++p)(u=l[p])&&(f=t.call(u,u.__data__,p,l))&&("__data__"in u&&(f.__data__=u.__data__),c[p]=f,I(c[p],e,n,p,c,S(u,n)));return new P(o,this._parents,e,n)},selectAll:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=(0,s.XM)(t));for(var r=this._groups,i=r.length,o=[],a=[],u=0;u<i;++u)for(var f,l=r[u],h=l.length,c=0;c<h;++c)if(f=l[c]){for(var p,d=t.call(f,f.__data__,c,l),y=S(f,n),g=0,m=d.length;g<m;++g)(p=d[g])&&I(p,e,n,g,d,y);o.push(d),a.push(f)}return new P(o,a,e,n)},selectChild:L.selectChild,selectChildren:L.selectChildren,filter:function(t){"function"!=typeof t&&(t=(0,s.jN)(t));for(var e=this._groups,n=e.length,r=Array(n),i=0;i<n;++i)for(var o,a=e[i],u=a.length,f=r[i]=[],l=0;l<u;++l)(o=a[l])&&t.call(o,o.__data__,l,a)&&f.push(o);return new P(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var e=this._groups,n=t._groups,r=e.length,i=n.length,o=Math.min(r,i),a=Array(r),u=0;u<o;++u)for(var s,f=e[u],l=n[u],h=f.length,c=a[u]=Array(h),p=0;p<h;++p)(s=f[p]||l[p])&&(c[p]=s);for(;u<r;++u)a[u]=e[u];return new P(a,this._parents,this._name,this._id)},selection:function(){return new R(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,n=++z,r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],s=u.length,f=0;f<s;++f)if(a=u[f]){var l=S(a,e);I(a,t,n,f,u,{time:l.time+l.delay+l.duration,delay:0,duration:l.duration,ease:l.ease})}return new P(r,this._parents,t,n)},call:L.call,nodes:L.nodes,node:L.node,size:L.size,empty:L.empty,each:L.each,on:function(t,e){var n,r,i,o,a,u,s=this._id;return arguments.length<2?S(this.node(),s).on.on(t):this.each((n=s,r=t,i=e,u=(r+"").trim().split(/^|\s+/).every(function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t})?k:T,function(){var t=u(this,n),e=t.on;e!==o&&(a=(o=e).copy()).on(r,i),t.on=a}))},attr:function(t,e){var n=(0,s.MF)(t),r="transform"===n?u.pN:U;return this.attrTween(t,"function"==typeof e?(n.local?function(t,e,n){var r,i,o;return function(){var a,u,s=n(this);return null==s?void this.removeAttributeNS(t.space,t.local):(a=this.getAttributeNS(t.space,t.local))===(u=s+"")?null:a===r&&u===i?o:(i=u,o=e(r=a,s))}}:function(t,e,n){var r,i,o;return function(){var a,u,s=n(this);return null==s?void this.removeAttribute(t):(a=this.getAttribute(t))===(u=s+"")?null:a===r&&u===i?o:(i=u,o=e(r=a,s))}})(n,r,C(this,"attr."+t,e)):null==e?(n.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(n):(n.local?function(t,e,n){var r,i,o=n+"";return function(){var a=this.getAttributeNS(t.space,t.local);return a===o?null:a===r?i:i=e(r=a,n)}}:function(t,e,n){var r,i,o=n+"";return function(){var a=this.getAttribute(t);return a===o?null:a===r?i:i=e(r=a,n)}})(n,r,e))},attrTween:function(t,e){var n="attr."+t;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==e)return this.tween(n,null);if("function"!=typeof e)throw Error();var r=(0,s.MF)(t);return this.tween(n,(r.local?function(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(e){this.setAttributeNS(t.space,t.local,i.call(this,e))}),n}return i._value=e,i}:function(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(e){this.setAttribute(t,i.call(this,e))}),n}return i._value=e,i})(r,e))},style:function(t,e,n){var r,i,o,a,f,l,h,c,p,d,y,g,m,v,b,w,_,x,E,M,A,B="transform"==(t+="")?u.TE:U;return null==e?this.styleTween(t,(r=t,function(){var t=(0,s.iF)(this,r),e=(this.style.removeProperty(r),(0,s.iF)(this,r));return t===e?null:t===i&&e===o?a:a=B(i=t,o=e)})).on("end.style."+t,$(t)):"function"==typeof e?this.styleTween(t,(f=t,l=C(this,"style."+t,e),function(){var t=(0,s.iF)(this,f),e=l(this),n=e+"";return null==e&&(this.style.removeProperty(f),n=e=(0,s.iF)(this,f)),t===n?null:t===h&&n===c?p:(c=n,p=B(h=t,e))})).each((d=this._id,_="end."+(w="style."+(y=t)),function(){var t=T(this,d),e=t.on,n=null==t.value[w]?b||(b=$(y)):void 0;(e!==g||v!==n)&&(m=(g=e).copy()).on(_,v=n),t.on=m})):this.styleTween(t,(x=t,A=e+"",function(){var t=(0,s.iF)(this,x);return t===A?null:t===E?M:M=B(E=t,e)}),n).on("end.style."+t,null)},styleTween:function(t,e,n){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==e)return this.tween(r,null);if("function"!=typeof e)throw Error();return this.tween(r,function(t,e,n){var r,i;function o(){var o=e.apply(this,arguments);return o!==i&&(r=(i=o)&&function(e){this.style.setProperty(t,o.call(this,e),n)}),r}return o._value=e,o}(t,e,null==n?"":n))},text:function(t){var e,n;return this.tween("text","function"==typeof t?(e=C(this,"text",t),function(){var t=e(this);this.textContent=null==t?"":t}):(n=null==t?"":t+"",function(){this.textContent=n}))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!=typeof t)throw Error();return this.tween(e,function(t){var e,n;function r(){var r=t.apply(this,arguments);return r!==n&&(e=(n=r)&&function(t){this.textContent=r.call(this,t)}),e}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var e=this.parentNode;for(var n in this.__transition)if(+n!==t)return;e&&e.removeChild(this)}))},tween:function(t,e){var n=this._id;if(t+="",arguments.length<2){for(var r,i=S(this.node(),n).tween,o=0,a=i.length;o<a;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==e?function(t,e){var n,r;return function(){var i=T(this,t),o=i.tween;if(o!==n){r=n=o;for(var a=0,u=r.length;a<u;++a)if(r[a].name===e){(r=r.slice()).splice(a,1);break}}i.tween=r}}:function(t,e,n){var r,i;if("function"!=typeof n)throw Error();return function(){var o=T(this,t),a=o.tween;if(a!==r){i=(r=a).slice();for(var u={name:e,value:n},s=0,f=i.length;s<f;++s)if(i[s].name===e){i[s]=u;break}s===f&&i.push(u)}o.tween=i}})(n,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){k(this,t).delay=+e.apply(this,arguments)}}:function(t,e){return e*=1,function(){k(this,t).delay=e}})(e,t)):S(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?function(t,e){return function(){T(this,t).duration=+e.apply(this,arguments)}}:function(t,e){return e*=1,function(){T(this,t).duration=e}})(e,t)):S(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(function(t,e){if("function"!=typeof e)throw Error();return function(){T(this,t).ease=e}}(e,t)):S(this.node(),e).ease},easeVarying:function(t){var e;if("function"!=typeof t)throw Error();return this.each((e=this._id,function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw Error();T(this,e).ease=n}))},end:function(){var t,e,n=this,r=n._id,i=n.size();return new Promise(function(o,a){var u={value:a},s={value:function(){0==--i&&o()}};n.each(function(){var n=T(this,r),i=n.on;i!==t&&((e=(t=i).copy())._.cancel.push(u),e._.interrupt.push(u),e._.end.push(s)),n.on=e}),0===i&&o()})},[Symbol.iterator]:L[Symbol.iterator]},function t(e){function n(t){return Math.pow(t,e)}return e*=1,n.exponent=t,n}(3),function t(e){function n(t){return 1-Math.pow(1-t,e)}return e*=1,n.exponent=t,n}(3),function t(e){function n(t){return((t*=2)<=1?Math.pow(t,e):2-Math.pow(2-t,e))/2}return e*=1,n.exponent=t,n}(3);var j=Math.PI;function D(t){return(Math.pow(2,-10*t)-9765625e-10)*1.0009775171065494}var F=4/11,q=1/(4/11)/(4/11);(function t(e){function n(t){return(t*=1)*t*(e*(t-1)+t)}return e*=1,n.overshoot=t,n})(1.70158),function t(e){function n(t){return--t*t*((t+1)*e+t)+1}return e*=1,n.overshoot=t,n}(1.70158),function t(e){function n(t){return((t*=2)<1?t*t*((e+1)*t-e):(t-=2)*t*((e+1)*t+e)+2)/2}return e*=1,n.overshoot=t,n}(1.70158);var X=2*Math.PI;(function t(e,n){var r=Math.asin(1/(e=Math.max(1,e)))*(n/=X);function i(t){return e*D(- --t)*Math.sin((r-t)/n)}return i.amplitude=function(e){return t(e,n*X)},i.period=function(n){return t(e,n)},i})(1,.3),function t(e,n){var r=Math.asin(1/(e=Math.max(1,e)))*(n/=X);function i(t){return 1-e*D(t*=1)*Math.sin((t+r)/n)}return i.amplitude=function(e){return t(e,n*X)},i.period=function(n){return t(e,n)},i}(1,.3),function t(e,n){var r=Math.asin(1/(e=Math.max(1,e)))*(n/=X);function i(t){return((t=2*t-1)<0?e*D(-t)*Math.sin((r-t)/n):2-e*D(t)*Math.sin((r+t)/n))/2}return i.amplitude=function(e){return t(e,n*X)},i.period=function(n){return t(e,n)},i}(1,.3);var Y={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};s.r1.prototype.interrupt=function(t){return this.each(function(){N(this,t)})},s.r1.prototype.transition=function(t){var e,n;t instanceof P?(e=t._id,t=t._name):(e=++z,(n=Y).time=m(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var a,u=r[o],s=u.length,f=0;f<s;++f)(a=u[f])&&I(a,t,e,f,u,n||function(t,e){for(var n;!(n=t.__transition)||!(n=n[e]);)if(!(t=t.parentNode))throw Error(`transition ${e} not found`);return n}(a,e));return new P(r,this._parents,t,e)};let V=t=>()=>t;function W(t,{sourceEvent:e,target:n,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function H(t,e,n){this.k=t,this.x=e,this.y=n}H.prototype={constructor:H,scale:function(t){return 1===t?this:new H(this.k*t,this.x,this.y)},translate:function(t,e){return 0===t&0===e?this:new H(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var G=new H(1,0,0);function Z(t){for(;!t.__zoom;)if(!(t=t.parentNode))return G;return t.__zoom}function K(t){t.stopImmediatePropagation()}function Q(t){t.preventDefault(),t.stopImmediatePropagation()}function J(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function tt(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function te(){return this.__zoom||G}function tn(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function tr(){return navigator.maxTouchPoints||"ontouchstart"in this}function ti(t,e,n){var r=t.invertX(e[0][0])-n[0][0],i=t.invertX(e[1][0])-n[1][0],o=t.invertY(e[0][1])-n[0][1],a=t.invertY(e[1][1])-n[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),a>o?(o+a)/2:Math.min(0,o)||Math.max(0,a))}function to(){var t,e,n,r=J,i=tt,f=ti,l=tn,h=tr,c=[0,1/0],p=[[-1/0,-1/0],[1/0,1/0]],d=250,y=u.p7,g=(0,o.J)("start","zoom","end"),m=0,v=10;function b(t){t.property("__zoom",te).on("wheel.zoom",B,{passive:!1}).on("mousedown.zoom",I).on("dblclick.zoom",k).filter(h).on("touchstart.zoom",T).on("touchmove.zoom",S).on("touchend.zoom touchcancel.zoom",C).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function w(t,e){return(e=Math.max(c[0],Math.min(c[1],e)))===t.k?t:new H(e,t.x,t.y)}function _(t,e,n){var r=e[0]-n[0]*t.k,i=e[1]-n[1]*t.k;return r===t.x&&i===t.y?t:new H(t.k,r,i)}function x(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function E(t,e,n,r){t.on("start.zoom",function(){M(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){M(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,o=M(this,t).event(r),a=i.apply(this,t),u=null==n?x(a):"function"==typeof n?n.apply(this,t):n,s=Math.max(a[1][0]-a[0][0],a[1][1]-a[0][1]),f=this.__zoom,l="function"==typeof e?e.apply(this,t):e,h=y(f.invert(u).concat(s/f.k),l.invert(u).concat(s/l.k));return function(t){if(1===t)t=l;else{var e=h(t),n=s/e[2];t=new H(n,u[0]-e[0]*n,u[1]-e[1]*n)}o.zoom(null,t)}})}function M(t,e,n){return!n&&t.__zooming||new A(t,e)}function A(t,e){this.that=t,this.args=e,this.active=0,this.sourceEvent=null,this.extent=i.apply(t,e),this.taps=0}function B(t,...e){if(r.apply(this,arguments)){var n=M(this,e).event(t),i=this.__zoom,o=Math.max(c[0],Math.min(c[1],i.k*Math.pow(2,l.apply(this,arguments)))),a=(0,s.Wn)(t);if(n.wheel)(n.mouse[0][0]!==a[0]||n.mouse[0][1]!==a[1])&&(n.mouse[1]=i.invert(n.mouse[0]=a)),clearTimeout(n.wheel);else{if(i.k===o)return;n.mouse=[a,i.invert(a)],N(this),n.start()}Q(t),n.wheel=setTimeout(function(){n.wheel=null,n.end()},150),n.zoom("mouse",f(_(w(i,o),n.mouse[0],n.mouse[1]),n.extent,p))}}function I(t,...e){if(!n&&r.apply(this,arguments)){var i=t.currentTarget,o=M(this,e,!0).event(t),u=(0,s.Lt)(t.view).on("mousemove.zoom",function(t){if(Q(t),!o.moved){var e=t.clientX-h,n=t.clientY-c;o.moved=e*e+n*n>m}o.event(t).zoom("mouse",f(_(o.that.__zoom,o.mouse[0]=(0,s.Wn)(t,i),o.mouse[1]),o.extent,p))},!0).on("mouseup.zoom",function(t){u.on("mousemove.zoom mouseup.zoom",null),(0,a.EH)(t.view,o.moved),Q(t),o.event(t).end()},!0),l=(0,s.Wn)(t,i),h=t.clientX,c=t.clientY;(0,a.XD)(t.view),K(t),o.mouse=[l,this.__zoom.invert(l)],N(this),o.start()}}function k(t,...e){if(r.apply(this,arguments)){var n=this.__zoom,o=(0,s.Wn)(t.changedTouches?t.changedTouches[0]:t,this),a=n.invert(o),u=n.k*(t.shiftKey?.5:2),l=f(_(w(n,u),o,a),i.apply(this,e),p);Q(t),d>0?(0,s.Lt)(this).transition().duration(d).call(E,l,o,t):(0,s.Lt)(this).call(b.transform,l,o,t)}}function T(n,...i){if(r.apply(this,arguments)){var o,a,u,f,l=n.touches,h=l.length,c=M(this,i,n.changedTouches.length===h).event(n);for(K(n),a=0;a<h;++a)u=l[a],f=[f=(0,s.Wn)(u,this),this.__zoom.invert(f),u.identifier],c.touch0?c.touch1||c.touch0[2]===f[2]||(c.touch1=f,c.taps=0):(c.touch0=f,o=!0,c.taps=1+!!t);t&&(t=clearTimeout(t)),o&&(c.taps<2&&(e=f[0],t=setTimeout(function(){t=null},500)),N(this),c.start())}}function S(t,...e){if(this.__zooming){var n,r,i,o,a=M(this,e).event(t),u=t.changedTouches,l=u.length;for(Q(t),n=0;n<l;++n)r=u[n],i=(0,s.Wn)(r,this),a.touch0&&a.touch0[2]===r.identifier?a.touch0[0]=i:a.touch1&&a.touch1[2]===r.identifier&&(a.touch1[0]=i);if(r=a.that.__zoom,a.touch1){var h=a.touch0[0],c=a.touch0[1],d=a.touch1[0],y=a.touch1[1],g=(g=d[0]-h[0])*g+(g=d[1]-h[1])*g,m=(m=y[0]-c[0])*m+(m=y[1]-c[1])*m;r=w(r,Math.sqrt(g/m)),i=[(h[0]+d[0])/2,(h[1]+d[1])/2],o=[(c[0]+y[0])/2,(c[1]+y[1])/2]}else{if(!a.touch0)return;i=a.touch0[0],o=a.touch0[1]}a.zoom("touch",f(_(r,i,o),a.extent,p))}}function C(t,...r){if(this.__zooming){var i,o,a=M(this,r).event(t),u=t.changedTouches,f=u.length;for(K(t),n&&clearTimeout(n),n=setTimeout(function(){n=null},500),i=0;i<f;++i)o=u[i],a.touch0&&a.touch0[2]===o.identifier?delete a.touch0:a.touch1&&a.touch1[2]===o.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(o=(0,s.Wn)(o,this),Math.hypot(e[0]-o[0],e[1]-o[1])<v)){var l=(0,s.Lt)(this).on("dblclick.zoom");l&&l.apply(this,arguments)}}}return b.transform=function(t,e,n,r){var i=t.selection?t.selection():t;i.property("__zoom",te),t!==i?E(t,e,n,r):i.interrupt().each(function(){M(this,arguments).event(r).start().zoom(null,"function"==typeof e?e.apply(this,arguments):e).end()})},b.scaleBy=function(t,e,n,r){b.scaleTo(t,function(){var t=this.__zoom.k,n="function"==typeof e?e.apply(this,arguments):e;return t*n},n,r)},b.scaleTo=function(t,e,n,r){b.transform(t,function(){var t=i.apply(this,arguments),r=this.__zoom,o=null==n?x(t):"function"==typeof n?n.apply(this,arguments):n,a=r.invert(o),u="function"==typeof e?e.apply(this,arguments):e;return f(_(w(r,u),o,a),t,p)},n,r)},b.translateBy=function(t,e,n,r){b.transform(t,function(){return f(this.__zoom.translate("function"==typeof e?e.apply(this,arguments):e,"function"==typeof n?n.apply(this,arguments):n),i.apply(this,arguments),p)},null,r)},b.translateTo=function(t,e,n,r,o){b.transform(t,function(){var t=i.apply(this,arguments),o=this.__zoom,a=null==r?x(t):"function"==typeof r?r.apply(this,arguments):r;return f(G.translate(a[0],a[1]).scale(o.k).translate("function"==typeof e?-e.apply(this,arguments):-e,"function"==typeof n?-n.apply(this,arguments):-n),t,p)},r,o)},A.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,e){return this.mouse&&"mouse"!==t&&(this.mouse[1]=e.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=e.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=e.invert(this.touch1[0])),this.that.__zoom=e,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var e=(0,s.Lt)(this.that).datum();g.call(t,this.that,new W(t,{sourceEvent:this.sourceEvent,target:b,type:t,transform:this.that.__zoom,dispatch:g}),e)}},b.wheelDelta=function(t){return arguments.length?(l="function"==typeof t?t:V(+t),b):l},b.filter=function(t){return arguments.length?(r="function"==typeof t?t:V(!!t),b):r},b.touchable=function(t){return arguments.length?(h="function"==typeof t?t:V(!!t),b):h},b.extent=function(t){return arguments.length?(i="function"==typeof t?t:V([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),b):i},b.scaleExtent=function(t){return arguments.length?(c[0]=+t[0],c[1]=+t[1],b):[c[0],c[1]]},b.translateExtent=function(t){return arguments.length?(p[0][0]=+t[0][0],p[1][0]=+t[1][0],p[0][1]=+t[0][1],p[1][1]=+t[1][1],b):[[p[0][0],p[0][1]],[p[1][0],p[1][1]]]},b.constrain=function(t){return arguments.length?(f=t,b):f},b.duration=function(t){return arguments.length?(d=+t,b):d},b.interpolate=function(t){return arguments.length?(y=t,b):y},b.on=function(){var t=g.on.apply(g,arguments);return t===g?b:t},b.clickDistance=function(t){return arguments.length?(m=(t*=1)*t,b):Math.sqrt(m)},b.tapDistance=function(t){return arguments.length?(v=+t,b):v},b}Z.prototype=H.prototype}}]);