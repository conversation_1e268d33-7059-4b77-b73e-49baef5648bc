exports.id=4912,exports.ids=[4912],exports.modules={2052:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(60687),a=s(16189),i=s(50549),n=s(36721),l=s(24868),c=s(34374);function o({children:e}){let t=(0,a.usePathname)(),{toasts:s,removeToast:o}=(0,c.dj)();return"/"===t||t.startsWith("/pricing")||t.startsWith("/features")||t.startsWith("/about")||t.startsWith("/routing-strategies")||t.startsWith("/contact")||t.startsWith("/docs")||t.startsWith("/blog")||t.startsWith("/auth/")||t.startsWith("/privacy")||t.startsWith("/terms")||t.startsWith("/cookies")||t.startsWith("/security")?(0,r.jsxs)(r.Fragment,{children:[e,(0,r.jsx)(c.N9,{toasts:s,onRemove:o})]}):(0,r.jsx)(i.G,{children:(0,r.jsx)(n.i9,{children:(0,r.jsx)(l.A,{children:e})})})}},11016:(e,t,s)=>{"use strict";s.d(t,{R:()=>i});var r=s(43210),a=s(79481);function i(){let[e,t]=(0,r.useState)(null),[i,n]=(0,r.useState)(null),[l,c]=(0,r.useState)(null),[o,d]=(0,r.useState)(!0),[u,h]=(0,r.useState)(null);(0,a.createSupabaseBrowserClient)();let m=async e=>{try{let t=Date.now(),s=await fetch(`/api/stripe/subscription-status?userId=${e.id}&_t=${t}`,{cache:"no-store",headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(!s.ok)throw await s.text(),Error(`Failed to fetch subscription status: ${s.status} ${s.statusText}`);let r=await s.json();n(r)}catch(e){e instanceof Error&&e.message.includes("Failed to fetch")?n({hasActiveSubscription:!1,tier:"free",status:null,currentPeriodEnd:null,cancelAtPeriodEnd:!1,isFree:!0}):h(e instanceof Error?e.message:"Unknown error")}},g=async e=>{try{let t=await fetch("/api/stripe/subscription-status",{method:"POST",cache:"no-store",headers:{"Content-Type":"application/json","Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"},body:JSON.stringify({userId:e.id,_t:Date.now()})});if(!t.ok)throw Error("Failed to fetch usage status");let s=await t.json();c(s)}catch(e){e instanceof Error&&e.message.includes("Failed to fetch")?c({tier:"free",usage:{configurations:0,apiKeys:0,apiRequests:0},limits:{configurations:1,apiKeysPerConfig:3,apiRequests:999999,canUseAdvancedRouting:!1,canUseCustomRoles:!1,maxCustomRoles:0,canUsePromptEngineering:!1,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1},canCreateConfig:!0,canCreateApiKey:!0}):h(e instanceof Error?e.message:"Unknown error")}finally{d(!1)}};return{subscriptionStatus:i,usageStatus:l,loading:o,error:u,createCheckoutSession:async t=>{if(!e)throw Error("User not authenticated");let s=await fetch("/api/stripe/create-checkout-session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({priceId:function(e){switch(e){case"free":return"price_1Rcej4C97XFBBUvdWED2Xp8B";case"starter":return"price_1RcekMC97XFBBUvdYnl0leWM";case"professional":return"price_1RcelCC97XFBBUvdfvuJnGnC";case"enterprise":return"price_1RceljC97XFBBUvdyCtcBYyT";default:throw Error(`Invalid tier: ${e}`)}}(t),userId:e.id,userEmail:e.email,tier:t})});if(!s.ok)throw Error((await s.json()).error||"Failed to create checkout session");let{url:r}=await s.json();window.location.href=r},openCustomerPortal:async t=>{if(!e)throw Error("User not authenticated");let s=await fetch("/api/stripe/customer-portal",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e.id,returnUrl:t})});if(!s.ok)throw Error((await s.json()).error||"Failed to open customer portal");let{url:r}=await s.json();window.location.href=r},refreshStatus:async()=>{if(e){d(!0),h(null);try{let{clearUserSpecificCache:t}=await s.e(8979).then(s.bind(s,18979));await t(e.id)}catch(e){}try{if("caches"in window){let e=await caches.keys();await Promise.all(e.map(e=>caches.delete(e)))}}catch(e){}await new Promise(e=>setTimeout(e,500));try{n(null),c(null),await Promise.all([m(e),g(e)])}catch(e){h(e instanceof Error?e.message:"Failed to refresh subscription status")}}},isAuthenticated:!!e,user:e}}},11638:(e,t,s)=>{"use strict";s.d(t,{default:()=>a}),s(43210);var r=s(16189);function a(){return(0,r.usePathname)(),null}},24868:(e,t,s)=>{"use strict";s.d(t,{A:()=>ei});var r=s(60687),a=s(43210),i=s(85814),n=s.n(i),l=s(6510),c=s(30922),o=s(52238),d=s(51426),u=s(27010),h=s(724),m=s(61245),g=s(34944),x=s(50549),p=s(16189),f=s(11016),y=s(79481),b=s(81836),v=s(45994),j=s(97450),w=s(86297),N=s(10799);function S({isOpen:e,onClose:t}){let[s,i]=(0,a.useState)(""),[n,l]=(0,a.useState)([]),[o,d]=(0,a.useState)(!1),[u,h]=(0,a.useState)(0),g=(0,p.useRouter)(),x=(0,a.useRef)(null);v.A,j.A,w.A,N.A,m.A;let f=e=>{g.push(e.href),t(),i("")};return e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-sm z-50",onClick:t}),(0,r.jsx)("div",{className:"fixed top-20 left-1/2 transform -translate-x-1/2 w-full max-w-2xl mx-4 z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center px-4 py-3 border-b border-gray-200",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,r.jsx)("input",{ref:x,type:"text",placeholder:"Search configurations, API keys, pages...",value:s,onChange:e=>i(e.target.value),onKeyDown:e=>{switch(e.key){case"ArrowDown":e.preventDefault(),h(e=>Math.min(e+1,n.length-1));break;case"ArrowUp":e.preventDefault(),h(e=>Math.max(e-1,0));break;case"Enter":e.preventDefault(),n[u]&&f(n[u]);break;case"Escape":e.preventDefault(),t()}},className:"flex-1 text-gray-900 placeholder-gray-500 bg-transparent border-none outline-none text-sm"}),(0,r.jsx)("button",{onClick:t,className:"p-1 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(b.A,{className:"h-4 w-4 text-gray-400"})})]}),(0,r.jsx)("div",{className:"max-h-96 overflow-y-auto",children:o?(0,r.jsxs)("div",{className:"px-4 py-8 text-center text-gray-500",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500 mx-auto"}),(0,r.jsx)("p",{className:"mt-2 text-sm",children:"Searching..."})]}):n.length>0?(0,r.jsx)("div",{className:"py-2",children:n.map((e,t)=>{let s=e.icon;return(0,r.jsxs)("button",{onClick:()=>f(e),className:`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center space-x-3 ${t===u?"bg-orange-50 border-r-2 border-orange-500":""}`,children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(s,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),(0,r.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.subtitle})]}),e.metadata&&(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs text-gray-400",children:e.metadata})})]},e.id)})}):s.trim()?(0,r.jsxs)("div",{className:"px-4 py-8 text-center text-gray-500",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 mx-auto text-gray-300 mb-2"}),(0,r.jsxs)("p",{className:"text-sm",children:['No results found for "',s,'"']}),(0,r.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:'Try searching for pages like "dashboard", "playground", or "settings"'})]}):(0,r.jsxs)("div",{className:"px-4 py-8 text-center text-gray-500",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 mx-auto text-gray-300 mb-2"}),(0,r.jsx)("p",{className:"text-sm",children:"Start typing to search..."}),(0,r.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Search across configurations, API keys, and pages"})]})}),(0,r.jsxs)("div",{className:"px-4 py-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("span",{children:"↑↓ Navigate"}),(0,r.jsx)("span",{children:"↵ Select"}),(0,r.jsx)("span",{children:"Esc Close"})]}),(0,r.jsxs)("span",{children:[n.length," results"]})]})]})})]}):null}function k(){let{isCollapsed:e,isHovered:t,toggleSidebar:i}=(0,x.c)(),b=(0,p.usePathname)(),{user:v,subscriptionStatus:j}=(0,f.R)(),[w,N]=(0,a.useState)(!1),[k,C]=(0,a.useState)(!1),[A,I]=(0,a.useState)(!1),P=(0,y.createSupabaseBrowserClient)(),R=v?.user_metadata?.first_name||v?.user_metadata?.full_name?.split(" ")[0]||"User",E=R.charAt(0).toUpperCase()+(v?.user_metadata?.last_name?.charAt(0)?.toUpperCase()||R.charAt(1)?.toUpperCase()||"U"),T=(e=>{switch(e){case"/dashboard":return{title:"Dashboard",subtitle:"Overview & analytics"};case"/playground":return{title:"Playground",subtitle:"Test your models"};case"/my-models":return{title:"My Models",subtitle:"API key management"};case"/routing-setup":return{title:"Routing Setup",subtitle:"Configure routing"};case"/tools":return{title:"Tool Connections",subtitle:"Manage connected tools"};case"/logs":return{title:"Logs",subtitle:"Request history"};case"/training":return{title:"Prompt Engineering",subtitle:"Custom prompts"};case"/analytics":return{title:"Analytics",subtitle:"Advanced insights"};case"/add-keys":return{title:"Add Keys",subtitle:"API key setup"};default:return{title:"Dashboard",subtitle:"Overview"}}})(b),D=j?.tier==="free"?"Free Plan":j?.tier==="starter"?"Starter Plan":j?.tier==="professional"?"Professional Plan":j?.tier==="enterprise"?"Enterprise Plan":"Free Plan",$=async()=>{try{let{clearAllUserCache:e}=await s.e(8979).then(s.bind(s,18979));await e(),await P.auth.signOut(),window.location.href="/auth/signin"}catch(e){try{localStorage.clear(),sessionStorage.clear()}catch(e){}window.location.href="/auth/signin"}};return(0,r.jsxs)("nav",{className:"header border-b border-gray-800/50 bg-[#040716] backdrop-blur-sm w-full",children:[(0,r.jsx)("div",{className:`px-4 sm:px-6 lg:px-8 ${A&&(!e||t)?"max-w-7xl mx-auto":A?"max-w-none":"max-w-7xl mx-auto"}`,children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("button",{onClick:i,className:"lg:hidden p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200",title:"Toggle sidebar",children:(0,r.jsx)(l.A,{className:"h-6 w-6 text-gray-400"})}),(0,r.jsx)("div",{className:"lg:hidden",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"RouKey"})}),(0,r.jsxs)("div",{className:"hidden lg:flex items-center space-x-2 text-sm text-gray-400",children:[(0,r.jsx)("span",{children:T.title}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)("span",{className:"text-white font-medium",children:T.subtitle})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,r.jsxs)("div",{className:"hidden xl:block relative",children:[(0,r.jsxs)("button",{onClick:()=>C(!0),className:"w-64 pl-10 pr-4 py-2.5 text-sm bg-gray-800/50 border border-gray-700 rounded-lg text-gray-400 hover:text-gray-200 hover:border-gray-600 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200 text-left flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Search..."}),(0,r.jsx)("kbd",{className:"hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium text-gray-500 bg-gray-700 border border-gray-600 rounded",children:"⌘K"})]}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(c.A,{className:"h-4 w-4 text-gray-500"})})]}),(0,r.jsx)("button",{onClick:()=>C(!0),className:"xl:hidden p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200",children:(0,r.jsx)(c.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsxs)("button",{className:"p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 relative",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-gray-400"}),(0,r.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full"})]}),(0,r.jsxs)("div",{className:"hidden sm:block relative",children:[(0,r.jsxs)("button",{onClick:()=>N(!w),className:"p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 flex items-center space-x-1",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 text-gray-400"}),(0,r.jsx)(u.A,{className:`h-3 w-3 text-gray-400 transition-transform duration-200 ${w?"rotate-180":""}`})]}),w&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>N(!1)}),(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 py-1 z-20",children:[(0,r.jsxs)(n(),{href:"/settings",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors duration-200",onClick:()=>N(!1),children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mr-3 text-gray-400"}),"Account Settings"]}),(0,r.jsxs)(n(),{href:"/billing",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors duration-200",onClick:()=>N(!1),children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-3 text-gray-400"}),"Billing & Plans"]}),(0,r.jsxs)(n(),{href:"/docs",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors duration-200",onClick:()=>N(!1),children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-3 text-gray-400"}),"Documentation"]}),(0,r.jsx)("hr",{className:"my-1 border-gray-700"}),(0,r.jsxs)("button",{onClick:$,className:"flex items-center w-full px-4 py-2 text-sm text-red-400 hover:bg-red-900/20 transition-colors duration-200",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-3 text-red-400"}),"Sign Out"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 cursor-pointer",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-semibold text-sm",children:E})}),(0,r.jsxs)("div",{className:"hidden md:block",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-white",children:R}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:D})]})]})]})]})}),(0,r.jsx)(S,{isOpen:k,onClose:()=>C(!1)})]})}s(14566);var C=s(30474),A=s(20816),I=s(45428),P=s(71155),R=s(56439),E=s(21134),T=s(13530),D=s(86870),$=s(54293),M=s(78129),L=s(36721);class O{setRouter(e){this.router=e}async prefetchRoute(e,t={}){if(!this.router)return;let{priority:s="low",delay:r=0,condition:a}=t;if(a&&!a())return;let i=this.prefetchedRoutes.get(e);i&&i.prefetched&&Date.now()-i.timestamp<3e5||(this.prefetchQueue.push({route:e,options:t}),this.prefetchedRoutes.set(e,{route:e,timestamp:Date.now(),prefetched:!1}),this.processQueue())}async processQueue(){if(!this.isProcessing&&0!==this.prefetchQueue.length){for(this.isProcessing=!0,this.prefetchQueue.sort((e,t)=>{let s={high:0,low:1};return s[e.options.priority||"low"]-s[t.options.priority||"low"]});this.prefetchQueue.length>0;){let{route:e,options:t}=this.prefetchQueue.shift();try{if(t.delay&&t.delay>0&&await new Promise(e=>setTimeout(e,t.delay)),t.condition&&!t.condition())continue;await this.router.prefetch(e),await this.prefetchBundles(e);let s=this.prefetchedRoutes.get(e);s&&(s.prefetched=!0,s.timestamp=Date.now()),await new Promise(e=>setTimeout(e,50))}catch(e){}}this.isProcessing=!1}}async prefetchBundles(e){try{["https://fonts.googleapis.com","https://fonts.gstatic.com"].forEach(e=>{if(document.querySelector(`link[href="${e}"][rel="preconnect"]`))return;let t=document.createElement("link");t.rel="preconnect",t.href=e,t.crossOrigin="anonymous",document.head.appendChild(t)})}catch(e){}}cleanup(){let e=Date.now();for(let[t,s]of this.prefetchedRoutes.entries())e-s.timestamp>6e5&&this.prefetchedRoutes.delete(t)}constructor(){this.prefetchedRoutes=new Map,this.router=null,this.prefetchQueue=[],this.isProcessing=!1}}let U=new O,_=()=>{let e=(0,p.useRouter)(),t=(0,a.useRef)();(0,a.useEffect)(()=>(U.setRouter(e),t.current=setInterval(()=>{U.cleanup()},3e5),()=>{t.current&&clearInterval(t.current)}),[e]);let s=(0,a.useCallback)((e,t)=>{U.prefetchRoute(e,t)},[]),r=(0,a.useCallback)((e,t=100)=>({onMouseEnter:()=>{s(e,{priority:"high",delay:t})}}),[s]),i=(0,a.useCallback)((e,t)=>{if(!t)return;let r=new IntersectionObserver(t=>{t.forEach(t=>{t.isIntersecting&&(s(e,{priority:"low",delay:200}),r.disconnect())})},{threshold:.1});return r.observe(t),()=>r.disconnect()},[s]);return{prefetchRoute:s,prefetchOnHover:r,prefetchOnVisible:i}},z=()=>{let{prefetchRoute:e}=_(),t=(0,a.useRef)({lastActivity:Date.now(),isIdle:!1,mouseMovements:0});return(0,a.useEffect)(()=>{let e,s,r=()=>{t.current.lastActivity=Date.now(),t.current.isIdle=!1,clearTimeout(e),e=setTimeout(()=>{t.current.isIdle=!0},3e3)},a=()=>{t.current.mouseMovements++,r()},i=()=>{r()};return document.addEventListener("mousemove",a),document.addEventListener("keypress",i),document.addEventListener("click",r),document.addEventListener("scroll",r),s=setInterval(()=>{t.current.mouseMovements=0},1e4),()=>{document.removeEventListener("mousemove",a),document.removeEventListener("keypress",i),document.removeEventListener("click",r),document.removeEventListener("scroll",r),clearTimeout(e),clearInterval(s)}},[]),{prefetchWhenIdle:(0,a.useCallback)(s=>{let r=setInterval(()=>{t.current.isIdle&&t.current.mouseMovements<5&&s.forEach((s,r)=>{e(s,{priority:"low",delay:500*r,condition:()=>t.current.isIdle})})},2e3);return()=>clearInterval(r)},[e]),isUserIdle:()=>t.current.isIdle}};var W=s(48427);let F="rokey_navigation_patterns";function B(){let[e,t]=(0,a.useState)(null),[s,r]=(0,a.useState)([]),i=(0,p.usePathname)(),{prefetchRoute:n}=_(),l=(0,a.useRef)(Date.now());function c(){let e=new Date().getHours();return e>=6&&e<12?"morning":e>=12&&e<17?"afternoon":e>=17&&e<21?"evening":"night"}(0,a.useRef)(Date.now());let o=(0,a.useCallback)((s,r)=>{if(!e||s===r)return;let a=Date.now(),i=a-l.current;l.current=a,t(e=>{if(!e)return null;let t=[...e.patterns],n=t.find(e=>e.from===s&&e.to===r);n?(n.frequency+=1,n.lastUsed=a,n.avgTimeSpent=(n.avgTimeSpent+i)/2):t.push({from:s,to:r,frequency:1,lastUsed:a,avgTimeSpent:i});let l=new Map;t.forEach(e=>{l.set(e.to,(l.get(e.to)||0)+e.frequency)});let o=Array.from(l.entries()).sort((e,t)=>t[1]-e[1]).slice(0,5).map(([e])=>e),d={...e,patterns:t,totalNavigations:e.totalNavigations+1,preferredRoutes:o,timeOfDay:c()};try{localStorage.setItem(F,JSON.stringify(d))}catch(e){}return d})},[e]);(0,a.useCallback)(()=>e&&i?(c(),[...new Set([...e.patterns.filter(e=>e.from===i&&e.frequency>=2).sort((e,t)=>{let s=e.frequency*(1+(Date.now()-e.lastUsed)/864e5);return t.frequency*(1+(Date.now()-t.lastUsed)/864e5)-s}).slice(0,3).map(e=>e.to),...e.patterns.filter(e=>2>=Math.abs(new Date(e.lastUsed).getHours()-new Date().getHours())).sort((e,t)=>t.frequency-e.frequency).slice(0,2).map(e=>e.to)])].slice(0,4)):[],[e,i]),(0,a.useRef)(i);let d=(0,a.useCallback)(()=>{if(!e)return[];let t=[];e.preferredRoutes.length>0&&t.push(`Most visited: ${e.preferredRoutes[0]}`),e.totalNavigations>10&&t.push(`${e.totalNavigations} total navigations this session`);let s=e.patterns.filter(e=>1>=Math.abs(new Date(e.lastUsed).getHours()-new Date().getHours()));return s.length>0&&t.push(`${s.length} patterns match current time`),t},[e]),u=(0,a.useCallback)(()=>{localStorage.removeItem(F),t({patterns:[],sessionStartTime:Date.now(),totalNavigations:0,preferredRoutes:[],timeOfDay:c()}),r([])},[]);return{predictions:s,userBehavior:e,insights:d(),trackNavigation:o,clearPatterns:u,isLearning:!!e?.totalNavigations&&e.totalNavigations>5}}let K=[{href:"/dashboard",label:"Dashboard",icon:A.A,iconSolid:P.A,description:"Overview & analytics"},{href:"/my-models",label:"My Models",icon:j.A,iconSolid:R.A,description:"API key management"},{href:"/playground",label:"Playground",icon:w.A,iconSolid:E.A,description:"Test your models"},{href:"/routing-setup",label:"Routing Setup",icon:N.A,iconSolid:T.A,description:"Configure routing"},{href:"/logs",label:"Logs",icon:m.A,iconSolid:D.A,description:"Request history"},{href:"/training",label:"Training",icon:I.A,iconSolid:$.A,description:"AI training & knowledge"},{href:"/analytics",label:"Analytics",icon:v.A,iconSolid:M.A,description:"Advanced insights"}];function G(){let e=(0,p.usePathname)(),{isCollapsed:t,isHovered:s,isHoverDisabled:i,setHovered:l}=(0,x.c)(),{navigateOptimistically:c}=(0,L.bu)()||{navigateOptimistically:()=>{}},{prefetchOnHover:o}=_(),{prefetchWhenIdle:d}=z(),{prefetchChatHistory:u}=(0,W.l2)(),{predictions:h,isLearning:m}=B(),g=function(){(0,p.usePathname)();let[e,t]=(0,a.useState)([]);return e}(),f=!t||s;return(0,r.jsxs)("aside",{className:`sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-[#030614] relative ${f?"w-64":"w-16"}`,onMouseEnter:()=>!i&&l(!0),onMouseLeave:()=>!i&&l(!1),children:[(0,r.jsx)("div",{className:"absolute top-0 right-0 h-full w-px bg-gradient-to-b from-gray-500/30 via-gray-400/40 to-gray-500/30"}),(0,r.jsx)("div",{className:"absolute top-0 right-0 h-full w-0.5 bg-gray-400/15 blur-sm"}),(0,r.jsx)("div",{className:"flex-1 relative overflow-hidden",children:(0,r.jsxs)("div",{className:`p-6 transition-all duration-200 ease-out ${f?"px-6":"px-3"}`,children:[(0,r.jsx)("div",{className:`mb-8 pt-4 transition-all duration-200 ease-out ${f?"":"text-center"}`,children:(0,r.jsxs)("div",{className:"relative overflow-hidden",children:[(0,r.jsx)("div",{className:`transition-all duration-200 ease-out ${f?"opacity-0 scale-75 -translate-y-2":"opacity-100 scale-100 translate-y-0"} ${f?"absolute":"relative"} w-8 h-8 bg-black rounded-lg flex items-center justify-center mx-auto p-0.5`,children:(0,r.jsx)(C.default,{src:"/RouKey_Logo_GLOW.png",alt:"RouKey",width:28,height:28,className:"object-contain"})}),(0,r.jsxs)("div",{className:`transition-all duration-200 ease-out ${f?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-75 translate-y-2"} ${f?"relative":"absolute top-0 left-0 w-full"}`,children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white tracking-tight whitespace-nowrap",children:"RouKey"}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1 whitespace-nowrap",children:"Smart LLM Router"})]})]})}),(0,r.jsx)("nav",{className:"space-y-2",children:K.map(t=>{let s=e===t.href||e.startsWith(t.href+"/"),a=s?t.iconSolid:t.icon,i=h.includes(t.href),l=g.find(e=>e.route===t.href),d="/playground"===t.href?{onMouseEnter:()=>{if("/playground"===t.href){o(t.href,50).onMouseEnter();let e=new URLSearchParams(window.location.search).get("config");e&&u(e)}}}:o(t.href,50);return(0,r.jsx)(n(),{href:t.href,onClick:e=>{e.preventDefault(),c(t.href)},className:`sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left ${s?"active":""} ${f?"":"collapsed"}`,title:f?void 0:t.label,...d,children:(0,r.jsxs)("div",{className:"relative flex items-center w-full overflow-hidden",children:[(0,r.jsxs)("div",{className:`relative flex items-center justify-center transition-all duration-200 ease-out ${f?"w-5 h-5 mr-3":"w-10 h-10 rounded-xl"} ${!f&&s?"bg-white shadow-sm":!f?"bg-transparent hover:bg-white/10":""}`,children:[(0,r.jsx)(a,{className:`transition-all duration-200 ease-out h-5 w-5 ${s?"text-orange-500":"text-white"}`}),i&&!s&&(0,r.jsx)("div",{className:`absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out ${f?"-top-1 -right-1 w-2 h-2":"-top-1 -right-1 w-3 h-3"}`,title:"Predicted next destination"})]}),(0,r.jsxs)("div",{className:`flex-1 transition-all duration-200 ease-out ${f?"opacity-100 translate-x-0 max-w-full":"opacity-0 translate-x-4 max-w-0"}`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between whitespace-nowrap",children:[(0,r.jsx)("div",{className:"font-medium text-sm",children:t.label}),l&&!s&&(0,r.jsx)("span",{className:`text-xs px-1.5 py-0.5 rounded-full ml-2 ${"high"===l.priority?"bg-blue-500/20 text-blue-300":"bg-gray-500/20 text-gray-300"}`,children:"high"===l.priority?"!":"\xb7"})]}),(0,r.jsx)("div",{className:`text-xs transition-colors duration-200 whitespace-nowrap ${s?"text-orange-400":"text-gray-400"}`,children:l?l.reason:t.description})]})]})},t.href)})})]})})]})}var H=s(35291);let q={"/dashboard":{title:"Dashboard",subtitle:"Loading overview & analytics...",icon:A.A,color:"text-blue-500",bgColor:"bg-blue-50"},"/my-models":{title:"My Models",subtitle:"Loading API key management...",icon:j.A,color:"text-green-500",bgColor:"bg-green-50"},"/playground":{title:"Playground",subtitle:"Loading model testing environment...",icon:w.A,color:"text-orange-500",bgColor:"bg-orange-50"},"/routing-setup":{title:"Routing Setup",subtitle:"Loading routing configuration...",icon:N.A,color:"text-purple-500",bgColor:"bg-purple-50"},"/logs":{title:"Logs",subtitle:"Loading request history...",icon:m.A,color:"text-gray-500",bgColor:"bg-gray-50"},"/training":{title:"Prompt Engineering",subtitle:"Loading custom prompts...",icon:I.A,color:"text-indigo-500",bgColor:"bg-indigo-50"},"/analytics":{title:"Analytics",subtitle:"Loading advanced insights...",icon:v.A,color:"text-pink-500",bgColor:"bg-pink-50"}};function Q({targetRoute:e}){let{clearNavigation:t}=(0,L.bu)()||{clearNavigation:()=>{}};if(!(e?q[e]:null))return(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,r.jsx)(w.A,{className:"w-8 h-8 text-gray-400"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Loading..."}),(0,r.jsx)("p",{className:"text-gray-500",children:"Please wait while we load the page"})]})});let s=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-1 space-y-3",children:Array.from({length:5}).map((e,t)=>(0,r.jsx)("div",{className:"h-16 bg-gray-200 rounded-lg animate-pulse"},t))}),(0,r.jsx)("div",{className:"lg:col-span-3",children:(0,r.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg animate-pulse"})})]})]}),a=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-16"})]})]}),(0,r.jsx)("div",{className:"space-y-3",children:Array.from({length:8}).map((e,t)=>(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"},t))})]});return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("button",{onClick:t,className:"absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",title:"Cancel loading",children:(0,r.jsx)(b.A,{className:"w-5 h-5"})}),(()=>{switch(e){case"/dashboard":default:return(0,r.jsx)(H.O2,{});case"/my-models":return(0,r.jsx)(H.MyModelsSkeleton,{});case"/playground":return(0,r.jsx)(s,{});case"/routing-setup":return(0,r.jsx)(H.RoutingSetupSkeleton,{});case"/logs":return(0,r.jsx)(a,{});case"/training":return(0,r.jsx)(H.vD,{});case"/analytics":return(0,r.jsx)(H.AnalyticsSkeleton,{})}})()]})}let J=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"})]},e))}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})]})]}),V=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"bg-white p-8 rounded-2xl border-2",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3 mx-auto"})]}),(0,r.jsx)("div",{className:"space-y-3 mb-8",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]},e))})]}),Y=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,r.jsx)("div",{className:"h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsx)("div",{className:"py-20",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[1,2,3,4,5,6].map(e=>(0,r.jsx)("div",{className:"bg-white rounded-2xl p-8 border",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-xl"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mb-3"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-4"}),(0,r.jsx)("div",{className:"space-y-2",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"},e))})]})]})},e))})})]}),X=()=>(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]})]})}),Z=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded mt-4"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded mt-4"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})]}),ee=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg border",children:(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})})]});function et({targetRoute:e,children:t}){let s,[i,n]=(0,a.useState)(!0),[l,c]=(0,a.useState)(!1),o=(0,p.usePathname)();(0,a.useRef)();let{isPageCached:d}=(0,L.bu)()||{isPageCached:()=>!1};return o!==e&&i||o===e&&i&&!l?(0,r.jsx)("div",{className:"optimistic-loading-container",children:(s=e).startsWith("/dashboard")?(0,r.jsx)(J,{}):s.startsWith("/pricing")?(0,r.jsx)(V,{}):s.startsWith("/features")?(0,r.jsx)(Y,{}):s.startsWith("/auth/")?(0,r.jsx)(X,{}):s.startsWith("/playground")?(0,r.jsx)(Z,{}):(0,r.jsx)(ee,{})}):(0,r.jsx)("div",{className:`transition-opacity duration-300 ${l?"opacity-100":"opacity-0"}`,children:t})}let es={maxConcurrent:3,idleTimeout:2e3,hoverDelay:100,backgroundDelay:5e3};var er=s(34374);function ea({children:e}){let{isCollapsed:t,isHovered:s,collapseSidebar:i}=(0,x.c)(),{isNavigating:n,targetRoute:l,isPageCached:c}=(0,L.bu)()||{isNavigating:!1,targetRoute:null,isPageCached:()=>!1},{toasts:o,removeToast:d}=(0,er.dj)(),[u,h]=(0,a.useState)(!1),m=u?!t||s?256:64:0;return!function(e={}){let t=(0,p.usePathname)();(0,p.useRouter)();let{predictions:s,isLearning:r}=B(),{prefetchRoute:i}=_(),n={...es,...e},l=(0,a.useRef)([]),c=(0,a.useRef)(new Set),o=(0,a.useRef)(null),d=(0,a.useCallback)(()=>{let e={immediate:[],onIdle:[],onHover:[],background:[]};switch(t){case"/dashboard":e.immediate=["/playground"],e.onIdle=["/my-models","/logs"],e.background=["/routing-setup","/analytics"];break;case"/my-models":e.immediate=["/playground","/routing-setup"],e.onIdle=["/logs"],e.background=["/dashboard","/analytics"];break;case"/playground":e.immediate=["/logs"],e.onIdle=["/my-models"],e.background=["/dashboard","/training"];break;case"/logs":e.immediate=["/playground"],e.onIdle=["/analytics"],e.background=["/my-models","/dashboard"];break;case"/routing-setup":e.immediate=["/playground"],e.onIdle=["/my-models"],e.background=["/logs","/dashboard"];break;default:e.onIdle=["/dashboard","/playground"]}return r&&s.length>0&&(s.slice(0,2).forEach(t=>{e.immediate.includes(t)||e.immediate.unshift(t)}),s.slice(2).forEach(t=>{e.onIdle.includes(t)||e.onIdle.push(t)})),Object.keys(e).forEach(s=>{e[s]=e[s].filter(e=>e!==t)}),e},[t,s,r]),u=(0,a.useCallback)(async(e,t="medium")=>{if(c.current.has(e)||c.current.size>=n.maxConcurrent)return void l.current.push(e);c.current.add(e);try{await i(e,{priority:"medium"===t?"low":t,delay:"high"===t?0:"medium"===t?100:300})}catch(e){}finally{if(c.current.delete(e),l.current.length>0){let e=l.current.shift();e&&setTimeout(()=>u(e,"low"),100)}}},[i,n.maxConcurrent]);(0,a.useCallback)(e=>({onMouseEnter:()=>{setTimeout(()=>{u(e,"high")},n.hoverDelay)}}),[u,n.hoverDelay]),(0,a.useCallback)(()=>({activePreloads:Array.from(c.current),queuedPreloads:[...l.current],strategy:d()}),[d]),(0,a.useCallback)(()=>{c.current.clear(),l.current=[],o.current&&(cancelIdleCallback(o.current),o.current=null)},[]),c.current.size}({maxConcurrent:2,idleTimeout:1500,backgroundDelay:3e3}),(0,r.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,r.jsx)("div",{className:"hidden lg:block fixed left-0 top-0 h-full z-40",children:(0,r.jsx)(G,{})}),(0,r.jsxs)("div",{className:`lg:hidden fixed inset-0 z-50 ${t?"pointer-events-none":""}`,children:[(0,r.jsx)("div",{onClick:i,className:`absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer ${t?"opacity-0":"opacity-50"}`}),(0,r.jsx)("div",{className:`absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out ${t?"-translate-x-full":"translate-x-0"}`,children:(0,r.jsx)(G,{})})]}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col min-w-0 transition-all duration-200 ease-out",style:{marginLeft:`${m}px`},children:[(0,r.jsx)("div",{className:"fixed top-0 right-0 z-30 transition-all duration-200 ease-out",style:{left:`${m}px`},children:(0,r.jsx)(k,{})}),(0,r.jsx)("main",{className:"flex-1 overflow-y-auto content-area mt-16 bg-[#040716] w-full",children:(0,r.jsx)("div",{className:"w-full h-full min-h-screen",children:(0,r.jsx)("div",{className:"page-transition",children:n&&l?(0,r.jsx)(et,{targetRoute:l,children:e}):e})})})]}),(0,r.jsx)(er.N9,{toasts:o,onRemove:d})]})}function ei({children:e}){return(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)(Q,{targetRoute:null}),children:(0,r.jsx)(ea,{children:e})})}},34374:(e,t,s)=>{"use strict";s.d(t,{N9:()=>u,dj:()=>h});var r=s(60687),a=s(43210),i=s(58089),n=s(81521),l=s(59168),c=s(99127),o=s(81836);let d=({toast:e,onRemove:t})=>{let[s,d]=(0,a.useState)(!1),[u,h]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=setTimeout(()=>d(!0),10);return()=>clearTimeout(e)},[]),(0,a.useEffect)(()=>{if(e.duration&&e.duration>0){let t=setTimeout(()=>{m()},e.duration);return()=>clearTimeout(t)}},[e.duration]);let m=()=>{h(!0),setTimeout(()=>{t(e.id)},300)};return(0,r.jsx)("div",{className:`
        transform transition-all duration-300 ease-in-out
        ${s&&!u?"translate-x-0 opacity-100":"translate-x-full opacity-0"}
        ${(()=>{let t="glass rounded-xl p-4 shadow-lg border";switch(e.type){case"success":return`${t} border-green-500/20 bg-green-500/10`;case"error":return`${t} border-red-500/20 bg-red-500/10`;case"warning":return`${t} border-yellow-500/20 bg-yellow-500/10`;case"info":return`${t} border-blue-500/20 bg-blue-500/10`}})()}
      `,children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(()=>{switch(e.type){case"success":return(0,r.jsx)(i.A,{className:"h-5 w-5 text-green-400"});case"error":return(0,r.jsx)(n.A,{className:"h-5 w-5 text-red-400"});case"warning":return(0,r.jsx)(l.A,{className:"h-5 w-5 text-yellow-400"});case"info":return(0,r.jsx)(c.A,{className:"h-5 w-5 text-blue-400"})}})()}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-white",children:e.title}),e.message&&(0,r.jsx)("p",{className:"text-sm text-gray-300 mt-1",children:e.message})]}),(0,r.jsx)("button",{onClick:m,className:"flex-shrink-0 p-1 rounded-lg hover:bg-white/10 transition-colors duration-200",children:(0,r.jsx)(o.A,{className:"h-4 w-4 text-gray-400 hover:text-white"})})]})})},u=({toasts:e,onRemove:t})=>(0,r.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-3 max-w-sm w-full",children:e.map(e=>(0,r.jsx)(d,{toast:e,onRemove:t},e.id))}),h=()=>{let[e,t]=(0,a.useState)([]),s=e=>{let s=Math.random().toString(36).substr(2,9),r={...e,id:s,duration:e.duration??5e3};return t(e=>[...e,r]),s};return{toasts:e,addToast:s,removeToast:e=>{t(t=>t.filter(t=>t.id!==e))},success:(e,t,r)=>s({type:"success",title:e,message:t,duration:r}),error:(e,t,r)=>s({type:"error",title:e,message:t,duration:r}),warning:(e,t,r)=>s({type:"warning",title:e,message:t,duration:r}),info:(e,t,r)=>s({type:"info",title:e,message:t,duration:r})}}},34701:(e,t,s)=>{"use strict";s.d(t,{default:()=>a}),s(43210);var r=s(16189);function a(){return(0,r.useRouter)(),null}},35291:(e,t,s)=>{"use strict";s.d(t,{AnalyticsSkeleton:()=>u,ConfigSelectorSkeleton:()=>n,MessageSkeleton:()=>i,MyModelsSkeleton:()=>c,O2:()=>l,RoutingSetupSkeleton:()=>o,vD:()=>d});var r=s(60687);s(76180),s(43210);let a=({className:e="",variant:t="text",width:s="100%",height:a="1rem",lines:i=1})=>{let n="animate-pulse bg-gray-200 rounded",l=()=>{switch(t){case"circular":return"rounded-full";case"rectangular":return"rounded-lg";default:return"rounded"}},c={width:"number"==typeof s?`${s}px`:s,height:"number"==typeof a?`${a}px`:a};return i>1?(0,r.jsx)("div",{className:`space-y-2 ${e}`,children:Array.from({length:i}).map((e,t)=>(0,r.jsx)("div",{className:`${n} ${l()}`,style:{...c,width:t===i-1?"75%":c.width}},t))}):(0,r.jsx)("div",{className:`${n} ${l()} ${e}`,style:c})},i=()=>(0,r.jsx)("div",{className:"space-y-6 py-8",children:Array.from({length:3}).map((e,t)=>(0,r.jsx)("div",{className:`flex ${t%2==0?"justify-end":"justify-start"}`,children:(0,r.jsx)("div",{className:`max-w-3xl p-4 rounded-2xl ${t%2==0?"bg-orange-50":"bg-white border border-gray-200"}`,children:(0,r.jsx)(a,{lines:3,height:"1rem"})})},t))}),n=()=>(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(a,{variant:"circular",width:32,height:32}),(0,r.jsx)(a,{width:"8rem",height:"1.5rem"})]}),l=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(a,{height:"2.5rem",width:"12rem",className:"mb-2"}),(0,r.jsx)(a,{height:"1.25rem",width:"20rem"})]}),(0,r.jsx)(a,{variant:"rectangular",height:"2.5rem",width:"8rem"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:4}).map((e,t)=>(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)(a,{variant:"circular",width:40,height:40}),(0,r.jsx)(a,{height:"1rem",width:"3rem"})]}),(0,r.jsx)(a,{height:"2rem",width:"4rem",className:"mb-2"}),(0,r.jsx)(a,{height:"0.875rem",width:"6rem"})]},t))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(a,{height:"1.5rem",width:"8rem",className:"mb-4"}),(0,r.jsx)(a,{variant:"rectangular",height:"20rem"})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(a,{height:"1.5rem",width:"10rem",className:"mb-4"}),(0,r.jsx)(a,{variant:"rectangular",height:"20rem"})]})]})]}),c=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(a,{height:"2.5rem",width:"10rem",className:"mb-2"}),(0,r.jsx)(a,{height:"1.25rem",width:"18rem"})]}),(0,r.jsx)(a,{variant:"rectangular",height:"2.5rem",width:"10rem"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,t)=>(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(a,{height:"1.5rem",width:"8rem",className:"mb-2"}),(0,r.jsx)(a,{height:"1rem",width:"12rem"})]}),(0,r.jsx)(a,{variant:"circular",width:32,height:32})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(a,{height:"0.875rem",width:"4rem"}),(0,r.jsx)(a,{height:"0.875rem",width:"2rem"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(a,{height:"0.875rem",width:"5rem"}),(0,r.jsx)(a,{height:"0.875rem",width:"3rem"})]})]})]},t))})]}),o=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(a,{height:"3rem",width:"16rem",className:"mx-auto mb-4"}),(0,r.jsx)(a,{height:"1.25rem",width:"24rem",className:"mx-auto"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:Array.from({length:4}).map((e,t)=>(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)(a,{variant:"circular",width:48,height:48,className:"mr-4"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(a,{height:"1.5rem",width:"10rem",className:"mb-2"}),(0,r.jsx)(a,{height:"1rem",width:"8rem"})]})]}),(0,r.jsx)(a,{lines:3,height:"0.875rem"})]},t))})]}),d=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(a,{height:"2.5rem",width:"8rem",className:"mb-2"}),(0,r.jsx)(a,{height:"1.25rem",width:"16rem"})]}),(0,r.jsx)(a,{variant:"rectangular",height:"2.5rem",width:"12rem"})]}),(0,r.jsx)("div",{className:"card p-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(a,{variant:"circular",width:64,height:64,className:"mx-auto mb-4"}),(0,r.jsx)(a,{height:"1.5rem",width:"12rem",className:"mx-auto mb-2"}),(0,r.jsx)(a,{height:"1rem",width:"20rem",className:"mx-auto"})]})}),(0,r.jsxs)("div",{className:"card",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,r.jsx)(a,{height:"1.5rem",width:"10rem"})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:Array.from({length:3}).map((e,t)=>(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(a,{height:"1.25rem",width:"12rem",className:"mb-2"}),(0,r.jsx)(a,{height:"1rem",width:"8rem"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(a,{height:"1.5rem",width:"4rem"}),(0,r.jsx)(a,{variant:"circular",width:32,height:32})]})]})},t))})]})]}),u=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(a,{height:"2.5rem",width:"9rem",className:"mb-2"}),(0,r.jsx)(a,{height:"1.25rem",width:"18rem"})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(a,{variant:"rectangular",height:"2.5rem",width:"8rem"}),(0,r.jsx)(a,{variant:"rectangular",height:"2.5rem",width:"6rem"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,t)=>(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(a,{height:"1.25rem",width:"6rem",className:"mb-4"}),(0,r.jsx)(a,{height:"2.5rem",width:"5rem",className:"mb-2"}),(0,r.jsx)(a,{height:"1rem",width:"8rem"})]},t))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(a,{height:"1.5rem",width:"12rem",className:"mb-6"}),(0,r.jsx)(a,{variant:"rectangular",height:"24rem"})]}),(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)(a,{height:"1.5rem",width:"10rem",className:"mb-6"}),(0,r.jsx)(a,{variant:"rectangular",height:"24rem"})]})]})]})},35760:()=>{},36721:(e,t,s)=>{"use strict";s.d(t,{bu:()=>c,i9:()=>l});var r=s(60687),a=s(43210),i=s(16189);let n=(0,a.createContext)(void 0);function l({children:e}){let[t,s]=(0,a.useState)(!1),[l,c]=(0,a.useState)(null),[o,d]=(0,a.useState)([]),[u,h]=(0,a.useState)(new Set),[m,g]=(0,a.useState)(!1),x=(0,i.usePathname)(),p=(0,i.useRouter)(),f=(0,a.useRef)(null),y=(0,a.useRef)([]),b=(0,a.useRef)(null),v=(0,a.useRef)(0),j=(0,a.useRef)({}),w=(0,a.useRef)({}),N=(0,a.useCallback)(e=>{},[m]),S=(0,a.useCallback)(e=>u.has(e),[u]),k=(0,a.useCallback)(()=>{if(0===y.current.length)return;let e=y.current[y.current.length-1];y.current=[e];let{route:t,id:r}=e;N(`🚀 [OPTIMISTIC NAV] Processing navigation to: ${t} (id: ${r})`),f.current&&(clearTimeout(f.current),f.current=null),b.current=r;let a=S(t);a&&(N(`⚡ [OPTIMISTIC NAV] Using cached navigation for: ${t}`),setTimeout(()=>{b.current===r&&s(!1)},100));try{p.push(t)}catch(e){N(`❌ [OPTIMISTIC NAV] Router.push failed for: ${t}, using fallback`),window.location.href=t;return}f.current=setTimeout(()=>{if(N(`⚠️ [OPTIMISTIC NAV] Timeout reached for: ${t} (id: ${r}), current path: ${x}`),b.current===r){N(`🔄 [OPTIMISTIC NAV] Attempting fallback navigation to: ${t}`);try{window.location.href=t}catch(e){N(`❌ [OPTIMISTIC NAV] Fallback navigation failed: ${e}`)}s(!1),c(null),b.current=null}f.current=null},a?800:3e3)},[p,x,S,N]),C=(0,a.useCallback)(e=>{if(x===e||!m)return;let t=Date.now();if(t-v.current<100&&l===e)return void N(`🔄 [OPTIMISTIC NAV] Debouncing duplicate navigation to: ${e}`);if(v.current=t,j.current[e]||(j.current[e]=0),j.current[e]++,w.current[e]&&clearTimeout(w.current[e]),w.current[e]=setTimeout(()=>{j.current[e]=0},2e3),j.current[e]>=3){N(`🚨 [OPTIMISTIC NAV] Force navigation escape hatch for: ${e}`),j.current[e]=0;return}f.current&&(clearTimeout(f.current),f.current=null),s(!0),c(e);let r=`nav_${t}_${Math.random().toString(36).substr(2,9)}`;y.current=[{route:e,timestamp:t,id:r}],k()},[x,l,k,N,m]),A=(0,a.useCallback)(()=>{f.current&&(clearTimeout(f.current),f.current=null),s(!1),c(null),b.current=null,y.current=[]},[]);return(0,r.jsx)(n.Provider,{value:{isNavigating:t,targetRoute:l,navigateOptimistically:C,clearNavigation:A,isPageCached:S,navigationHistory:o},children:e})}function c(){return(0,a.useContext)(n)||null}},39727:()=>{},44366:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ConditionalLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\ConditionalLayout.tsx","default")},47990:()=>{},48427:(e,t,s)=>{"use strict";s.d(t,{l2:()=>o,mx:()=>c});var r=s(43210);let a=new Map,i={hits:0,misses:0},n=new Set,l=!1,c=({configId:e,enablePrefetch:t=!0,cacheTimeout:s=3e5,staleTimeout:c=3e4})=>{let[o,d]=(0,r.useState)([]),[u,h]=(0,r.useState)(!1),[m,g]=(0,r.useState)(!1),[x,p]=(0,r.useState)(null),f=(0,r.useRef)(null),y=(0,r.useRef)(null),b=(0,r.useCallback)(async(e,r=!1,l=!1)=>{let o=a.get(e),d=Date.now();if(!r&&o&&d-o.timestamp<s)return i.hits++,d-o.timestamp>c&&!o.isStale&&(o.isStale=!0,t&&(n.add(e),v())),o.data;i.misses++,f.current&&f.current.abort(),f.current=new AbortController;try{let t=e.startsWith("workflow_"),s=t?e.replace("workflow_",""):e,r=`/api/chat/conversations?${t?"workflow_id":"custom_api_config_id"}=${s}`,i=await fetch(r,{signal:f.current.signal,headers:{"Cache-Control":"no-cache","X-Requested-With":"XMLHttpRequest"}});if(!i.ok)throw Error(`Failed to fetch chat history: ${i.status} ${i.statusText}`);let n=await i.json();return a.set(e,{data:n,timestamp:d,isStale:!1}),n}catch(e){if("AbortError"===e.name)throw e;if(o&&o.data.length>0)return o.data;throw e}},[s,c,t]),v=(0,r.useCallback)(async()=>{if(!l&&0!==n.size){l=!0;try{let e=Array.from(n);for(let t of(n.clear(),e))try{await b(t,!0,!0),await new Promise(e=>setTimeout(e,100))}catch(e){}}finally{l=!1}}},[b]),j=(0,r.useCallback)(async(t=!1)=>{if(!e)return;let s=a.get(e);!t&&s&&s.data.length>0&&(d(s.data),g(s.isStale),p(null)),h(!0),y.current=e;try{let s=await b(e,t);y.current===e&&(d(s),g(!1),p(null))}catch(t){"AbortError"!==t.name&&y.current===e&&p(`Failed to load chat history: ${t.message}`)}finally{y.current===e&&h(!1)}},[e,b]),w=(0,r.useCallback)(async e=>{t&&(n.add(e),v())},[t,v]),N=(0,r.useCallback)(e=>{e?a.delete(e):a.clear()},[]),S=(0,r.useCallback)(()=>({size:a.size,hits:i.hits,misses:i.misses}),[]);return(0,r.useEffect)(()=>{e?j():(d([]),h(!1),p(null),g(!1))},[e,j]),(0,r.useEffect)(()=>()=>{f.current&&f.current.abort()},[]),{chatHistory:o,isLoading:u,isStale:m,error:x,refetch:j,prefetch:w,invalidateCache:N,getCacheStats:S}},o=()=>{let e=(0,r.useRef)(new Set);return{prefetchChatHistory:(0,r.useCallback)(async t=>{e.current.has(t)||(e.current.add(t),n.add(t),setTimeout(()=>{n.size>0&&(async()=>{if(!l){l=!0;try{let e=Array.from(n);for(let t of(n.clear(),e)){try{let e=`/api/chat/conversations?custom_api_config_id=${t}`,s=await fetch(e,{headers:{"X-Prefetch":"true"}});if(s.ok){let e=await s.json();a.set(t,{data:e,timestamp:Date.now(),isStale:!1})}}catch(e){}await new Promise(e=>setTimeout(e,100))}}finally{l=!1}}})()},200))},[])}}},49831:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},50549:(e,t,s)=>{"use strict";s.d(t,{G:()=>n,c:()=>l});var r=s(60687),a=s(43210);let i=(0,a.createContext)(void 0);function n({children:e}){let[t,s]=(0,a.useState)(!0),[n,l]=(0,a.useState)(!1),[c,o]=(0,a.useState)(!1);return(0,r.jsx)(i.Provider,{value:{isCollapsed:t,isHovered:n,isHoverDisabled:c,toggleSidebar:()=>s(!t),collapseSidebar:()=>s(!0),expandSidebar:()=>s(!1),setHovered:e=>{c||l(e)},setHoverDisabled:e=>{o(e),e&&l(!1)}},children:e})}function l(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}},54413:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(37413),a=s(4536),i=s.n(a);function n(){return(0,r.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-6xl font-bold text-orange-500 mb-4",children:"404"}),(0,r.jsx)("h2",{className:"text-2xl font-semibold text-white mb-4",children:"Page Not Found"}),(0,r.jsx)("p",{className:"text-gray-400 mb-8",children:"The page you're looking for doesn't exist."}),(0,r.jsx)(i(),{href:"/",className:"bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors",children:"Go Home"})]})})}},61135:()=>{},62732:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentTitleUpdater.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\DocumentTitleUpdater.tsx","default")},69539:(e,t,s)=>{Promise.resolve().then(s.bind(s,27833)),Promise.resolve().then(s.t.bind(s,47429,23)),Promise.resolve().then(s.bind(s,44366)),Promise.resolve().then(s.bind(s,62732)),Promise.resolve().then(s.bind(s,91695)),Promise.resolve().then(s.bind(s,78676))},70058:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=70058,e.exports=t},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74089:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,85814,23))},78676:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\PerformanceTracker.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\PerformanceTracker.tsx","default")},79481:(e,t,s)=>{"use strict";s.d(t,{createSupabaseBrowserClient:()=>a});var r=s(79384);function a(){return(0,r.createBrowserClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8")}},80095:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},80171:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(43210),a=s(16189),i=s(86995);let n={static:{ttl:864e5,priority:"high",tags:["static"]},user:{ttl:12e4,priority:"high",tags:["user"]},system:{ttl:3e4,priority:"low",tags:["system"]},pricing:{ttl:36e5,priority:"medium",tags:["pricing"]}},l={LANDING_FEATURES:"landing:features",PRICING_TIERS:"pricing:tiers",PRICING_COMPARISON:"pricing:comparison",SYSTEM_STATUS:"system:status",SYSTEM_MODELS:"system:models",USER_CONFIGS:"user:configs",USER_ANALYTICS:"user:analytics"};class c{static getInstance(){return c.instance||(c.instance=new c),c.instance}trackNavigation(e,t){let s=`${e}->${t}`,r=this.userBehavior.get(s)||0;this.userBehavior.set(s,r+1),r>2&&this.schedulePrefetch(t)}schedulePrefetch(e){this.prefetchQueue.has(e)||(this.prefetchQueue.add(e),this.processPrefetchQueue())}async processPrefetchQueue(){if(!this.isProcessing&&0!==this.prefetchQueue.size){for(let e of(this.isProcessing=!0,this.prefetchQueue)){try{await this.prefetchRoute(e),this.prefetchQueue.delete(e)}catch(e){}await new Promise(e=>setTimeout(e,100))}this.isProcessing=!1}}async prefetchRoute(e){let t={"/dashboard":()=>this.prefetchDashboardData(),"/pricing":()=>this.prefetchPricingData(),"/auth/signup":()=>this.prefetchAuthData(),"/features":()=>this.prefetchFeaturesData()}[e];t&&await t()}async prefetchDashboardData(){let e=[this.cacheIfNotExists(l.USER_CONFIGS,"/api/custom-configs",n.user),this.cacheIfNotExists(l.USER_ANALYTICS,"/api/analytics",n.user),this.cacheIfNotExists(l.SYSTEM_STATUS,"/api/system-status",n.system)];await Promise.allSettled(e)}async prefetchPricingData(){let e=[this.cacheIfNotExists(l.PRICING_TIERS,"/api/pricing/tiers",n.pricing),this.cacheIfNotExists(l.PRICING_COMPARISON,"/api/pricing/comparison",n.pricing)];await Promise.allSettled(e)}async prefetchAuthData(){let e=[this.cacheIfNotExists(l.PRICING_TIERS,"/api/pricing/tiers",n.pricing)];await Promise.allSettled(e)}async prefetchFeaturesData(){let e=[this.cacheIfNotExists(l.LANDING_FEATURES,"/api/features",n.static),this.cacheIfNotExists(l.SYSTEM_MODELS,"/api/models",n.static)];await Promise.allSettled(e)}async cacheIfNotExists(e,t,s){let r=i.globalCache.get(e);if(r)return r;try{let r=await fetch(t);if(r.ok){let t=await r.json();return i.globalCache.set(e,t,s),t}}catch(e){}}constructor(){this.prefetchQueue=new Set,this.isProcessing=!1,this.userBehavior=new Map}}function o({enableUserBehaviorTracking:e=!0,enableNavigationTracking:t=!0,enableInteractionTracking:s=!0}){(0,a.usePathname)(),(0,r.useRef)(""),(0,r.useRef)(0);let{exportMetrics:i}=function(e,t={}){let{enableMonitoring:s=!0,enableMemoryTracking:a=!0,enableBundleAnalysis:i=!1,enableCacheTracking:n=!0,warningThresholds:l={renderTime:100,memoryUsage:0x3200000,bundleSize:1048576}}=t,[c,o]=(0,r.useState)({renderTime:0}),d=(0,r.useRef)(0),u=(0,r.useRef)(0),h=(0,r.useRef)(0),m=(0,r.useCallback)(()=>{s&&(d.current=performance.now())},[s]),g=(0,r.useCallback)(()=>{if(!s||!d.current)return;let e=performance.now()-d.current;o(t=>({...t,renderTime:e})),l.renderTime,d.current=0},[e,s,l.renderTime]),x=(0,r.useCallback)(()=>{if(!a||!("memory"in performance))return;let e=performance.memory,t={used:e.usedJSHeapSize,total:e.totalJSHeapSize,limit:e.jsHeapSizeLimit};o(e=>({...e,memoryUsage:t})),t.used,l.memoryUsage},[e,a,l.memoryUsage]),p=(0,r.useCallback)(()=>{if(!i)return;let e=performance.getEntriesByType("resource"),t=0;e.forEach(e=>{e.name.includes(".js")&&e.transferSize&&(t+=e.transferSize)}),o(e=>({...e,bundleSize:t})),l.bundleSize},[i,l.bundleSize]),f=(0,r.useCallback)(()=>{if(!n)return;let e=h.current>0?u.current/h.current*100:0;o(t=>({...t,cacheHitRate:e}))},[n]),y=(0,r.useCallback)(()=>{let e=[];return c.renderTime>100&&(e.push("Consider memoizing expensive calculations"),e.push("Use React.memo for component optimization"),e.push("Implement virtualization for large lists")),c.memoryUsage&&c.memoryUsage.used>0x3200000&&(e.push("Check for memory leaks"),e.push("Optimize image sizes and formats"),e.push("Implement proper cleanup in useEffect")),c.bundleSize&&c.bundleSize>1048576&&(e.push("Implement code splitting"),e.push("Use dynamic imports for heavy components"),e.push("Remove unused dependencies")),void 0!==c.cacheHitRate&&c.cacheHitRate<70&&(e.push("Improve caching strategy"),e.push("Implement service worker caching"),e.push("Use browser cache headers")),e},[c]),b=(0,r.useCallback)(()=>({component:e,timestamp:new Date().toISOString(),metrics:c,suggestions:y(),userAgent:navigator.userAgent,url:window.location.href}),[e,c,y]);return{metrics:c,startMeasurement:m,endMeasurement:g,trackMemoryUsage:x,analyzeBundleSize:p,trackCacheHitRate:f,getOptimizationSuggestions:y,exportMetrics:b}}("PerformanceTracker");return null}c.getInstance()},83611:(e,t,s)=>{Promise.resolve().then(s.bind(s,72847)),Promise.resolve().then(s.t.bind(s,79167,23)),Promise.resolve().then(s.bind(s,2052)),Promise.resolve().then(s.bind(s,11638)),Promise.resolve().then(s.bind(s,34701)),Promise.resolve().then(s.bind(s,80171))},83817:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},86995:(e,t,s)=>{"use strict";s.d(t,{globalCache:()=>a});class r{constructor(e=100){this.cache=new Map,this.cleanupInterval=null,this.maxSize=e,this.startCleanup()}set(e,t,s={}){let{ttl:r=3e5,tags:a=[],priority:i="medium",serialize:n=!1}=s,l={data:n?JSON.parse(JSON.stringify(t)):t,timestamp:Date.now(),ttl:r,accessCount:0,lastAccessed:Date.now(),tags:a,priority:i};this.cache.size>=this.maxSize&&this.evictLeastUsed(),this.cache.set(e,l)}get(e){let t=this.cache.get(e);return t?this.isExpired(t)?(this.cache.delete(e),null):(t.accessCount++,t.lastAccessed=Date.now(),t.data):null}getStale(e){let t=this.cache.get(e);if(!t)return{data:null,isStale:!1};let s=this.isExpired(t);return t.accessCount++,t.lastAccessed=Date.now(),{data:t.data,isStale:s}}has(e){let t=this.cache.get(e);return!!t&&(!this.isExpired(t)||(this.cache.delete(e),!1))}delete(e){return this.cache.delete(e)}invalidateByTags(e){let t=0;for(let[s,r]of this.cache.entries())r.tags.some(t=>e.includes(t))&&(this.cache.delete(s),t++);return t}clear(){this.cache.clear()}getStats(){let e=Array.from(this.cache.values()),t=Date.now();return{size:this.cache.size,maxSize:this.maxSize,hitRate:this.calculateHitRate(),averageAge:e.reduce((e,s)=>e+(t-s.timestamp),0)/e.length||0,totalAccesses:e.reduce((e,t)=>e+t.accessCount,0),expiredEntries:e.filter(e=>this.isExpired(e)).length,priorityDistribution:{high:e.filter(e=>"high"===e.priority).length,medium:e.filter(e=>"medium"===e.priority).length,low:e.filter(e=>"low"===e.priority).length}}}async preload(e,t,s={}){let r=this.get(e);if(r)return this.backgroundRefresh(e,t,s),r;let a=await t();return this.set(e,a,s),a}async backgroundRefresh(e,t,s){try{let r=await t();this.set(e,r,s)}catch(e){}}isExpired(e){return Date.now()-e.timestamp>e.ttl}evictLeastUsed(){if(0===this.cache.size)return;let[e]=Array.from(this.cache.entries()).sort(([,e],[,t])=>{let s={low:0,medium:1,high:2},r=s[e.priority]-s[t.priority];return 0!==r?r:e.lastAccessed-t.lastAccessed})[0];this.cache.delete(e)}calculateHitRate(){let e=Array.from(this.cache.values()).reduce((e,t)=>e+t.accessCount,0);return e>0?this.cache.size/e*100:0}startCleanup(){this.cleanupInterval=setInterval(()=>{this.cleanup()},6e4)}cleanup(){Date.now();let e=[];for(let[t,s]of this.cache.entries())this.isExpired(s)&&e.push(t);e.forEach(e=>this.cache.delete(e)),e.length}destroy(){this.cleanupInterval&&clearInterval(this.cleanupInterval),this.cache.clear()}}let a=new r(200)},91695:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\GlobalPrefetcher.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\GlobalPrefetcher.tsx","default")},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g,metadata:()=>m});var r=s(37413),a=s(91326),i=s.n(a),n=s(61120);s(61135),s(35760);var l=s(44366),c=s(62732),o=s(78676),d=s(91695),u=s(36162),h=s(27833);let m={title:"RouKey - Smart LLM Key Router",description:"Advanced LLM API key routing and management",icons:{icon:[{url:"/RouKey_Logo_GLOW.png",sizes:"32x32",type:"image/png"},{url:"/RouKey_Logo_GLOW.png",sizes:"16x16",type:"image/png"}],apple:[{url:"/RouKey_Logo_GLOW.png",sizes:"180x180",type:"image/png"}],shortcut:"/RouKey_Logo_GLOW.png"}};function g({children:e}){return(0,r.jsxs)("html",{lang:"en",className:i().variable,children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("link",{rel:"preload",href:"/api/custom-configs",as:"fetch",crossOrigin:"anonymous"}),(0,r.jsx)("link",{rel:"preload",href:"/api/system-status",as:"fetch",crossOrigin:"anonymous"}),(0,r.jsx)("link",{rel:"dns-prefetch",href:"//fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:""}),(0,r.jsx)("link",{rel:"prefetch",href:"/dashboard"}),(0,r.jsx)("link",{rel:"prefetch",href:"/playground"}),(0,r.jsx)("link",{rel:"prefetch",href:"/logs"}),(0,r.jsx)("link",{rel:"prefetch",href:"/my-models"})]}),(0,r.jsxs)("body",{className:"font-sans antialiased bg-[#1B1C1D]",children:[(0,r.jsx)(n.Suspense,{fallback:null,children:(0,r.jsx)(c.default,{})}),(0,r.jsx)(d.default,{}),(0,r.jsx)(o.default,{enableUserBehaviorTracking:!0,enableNavigationTracking:!0,enableInteractionTracking:!0}),(0,r.jsx)(l.default,{children:e}),(0,r.jsx)(h.SpeedInsights,{}),!1,(0,r.jsx)(u.default,{id:"sw-register",strategy:"afterInteractive",children:`
            if ('serviceWorker' in navigator) {
              window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                  .then(function(registration) {
                    console.log('✅ Service Worker registered successfully');

                    // Preload critical data after SW is ready
                    if (window.location.pathname === '/') {
                      // Preload landing page data
                      fetch('/api/system-status').catch(() => {});

                      // Prefetch all critical pages immediately
                      setTimeout(() => {
                        const criticalPages = ['/features', '/pricing', '/about', '/auth/signin', '/auth/signup'];
                        criticalPages.forEach(page => {
                          const link = document.createElement('link');
                          link.rel = 'prefetch';
                          link.href = page;
                          document.head.appendChild(link);
                        });
                      }, 500); // Much faster prefetching
                    }
                  })
                  .catch(function(registrationError) {
                    console.warn('⚠️ Service Worker registration failed:', registrationError);
                  });
              });
            }
          `})]})]})}}};