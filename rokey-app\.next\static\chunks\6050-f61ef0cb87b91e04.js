"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6050],{3332:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(12115);let l=r.forwardRef(function(e,t){let{title:n,titleId:l,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},o),n?r.createElement("title",{id:l},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.181 8.68a4.503 4.503 0 0 1 1.903 6.405m-9.768-2.782L3.56 14.06a4.5 4.5 0 0 0 6.364 6.365l3.129-3.129m5.614-5.615 1.757-1.757a4.5 4.5 0 0 0-6.364-6.365l-4.5 4.5c-.258.26-.479.541-.661.84m1.903 6.405a4.495 4.495 0 0 1-1.242-.88 4.483 4.483 0 0 1-1.062-1.683m6.587 2.345 5.907 5.907m-5.907-5.907L8.898 8.898M2.991 2.99 8.898 8.9"}))})},6004:(e,t,n)=>{},15782:(e,t,n)=>{n.d(t,{ll:()=>c.ll,UU:()=>v,vW:()=>en,cY:()=>h,BN:()=>p,Ej:()=>w,we:()=>V,Zx:()=>er,bv:()=>X});var r=n(12115),l=n.t(r,2),o=n(86301);function u(){let e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(e=>{let{brand:t,version:n}=e;return t+"/"+n}).join(" "):navigator.userAgent}var i=n(671);n(25095);var a=n(47650),c=n(41093),s="undefined"!=typeof document?r.useLayoutEffect:function(){};function f(e,t){let n,r,l;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!f(e[r],t[r]))return!1;return!0}if((n=(l=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,l[r]))return!1;for(r=n;0!=r--;){let n=l[r];if(("_owner"!==n||!e.$$typeof)&&!f(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function d(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function m(e,t){let n=d(e);return Math.round(t*n)/n}function g(e){let t=r.useRef(e);return s(()=>{t.current=e}),t}let h=(e,t)=>({...(0,c.cY)(e),options:[e,t]}),p=(e,t)=>({...(0,c.BN)(e),options:[e,t]}),v=(e,t)=>({...(0,c.UU)(e),options:[e,t]}),w=(e,t)=>({...(0,c.Ej)(e),options:[e,t]}),b={...l},y=b.useInsertionEffect||(e=>e());function k(e){let t=r.useRef(()=>{});return y(()=>{t.current=e}),r.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}let M="ArrowUp",x="ArrowDown",E="ArrowLeft",C="ArrowRight";function R(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:l,amount:o=1}=void 0===t?{}:t,u=e.current,i=n;do i+=r?-o:o;while(i>=0&&i<=u.length-1&&function(e,t,n){if(n)return n.includes(t);let r=e[t];return null==r||r.hasAttribute("disabled")||"true"===r.getAttribute("aria-disabled")}(u,i,l));return i}var A="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;let L=!1,S=0,j=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+S++,T=b.useId||function(){let[e,t]=r.useState(()=>L?j():void 0);return A(()=>{null==e&&t(j())},[]),r.useEffect(()=>{L=!0},[]),e},O=r.createContext(null),H=r.createContext(null),_=()=>{var e;return(null==(e=r.useContext(O))?void 0:e.id)||null},B=()=>r.useContext(H),W=()=>{},D=r.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:W,setState:W,isInstantPhase:!1}),P=0,I=new WeakMap,q=new WeakSet,N={},U=0,z=e=>e&&(e.host||z(e.parentNode)),Y=(e,t)=>t.map(t=>{if(e.contains(t))return t;let n=z(t);return e.contains(n)?n:null}).filter(e=>null!=e),F=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function K(e,t){let n=tabbable(e,F());"prev"===t&&n.reverse();let r=n.indexOf(activeElement(getDocument(e)));return n.slice(r+1)[0]}let Z="data-floating-ui-focusable",$=null;function V(e){void 0===e&&(e={});let{nodeId:t}=e,n=function(e){let{open:t=!1,onOpenChange:n,elements:l}=e,o=T(),u=r.useRef({}),[i]=r.useState(()=>(function(){let e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach(e=>e(n))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter(e=>e!==n))||[])}}})()),a=null!=_(),[c,s]=r.useState(l.reference),f=k((e,t,r)=>{u.current.openEvent=e?t:void 0,i.emit("openchange",{open:e,event:t,reason:r,nested:a}),null==n||n(e,t,r)}),d=r.useMemo(()=>({setPositionReference:s}),[]),m=r.useMemo(()=>({reference:c||l.reference||null,floating:l.floating||null,domReference:l.reference}),[c,l.reference,l.floating]);return r.useMemo(()=>({dataRef:u,open:t,onOpenChange:f,elements:m,events:i,floatingId:o,refs:d}),[t,f,m,i,o,d])}({...e,elements:{reference:null,floating:null,...e.elements}}),l=e.rootContext||n,u=l.elements,[i,h]=r.useState(null),[p,v]=r.useState(null),w=(null==u?void 0:u.domReference)||i,b=r.useRef(null),y=B();A(()=>{w&&(b.current=w)},[w]);let M=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:l=[],platform:o,elements:{reference:u,floating:i}={},transform:h=!0,whileElementsMounted:p,open:v}=e,[w,b]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[y,k]=r.useState(l);f(y,l)||k(l);let[M,x]=r.useState(null),[E,C]=r.useState(null),R=r.useCallback(e=>{e!==j.current&&(j.current=e,x(e))},[]),A=r.useCallback(e=>{e!==T.current&&(T.current=e,C(e))},[]),L=u||M,S=i||E,j=r.useRef(null),T=r.useRef(null),O=r.useRef(w),H=null!=p,_=g(p),B=g(o),W=g(v),D=r.useCallback(()=>{if(!j.current||!T.current)return;let e={placement:t,strategy:n,middleware:y};B.current&&(e.platform=B.current),(0,c.rD)(j.current,T.current,e).then(e=>{let t={...e,isPositioned:!1!==W.current};P.current&&!f(O.current,t)&&(O.current=t,a.flushSync(()=>{b(t)}))})},[y,t,n,B,W]);s(()=>{!1===v&&O.current.isPositioned&&(O.current.isPositioned=!1,b(e=>({...e,isPositioned:!1})))},[v]);let P=r.useRef(!1);s(()=>(P.current=!0,()=>{P.current=!1}),[]),s(()=>{if(L&&(j.current=L),S&&(T.current=S),L&&S){if(_.current)return _.current(L,S,D);D()}},[L,S,D,_,H]);let I=r.useMemo(()=>({reference:j,floating:T,setReference:R,setFloating:A}),[R,A]),q=r.useMemo(()=>({reference:L,floating:S}),[L,S]),N=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!q.floating)return e;let t=m(q.floating,w.x),r=m(q.floating,w.y);return h?{...e,transform:"translate("+t+"px, "+r+"px)",...d(q.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,h,q.floating,w.x,w.y]);return r.useMemo(()=>({...w,update:D,refs:I,elements:q,floatingStyles:N}),[w,D,I,q,N])}({...e,elements:{...u,...p&&{reference:p}}}),x=r.useCallback(e=>{let t=(0,o.vq)(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;v(t),M.refs.setReference(t)},[M.refs]),E=r.useCallback(e=>{((0,o.vq)(e)||null===e)&&(b.current=e,h(e)),((0,o.vq)(M.refs.reference.current)||null===M.refs.reference.current||null!==e&&!(0,o.vq)(e))&&M.refs.setReference(e)},[M.refs]),C=r.useMemo(()=>({...M.refs,setReference:E,setPositionReference:x,domReference:b}),[M.refs,E,x]),R=r.useMemo(()=>({...M.elements,domReference:w}),[M.elements,w]),L=r.useMemo(()=>({...M,...l,refs:C,elements:R,nodeId:t}),[M,C,R,t,l]);return A(()=>{l.dataRef.current.floatingContext=L;let e=null==y?void 0:y.nodesRef.current.find(e=>e.id===t);e&&(e.context=L)}),r.useMemo(()=>({...M,context:L,refs:C,elements:R}),[M,C,R,L])}let G="active",J="selected";function Q(e,t,n){let r=new Map,l="item"===n,o=e;if(l&&e){let{[G]:t,[J]:n,...r}=e;o=r}return{..."floating"===n&&{tabIndex:-1,[Z]:""},...o,...t.map(t=>{let r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r}).concat(e).reduce((e,t)=>(t&&Object.entries(t).forEach(t=>{let[n,o]=t;if(!(l&&[G,J].includes(n)))if(0===n.indexOf("on")){if(r.has(n)||r.set(n,[]),"function"==typeof o){var u;null==(u=r.get(n))||u.push(o),e[n]=function(){for(var e,t=arguments.length,l=Array(t),o=0;o<t;o++)l[o]=arguments[o];return null==(e=r.get(n))?void 0:e.map(e=>e(...l)).find(e=>void 0!==e)}}}else e[n]=o}),e),{})}}function X(e){void 0===e&&(e=[]);let t=e.map(e=>null==e?void 0:e.reference),n=e.map(e=>null==e?void 0:e.floating),l=e.map(e=>null==e?void 0:e.item),o=r.useCallback(t=>Q(t,e,"reference"),t),u=r.useCallback(t=>Q(t,e,"floating"),n),i=r.useCallback(t=>Q(t,e,"item"),l);return r.useMemo(()=>({getReferenceProps:o,getFloatingProps:u,getItemProps:i}),[o,u,i])}function ee(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function et(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}let en=e=>({name:"inner",options:e,async fn(t){let{listRef:n,overflowRef:r,onFallbackChange:l,offset:o=0,index:u=0,minItemsVisible:s=4,referenceOverflowThreshold:f=0,scrollRef:d,...m}=(0,i._3)(e,t),{rects:g,elements:{floating:p}}=t,v=n.current[u],w=(null==d?void 0:d.current)||p,b=p.clientTop||w.clientTop,y=0!==p.clientTop,k=0!==w.clientTop,M=p===w;if(!v)return{};let x={...t,...await h(-v.offsetTop-p.clientTop-g.reference.height/2-v.offsetHeight/2-o).fn(t)},E=await (0,c.__)(et(x,w.scrollHeight+b+p.clientTop),m),C=await (0,c.__)(x,{...m,elementContext:"reference"}),R=(0,i.T9)(0,E.top),A=x.y+R,L=(w.scrollHeight>w.clientHeight?e=>e:i.LI)((0,i.T9)(0,w.scrollHeight+(y&&M||k?2*b:0)-R-(0,i.T9)(0,E.bottom)));if(w.style.maxHeight=L+"px",w.scrollTop=R,l){let e=w.offsetHeight<v.offsetHeight*(0,i.jk)(s,n.current.length)-1||C.top>=-f||C.bottom>=-f;a.flushSync(()=>l(e))}return r&&(r.current=await (0,c.__)(et({...x,y:A},w.offsetHeight+b+p.clientTop),m)),{y:A}}});function er(e,t){let{open:n,elements:l}=e,{enabled:o=!0,overflowRef:i,scrollRef:c,onChange:s}=t,f=k(s),d=r.useRef(!1),m=r.useRef(null),g=r.useRef(null);r.useEffect(()=>{if(!o)return;function e(e){if(e.ctrlKey||!t||null==i.current)return;let n=e.deltaY,r=i.current.top>=-.5,l=i.current.bottom>=-.5,o=t.scrollHeight-t.clientHeight,c=n<0?-1:1,s=n<0?"max":"min";!(t.scrollHeight<=t.clientHeight)&&(!r&&n>0||!l&&n<0?(e.preventDefault(),a.flushSync(()=>{f(e=>e+Math[s](n,o*c))})):/firefox/i.test(u())&&(t.scrollTop+=n))}let t=(null==c?void 0:c.current)||l.floating;if(n&&t)return t.addEventListener("wheel",e),requestAnimationFrame(()=>{m.current=t.scrollTop,null!=i.current&&(g.current={...i.current})}),()=>{m.current=null,g.current=null,t.removeEventListener("wheel",e)}},[o,n,l.floating,i,c,f]);let h=r.useMemo(()=>({onKeyDown(){d.current=!0},onWheel(){d.current=!1},onPointerMove(){d.current=!1},onScroll(){let e=(null==c?void 0:c.current)||l.floating;if(i.current&&e&&d.current){if(null!==m.current){let t=e.scrollTop-m.current;(i.current.bottom<-.5&&t<-1||i.current.top<-.5&&t>1)&&a.flushSync(()=>f(e=>e+t))}requestAnimationFrame(()=>{m.current=e.scrollTop})}}}),[l.floating,f,i,c]);return r.useMemo(()=>o?{floating:h}:{},[o,h])}},34896:()=>{},40975:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(12115);let l=r.forwardRef(function(e,t){let{title:n,titleId:l,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},o),n?r.createElement("title",{id:l},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))})},42278:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(12115);let l=r.forwardRef(function(e,t){let{title:n,titleId:l,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},o),n?r.createElement("title",{id:l},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.125 2.25h-4.5c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125v-9M10.125 2.25h.375a9 9 0 0 1 9 9v.375M10.125 2.25A3.375 3.375 0 0 1 13.5 5.625v1.5c0 .621.504 1.125 1.125 1.125h1.5a3.375 3.375 0 0 1 3.375 3.375M9 15l2.25 2.25L15 12"}))})},68673:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(12115);let l=r.forwardRef(function(e,t){let{title:n,titleId:l,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},o),n?r.createElement("title",{id:l},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"}))})},69454:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(12115);let l=r.forwardRef(function(e,t){let{title:n,titleId:l,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},o),n?r.createElement("title",{id:l},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 12.75c1.148 0 2.278.08 3.383.237 1.037.146 1.866.966 1.866 2.013 0 3.728-2.35 6.75-5.25 6.75S6.75 18.728 6.75 15c0-1.046.83-1.867 1.866-2.013A24.204 24.204 0 0 1 12 12.75Zm0 0c2.883 0 5.647.508 8.207 1.44a23.91 23.91 0 0 1-1.152 6.06M12 12.75c-2.883 0-5.647.508-8.208 1.44.125 2.104.52 4.136 1.153 6.06M12 12.75a2.25 2.25 0 0 0 2.248-2.354M12 12.75a2.25 2.25 0 0 1-2.248-2.354M12 8.25c.995 0 1.971-.08 2.922-.236.403-.066.74-.358.795-.762a3.778 3.778 0 0 0-.399-2.25M12 8.25c-.995 0-1.97-.08-2.922-.236-.402-.066-.74-.358-.795-.762a3.734 3.734 0 0 1 .4-2.253M12 8.25a2.25 2.25 0 0 0-2.248 2.146M12 8.25a2.25 2.25 0 0 1 2.248 2.146M8.683 5a6.032 6.032 0 0 1-1.155-1.002c.07-.63.27-1.222.574-1.747m.581 2.749A3.75 3.75 0 0 1 15.318 5m0 0c.427-.283.815-.62 1.155-.999a4.471 4.471 0 0 0-.575-1.752M4.921 6a24.048 24.048 0 0 0-.392 3.314c1.668.546 3.416.914 5.223 1.082M19.08 6c.205 1.08.337 2.187.392 3.314a23.882 23.882 0 0 1-5.223 1.082"}))})},70405:(e,t,n)=>{n(6004)},82967:(e,t,n)=>{n(6004)},87027:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(12115);let l=r.forwardRef(function(e,t){let{title:n,titleId:l,...o}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},o),n?r.createElement("title",{id:l},n):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 15 12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9"}))})}}]);