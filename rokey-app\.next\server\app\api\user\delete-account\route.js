(()=>{var e={};e.id=773,e.ids=[773,1489],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{H:()=>c,Q:()=>u,createSupabaseServerClientOnRequest:()=>a});var s=r(34386),i=r(39398),o=r(44999);async function a(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function u(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}function c(){return(0,i.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},93586:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{DELETE:()=>n});var i=r(96559),o=r(48088),a=r(37719),u=r(32190),c=r(2507);async function n(e){let t=(0,c.Q)(e);try{let{data:{user:e},error:r}=await t.auth.getUser();if(r||!e)return u.NextResponse.json({error:"Unauthorized"},{status:401});let s=[];try{let{error:r}=await t.from("user_generated_api_keys").delete().eq("user_id",e.id);r&&s.push("Failed to delete user-generated API keys")}catch(e){s.push("Failed to delete user-generated API keys")}try{let{error:r}=await t.from("api_keys").delete().eq("user_id",e.id);r&&s.push("Failed to delete API keys")}catch(e){s.push("Failed to delete API keys")}try{let{error:r}=await t.from("user_custom_roles").delete().eq("user_id",e.id);r&&s.push("Failed to delete custom roles")}catch(e){s.push("Failed to delete custom roles")}try{let{error:r}=await t.from("custom_api_configs").delete().eq("user_id",e.id);r&&s.push("Failed to delete configurations")}catch(e){s.push("Failed to delete configurations")}try{let{error:r}=await t.from("semantic_cache").delete().eq("user_id",e.id);r&&s.push("Failed to delete semantic cache")}catch(e){s.push("Failed to delete semantic cache")}try{let{error:r}=await t.from("workflow_usage").delete().eq("user_id",e.id);r&&s.push("Failed to delete workflow usage")}catch(e){s.push("Failed to delete workflow usage")}try{let{error:r}=await t.from("cost_optimization_profiles").delete().eq("user_id",e.id);r&&s.push("Failed to delete cost optimization profiles")}catch(e){s.push("Failed to delete cost optimization profiles")}try{let{error:r}=await t.from("user_profiles").delete().eq("id",e.id);r&&s.push("Failed to delete user profile")}catch(e){s.push("Failed to delete user profile")}try{let{error:r}=await t.auth.admin.deleteUser(e.id);if(r)return u.NextResponse.json({error:"Failed to delete user account",details:r.message,cleanupErrors:s},{status:500})}catch(e){return u.NextResponse.json({error:"Failed to delete user account",details:"Auth deletion failed",cleanupErrors:s},{status:500})}if(s.length>0)return u.NextResponse.json({success:!0,message:"Account deleted successfully with some cleanup warnings",warnings:s});return u.NextResponse.json({success:!0,message:"Account deleted successfully"})}catch(e){return u.NextResponse.json({error:"Failed to delete account",details:e.message},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/user/delete-account/route",pathname:"/api/user/delete-account",filename:"route",bundlePath:"app/api/user/delete-account/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\user\\delete-account\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=p;function m(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(93586));module.exports=s})();