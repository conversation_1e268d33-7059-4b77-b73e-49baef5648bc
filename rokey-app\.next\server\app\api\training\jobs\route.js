(()=>{var e={};e.id=2335,e.ids=[1489,2335],e.modules={2507:(e,t,s)=>{"use strict";s.d(t,{H:()=>u,Q:()=>o,createSupabaseServerClientOnRequest:()=>a});var r=s(34386),n=s(39398),i=s(44999);async function a(){let e=await (0,i.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){try{e.set({name:t,value:s,...r})}catch(e){}},remove(t,s){try{e.set({name:t,value:"",...s})}catch(e){}}}})}function o(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,s){},remove(e,t){}}})}function u(){return(0,n.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},20275:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>E,routeModule:()=>m,serverHooks:()=>I,workAsyncStorage:()=>R,workUnitAsyncStorage:()=>_});var r={};s.r(r),s.d(r,{DELETE:()=>g,GET:()=>d,POST:()=>p,PUT:()=>l});var n=s(96559),i=s(48088),a=s(37719),o=s(32190),u=s(2507),c=s(43156);async function d(e){let t=await (0,u.createSupabaseServerClientOnRequest)(),{data:{user:s},error:r}=await t.auth.getUser();if(r||!s)return o.NextResponse.json({error:"Unauthorized: You must be logged in to view training jobs."},{status:401});let{searchParams:n}=new URL(e.url),i=n.get("custom_api_config_id"),a="true"===n.get("active_only");if(!i)return o.NextResponse.json({error:"custom_api_config_id query parameter is required"},{status:400});try{let e=t.from("training_jobs").select("*").eq("custom_api_config_id",i);e=a?e.eq("status","completed").order("created_at",{ascending:!1}).limit(1):e.order("created_at",{ascending:!1});let{data:s,error:r}=await e;if(r)return o.NextResponse.json({error:"Failed to fetch training jobs",details:r.message},{status:500});if(a&&s&&s.length>0){let e=s[0];return o.NextResponse.json({has_training:!0,training_data:e.training_data,job_id:e.id,created_at:e.created_at},{status:200})}return o.NextResponse.json(a?{has_training:!1}:s||[],{status:200})}catch(e){return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function p(e){let t=await (0,u.createSupabaseServerClientOnRequest)(),{data:{user:r},error:n}=await t.auth.getUser();if(n||!r)return o.NextResponse.json({error:"Unauthorized: You must be logged in to create training jobs."},{status:401});try{let{custom_api_config_id:n,name:i,description:a,training_data:u,parameters:d}=await e.json();if(!n||!i)return o.NextResponse.json({error:"Missing required fields: custom_api_config_id, name"},{status:400});let{data:p}=await t.from("subscriptions").select("tier").eq("user_id",r.id).eq("status","active").single(),l=p?.tier||"free";if(!(0,c.Nu)(l,"prompt_engineering"))return o.NextResponse.json({error:`Prompt engineering is not available on the ${l} plan. Please upgrade to create training jobs.`},{status:403});let{data:g,error:m}=await t.from("training_jobs").select("id, name, status, created_at").eq("custom_api_config_id",n).order("created_at",{ascending:!1}).limit(1);if(m);else if(g&&g.length>0){let e=g[0];return o.NextResponse.json({...e,warning:"Training job already exists for this configuration. Returned existing job to prevent data loss.",recommendation:"Use PUT method to update existing training job instead."},{status:200})}let{data:R,error:_}=await t.from("training_jobs").insert({custom_api_config_id:n,name:i,description:a,training_data:u,parameters:d,status:"completed",progress_percentage:100,started_at:new Date().toISOString(),completed_at:new Date().toISOString(),user_id:r.id}).select().single();if(_)return o.NextResponse.json({error:"Failed to create training job",details:_.message},{status:500});try{let{trainingDataCache:e}=await s.e(2842).then(s.bind(s,2842));e.invalidate(n)}catch(e){}return o.NextResponse.json(R,{status:201})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function l(e){let t=await (0,u.createSupabaseServerClientOnRequest)(),{searchParams:r}=new URL(e.url),n=r.get("id");if(!n)return o.NextResponse.json({error:"id query parameter is required"},{status:400});try{let r=await e.json(),{data:i,error:a}=await t.from("training_jobs").select("id, name, status, custom_api_config_id, created_at").eq("id",n).single();if(a)return o.NextResponse.json({error:"Failed to verify training job exists",details:a.message},{status:500});if(!i)return o.NextResponse.json({error:"Training job not found"},{status:404});let{data:u,error:c}=await t.from("training_jobs").update({...r,updated_at:new Date().toISOString()}).eq("id",n).select().single();if(c)return o.NextResponse.json({error:"Failed to update training job",details:c.message},{status:500});try{let{trainingDataCache:e}=await s.e(2842).then(s.bind(s,2842));e.invalidate(i.custom_api_config_id)}catch(e){}return o.NextResponse.json(u,{status:200})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function g(e){let t=await (0,u.createSupabaseServerClientOnRequest)(),{searchParams:r}=new URL(e.url),n=r.get("id");if(!n)return o.NextResponse.json({error:"id query parameter is required"},{status:400});try{let{data:e,error:r}=await t.from("training_jobs").select("custom_api_config_id").eq("id",n).single();if(r)return o.NextResponse.json({error:"Failed to fetch training job",details:r.message},{status:500});let{error:i}=await t.from("training_jobs").delete().eq("id",n);if(i)return o.NextResponse.json({error:"Failed to delete training job",details:i.message},{status:500});if(e)try{let{trainingDataCache:t}=await s.e(2842).then(s.bind(s,2842));t.invalidate(e.custom_api_config_id)}catch(e){}return o.NextResponse.json({message:"Training job deleted successfully"},{status:200})}catch(e){return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/training/jobs/route",pathname:"/api/training/jobs",filename:"route",bundlePath:"app/api/training/jobs/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\training\\jobs\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:R,workUnitAsyncStorage:_,serverHooks:I}=m;function E(){return(0,a.patchFetch)({workAsyncStorage:R,workUnitAsyncStorage:_})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},43156:(e,t,s)=>{"use strict";s.d(t,{Nu:()=>o,iK:()=>a,zX:()=>i});var r=s(94473);let n={free:{name:"Free",price:"$0",priceId:r.Dm.FREE,productId:r.Zu.FREE,features:["Unlimited API requests","1 Custom Configuration","3 API Keys per config","All 300+ AI models","Strict fallback routing only","Basic analytics only","No custom roles, basic router only","Limited logs","Community support"],limits:{configurations:1,apiKeysPerConfig:3,apiRequests:999999,canUseAdvancedRouting:!1,canUseCustomRoles:!1,maxCustomRoles:0,canUsePromptEngineering:!1,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},starter:{name:"Starter",price:"$20",priceId:r.Dm.STARTER,productId:r.Zu.STARTER,features:["Unlimited API requests","15 Custom Configurations","5 API Keys per config","All 300+ AI models","Intelligent routing strategies","Up to 3 custom roles","Intelligent role routing","Prompt engineering (no file upload)","Enhanced logs and analytics","Community support"],limits:{configurations:15,apiKeysPerConfig:5,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:3,canUsePromptEngineering:!0,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},professional:{name:"Professional",price:"$50",priceId:r.Dm.PROFESSIONAL,productId:r.Zu.PROFESSIONAL,features:["Unlimited API requests","Unlimited Custom Configurations","Unlimited API Keys per config","All 300+ AI models","All advanced routing strategies","Unlimited custom roles","Prompt engineering + Knowledge base (5 documents)","Semantic caching","Advanced analytics and logging","Priority email support"],limits:{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:5,canUseSemanticCaching:!0}},enterprise:{name:"Enterprise",price:"$149",priceId:r.Dm.ENTERPRISE,productId:r.Zu.ENTERPRISE,features:["Unlimited API requests","Unlimited configurations","Unlimited API keys","All 300+ models + priority access","All routing strategies","Unlimited custom roles","All features + priority support","Unlimited knowledge base documents","Advanced semantic caching","Custom integrations","Dedicated support + phone","SLA guarantee"],limits:{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:999999,canUseSemanticCaching:!0}}};function i(e){return n[e]}function a(e,t,s){let r=n[e].limits;switch(t){case"create_config":return s<r.configurations;case"create_api_key":return s<r.apiKeysPerConfig;default:return!0}}function o(e,t){let s=n[e].limits;switch(t){case"custom_roles":return s.canUseCustomRoles;case"knowledge_base":return s.canUseKnowledgeBase;case"advanced_routing":return s.canUseAdvancedRouting;case"prompt_engineering":return s.canUsePromptEngineering;case"semantic_caching":return s.canUseSemanticCaching;case"configurations":return s.configurations>0;default:return!1}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94473:(e,t,s)=>{"use strict";s.d(t,{Dm:()=>n,Lj:()=>r,Zu:()=>i});let r={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},n={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},i={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,r.publishableKey&&r.publishableKey.substring(0,20),r.secretKey&&r.secretKey.substring(0,20),r.webhookSecret&&r.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,9398,3410],()=>s(20275));module.exports=r})();