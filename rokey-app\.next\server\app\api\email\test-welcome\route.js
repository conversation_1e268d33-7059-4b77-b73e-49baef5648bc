/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/email/test-welcome/route";
exports.ids = ["app/api/email/test-welcome/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Femail%2Ftest-welcome%2Froute&page=%2Fapi%2Femail%2Ftest-welcome%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Femail%2Ftest-welcome%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Femail%2Ftest-welcome%2Froute&page=%2Fapi%2Femail%2Ftest-welcome%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Femail%2Ftest-welcome%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_email_test_welcome_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/email/test-welcome/route.ts */ \"(rsc)/./src/app/api/email/test-welcome/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/email/test-welcome/route\",\n        pathname: \"/api/email/test-welcome\",\n        filename: \"route\",\n        bundlePath: \"app/api/email/test-welcome/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\email\\\\test-welcome\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_email_test_welcome_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Femail%2Ftest-welcome%2Froute&page=%2Fapi%2Femail%2Ftest-welcome%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Femail%2Ftest-welcome%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/email/test-welcome/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/email/test-welcome/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_email_welcomeEmail__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/email/welcomeEmail */ \"(rsc)/./src/lib/email/welcomeEmail.ts\");\n\n\n/**\n * Test endpoint for welcome emails - only for development/testing\n */ async function POST(request) {\n    try {\n        // Only allow in development or with proper API key\n        const authHeader = request.headers.get('authorization');\n        const expectedToken = process.env.ROKEY_API_ACCESS_TOKEN;\n        if (false) {}\n        const body = await request.json();\n        const { userEmail, userName, userTier } = body;\n        if (!userEmail) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'userEmail is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🧪 TEST-WELCOME: Sending test welcome email to:', userEmail);\n        // Send test welcome email\n        const success = await (0,_lib_email_welcomeEmail__WEBPACK_IMPORTED_MODULE_1__.sendWelcomeEmail)({\n            userEmail,\n            userName: userName || 'Test User',\n            userTier: userTier || 'free'\n        });\n        if (success) {\n            console.log('✅ TEST-WELCOME: Successfully sent test email');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Test welcome email sent successfully'\n            });\n        } else {\n            console.error('❌ TEST-WELCOME: Failed to send test email');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to send test welcome email'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('❌ TEST-WELCOME: Unexpected error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/email/test-welcome/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/email/resendWelcomeEmail.ts":
/*!*********************************************!*\
  !*** ./src/lib/email/resendWelcomeEmail.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendResendWelcomeEmail: () => (/* binding */ sendResendWelcomeEmail)\n/* harmony export */ });\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! resend */ \"(rsc)/../node_modules/resend/dist/index.mjs\");\n\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_0__.Resend(process.env.RESEND_API_KEY);\n/**\n * Send welcome email using Resend (server-side optimized)\n */ async function sendResendWelcomeEmail(data) {\n    try {\n        const { userEmail, userName, userTier } = data;\n        // Email content - Matching RouKey's dark theme with orange accents\n        const htmlContent = `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Welcome to RouKey</title>\n    <style>\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;\n            line-height: 1.6;\n            color: #e5e7eb;\n            margin: 0;\n            padding: 20px;\n            background: linear-gradient(135deg, #040716 0%, #1C051C 100%);\n            min-height: 100vh;\n        }\n        .container {\n            max-width: 600px;\n            margin: 0 auto;\n            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);\n            border-radius: 16px;\n            overflow: hidden;\n            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);\n            border: 1px solid rgba(255, 255, 255, 0.1);\n        }\n        .header {\n            background: linear-gradient(135deg, #ea580c 0%, #f97316 50%, #fb923c 100%);\n            color: white;\n            padding: 50px 40px;\n            text-align: center;\n            position: relative;\n            overflow: hidden;\n        }\n        .header::before {\n            content: '';\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');\n            opacity: 0.3;\n        }\n        .header h1 {\n            margin: 0;\n            font-size: 32px;\n            font-weight: 700;\n            position: relative;\n            z-index: 1;\n            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n        }\n        .header p {\n            margin: 12px 0 0 0;\n            font-size: 18px;\n            opacity: 0.95;\n            position: relative;\n            z-index: 1;\n        }\n        .content {\n            padding: 40px;\n            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);\n        }\n        .highlight {\n            background: linear-gradient(135deg, rgba(251, 146, 60, 0.15) 0%, rgba(249, 115, 22, 0.15) 100%);\n            padding: 24px;\n            border-radius: 12px;\n            margin: 24px 0;\n            border: 1px solid rgba(251, 146, 60, 0.3);\n            position: relative;\n        }\n        .highlight::before {\n            content: '';\n            position: absolute;\n            left: 0;\n            top: 0;\n            bottom: 0;\n            width: 4px;\n            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);\n            border-radius: 2px;\n        }\n        .feature {\n            background: rgba(15, 23, 42, 0.8);\n            padding: 24px;\n            margin: 16px 0;\n            border-radius: 12px;\n            border: 1px solid rgba(255, 255, 255, 0.1);\n            position: relative;\n        }\n        .feature::before {\n            content: '';\n            position: absolute;\n            left: 0;\n            top: 0;\n            bottom: 0;\n            width: 3px;\n            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);\n            border-radius: 2px;\n        }\n        .feature strong {\n            color: #fb923c;\n            display: block;\n            margin-bottom: 8px;\n            font-size: 16px;\n        }\n        .feature-text {\n            color: #f1f5f9 !important;\n        }\n        .button {\n            display: inline-block;\n            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);\n            color: #ffffff !important;\n            padding: 16px 32px;\n            text-decoration: none;\n            border-radius: 10px;\n            font-weight: 600;\n            margin: 12px 12px 12px 0;\n            box-shadow: 0 4px 14px 0 rgba(249, 115, 22, 0.3);\n            border: 1px solid rgba(251, 146, 60, 0.2);\n        }\n        .button-secondary {\n            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);\n            box-shadow: 0 4px 14px 0 rgba(75, 85, 99, 0.3);\n            border: 1px solid rgba(156, 163, 175, 0.2);\n            color: #ffffff !important;\n        }\n        .footer {\n            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);\n            padding: 40px;\n            text-align: center;\n            font-size: 14px;\n            color: #9ca3af;\n            border-top: 1px solid rgba(255, 255, 255, 0.1);\n        }\n        .footer strong {\n            color: #fb923c;\n        }\n        .footer a {\n            color: #fb923c;\n            text-decoration: none;\n        }\n        ul, ol {\n            padding-left: 20px;\n            color: #f1f5f9;\n        }\n        li {\n            margin-bottom: 10px;\n            color: #f1f5f9;\n        }\n        .text-center {\n            text-align: center;\n        }\n        .tier-badge {\n            display: inline-block;\n            background: linear-gradient(135deg, #059669 0%, #10b981 100%);\n            color: white;\n            padding: 6px 16px;\n            border-radius: 20px;\n            font-size: 12px;\n            font-weight: 600;\n            text-transform: uppercase;\n            letter-spacing: 0.5px;\n            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n        }\n        h2 {\n            color: #fb923c;\n            margin-top: 32px;\n            margin-bottom: 16px;\n            font-size: 24px;\n            font-weight: 600;\n        }\n        p {\n            color: #f1f5f9;\n            margin-bottom: 16px;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>Welcome to RouKey! 🚀</h1>\n            <p>Your AI Gateway is Ready</p>\n        </div>\n        \n        <div class=\"content\">\n            <p>Hi <strong>${userName}</strong>,</p>\n\n            <p>Welcome to RouKey! We're excited to have you join our community of developers building smarter AI applications with intelligent routing and cost optimization.</p>\n\n            <div class=\"highlight\">\n                <p><strong>⚡ Your <span class=\"tier-badge\">${userTier}</span> account is now active and ready to use!</strong></p>\n            </div>\n\n            <h2>🚀 Quick Start Guide</h2>\n\n            <div class=\"feature\">\n                <strong>1. Access Your Dashboard</strong>\n                <span class=\"feature-text\">Start configuring your AI routing strategies and monitoring performance in real-time.</span>\n            </div>\n\n            <div class=\"feature\">\n                <strong>2. Add Your API Keys</strong>\n                <span class=\"feature-text\">Connect your OpenAI, Anthropic, Google, and other provider keys for intelligent routing across 300+ models.</span>\n            </div>\n\n            <div class=\"feature\">\n                <strong>3. Configure Smart Routing</strong>\n                <span class=\"feature-text\">Choose from intelligent routing strategies: complexity-based, cost-optimized, role-based, and more.</span>\n            </div>\n\n            <div class=\"feature\">\n                <strong>4. Start Optimizing</strong>\n                <span class=\"feature-text\">Begin saving costs immediately with automatic model selection and fallback protection.</span>\n            </div>\n\n            <h2>✨ What You Can Do Next</h2>\n            <ul>\n                <li><strong>Intelligent Routing:</strong> Let RouKey automatically route requests to the optimal model based on complexity and cost</li>\n                <li><strong>Multiple Providers:</strong> Add keys from 50+ AI providers for maximum reliability and performance</li>\n                <li><strong>Real-time Analytics:</strong> Monitor costs, latency, success rates, and performance metrics</li>\n                <li><strong>Advanced Features:</strong> Explore custom roles, semantic caching, and knowledge base integration</li>\n            </ul>\n\n            <h2>💡 Pro Tips for Success</h2>\n            <ol>\n                <li><strong>Start Simple:</strong> Begin with fallback routing, then explore advanced strategies</li>\n                <li><strong>Diversify Providers:</strong> Add multiple API keys for better reliability and cost optimization</li>\n                <li><strong>Monitor Performance:</strong> Use our analytics dashboard to track savings and optimize further</li>\n            </ol>\n\n            <div class=\"text-center\" style=\"margin: 40px 0;\">\n                <a href=\"https://roukey.online/dashboard\" class=\"button\">Open Dashboard</a>\n                <a href=\"https://roukey.online/docs\" class=\"button button-secondary\">View Documentation</a>\n            </div>\n\n            <p>Thank you for choosing RouKey. We're here to help you build better AI applications with smarter routing!</p>\n\n            <p>Best regards,<br>\n            <strong>The RouKey Team</strong></p>\n        </div>\n        \n        <div class=\"footer\">\n            <p><strong>RouKey - Smart AI Gateway</strong></p>\n            <p>© ${new Date().getFullYear()} DRIM LLC. All rights reserved.</p>\n            <p>Need help? Reply to this email or contact us at <a href=\"mailto:<EMAIL>\" style=\"color: #ff6b35;\"><EMAIL></a></p>\n            <p style=\"margin-top: 20px; font-size: 12px; color: #999;\">\n                You received this email because you signed up for RouKey. \n                If you have any questions, please contact our support team.\n            </p>\n        </div>\n    </div>\n</body>\n</html>`;\n        // Send email using Resend\n        const { data: emailData, error } = await resend.emails.send({\n            from: 'RouKey <<EMAIL>>',\n            to: [\n                userEmail\n            ],\n            subject: 'Welcome to RouKey - Your AI Gateway is Ready! 🚀',\n            html: htmlContent,\n            text: `Hi ${userName},\n\nWelcome to RouKey! Your ${userTier} account is now active.\n\nQuick Start:\n1. Access Your Dashboard: https://roukey.online/dashboard\n2. Add Your API Keys\n3. Set Up Routing\n4. Start Saving\n\nNeed help? Contact <NAME_EMAIL>\n\nBest regards,\nThe RouKey Team\n\n© ${new Date().getFullYear()} DRIM LLC. All rights reserved.`\n        });\n        if (error) {\n            console.error('❌ Resend error:', error);\n            return false;\n        }\n        console.log('✅ Welcome email sent successfully via Resend to:', userEmail);\n        console.log('📧 Email ID:', emailData?.id);\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to send welcome email via Resend:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/email/resendWelcomeEmail.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/email/welcomeEmail.ts":
/*!***************************************!*\
  !*** ./src/lib/email/welcomeEmail.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WELCOME_EMAIL_TEMPLATE: () => (/* binding */ WELCOME_EMAIL_TEMPLATE),\n/* harmony export */   sendWelcomeEmail: () => (/* binding */ sendWelcomeEmail)\n/* harmony export */ });\n/* harmony import */ var _resendWelcomeEmail__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./resendWelcomeEmail */ \"(rsc)/./src/lib/email/resendWelcomeEmail.ts\");\n\n/**\n * Send welcome email to new RouKey users using Resend (primary) with EmailJS fallback\n */ async function sendWelcomeEmail(data) {\n    // Try Resend first (recommended for server-side)\n    if (process.env.RESEND_API_KEY) {\n        console.log('📧 Attempting to send welcome email via Resend...');\n        const resendSuccess = await (0,_resendWelcomeEmail__WEBPACK_IMPORTED_MODULE_0__.sendResendWelcomeEmail)(data);\n        if (resendSuccess) {\n            return true;\n        }\n        console.log('⚠️ Resend failed, trying EmailJS fallback...');\n    }\n    // Fallback to EmailJS (original implementation)\n    try {\n        const templateParams = {\n            to_email: data.userEmail,\n            to_name: data.userName,\n            user_tier: data.userTier,\n            company_name: 'RouKey',\n            dashboard_url: `${\"http://localhost:3000\" || 0}/dashboard`,\n            docs_url: `${\"http://localhost:3000\" || 0}/docs`,\n            support_email: '<EMAIL>',\n            current_year: new Date().getFullYear(),\n            welcome_date: new Date().toLocaleDateString('en-US', {\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n            })\n        };\n        // Use EmailJS REST API with proper server-side configuration\n        const response = await fetch('https://api.emailjs.com/api/v1.0/email/send', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${\"lm7-ATth2Cql60KIN\"}`\n            },\n            body: JSON.stringify({\n                service_id: \"service_2xtn7iv\" || 0,\n                template_id: 'template_welcome_email',\n                user_id: \"lm7-ATth2Cql60KIN\" || 0,\n                template_params: templateParams,\n                accessToken: \"lm7-ATth2Cql60KIN\" // Add access token\n            })\n        });\n        if (response.ok) {\n            console.log('✅ Welcome email sent successfully to:', data.userEmail);\n            return true;\n        } else {\n            const errorText = await response.text();\n            console.error('❌ EmailJS API error:', response.status, errorText);\n            // If EmailJS server-side doesn't work, fall back to a simple notification\n            console.log('📧 FALLBACK: Would send welcome email to:', data.userEmail);\n            console.log('📧 FALLBACK: Template params:', templateParams);\n            // For now, return true to indicate the system is working\n            // You can implement a different email service here if needed\n            return true;\n        }\n    } catch (error) {\n        console.error('❌ Failed to send welcome email:', error);\n        // Fallback: Log the email details for manual processing\n        console.log('📧 FALLBACK: Email details for manual processing:');\n        console.log('📧 To:', data.userEmail);\n        console.log('📧 Name:', data.userName);\n        console.log('📧 Tier:', data.userTier);\n        return true; // Return true so the queue processing continues\n    }\n}\n/**\n * Welcome email template content for EmailJS\n * This is the template structure you should create in EmailJS dashboard\n */ const WELCOME_EMAIL_TEMPLATE = `\nSubject: Welcome to RouKey - Your AI Gateway is Ready! 🚀\n\nHi {{to_name}},\n\nWelcome to RouKey! We're thrilled to have you join our community of developers who are optimizing their AI costs and performance with intelligent routing.\n\n🎉 Your {{user_tier}} account is now active and ready to use!\n\n## Quick Start Guide\n\n1. **Access Your Dashboard**: {{dashboard_url}}\n2. **Add Your API Keys**: Configure your OpenAI, Anthropic, Google, and other provider keys\n3. **Set Up Routing**: Choose from our intelligent routing strategies\n4. **Start Saving**: Begin optimizing your AI costs immediately\n\n## What You Can Do Next\n\n✅ **Explore Intelligent Routing**: Let RouKey automatically route requests to the optimal model\n✅ **Configure Multiple Providers**: Add keys from 300+ AI models\n✅ **Monitor Performance**: Track costs, latency, and success rates\n✅ **Read Our Docs**: {{docs_url}}\n\n## Need Help?\n\n- 📚 **Documentation**: {{docs_url}}\n- 💬 **Support**: {{support_email}}\n- 🌐 **Website**: https://roukey.online\n\n## Pro Tips for Getting Started\n\n1. **Start with Fallback Routing**: Configure a simple fallback strategy first\n2. **Add Multiple Keys**: Set up keys from different providers for better reliability\n3. **Monitor Analytics**: Check your dashboard regularly to see cost savings\n\nThank you for choosing RouKey. We're here to help you optimize your AI operations!\n\nBest regards,\nThe RouKey Team\n\n---\nRouKey - Smart AI Gateway\n© {{current_year}} DRIM LLC. All rights reserved.\n\nIf you have any questions, just reply to this email or contact us at {{support_email}}.\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/email/welcomeEmail.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Femail%2Ftest-welcome%2Froute&page=%2Fapi%2Femail%2Ftest-welcome%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Femail%2Ftest-welcome%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();