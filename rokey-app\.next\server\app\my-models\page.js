(()=>{var e={};e.id=5690,e.ids=[5690],e.modules={192:(e,t,r)=>{Promise.resolve().then(r.bind(r,39482))},2969:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13344:(e,t,r)=>{Promise.resolve().then(r.bind(r,69188))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26403:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39482:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(60687),a=r(43210),o=r(85814),n=r.n(o),i=r(71031),l=r(97450),d=r(2969),c=r(71178),m=r(26403),u=r(50181),p=r(20404),x=r(9776),h=r(5097),g=r(4847),f=r(8449),b=r(11016);function y(){let[e,t]=(0,a.useState)([]),[r,o]=(0,a.useState)(!0),[y,j]=(0,a.useState)(null),[v,w]=(0,a.useState)(""),[N,k]=(0,a.useState)(!1),C=(0,p.Z)(),[A,P]=(0,a.useState)(!1),{createHoverPrefetch:M,prefetchManageKeysData:q}=(0,h._)(),{subscriptionStatus:E,user:_}=(0,b.R)(),R=async()=>{o(!0),j(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch configurations")}let r=await e.json();t(r)}catch(e){j(e.message)}finally{o(!1)}},L=async e=>{if(e.preventDefault(),!v.trim())return void j("Configuration name cannot be empty.");k(!0),j(null);try{let e=await fetch("/api/custom-configs",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:v})}),t=await e.json();if(!e.ok)throw Error(t.details||t.error||"Failed to create configuration");w(""),P(!1),await R()}catch(e){j(e.message)}finally{k(!1)}},S=(e,t)=>{C.showConfirmation({title:"Delete Configuration",message:`Are you sure you want to delete "${t}"? This will permanently remove the configuration and all associated API keys. This action cannot be undone.`,confirmText:"Delete Configuration",cancelText:"Cancel",type:"danger"},async()=>{j(null);try{let t=await fetch(`/api/custom-configs/${e}`,{method:"DELETE"}),r=await t.json();if(!t.ok)throw Error(r.details||r.error||"Failed to delete configuration");await R()}catch(e){throw j(`Failed to delete: ${e.message}`),e}})};return(0,s.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:(0,s.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8 animate-fade-in",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-4xl font-bold mb-2",children:(0,s.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400",children:"My API Models"})}),(0,s.jsx)("p",{className:"text-gray-400 mt-2",children:"Manage your custom API configurations and keys"})]}),(0,s.jsx)(g.sU,{feature:"configurations",currentCount:e.length,customMessage:"You've reached your configuration limit. Upgrade to create more API configurations and organize your models better.",fallback:(0,s.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,s.jsxs)("button",{disabled:!0,className:"btn-primary opacity-50 cursor-not-allowed",children:[(0,s.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Create New Model"]}),(0,s.jsx)("p",{className:"text-xs text-orange-600 font-medium",children:E?.tier==="free"?"Upgrade to Starter for more configurations":"Configuration limit reached - upgrade for more"})]}),children:(0,s.jsxs)("button",{onClick:()=>P(!A),className:A?"btn-secondary-dark":"btn-primary",children:[(0,s.jsx)(i.A,{className:"h-4 w-4 mr-2"}),A?"Cancel":"Create New Model"]})})]}),E?.tier==="free"&&(0,s.jsx)(f.L,{message:"Unlock intelligent routing and 15 configurations",variant:"compact",className:"mb-4"}),(0,s.jsx)(f.p,{className:"mb-4"}),y&&(0,s.jsx)("div",{className:"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,s.jsx)("p",{className:"text-red-300",children:y})]})}),A&&(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg max-w-md animate-scale-in p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Create New Model"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Set up a new API configuration"})]}),(0,s.jsxs)("form",{onSubmit:L,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"configName",className:"block text-sm font-medium text-gray-300 mb-2",children:"Model Name"}),(0,s.jsx)("input",{type:"text",id:"configName",value:v,onChange:e=>w(e.target.value),required:!0,className:"w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",placeholder:"e.g., My Main Chat Assistant"})]}),(0,s.jsx)("button",{type:"submit",disabled:N,className:"btn-primary w-full",children:N?"Creating...":"Create Model"})]})]}),r&&(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,t)=>(0,s.jsx)(x.B0,{},t))}),!r&&!e.length&&!y&&!A&&(0,s.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg text-center py-12",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-orange-500/20 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-orange-500/30",children:(0,s.jsx)(l.A,{className:"h-8 w-8 text-orange-400"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No API Models Yet"}),(0,s.jsx)("p",{className:"text-gray-400 mb-6",children:"Create your first API model configuration to get started with RoKey."}),(0,s.jsx)(g.sU,{feature:"configurations",currentCount:e.length,customMessage:"Create your first API configuration to get started with RouKey. Free tier includes 1 configuration.",fallback:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,s.jsxs)("button",{disabled:!0,className:"btn-primary opacity-50 cursor-not-allowed",children:[(0,s.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Create Your First Model"]}),(0,s.jsx)("p",{className:"text-xs text-orange-600 font-medium",children:"Upgrade to create configurations"})]}),children:(0,s.jsxs)("button",{onClick:()=>P(!0),className:"btn-primary",children:[(0,s.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Create Your First Model"]})})]})}),!r&&e.length>0&&(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map((e,t)=>(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 hover:border-gray-700/50 transition-all duration-200 animate-slide-in",style:{animationDelay:`${100*t}ms`},children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-white mb-2 truncate",children:e.name}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center text-xs text-gray-400",children:[(0,s.jsx)(l.A,{className:"h-3 w-3 mr-1"}),"ID: ",e.id.slice(0,8),"..."]}),(0,s.jsxs)("div",{className:"flex items-center text-xs text-gray-400",children:[(0,s.jsx)(d.A,{className:"h-3 w-3 mr-1"}),"Created: ",new Date(e.created_at).toLocaleDateString()]})]})]}),(0,s.jsx)("div",{className:"w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center shrink-0 border border-orange-500/30",children:(0,s.jsx)(l.A,{className:"h-6 w-6 text-orange-400"})})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 mt-6",children:[(0,s.jsx)(n(),{href:`/my-models/${e.id}`,className:"flex-1",...M(e.id),children:(0,s.jsxs)("button",{className:"btn-primary w-full",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Manage Keys"]})}),(0,s.jsxs)("button",{onClick:()=>S(e.id,e.name),className:"btn-secondary-dark text-red-400 hover:text-red-300 hover:bg-red-900/20",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]},e.id))}),(0,s.jsx)(u.A,{isOpen:C.isOpen,onClose:C.hideConfirmation,onConfirm:C.onConfirm,title:C.title,message:C.message,confirmText:C.confirmText,cancelText:C.cancelText,type:C.type,isLoading:C.isLoading})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69188:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\page.tsx","default")},70149:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89507:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),o=r(88170),n=r.n(o),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["my-models",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,69188)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,92529)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\my-models\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/my-models/page",pathname:"/my-models",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5449,2535,4912,4847,4629],()=>r(89507));module.exports=s})();