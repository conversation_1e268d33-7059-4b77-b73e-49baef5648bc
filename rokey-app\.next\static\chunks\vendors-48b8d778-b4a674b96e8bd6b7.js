(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8925],{20294:e=>{"use strict";e.exports=function(){throw Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},44741:(e,t,n)=>{"use strict";n.d(t,{h:()=>h,n:()=>d});var o=n(12115),r=n(45643);let i=e=>{let t,n=new Set,o=(e,o)=>{let r="function"==typeof e?e(t):e;if(!Object.is(r,t)){let e=t;t=(null!=o?o:"object"!=typeof r||null===r)?r:Object.assign({},t,r),n.forEach(n=>n(t,e))}},r=()=>t,i={setState:o,getState:r,getInitialState:()=>l,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},l=t=e(o,r,i);return i},l=e=>e?i(e):i,{useDebugValue:a}=o,{useSyncExternalStoreWithSelector:s}=r,u=e=>e;function d(e,t=u,n){let o=s(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return a(o),o}let c=(e,t)=>{let n=l(e),o=(e,o=t)=>d(n,e,o);return Object.assign(o,n),o},h=(e,t)=>e?c(e,t):c},66419:()=>{},75745:(e,t,n)=>{"use strict";function o(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(let[n,o]of e)if(!Object.is(o,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}let n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let o of n)if(!Object.prototype.hasOwnProperty.call(t,o)||!Object.is(e[o],t[o]))return!1;return!0}n.d(t,{x:()=>o})},89724:(e,t,n)=>{"use strict";n.d(t,{Do:()=>l,Eo:()=>ea,Er:()=>ec,Ff:()=>Z,Fp:()=>eg,HF:()=>v,Hm:()=>eB,I$:()=>eG,IO:()=>z,Jo:()=>I,KE:()=>X,Mi:()=>F,No:()=>eW,Q6:()=>ei,QE:()=>et,Qc:()=>i,R4:()=>Q,TG:()=>a,Tq:()=>T,U$:()=>P,Ue:()=>U,WZ:()=>o,X6:()=>j,YN:()=>er,ZO:()=>p,_s:()=>eo,aE:()=>k,aQ:()=>e1,aW:()=>eL,aZ:()=>H,ah:()=>e$,b$:()=>b,b5:()=>ek,bK:()=>m,bi:()=>eR,di:()=>e2,e_:()=>ey,kM:()=>R,kO:()=>tt,kf:()=>W,mW:()=>Y,ny:()=>r,oB:()=>E,oN:()=>eN,oj:()=>es,q1:()=>eh,qX:()=>ew,qn:()=>eq,r8:()=>ej,rN:()=>eM,s_:()=>q,tM:()=>g,tn:()=>x,uD:()=>ee,uL:()=>eY,uj:()=>ev,us:()=>en,v5:()=>ed,vS:()=>eC,xN:()=>u,xc:()=>y,yX:()=>s,zj:()=>K});var o,r,i,l,a,s,u,d=n(37396),c=n(63664),h=n(76528),f=n(7609);let y={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error002:()=>"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error007:e=>`The old edge with id=${e} does not exist.`,error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,{id:t,sourceHandle:n,targetHandle:o})=>`Couldn't create edge for ${e} handle id: "${"source"===e?n:o}", edge id: ${t}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,error013:(e="react")=>`It seems that you haven't loaded the styles. Please import '@xyflow/${e}/dist/style.css' or base.css to make sure everything is working properly.`,error014:()=>"useNodeConnections: No node ID found. Call useNodeConnections inside a custom Node or provide a node ID.",error015:()=>"It seems that you are trying to drag a node that is not initialized. Please use onNodesChange as explained in the docs."},p=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],x=["Enter"," ","Escape"],g={"node.a11yDescription.default":"Press enter or space to select a node. Press delete to remove it and escape to cancel.","node.a11yDescription.keyboardDisabled":"Press enter or space to select a node. You can then use the arrow keys to move the node around. Press delete to remove it and escape to cancel.","node.a11yDescription.ariaLiveMessage":({direction:e,x:t,y:n})=>`Moved selected node ${e}. New position, x: ${t}, y: ${n}`,"edge.a11yDescription.default":"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.","controls.ariaLabel":"Control Panel","controls.zoomIn.ariaLabel":"Zoom In","controls.zoomOut.ariaLabel":"Zoom Out","controls.fitView.ariaLabel":"Fit View","controls.interactive.ariaLabel":"Toggle Interactivity","minimap.ariaLabel":"Mini Map","handle.ariaLabel":"Handle"};!function(e){e.Strict="strict",e.Loose="loose"}(o||(o={})),function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"}(r||(r={})),function(e){e.Partial="partial",e.Full="full"}(i||(i={}));let m={inProgress:!1,isValid:null,from:null,fromHandle:null,fromPosition:null,fromNode:null,to:null,toHandle:null,toPosition:null,toNode:null};!function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"}(l||(l={})),function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"}(a||(a={})),function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"}(s||(s={}));let w={[s.Left]:s.Right,[s.Right]:s.Left,[s.Top]:s.Bottom,[s.Bottom]:s.Top};function v(e){return null===e?null:e?"valid":"invalid"}let b=e=>"id"in e&&"source"in e&&"target"in e,E=e=>"id"in e&&"position"in e&&!("source"in e)&&!("target"in e),M=e=>"id"in e&&"internals"in e&&!("source"in e)&&!("target"in e),$=(e,t=[0,0])=>{let{width:n,height:o}=ee(e),r=e.origin??t,i=n*r[0],l=o*r[1];return{x:e.position.x-i,y:e.position.y-l}},I=(e,t={nodeOrigin:[0,0]})=>0===e.length?{x:0,y:0,width:0,height:0}:C(e.reduce((e,n)=>{let o="string"==typeof n,r=t.nodeLookup||o?void 0:n;return t.nodeLookup&&(r=o?t.nodeLookup.get(n):M(n)?n:t.nodeLookup.get(n.id)),O(e,r?D(r,t.nodeOrigin):{x:0,y:0,x2:0,y2:0})},{x:1/0,y:1/0,x2:-1/0,y2:-1/0})),H=(e,t={})=>{if(0===e.size)return{x:0,y:0,width:0,height:0};let n={x:1/0,y:1/0,x2:-1/0,y2:-1/0};return e.forEach(e=>{if(void 0===t.filter||t.filter(e)){let t=D(e);n=O(n,t)}}),C(n)},P=(e,t,[n,o,r]=[0,0,1],i=!1,l=!1)=>{let a={...Z(t,[n,o,r]),width:t.width/r,height:t.height/r},s=[];for(let t of e.values()){let{measured:e,selectable:n=!0,hidden:o=!1}=t;if(l&&!n||o)continue;let r=e.width??t.width??t.initialWidth??null,u=e.height??t.height??t.initialHeight??null,d=j(a,R(t)),c=(r??0)*(u??0),h=i&&d>0;(!t.internals.handleBounds||h||d>=c||t.dragging)&&s.push(t)}return s},N=(e,t)=>{let n=new Set;return e.forEach(e=>{n.add(e.id)}),t.filter(e=>n.has(e.source)||n.has(e.target))};async function z({nodes:e,width:t,height:n,panZoom:o,minZoom:r,maxZoom:i},l){if(0===e.size)return Promise.resolve(!0);let a=Q(H(function(e,t){let n=new Map,o=t?.nodes?new Set(t.nodes.map(e=>e.id)):null;return e.forEach(e=>{e.measured.width&&e.measured.height&&(t?.includeHiddenNodes||!e.hidden)&&(!o||o.has(e.id))&&n.set(e.id,e)}),n}(e,l)),t,n,l?.minZoom??r,l?.maxZoom??i,l?.padding??.1);return await o.setViewport(a,{duration:l?.duration,ease:l?.ease,interpolate:l?.interpolate}),Promise.resolve(!0)}function k({nodeId:e,nextPosition:t,nodeLookup:n,nodeOrigin:o=[0,0],nodeExtent:r,onError:i}){let l=n.get(e),a=l.parentId?n.get(l.parentId):void 0,{x:s,y:u}=a?a.internals.positionAbsolute:{x:0,y:0},d=l.origin??o,c=r;if("parent"!==l.extent||l.expandParent)a&&J(l.extent)&&(c=[[l.extent[0][0]+s,l.extent[0][1]+u],[l.extent[1][0]+s,l.extent[1][1]+u]]);else if(a){let e=a.measured.width,t=a.measured.height;e&&t&&(c=[[s,u],[s+e,u+t]])}else i?.("005",y.error005());let h=J(c)?A(t,c,l.measured):t;return(void 0===l.measured.width||void 0===l.measured.height)&&i?.("015",y.error015()),{position:{x:h.x-s+(l.measured.width??0)*d[0],y:h.y-u+(l.measured.height??0)*d[1]},positionAbsolute:h}}async function T({nodesToRemove:e=[],edgesToRemove:t=[],nodes:n,edges:o,onBeforeDelete:r}){let i=new Set(e.map(e=>e.id)),l=[];for(let e of n){if(!1===e.deletable)continue;let t=i.has(e.id),n=!t&&e.parentId&&l.find(t=>t.id===e.parentId);(t||n)&&l.push(e)}let a=new Set(t.map(e=>e.id)),s=o.filter(e=>!1!==e.deletable),u=N(l,s);for(let e of s)a.has(e.id)&&!u.find(t=>t.id===e.id)&&u.push(e);if(!r)return{edges:u,nodes:l};let d=await r({nodes:l,edges:u});return"boolean"==typeof d?d?{edges:u,nodes:l}:{edges:[],nodes:[]}:d}let S=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),A=(e={x:0,y:0},t,n)=>({x:S(e.x,t[0][0],t[1][0]-(n?.width??0)),y:S(e.y,t[0][1],t[1][1]-(n?.height??0))});function L(e,t,n){let{width:o,height:r}=ee(n),{x:i,y:l}=n.internals.positionAbsolute;return A(e,[[i,l],[i+o,l+r]],t)}let B=(e,t,n)=>e<t?S(Math.abs(e-t),1,t)/t:e>n?-S(Math.abs(e-n),1,t)/t:0,_=(e,t,n=15,o=40)=>[B(e.x,o,t.width-o)*n,B(e.y,o,t.height-o)*n],O=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),V=({x:e,y:t,width:n,height:o})=>({x:e,y:t,x2:e+n,y2:t+o}),C=({x:e,y:t,x2:n,y2:o})=>({x:e,y:t,width:n-e,height:o-t}),R=(e,t=[0,0])=>{let{x:n,y:o}=M(e)?e.internals.positionAbsolute:$(e,t);return{x:n,y:o,width:e.measured?.width??e.width??e.initialWidth??0,height:e.measured?.height??e.height??e.initialHeight??0}},D=(e,t=[0,0])=>{let{x:n,y:o}=M(e)?e.internals.positionAbsolute:$(e,t);return{x:n,y:o,x2:n+(e.measured?.width??e.width??e.initialWidth??0),y2:o+(e.measured?.height??e.height??e.initialHeight??0)}},F=(e,t)=>C(O(V(e),V(t))),j=(e,t)=>Math.ceil(Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x))*Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y))),Y=e=>W(e.width)&&W(e.height)&&W(e.x)&&W(e.y),W=e=>!isNaN(e)&&isFinite(e),X=(e,t)=>{},q=(e,t=[1,1])=>({x:t[0]*Math.round(e.x/t[0]),y:t[1]*Math.round(e.y/t[1])}),Z=({x:e,y:t},[n,o,r],i=!1,l=[1,1])=>{let a={x:(e-n)/r,y:(t-o)/r};return i?q(a,l):a},K=({x:e,y:t},[n,o,r])=>({x:e*r+n,y:t*r+o});function G(e,t){if("number"==typeof e)return Math.floor((t-t/(1+e))*.5);if("string"==typeof e&&e.endsWith("px")){let t=parseFloat(e);if(!Number.isNaN(t))return Math.floor(t)}if("string"==typeof e&&e.endsWith("%")){let n=parseFloat(e);if(!Number.isNaN(n))return Math.floor(t*n*.01)}return console.error(`[React Flow] The padding value "${e}" is invalid. Please provide a number or a string with a valid unit (px or %).`),0}let Q=(e,t,n,o,r,i)=>{let l=function(e,t,n){if("string"==typeof e||"number"==typeof e){let o=G(e,n),r=G(e,t);return{top:o,right:r,bottom:o,left:r,x:2*r,y:2*o}}if("object"==typeof e){let o=G(e.top??e.y??0,n),r=G(e.bottom??e.y??0,n),i=G(e.left??e.x??0,t),l=G(e.right??e.x??0,t);return{top:o,right:l,bottom:r,left:i,x:i+l,y:o+r}}return{top:0,right:0,bottom:0,left:0,x:0,y:0}}(i,t,n),a=S(Math.min((t-l.x)/e.width,(n-l.y)/e.height),o,r),s=e.x+e.width/2,u=e.y+e.height/2,d=t/2-s*a,c=n/2-u*a,h=function(e,t,n,o,r,i){let{x:l,y:a}=K(e,[t,n,o]),{x:s,y:u}=K({x:e.x+e.width,y:e.y+e.height},[t,n,o]);return{left:Math.floor(l),top:Math.floor(a),right:Math.floor(r-s),bottom:Math.floor(i-u)}}(e,d,c,a,t,n),f={left:Math.min(h.left-l.left,0),top:Math.min(h.top-l.top,0),right:Math.min(h.right-l.right,0),bottom:Math.min(h.bottom-l.bottom,0)};return{x:d-f.left+f.right,y:c-f.top+f.bottom,zoom:a}},U=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0;function J(e){return void 0!==e&&"parent"!==e}function ee(e){return{width:e.measured?.width??e.width??e.initialWidth??0,height:e.measured?.height??e.height??e.initialHeight??0}}function et(e){return(e.measured?.width??e.width??e.initialWidth)!==void 0&&(e.measured?.height??e.height??e.initialHeight)!==void 0}function en(e,t={width:0,height:0},n,o,r){let i={...e},l=o.get(n);if(l){let e=l.origin||r;i.x+=l.internals.positionAbsolute.x-(t.width??0)*e[0],i.y+=l.internals.positionAbsolute.y-(t.height??0)*e[1]}return i}function eo(e,t){if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}function er(){let e,t;return{promise:new Promise((n,o)=>{e=n,t=o}),resolve:e,reject:t}}function ei(e){return{...g,...e||{}}}function el(e,{snapGrid:t=[0,0],snapToGrid:n=!1,transform:o,containerBounds:r}){let{x:i,y:l}=eh(e),a=Z({x:i-(r?.left??0),y:l-(r?.top??0)},o),{x:s,y:u}=n?q(a,t):a;return{xSnapped:s,ySnapped:u,...a}}let ea=e=>({width:e.offsetWidth,height:e.offsetHeight}),es=e=>e?.getRootNode?.()||window?.document,eu=["INPUT","SELECT","TEXTAREA"];function ed(e){let t=e.composedPath?.()?.[0]||e.target;return t?.nodeType===1&&(eu.includes(t.nodeName)||t.hasAttribute("contenteditable")||!!t.closest(".nokey"))}let ec=e=>"clientX"in e,eh=(e,t)=>{let n=ec(e),o=n?e.clientX:e.touches?.[0].clientX,r=n?e.clientY:e.touches?.[0].clientY;return{x:o-(t?.left??0),y:r-(t?.top??0)}},ef=(e,t,n,o,r)=>{let i=t.querySelectorAll(`.${e}`);return i&&i.length?Array.from(i).map(t=>{let i=t.getBoundingClientRect();return{id:t.getAttribute("data-handleid"),type:e,nodeId:r,position:t.getAttribute("data-handlepos"),x:(i.left-n.left)/o,y:(i.top-n.top)/o,...ea(t)}}):null};function ey({sourceX:e,sourceY:t,targetX:n,targetY:o,sourceControlX:r,sourceControlY:i,targetControlX:l,targetControlY:a}){let s=.125*e+.375*r+.375*l+.125*n,u=.125*t+.375*i+.375*a+.125*o,d=Math.abs(s-e),c=Math.abs(u-t);return[s,u,d,c]}function ep(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function ex({pos:e,x1:t,y1:n,x2:o,y2:r,c:i}){switch(e){case s.Left:return[t-ep(t-o,i),n];case s.Right:return[t+ep(o-t,i),n];case s.Top:return[t,n-ep(n-r,i)];case s.Bottom:return[t,n+ep(r-n,i)]}}function eg({sourceX:e,sourceY:t,sourcePosition:n=s.Bottom,targetX:o,targetY:r,targetPosition:i=s.Top,curvature:l=.25}){let[a,u]=ex({pos:n,x1:e,y1:t,x2:o,y2:r,c:l}),[d,c]=ex({pos:i,x1:o,y1:r,x2:e,y2:t,c:l}),[h,f,y,p]=ey({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:a,sourceControlY:u,targetControlX:d,targetControlY:c});return[`M${e},${t} C${a},${u} ${d},${c} ${o},${r}`,h,f,y,p]}function em({sourceX:e,sourceY:t,targetX:n,targetY:o}){let r=Math.abs(n-e)/2,i=Math.abs(o-t)/2;return[n<e?n+r:n-r,o<t?o+i:o-i,r,i]}function ew({sourceNode:e,targetNode:t,selected:n=!1,zIndex:o,elevateOnSelect:r=!1}){return void 0!==o?o:(r&&n?1e3:0)+Math.max(e.parentId?e.internals.z:0,t.parentId?t.internals.z:0)}function ev({sourceNode:e,targetNode:t,width:n,height:o,transform:r}){let i=O(D(e),D(t));return i.x===i.x2&&(i.x2+=1),i.y===i.y2&&(i.y2+=1),j({x:-r[0]/r[2],y:-r[1]/r[2],width:n/r[2],height:o/r[2]},C(i))>0}let eb=({source:e,sourceHandle:t,target:n,targetHandle:o})=>`xy-edge__${e}${t||""}-${n}${o||""}`,eE=(e,t)=>t.some(t=>t.source===e.source&&t.target===e.target&&(t.sourceHandle===e.sourceHandle||!t.sourceHandle&&!e.sourceHandle)&&(t.targetHandle===e.targetHandle||!t.targetHandle&&!e.targetHandle)),eM=(e,t)=>{let n;return e.source&&e.target?eE(n=b(e)?{...e}:{...e,id:eb(e)},t)?t:(null===n.sourceHandle&&delete n.sourceHandle,null===n.targetHandle&&delete n.targetHandle,t.concat(n)):(X("006",y.error006()),t)};function e$({sourceX:e,sourceY:t,targetX:n,targetY:o}){let[r,i,l,a]=em({sourceX:e,sourceY:t,targetX:n,targetY:o});return[`M ${e},${t}L ${n},${o}`,r,i,l,a]}let eI={[s.Left]:{x:-1,y:0},[s.Right]:{x:1,y:0},[s.Top]:{x:0,y:-1},[s.Bottom]:{x:0,y:1}},eH=({source:e,sourcePosition:t=s.Bottom,target:n})=>t===s.Left||t===s.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},eP=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function eN({sourceX:e,sourceY:t,sourcePosition:n=s.Bottom,targetX:o,targetY:r,targetPosition:i=s.Top,borderRadius:l=5,centerX:a,centerY:u,offset:d=20}){let[c,h,f,y,p]=function({source:e,sourcePosition:t=s.Bottom,target:n,targetPosition:o=s.Top,center:r,offset:i}){let l,a,u=eI[t],d=eI[o],c={x:e.x+u.x*i,y:e.y+u.y*i},h={x:n.x+d.x*i,y:n.y+d.y*i},f=eH({source:c,sourcePosition:t,target:h}),y=0!==f.x?"x":"y",p=f[y],x=[],g={x:0,y:0},m={x:0,y:0},[w,v,b,E]=em({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(u[y]*d[y]==-1){l=r.x??w,a=r.y??v;let e=[{x:l,y:c.y},{x:l,y:h.y}],t=[{x:c.x,y:a},{x:h.x,y:a}];x=u[y]===p?"x"===y?e:t:"x"===y?t:e}else{let r=[{x:c.x,y:h.y}],s=[{x:h.x,y:c.y}];if(x="x"===y?u.x===p?s:r:u.y===p?r:s,t===o){let t=Math.abs(e[y]-n[y]);if(t<=i){let o=Math.min(i-1,i-t);u[y]===p?g[y]=(c[y]>e[y]?-1:1)*o:m[y]=(h[y]>n[y]?-1:1)*o}}if(t!==o){let e="x"===y?"y":"x",t=u[y]===d[e],n=c[e]>h[e],o=c[e]<h[e];(1===u[y]&&(!t&&n||t&&o)||1!==u[y]&&(!t&&o||t&&n))&&(x="x"===y?r:s)}let f={x:c.x+g.x,y:c.y+g.y},w={x:h.x+m.x,y:h.y+m.y};Math.max(Math.abs(f.x-x[0].x),Math.abs(w.x-x[0].x))>=Math.max(Math.abs(f.y-x[0].y),Math.abs(w.y-x[0].y))?(l=(f.x+w.x)/2,a=x[0].y):(l=x[0].x,a=(f.y+w.y)/2)}return[[e,{x:c.x+g.x,y:c.y+g.y},...x,{x:h.x+m.x,y:h.y+m.y},n],l,a,b,E]}({source:{x:e,y:t},sourcePosition:n,target:{x:o,y:r},targetPosition:i,center:{x:a,y:u},offset:d});return[c.reduce((e,t,n)=>{let o="";return e+(n>0&&n<c.length-1?function(e,t,n,o){let r=Math.min(eP(e,t)/2,eP(t,n)/2,o),{x:i,y:l}=t;if(e.x===i&&i===n.x||e.y===l&&l===n.y)return`L${i} ${l}`;if(e.y===l){let t=e.x<n.x?-1:1,o=e.y<n.y?1:-1;return`L ${i+r*t},${l}Q ${i},${l} ${i},${l+r*o}`}let a=e.x<n.x?1:-1,s=e.y<n.y?-1:1;return`L ${i},${l+r*s}Q ${i},${l} ${i+r*a},${l}`}(c[n-1],t,c[n+1],l):`${0===n?"M":"L"}${t.x} ${t.y}`)},""),h,f,y,p]}function ez(e){return e&&!!(e.internals.handleBounds||e.handles?.length)&&!!(e.measured.width||e.width||e.initialWidth)}function ek(e){let{sourceNode:t,targetNode:n}=e;if(!ez(t)||!ez(n))return null;let r=t.internals.handleBounds||eT(t.handles),i=n.internals.handleBounds||eT(n.handles),l=eA(r?.source??[],e.sourceHandle),a=eA(e.connectionMode===o.Strict?i?.target??[]:(i?.target??[]).concat(i?.source??[]),e.targetHandle);if(!l||!a)return e.onError?.("008",y.error008(!l?"source":"target",{id:e.id,sourceHandle:e.sourceHandle,targetHandle:e.targetHandle})),null;let u=l?.position||s.Bottom,d=a?.position||s.Top,c=eS(t,l,u),h=eS(n,a,d);return{sourceX:c.x,sourceY:c.y,targetX:h.x,targetY:h.y,sourcePosition:u,targetPosition:d}}function eT(e){if(!e)return null;let t=[],n=[];for(let o of e)o.width=o.width??1,o.height=o.height??1,"source"===o.type?t.push(o):"target"===o.type&&n.push(o);return{source:t,target:n}}function eS(e,t,n=s.Left,o=!1){let r=(t?.x??0)+e.internals.positionAbsolute.x,i=(t?.y??0)+e.internals.positionAbsolute.y,{width:l,height:a}=t??ee(e);if(o)return{x:r+l/2,y:i+a/2};switch(t?.position??n){case s.Top:return{x:r+l/2,y:i};case s.Right:return{x:r+l,y:i+a/2};case s.Bottom:return{x:r+l/2,y:i+a};case s.Left:return{x:r,y:i+a/2}}}function eA(e,t){return e&&(t?e.find(e=>e.id===t):e[0])||null}function eL(e,t){if(!e)return"";if("string"==typeof e)return e;let n=t?`${t}__`:"";return`${n}${Object.keys(e).sort().map(t=>`${t}=${e[t]}`).join("&")}`}function eB(e,{id:t,defaultColor:n,defaultMarkerStart:o,defaultMarkerEnd:r}){let i=new Set;return e.reduce((e,l)=>([l.markerStart||o,l.markerEnd||r].forEach(o=>{if(o&&"object"==typeof o){let r=eL(o,t);i.has(r)||(e.push({id:r,color:o.color||n,...o}),i.add(r))}}),e),[]).sort((e,t)=>e.id.localeCompare(t.id))}let e_={nodeOrigin:[0,0],nodeExtent:p,elevateNodesOnSelect:!0,defaults:{}},eO={...e_,checkEquality:!0};function eV(e,t){let n={...e};for(let e in t)void 0!==t[e]&&(n[e]=t[e]);return n}function eC(e,t,n){let o=eV(e_,n);for(let n of e.values())if(n.parentId)eD(n,e,t,o);else{let e=A($(n,o.nodeOrigin),J(n.extent)?n.extent:o.nodeExtent,ee(n));n.internals.positionAbsolute=e}}function eR(e,t,n,o){let r=eV(eO,o),i=e.length>0,l=new Map(t),a=1e3*!!r?.elevateNodesOnSelect;for(let s of(t.clear(),n.clear(),e)){let e=l.get(s.id);if(r.checkEquality&&s===e?.internals.userNode)t.set(s.id,e);else{let n=A($(s,r.nodeOrigin),J(s.extent)?s.extent:r.nodeExtent,ee(s));e={...r.defaults,...s,measured:{width:s.measured?.width,height:s.measured?.height},internals:{positionAbsolute:n,handleBounds:s.measured?e?.internals.handleBounds:void 0,z:eF(s,a),userNode:s}},t.set(s.id,e)}void 0!==e.measured&&void 0!==e.measured.width&&void 0!==e.measured.height||e.hidden||(i=!1),s.parentId&&eD(e,t,n,o)}return i}function eD(e,t,n,o){let{elevateNodesOnSelect:r,nodeOrigin:i,nodeExtent:l}=eV(e_,o),a=e.parentId,s=t.get(a);if(!s)return void console.warn(`Parent node ${a} not found. Please make sure that parent nodes are in front of their child nodes in the nodes array.`);!function(e,t){if(!e.parentId)return;let n=t.get(e.parentId);n?n.set(e.id,e):t.set(e.parentId,new Map([[e.id,e]]))}(e,n);let{x:u,y:d,z:c}=function(e,t,n,o,r){let{x:i,y:l}=t.internals.positionAbsolute,a=ee(e),s=$(e,n),u=J(e.extent)?A(s,e.extent,a):s,d=A({x:i+u.x,y:l+u.y},o,a);"parent"===e.extent&&(d=L(d,a,t));let c=eF(e,r),h=t.internals.z??0;return{x:d.x,y:d.y,z:h>=c?h+1:c}}(e,s,i,l,1e3*!!r),{positionAbsolute:h}=e.internals,f=u!==h.x||d!==h.y;(f||c!==e.internals.z)&&t.set(e.id,{...e,internals:{...e.internals,positionAbsolute:f?{x:u,y:d}:h,z:c}})}function eF(e,t){return(W(e.zIndex)?e.zIndex:0)+(e.selected?t:0)}function ej(e,t,n,o=[0,0]){let r=[],i=new Map;for(let n of e){let e=t.get(n.parentId);if(!e)continue;let o=F(i.get(n.parentId)?.expandedRect??R(e),n.rect);i.set(n.parentId,{expandedRect:o,parent:e})}return i.size>0&&i.forEach(({expandedRect:t,parent:i},l)=>{let a=i.internals.positionAbsolute,s=ee(i),u=i.origin??o,d=t.x<a.x?Math.round(Math.abs(a.x-t.x)):0,c=t.y<a.y?Math.round(Math.abs(a.y-t.y)):0,h=Math.max(s.width,Math.round(t.width)),f=Math.max(s.height,Math.round(t.height)),y=(h-s.width)*u[0],p=(f-s.height)*u[1];(d>0||c>0||y||p)&&(r.push({id:l,type:"position",position:{x:i.position.x-d+y,y:i.position.y-c+p}}),n.get(l)?.forEach(t=>{e.some(e=>e.id===t.id)||r.push({id:t.id,type:"position",position:{x:t.position.x+d,y:t.position.y+c}})})),(s.width<t.width||s.height<t.height||d||c)&&r.push({id:l,type:"dimensions",setAttributes:!0,dimensions:{width:h+(d?u[0]*d-y:0),height:f+(c?u[1]*c-p:0)}})}),r}function eY(e,t,n,o,r,i){let l=o?.querySelector(".xyflow__viewport"),a=!1;if(!l)return{changes:[],updatedInternals:a};let s=[],u=window.getComputedStyle(l),{m22:d}=new window.DOMMatrixReadOnly(u.transform),c=[];for(let o of e.values()){let e=t.get(o.id);if(!e)continue;if(e.hidden){t.set(e.id,{...e,internals:{...e.internals,handleBounds:void 0}}),a=!0;continue}let l=ea(o.nodeElement),u=e.measured.width!==l.width||e.measured.height!==l.height;if(l.width&&l.height&&(u||!e.internals.handleBounds||o.force)){let h=o.nodeElement.getBoundingClientRect(),f=J(e.extent)?e.extent:i,{positionAbsolute:y}=e.internals;e.parentId&&"parent"===e.extent?y=L(y,l,t.get(e.parentId)):f&&(y=A(y,f,l));let p={...e,measured:l,internals:{...e.internals,positionAbsolute:y,handleBounds:{source:ef("source",o.nodeElement,h,d,e.id),target:ef("target",o.nodeElement,h,d,e.id)}}};t.set(e.id,p),e.parentId&&eD(p,t,n,{nodeOrigin:r}),a=!0,u&&(s.push({id:e.id,type:"dimensions",dimensions:l}),e.expandParent&&e.parentId&&c.push({id:e.id,parentId:e.parentId,rect:R(p,r)}))}}if(c.length>0){let e=ej(c,t,n,r);s.push(...e)}return{changes:s,updatedInternals:a}}async function eW({delta:e,panZoom:t,transform:n,translateExtent:o,width:r,height:i}){if(!t||!e.x&&!e.y)return Promise.resolve(!1);let l=await t.setViewportConstrained({x:n[0]+e.x,y:n[1]+e.y,zoom:n[2]},[[0,0],[r,i]],o);return Promise.resolve(!!l&&(l.x!==n[0]||l.y!==n[1]||l.k!==n[2]))}function eX(e,t,n,o,r,i){let l=r,a=o.get(l)||new Map;o.set(l,a.set(n,t)),l=`${r}-${e}`;let s=o.get(l)||new Map;if(o.set(l,s.set(n,t)),i){l=`${r}-${e}-${i}`;let a=o.get(l)||new Map;o.set(l,a.set(n,t))}}function eq(e,t,n){for(let o of(e.clear(),t.clear(),n)){let{source:n,target:r,sourceHandle:i=null,targetHandle:l=null}=o,a={edgeId:o.id,source:n,target:r,sourceHandle:i,targetHandle:l},s=`${n}-${i}--${r}-${l}`;eX("source",a,`${r}-${l}--${n}-${i}`,e,n,i),eX("target",a,s,e,r,l),t.set(o.id,o)}}function eZ(e,t,n){let o=e;do{if(o?.matches?.(t))return!0;if(o===n)break;o=o?.parentElement}while(o);return!1}function eK({nodeId:e,dragItems:t,nodeLookup:n,dragging:o=!0}){let r=[];for(let[e,i]of t){let t=n.get(e)?.internals.userNode;t&&r.push({...t,position:i.position,dragging:o})}if(!e)return[r[0],r];let i=n.get(e)?.internals.userNode;return[i?{...i,position:t.get(e)?.position||i.position,dragging:o}:r[0],r]}function eG({onNodeMouseDown:e,getStoreItems:t,onDragStart:n,onDrag:o,onDragStop:r}){let i={x:null,y:null},l=0,a=new Map,s=!1,u={x:0,y:0},h=null,f=!1,y=null,p=!1,x=!1;return{update:function({noDragClassName:g,handleSelector:m,domNode:w,isSelectable:v,nodeId:b,nodeClickDistance:E=0}){function M({x:e,y:n},r){let{nodeLookup:l,nodeExtent:s,snapGrid:u,snapToGrid:d,nodeOrigin:c,onNodeDrag:h,onSelectionDrag:f,onError:y,updateNodePositions:p}=t();i={x:e,y:n};let g=!1,m={x:0,y:0,x2:0,y2:0};for(let[t,o]of(a.size>1&&s&&(m=V(H(a))),a)){if(!l.has(t))continue;let r={x:e-o.distance.x,y:n-o.distance.y};d&&(r=q(r,u));let i=[[s[0][0],s[0][1]],[s[1][0],s[1][1]]];if(a.size>1&&s&&!o.extent){let{positionAbsolute:e}=o.internals,t=e.x-m.x+s[0][0],n=e.x+o.measured.width-m.x2+s[1][0];i=[[t,e.y-m.y+s[0][1]],[n,e.y+o.measured.height-m.y2+s[1][1]]]}let{position:h,positionAbsolute:f}=k({nodeId:t,nextPosition:r,nodeLookup:l,nodeExtent:i,nodeOrigin:c,onError:y});g=g||o.position.x!==h.x||o.position.y!==h.y,o.position=h,o.internals.positionAbsolute=f}if(x=x||g,g&&(p(a,!0),r&&(o||h||!b&&f))){let[e,t]=eK({nodeId:b,dragItems:a,nodeLookup:l});o?.(r,a,e,t),h?.(r,e,t),b||f?.(r,t)}}async function $(){if(!h)return;let{transform:e,panBy:n,autoPanSpeed:o,autoPanOnNodeDrag:r}=t();if(!r){s=!1,cancelAnimationFrame(l);return}let[a,d]=_(u,h,o);(0!==a||0!==d)&&(i.x=(i.x??0)-a/e[2],i.y=(i.y??0)-d/e[2],await n({x:a,y:d})&&M(i,null)),l=requestAnimationFrame($)}function I(o){let{nodeLookup:r,multiSelectionActive:l,nodesDraggable:s,transform:u,snapGrid:d,snapToGrid:c,selectNodesOnDrag:y,onNodeDragStart:p,onSelectionDragStart:x,unselectNodesAndEdges:g}=t();f=!0,y&&v||l||!b||r.get(b)?.selected||g(),v&&y&&b&&e?.(b);let m=el(o.sourceEvent,{transform:u,snapGrid:d,snapToGrid:c,containerBounds:h});if(i=m,(a=function(e,t,n,o){let r=new Map;for(let[i,l]of e)if((l.selected||l.id===o)&&(!l.parentId||!function e(t,n){if(!t.parentId)return!1;let o=n.get(t.parentId);return!!o&&(!!o.selected||e(o,n))}(l,e))&&(l.draggable||t&&void 0===l.draggable)){let t=e.get(i);t&&r.set(i,{id:i,position:t.position||{x:0,y:0},distance:{x:n.x-t.internals.positionAbsolute.x,y:n.y-t.internals.positionAbsolute.y},extent:t.extent,parentId:t.parentId,origin:t.origin,expandParent:t.expandParent,internals:{positionAbsolute:t.internals.positionAbsolute||{x:0,y:0}},measured:{width:t.measured.width??0,height:t.measured.height??0}})}return r}(r,s,m,b)).size>0&&(n||p||!b&&x)){let[e,t]=eK({nodeId:b,dragItems:a,nodeLookup:r});n?.(o.sourceEvent,a,e,t),p?.(o.sourceEvent,e,t),b||x?.(o.sourceEvent,t)}}y=(0,c.Lt)(w);let P=(0,d.$E)().clickDistance(E).on("start",e=>{let{domNode:n,nodeDragThreshold:o,transform:r,snapGrid:l,snapToGrid:a}=t();h=n?.getBoundingClientRect()||null,p=!1,x=!1,0===o&&I(e),i=el(e.sourceEvent,{transform:r,snapGrid:l,snapToGrid:a,containerBounds:h}),u=eh(e.sourceEvent,h)}).on("drag",e=>{let{autoPanOnNodeDrag:n,transform:o,snapGrid:r,snapToGrid:l,nodeDragThreshold:d,nodeLookup:c}=t(),y=el(e.sourceEvent,{transform:o,snapGrid:r,snapToGrid:l,containerBounds:h});if(("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1||b&&!c.has(b))&&(p=!0),!p){if(!s&&n&&f&&(s=!0,$()),!f){let t=y.xSnapped-(i.x??0),n=y.ySnapped-(i.y??0);Math.sqrt(t*t+n*n)>d&&I(e)}(i.x!==y.xSnapped||i.y!==y.ySnapped)&&a&&f&&(u=eh(e.sourceEvent,h),M(y,e.sourceEvent))}}).on("end",e=>{if(f&&!p&&(s=!1,f=!1,cancelAnimationFrame(l),a.size>0)){let{nodeLookup:n,updateNodePositions:o,onNodeDragStop:i,onSelectionDragStop:l}=t();if(x&&(o(a,!1),x=!1),r||i||!b&&l){let[t,o]=eK({nodeId:b,dragItems:a,nodeLookup:n,dragging:!1});r?.(e.sourceEvent,a,t,o),i?.(e.sourceEvent,t,o),b||l?.(e.sourceEvent,o)}}}).filter(e=>{let t=e.target;return!e.button&&(!g||!eZ(t,`.${g}`,w))&&(!m||eZ(t,m,w))});y.call(P)},destroy:function(){y?.on(".drag",null)}}}function eQ(e,t,n,o,r,i=!1){let l=o.get(e);if(!l)return null;let a="strict"===r?l.internals.handleBounds?.[t]:[...l.internals.handleBounds?.source??[],...l.internals.handleBounds?.target??[]],s=(n?a?.find(e=>e.id===n):a?.[0])??null;return s&&i?{...s,...eS(l,s,s.position,!0)}:s}function eU(e,t){return e?e:t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null}let eJ=()=>!0;function e0(e,{handle:t,connectionMode:n,fromNodeId:r,fromHandleId:i,fromType:l,doc:a,lib:s,flowId:u,isValidConnection:d=eJ,nodeLookup:c}){let h="target"===l,f=t?a.querySelector(`.${s}-flow__handle[data-id="${u}-${t?.nodeId}-${t?.id}-${t?.type}"]`):null,{x:y,y:p}=eh(e),x=a.elementFromPoint(y,p),g=x?.classList.contains(`${s}-flow__handle`)?x:f,m={handleDomNode:g,isValid:!1,connection:null,toHandle:null};if(g){let e=eU(void 0,g),t=g.getAttribute("data-nodeid"),l=g.getAttribute("data-handleid"),a=g.classList.contains("connectable"),s=g.classList.contains("connectableend");if(!t||!e)return m;let u={source:h?t:r,sourceHandle:h?l:i,target:h?r:t,targetHandle:h?i:l};m.connection=u,m.isValid=a&&s&&(n===o.Strict?h&&"source"===e||!h&&"target"===e:t!==r||l!==i)&&d(u),m.toHandle=eQ(t,e,l,c,n,!0)}return m}let e1={onPointerDown:function(e,{connectionMode:t,connectionRadius:n,handleId:o,nodeId:r,edgeUpdaterType:i,isTarget:l,domNode:a,nodeLookup:u,lib:d,autoPanOnConnect:c,flowId:h,panBy:f,cancelConnection:y,onConnectStart:p,onConnect:x,onConnectEnd:g,isValidConnection:m=eJ,onReconnectEnd:v,updateConnection:b,getTransform:E,getFromHandle:M,autoPanSpeed:$,dragThreshold:I=1}){let H,P=es(e.target),N=0,{x:z,y:k}=eh(e),T=eU(i,P?.elementFromPoint(z,k)),S=a?.getBoundingClientRect(),A=!1;if(!S||!T)return;let L=eQ(r,T,o,u,t);if(!L)return;let B=eh(e,S),O=!1,V=null,C=!1,D=null,F={...L,nodeId:r,type:T,position:L.position},Y=u.get(r),W={inProgress:!0,isValid:null,from:eS(Y,F,s.Left,!0),fromHandle:F,fromPosition:F.position,fromNode:Y,to:B,toHandle:null,toPosition:w[F.position],toNode:null};function X(){A=!0,b(W),p?.(e,{nodeId:r,handleId:o,handleType:T})}function q(e){var i,a;let s;if(!A){let{x:t,y:n}=eh(e),o=t-z,r=n-k;if(!(o*o+r*r>I*I))return;X()}if(!M()||!F)return void G(e);let y=E();H=function(e,t,n,o){let r=[],i=1/0;for(let l of function(e,t,n){let o=[],r={x:e.x-n,y:e.y-n,width:2*n,height:2*n};for(let e of t.values())j(r,R(e))>0&&o.push(e);return o}(e,n,t+250))for(let n of[...l.internals.handleBounds?.source??[],...l.internals.handleBounds?.target??[]]){if(o.nodeId===n.nodeId&&o.type===n.type&&o.id===n.id)continue;let{x:a,y:s}=eS(l,n,n.position,!0),u=Math.sqrt(Math.pow(a-e.x,2)+Math.pow(s-e.y,2));u>t||(u<i?(r=[{...n,x:a,y:s}],i=u):u===i&&r.push({...n,x:a,y:s}))}if(!r.length)return null;if(r.length>1){let e="source"===o.type?"target":"source";return r.find(t=>t.type===e)??r[0]}return r[0]}(Z(B=eh(e,S),y,!1,[1,1]),n,u,F),O||(!function e(){if(!c||!S)return;let[t,n]=_(B,S,$);f({x:t,y:n}),N=requestAnimationFrame(e)}(),O=!0);let p=e0(e,{handle:H,connectionMode:t,fromNodeId:r,fromHandleId:o,fromType:l?"target":"source",isValidConnection:m,doc:P,lib:d,flowId:h,nodeLookup:u});D=p.handleDomNode,V=p.connection,i=!!H,a=p.isValid,s=null,a?s=!0:i&&!a&&(s=!1),C=s;let x={...W,isValid:C,to:p.toHandle&&C?K({x:p.toHandle.x,y:p.toHandle.y},y):B,toHandle:p.toHandle,toPosition:C&&p.toHandle?p.toHandle.position:w[F.position],toNode:p.toHandle?u.get(p.toHandle.nodeId):null};C&&H&&W.toHandle&&x.toHandle&&W.toHandle.type===x.toHandle.type&&W.toHandle.nodeId===x.toHandle.nodeId&&W.toHandle.id===x.toHandle.id&&W.to.x===x.to.x&&W.to.y===x.to.y||(b(x),W=x)}function G(e){if(A){(H||D)&&V&&C&&x?.(V);let{inProgress:t,...n}=W,o={...n,toPosition:W.toHandle?W.toPosition:null};g?.(e,o),i&&v?.(e,o)}y(),cancelAnimationFrame(N),O=!1,C=!1,V=null,D=null,P.removeEventListener("mousemove",q),P.removeEventListener("mouseup",G),P.removeEventListener("touchmove",q),P.removeEventListener("touchend",G)}0===I&&X(),P.addEventListener("mousemove",q),P.addEventListener("mouseup",G),P.addEventListener("touchmove",q),P.addEventListener("touchend",G)},isValid:e0};function e2({domNode:e,panZoom:t,getTransform:n,getViewScale:o}){let r=(0,c.Lt)(e);return{update:function({translateExtent:e,width:i,height:l,zoomStep:a=10,pannable:s=!0,zoomable:u=!0,inversePan:d=!1}){let c=[0,0],f=(0,h.s_)().on("start",e=>{("mousedown"===e.sourceEvent.type||"touchstart"===e.sourceEvent.type)&&(c=[e.sourceEvent.clientX??e.sourceEvent.touches[0].clientX,e.sourceEvent.clientY??e.sourceEvent.touches[0].clientY])}).on("zoom",s?r=>{let a=n();if("mousemove"!==r.sourceEvent.type&&"touchmove"!==r.sourceEvent.type||!t)return;let s=[r.sourceEvent.clientX??r.sourceEvent.touches[0].clientX,r.sourceEvent.clientY??r.sourceEvent.touches[0].clientY],u=[s[0]-c[0],s[1]-c[1]];c=s;let h=o()*Math.max(a[2],Math.log(a[2]))*(d?-1:1),f={x:a[0]-u[0]*h,y:a[1]-u[1]*h};t.setViewportConstrained({x:f.x,y:f.y,zoom:a[2]},[[0,0],[i,l]],e)}:null).on("zoom.wheel",u?e=>{let o=n();if("wheel"!==e.sourceEvent.type||!t)return;let r=-e.sourceEvent.deltaY*(1===e.sourceEvent.deltaMode?.05:e.sourceEvent.deltaMode?1:.002)*a,i=o[2]*Math.pow(2,r);t.scaleTo(i)}:null);r.call(f,{})},destroy:function(){r.on("zoom",null)},pointer:c.Wn}}let e5=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,e7=e=>({x:e.x,y:e.y,zoom:e.k}),e3=({x:e,y:t,zoom:n})=>h.GS.translate(e,t).scale(n),e4=(e,t)=>e.target.closest(`.${t}`),e6=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),e8=e=>((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2,e9=(e,t=0,n=e8,o=()=>{})=>{let r="number"==typeof t&&t>0;return r||o(),r?e.transition().duration(t).ease(n).on("end",o):e},te=e=>{let t=e.ctrlKey&&U()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t};function tt({domNode:e,minZoom:t,maxZoom:n,paneClickDistance:o,translateExtent:i,viewport:l,onPanZoom:a,onPanZoomStart:s,onPanZoomEnd:u,onDraggingChange:d}){let y={isZoomingOrPanning:!1,usedRightMouseButton:!1,prevViewport:{x:0,y:0,zoom:0},mouseButton:0,timerId:void 0,panScrollTimeout:void 0,isPanScrolling:!1},p=e.getBoundingClientRect(),x=(0,h.s_)().clickDistance(!W(o)||o<0?0:o).scaleExtent([t,n]).translateExtent(i),g=(0,c.Lt)(e).call(x);E({x:l.x,y:l.y,zoom:S(l.zoom,t,n)},[[0,0],[p.width,p.height]],i);let m=g.on("wheel.zoom"),w=g.on("dblclick.zoom");function v(e,t){return g?new Promise(n=>{x?.interpolate(t?.interpolate==="linear"?f.GW:f.p7).transform(e9(g,t?.duration,t?.ease,()=>n(!0)),e)}):Promise.resolve(!1)}function b(){x.on("zoom",null)}async function E(e,t,n){let o=e3(e),r=x?.constrain()(o,t,n);return r&&await v(r),new Promise(e=>e(r))}return x.wheelDelta(te),{update:function({noWheelClassName:e,noPanClassName:t,onPaneContextMenu:n,userSelectionActive:o,panOnScroll:i,panOnDrag:l,panOnScrollMode:h,panOnScrollSpeed:f,preventScrolling:p,zoomOnPinch:v,zoomOnScroll:E,zoomOnDoubleClick:M,zoomActivationKeyPressed:$,lib:I,onTransformChange:H}){o&&!y.isZoomingOrPanning&&b();let P=!i||$||o?function({noWheelClassName:e,preventScrolling:t,d3ZoomHandler:n}){return function(o,r){let i="wheel"===o.type,l=!t&&i&&!o.ctrlKey,a=e4(o,e);if(o.ctrlKey&&i&&a&&o.preventDefault(),l||a)return null;o.preventDefault(),n.call(this,o,r)}}({noWheelClassName:e,preventScrolling:p,d3ZoomHandler:m}):function({zoomPanValues:e,noWheelClassName:t,d3Selection:n,d3Zoom:o,panOnScrollMode:i,panOnScrollSpeed:l,zoomOnPinch:a,onPanZoomStart:s,onPanZoom:u,onPanZoomEnd:d}){return h=>{if(e4(h,t))return!1;h.preventDefault(),h.stopImmediatePropagation();let f=n.property("__zoom").k||1;if(h.ctrlKey&&a){let e=(0,c.Wn)(h),t=f*Math.pow(2,te(h));o.scaleTo(n,t,e,h);return}let y=1===h.deltaMode?20:1,p=i===r.Vertical?0:h.deltaX*y,x=i===r.Horizontal?0:h.deltaY*y;!U()&&h.shiftKey&&i!==r.Vertical&&(p=h.deltaY*y,x=0),o.translateBy(n,-(p/f)*l,-(x/f)*l,{internal:!0});let g=e7(n.property("__zoom"));clearTimeout(e.panScrollTimeout),e.isPanScrolling||(e.isPanScrolling=!0,s?.(h,g)),e.isPanScrolling&&(u?.(h,g),e.panScrollTimeout=setTimeout(()=>{d?.(h,g),e.isPanScrolling=!1},150))}}({zoomPanValues:y,noWheelClassName:e,d3Selection:g,d3Zoom:x,panOnScrollMode:h,panOnScrollSpeed:f,zoomOnPinch:v,onPanZoomStart:s,onPanZoom:a,onPanZoomEnd:u});if(g.on("wheel.zoom",P,{passive:!1}),!o){let e=function({zoomPanValues:e,onDraggingChange:t,onPanZoomStart:n}){return o=>{if(o.sourceEvent?.internal)return;let r=e7(o.transform);e.mouseButton=o.sourceEvent?.button||0,e.isZoomingOrPanning=!0,e.prevViewport=r,o.sourceEvent?.type==="mousedown"&&t(!0),n&&n?.(o.sourceEvent,r)}}({zoomPanValues:y,onDraggingChange:d,onPanZoomStart:s});x.on("start",e);let t=function({zoomPanValues:e,panOnDrag:t,onPaneContextMenu:n,onTransformChange:o,onPanZoom:r}){return i=>{e.usedRightMouseButton=!!(n&&e6(t,e.mouseButton??0)),i.sourceEvent?.sync||o([i.transform.x,i.transform.y,i.transform.k]),r&&!i.sourceEvent?.internal&&r?.(i.sourceEvent,e7(i.transform))}}({zoomPanValues:y,panOnDrag:l,onPaneContextMenu:!!n,onPanZoom:a,onTransformChange:H});x.on("zoom",t);let o=function({zoomPanValues:e,panOnDrag:t,panOnScroll:n,onDraggingChange:o,onPanZoomEnd:r,onPaneContextMenu:i}){return l=>{if(!l.sourceEvent?.internal&&(e.isZoomingOrPanning=!1,i&&e6(t,e.mouseButton??0)&&!e.usedRightMouseButton&&l.sourceEvent&&i(l.sourceEvent),e.usedRightMouseButton=!1,o(!1),r&&e5(e.prevViewport,l.transform))){let t=e7(l.transform);e.prevViewport=t,clearTimeout(e.timerId),e.timerId=setTimeout(()=>{r?.(l.sourceEvent,t)},150*!!n)}}}({zoomPanValues:y,panOnDrag:l,panOnScroll:i,onPaneContextMenu:n,onPanZoomEnd:u,onDraggingChange:d});x.on("end",o)}let N=function({zoomActivationKeyPressed:e,zoomOnScroll:t,zoomOnPinch:n,panOnDrag:o,panOnScroll:r,zoomOnDoubleClick:i,userSelectionActive:l,noWheelClassName:a,noPanClassName:s,lib:u}){return d=>{let c=e||t,h=n&&d.ctrlKey;if(1===d.button&&"mousedown"===d.type&&(e4(d,`${u}-flow__node`)||e4(d,`${u}-flow__edge`)))return!0;if(!o&&!c&&!r&&!i&&!n||l||e4(d,a)&&"wheel"===d.type||e4(d,s)&&("wheel"!==d.type||r&&"wheel"===d.type&&!e)||!n&&d.ctrlKey&&"wheel"===d.type)return!1;if(!n&&"touchstart"===d.type&&d.touches?.length>1)return d.preventDefault(),!1;if(!c&&!r&&!h&&"wheel"===d.type||!o&&("mousedown"===d.type||"touchstart"===d.type)||Array.isArray(o)&&!o.includes(d.button)&&"mousedown"===d.type)return!1;let f=Array.isArray(o)&&o.includes(d.button)||!d.button||d.button<=1;return(!d.ctrlKey||"wheel"===d.type)&&f}}({zoomActivationKeyPressed:$,panOnDrag:l,zoomOnScroll:E,panOnScroll:i,zoomOnDoubleClick:M,zoomOnPinch:v,userSelectionActive:o,noPanClassName:t,noWheelClassName:e,lib:I});x.filter(N),M?g.on("dblclick.zoom",w):g.on("dblclick.zoom",null)},destroy:b,setViewport:async function e(e,t){let n=e3(e);return await v(n,t),new Promise(e=>e(n))},setViewportConstrained:E,getViewport:function(){let e=g?(0,h._V)(g.node()):{x:0,y:0,k:1};return{x:e.x,y:e.y,zoom:e.k}},scaleTo:function(e,t){return g?new Promise(n=>{x?.interpolate(t?.interpolate==="linear"?f.GW:f.p7).scaleTo(e9(g,t?.duration,t?.ease,()=>n(!0)),e)}):Promise.resolve(!1)},scaleBy:function(e,t){return g?new Promise(n=>{x?.interpolate(t?.interpolate==="linear"?f.GW:f.p7).scaleBy(e9(g,t?.duration,t?.ease,()=>n(!0)),e)}):Promise.resolve(!1)},setScaleExtent:function(e){x?.scaleExtent(e)},setTranslateExtent:function(e){x?.translateExtent(e)},syncViewport:function(e){if(g){let t=e3(e),n=g.property("__zoom");(n.k!==e.zoom||n.x!==e.x||n.y!==e.y)&&x?.transform(g,t,null,{sync:!0})}},setClickDistance:function(e){let t=!W(e)||e<0?0:e;x?.clickDistance(t)}}}!function(e){e.Line="line",e.Handle="handle"}(u||(u={}))}}]);