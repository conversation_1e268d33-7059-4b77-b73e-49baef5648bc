# Changelog

## Version 0.9.0

- many functions got overloads for `Matcher` type propagation in less common scenarios;
- `condition` function now accepts Parsers/Matchers with different value types, result value type is the union of the two;
- added type tests for overloads using [expect-type](https://github.com/mmkal/expect-type).

## Version 0.8.0

- Targeting Node.js version 14 and ES2020;
- Now should be discoverable with [denoify](https://github.com/garronej/denoify).

## Version 0.7.0

- `otherwise` function now has two overloads - `Parser * Matcher -> Matcher` and `Parser * Parser -> Parser`;
- `otherwise` function now accepts Parsers/Matchers with different value types, result value type is the union of the two;
- `otherwise` function now has an alias called `eitherOr` which might be more natural for combining parsers.

## Version 0.6.0

- ensure local imports have file extensions - fix "./core module cannot be found" issue.

## Version 0.5.4

- remove terser, source-map files;
- use only `rollup-plugin-cleanup` to condition published files.

## Version 0.5.3

- source-map files;
- minor documentation update.

## Version 0.5.2

- `peek` function keeps Parse<PERSON>/Matcher distinction;

## Version 0.5.1

- documentation updates;
- package marked as free of side effects for tree shaking.

## Version 0.5.0

- Initial release;
- Aiming at Node.js version 12 and up.
