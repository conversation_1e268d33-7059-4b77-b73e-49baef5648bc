"use strict";(()=>{var e={};e.id=8194,e.ids=[8194],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5649:(e,t,s)=>{s.d(t,{y:()=>r});var o=s(68811);class r{constructor(e,t){this.classificationApiKey=e,this.executionId=t}async validateStepOutput(e,t,s,r,a){let i=`As an AI orchestration moderator, evaluate this step output:

Original Request: "${r}"
Role: ${t}
Expected Outcome: ${a}
Actual Output: "${s}"

Evaluate:
1. Does the output fulfill the role's responsibility?
2. Is the quality sufficient for the next step?
3. Are there any issues or gaps?
4. Can we proceed to the next step?

Respond in JSON format:
{
  "isValid": true/false,
  "quality": 0.85,
  "issues": ["list of any issues"],
  "suggestions": ["list of improvements"],
  "canProceed": true/false,
  "reasoning": "detailed explanation"
}`;try{let s=await this.callModerator(i),r=JSON.parse(s);return await (0,o.Zi)(this.executionId,"moderator_commentary",{commentary:`Validating ${t} output: ${r.reasoning}`,validation:r},e,t),r}catch(e){return{isValid:s.length>50,quality:.7,issues:[],suggestions:[],canProceed:!0}}}async resolveConflicts(e,t){let s=`As an AI orchestration moderator, resolve conflicts between multiple AI outputs:

Original Request: "${t}"

Conflicting Outputs:
${e.map((e,t)=>`${t+1}. ${e.roleId} (confidence: ${e.confidence}): "${e.output}"`).join("\n")}

Determine the best approach:
1. Choose the best output
2. Combine elements from multiple outputs
3. Request modifications
4. Escalate for human review

Respond in JSON format:
{
  "action": "proceed|retry|escalate|modify|synthesize",
  "reasoning": "detailed explanation",
  "modifications": "specific changes needed (if action is modify)",
  "confidence": 0.85,
  "nextSteps": ["list of next actions"]
}`;try{let e=await this.callModerator(s),t=JSON.parse(e);return await (0,o.Zi)(this.executionId,"moderator_commentary",{commentary:`Conflict resolution: ${t.reasoning}`,decision:t}),t}catch(s){let t=e.reduce((e,t)=>t.confidence>e.confidence?t:e);return{action:"proceed",reasoning:`Selected ${t.roleId} output with highest confidence (${t.confidence})`,confidence:.6,nextSteps:["continue_with_selected_output"]}}}async synthesizeOutputs(e,t){await (0,o.Zi)(this.executionId,"synthesis_started",{commentary:"\uD83E\uDDE9 Beginning synthesis of all specialist outputs...",totalSteps:e.length});let s=`As an AI orchestration moderator, synthesize multiple specialist outputs into one cohesive response:

Original Request: "${t}"

Specialist Outputs:
${e.map(e=>`${e.stepNumber}. ${e.roleId} (quality: ${e.quality}): "${e.output}"`).join("\n\n")}

Create a comprehensive, well-structured response that:
1. Integrates all valuable insights
2. Maintains logical flow
3. Resolves any contradictions
4. Provides a complete answer to the original request

Respond in JSON format:
{
  "combinedOutput": "the synthesized response",
  "methodology": "how you combined the outputs",
  "qualityScore": 0.92,
  "conflictsResolved": ["list of conflicts resolved"],
  "improvements": ["how the synthesis improved upon individual outputs"]
}`;try{await (0,o.Zi)(this.executionId,"synthesis_progress",{commentary:"\uD83D\uDD04 Analyzing specialist contributions...",progress:.3});let e=await this.callModerator(s);await (0,o.Zi)(this.executionId,"synthesis_progress",{commentary:"\uD83C\uDFA8 Weaving outputs together...",progress:.7});let t=JSON.parse(e);return await (0,o.Zi)(this.executionId,"synthesis_progress",{commentary:"✨ Finalizing synthesized response...",progress:1}),t}catch(t){return{combinedOutput:e.map(e=>`**${e.roleId} Contribution:**
${e.output}`).join("\n\n"),methodology:"Simple concatenation due to synthesis error",qualityScore:.6,conflictsResolved:[],improvements:[]}}}generateLiveCommentary(e,t){let s={orchestration_started:["\uD83C\uDFAC Alright team, we've got an interesting challenge ahead!","\uD83D\uDE80 Let's break this down and see who's best suited for each part.","\uD83C\uDFAF Time to coordinate our AI specialists for optimal results."],task_decomposed:["\uD83D\uDCCB Task analysis complete! I've identified the perfect team composition.","\uD83C\uDFAA Perfect breakdown! Each specialist will handle their area of expertise.","⚡ Task decomposition successful! Ready to assign specialists."],step_assigned:[`📋 Assigning ${t.roleId} specialist to handle this part.`,`🎪 Our ${t.roleId} expert is stepping up to the plate!`,`⚡ Perfect match - ${t.roleId} is exactly what we need here.`],step_started:[`🎬 ${t.roleId} is diving deep into this challenge...`,`⚡ Watch ${t.roleId} work their specialized magic!`,`🎯 ${t.roleId} is laser-focused on delivering excellence.`],step_progress:[`🔥 ${t.roleId} is making great progress...`,`⚙️ The gears are turning smoothly in ${t.roleId}'s domain!`,`🌟 ${t.roleId} is crafting something special...`],step_streaming:[`📡 ${t.roleId} is streaming their thoughts in real-time...`,`⚡ Live updates from ${t.roleId} - watch the magic happen!`,`🌊 ${t.roleId} is flowing with brilliant insights...`],step_completed:[`✅ Excellent work from ${t.roleId}! Moving to the next phase.`,`🎉 ${t.roleId} delivered exactly what we needed. Handoff time!`,`💫 Beautiful execution by ${t.roleId}. The team is flowing perfectly.`],step_failed:[`⚠️ ${t.roleId} hit a snag, but we're adapting quickly!`,`🔄 Minor setback with ${t.roleId} - implementing recovery strategy.`,`🛠️ ${t.roleId} needs a different approach. Adjusting tactics...`],synthesis_started:["\uD83E\uDDE9 Time for the grand finale! I'm combining all these brilliant contributions...","\uD83C\uDFAD Watch as I orchestrate these individual masterpieces into one cohesive symphony!","\uD83C\uDF1F The magic happens now - weaving together all our specialists' expertise!"],synthesis_progress:["\uD83D\uDD04 Synthesis in progress - combining specialist outputs...","\uD83C\uDFA8 Weaving together the brilliant contributions...","⚙️ Processing and harmonizing all the expert insights..."],synthesis_streaming:["\uD83D\uDCE1 Streaming the synthesis process live...","\uD83C\uDF0A Watch the final result take shape in real-time...","⚡ Live synthesis - see how all pieces come together..."],synthesis_complete:["\uD83C\uDF8A Synthesis complete! All specialist outputs have been perfectly combined.","✨ The final masterpiece is ready! What an incredible team effort.","\uD83C\uDFC6 Synthesis successful! The AI team has delivered excellence."],orchestration_completed:["\uD83C\uDF89 What a performance! Our AI team delivered something truly remarkable.","✨ Mission accomplished! Each specialist played their part perfectly.","\uD83C\uDFC6 Outstanding collaboration - this is what AI teamwork looks like!"],orchestration_failed:["⚠️ The orchestration encountered issues, but we're learning from this.","\uD83D\uDD04 Orchestration failed - analyzing what went wrong for next time.","\uD83D\uDEE0️ Technical difficulties occurred - implementing improvements."],moderator_commentary:["\uD83C\uDF99️ Moderator providing guidance and coordination...","\uD83D\uDCCB Quality check and process optimization in progress...","\uD83C\uDFAF Ensuring optimal team coordination and output quality..."],specialist_message:[`💬 ${t.roleId} is sharing insights with the team...`,`🗣️ ${t.roleId} has something important to communicate...`,`📢 ${t.roleId} is contributing to the team discussion...`],moderator_assignment:[`🎯 Moderator assigning ${t.roleId} to the next task...`,`📋 Task delegation: ${t.roleId} is now taking the lead...`,`⚡ ${t.roleId} has been selected for this specialized work...`],specialist_acknowledgment:[`✅ ${t.roleId} acknowledges the assignment and is ready to proceed.`,`👍 ${t.roleId} confirms understanding and begins work.`,`🎯 ${t.roleId} is locked and loaded for this task.`],handoff_message:[`🤝 ${t.roleId} is handing off to the next specialist...`,`📤 ${t.roleId} has completed their part - passing the baton...`,`✨ ${t.roleId} finished beautifully - next specialist incoming...`],clarification_request:[`❓ ${t.roleId} needs clarification to deliver the best results...`,`🤔 ${t.roleId} is asking for more details to optimize their output...`,`💭 ${t.roleId} wants to ensure they understand the requirements perfectly...`],clarification_response:[`💡 Clarification provided - ${t.roleId} now has what they need!`,`✅ Question answered - ${t.roleId} can proceed with confidence.`,`🎯 All clear! ${t.roleId} has the details needed for success.`]}[e]||["\uD83E\uDD16 Processing..."];return s[Math.floor(Math.random()*s.length)]}async callModerator(e){let t=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.classificationApiKey}`},body:JSON.stringify({model:"gemini-2.0-flash-lite",messages:[{role:"system",content:"You are an expert AI orchestration moderator. You coordinate multiple AI specialists, validate their work, resolve conflicts, and synthesize their outputs into cohesive results. Always respond in the requested JSON format."},{role:"user",content:e}],temperature:.2,max_tokens:2e3,response_format:{type:"json_object"}})});if(!t.ok)throw Error(`Moderator API error: ${t.status}`);let s=await t.json(),o=s.choices?.[0]?.message?.content;if(!o)throw Error("Empty moderator response");return o}async analyzeParallelizationOpportunities(e){let t=`Analyze these orchestration steps for parallelization opportunities:

Steps:
${e.map(e=>`${e.stepNumber}. ${e.roleId}: "${e.prompt}" (depends on: ${e.dependencies.join(", ")||"none"})`).join("\n")}

Determine:
1. Which steps can run in parallel
2. Optimal grouping strategy
3. Expected performance improvement

Respond in JSON format:
{
  "parallelGroups": [[1], [2, 3], [4]],
  "reasoning": "explanation of grouping strategy",
  "estimatedSpeedup": 1.8
}`;try{let e=await this.callModerator(t);return JSON.parse(e)}catch(t){return{parallelGroups:e.map(e=>[e.stepNumber]),reasoning:"Sequential execution due to analysis error",estimatedSpeedup:1}}}}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52639:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>I,routeModule:()=>f,serverHooks:()=>x,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>w});var o={};s.r(o),s.d(o,{POST:()=>_});var r=s(96559),a=s(48088),i=s(37719),n=s(32190),l=s(2507),p=s(56534),c=s(45697),d=s(5649),u=s(68811);async function m(e,t,s=3,o){let r;for(let o=1;o<=s;o++)try{return await fetch(e,t)}catch(t){if(r=t,o===s)throw Error(t.message);let e=100*o;await new Promise(t=>setTimeout(t,e))}throw r}async function h(e,t,s,o,r){let a,i,n,l=new Date,p=new Date;try{let c=function(e,t){if(!e)return"";let s=t.toLowerCase(),o=`${s}/`;return e.toLowerCase().startsWith(o)?e.substring(o.length):e}(t,e||""),d=e?.toLowerCase()==="openrouter"?t:c;if(e?.toLowerCase()==="openai"){let{custom_api_config_id:e,role:t,...l}=o,c={...l,model:d,messages:o.messages,stream:o.stream};Object.keys(c).forEach(e=>void 0===c[e]&&delete c[e]);let u={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`,Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify(c)},h=await m("https://api.openai.com/v1/chat/completions",u);if(p=new Date,a=h.status,!h.ok){let e=await h.json().catch(()=>({error:{message:h.statusText}}));throw i={message:`OpenAI Error: ${e?.error?.message||h.statusText}`,status:h.status,provider_error:e}}if(o.stream&&h.body&&r){let e=h.body.getReader(),t=new TextDecoder,s="",o="";try{for(;;){let{done:a,value:i}=await e.read();if(a)break;let l=(o+=t.decode(i,{stream:!0})).split("\n");for(let e of(o=l.pop()||"",l))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),o=e.choices?.[0]?.delta?.content||"";o&&(s+=o,r(o)),e.usage&&(n={...n,usage:e.usage,choices:[{message:{content:s},finish_reason:e.choices?.[0]?.finish_reason||null}]})}catch(e){}}}}finally{e.releaseLock()}n||(n={choices:[{message:{content:s},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:0,total_tokens:0}})}else n=await h.json()}else if(e?.toLowerCase()==="openrouter"){let{custom_api_config_id:e,role:t,...l}=o,c={...l,model:d,messages:o.messages,stream:o.stream,usage:{include:!0}},u={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`,"HTTP-Referer":"https://rokey.app","X-Title":"RoKey Orchestration","User-Agent":"RoKey/1.0 (Orchestration)"},body:JSON.stringify(c)},h=await m("https://openrouter.ai/api/v1/chat/completions",u);if(p=new Date,a=h.status,!h.ok){let e=await h.json().catch(()=>({error:{message:h.statusText}}));throw i={message:`OpenRouter Error: ${e?.error?.message||h.statusText}`,status:h.status,provider_error:e}}if(o.stream&&h.body&&r){let e=h.body.getReader(),t=new TextDecoder,s="",o="";try{for(;;){let{done:a,value:i}=await e.read();if(a)break;let l=(o+=t.decode(i,{stream:!0})).split("\n");for(let e of(o=l.pop()||"",l))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),o=e.choices?.[0]?.delta?.content||"";o&&(s+=o,r(o)),e.usage&&(n={...n,usage:e.usage,choices:[{message:{content:s},finish_reason:e.choices?.[0]?.finish_reason||null}]})}catch(e){}}}}finally{e.releaseLock()}n||(n={choices:[{message:{content:s},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:0,total_tokens:0}})}else n=await h.json()}else if(e?.toLowerCase()==="google"){let e=d?.replace(/^models\//,"")||d,t=o.messages.map(e=>{if("string"==typeof e.content)return{role:e.role,content:e.content};if(Array.isArray(e.content)){let t=e.content.map(e=>"text"===e.type&&"string"==typeof e.text?{type:"text",text:e.text}:"image_url"===e.type&&e.image_url?.url?{type:"image_url",image_url:{url:e.image_url.url}}:null).filter(Boolean);return{role:e.role,content:t}}return{role:e.role,content:"[RoKey: Invalid content structure for Google]"}});if(0===t.length)throw i={message:"No processable message content found for Google provider after filtering.",status:400};let l={model:e,messages:t,stream:o.stream||!1};void 0!==o.temperature&&(l.temperature=o.temperature),void 0!==o.max_tokens&&(l.max_tokens=o.max_tokens),void 0!==o.top_p&&(l.top_p=o.top_p);let c={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`,"User-Agent":"RoKey/1.0 (Orchestration)",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify(l)},u=await m("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",c);if(p=new Date,a=u.status,!u.ok){let e=await u.json().catch(()=>({error:{message:u.statusText}})),t=e?.error?.message||u.statusText;throw Array.isArray(e)&&e[0]?.error?.message&&(t=e[0].error.message),i=503===u.status||429===u.status||t.includes("overloaded")||t.includes("rate limit")?{message:`Google Error: ${t}`,status:u.status,provider_error:e,retryable:!0}:{message:`Google Error: ${t}`,status:u.status,provider_error:e}}if(o.stream&&u.body&&r){let e=u.body.getReader(),t=new TextDecoder,s="",o="";try{for(;;){let{done:a,value:i}=await e.read();if(a)break;let l=(o+=t.decode(i,{stream:!0})).split("\n");for(let e of(o=l.pop()||"",l))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),o=e.choices?.[0]?.delta?.content||"";o&&(s+=o,r(o)),e.usage&&(n={...n,usage:e.usage,choices:[{message:{content:s},finish_reason:e.choices?.[0]?.finish_reason||null}]})}catch(e){}}}}finally{e.releaseLock()}n||(n={choices:[{message:{content:s},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:0,total_tokens:0}})}else n=await u.json()}else if(e?.toLowerCase()==="anthropic"){let e,t=o.max_tokens||2048,l=o.messages.filter(t=>"system"!==t.role||("string"==typeof t.content&&(e=t.content),!1)).map(e=>({role:e.role,content:e.content}));if(0===l.length||"user"!==l[0].role)throw i={message:"Invalid messages format for Anthropic: Must contain at least one user message and start with user after system filter.",status:400};let c={model:d,messages:l,max_tokens:t,stream:o.stream};e&&(c.system=e),void 0!==o.temperature&&(c.temperature=o.temperature),void 0!==o.top_p&&(c.top_p=o.top_p);let u={method:"POST",headers:{"Content-Type":"application/json","x-api-key":s,"anthropic-version":"2023-06-01","User-Agent":"RoKey/1.0 (Orchestration)"},body:JSON.stringify(c)},h=await m("https://api.anthropic.com/v1/messages",u);if(p=new Date,a=h.status,!h.ok){let e=await h.json().catch(()=>({error:{message:h.statusText}}));throw i={message:`Anthropic Error: ${e?.error?.message||h.statusText}`,status:h.status,provider_error:e}}if(o.stream&&h.body&&r){let e=h.body.getReader(),t=new TextDecoder,s="",o="";try{for(;;){let{done:a,value:i}=await e.read();if(a)break;let l=(o+=t.decode(i,{stream:!0})).split("\n");for(let e of(o=l.pop()||"",l))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),o=e.delta?.text||"";o&&(s+=o,r(o)),e.usage&&(n={id:e.id,object:"chat.completion",created:Math.floor(Date.now()/1e3),model:d,choices:[{index:0,message:{role:"assistant",content:s},finish_reason:e.delta?.stop_reason||"stop"}],usage:{prompt_tokens:e.usage?.input_tokens||0,completion_tokens:e.usage?.output_tokens||0,total_tokens:(e.usage?.input_tokens||0)+(e.usage?.output_tokens||0)}})}catch(e){}}}}finally{e.releaseLock()}n||(n={id:`anthropic-${Date.now()}`,object:"chat.completion",created:Math.floor(Date.now()/1e3),model:d,choices:[{index:0,message:{role:"assistant",content:s},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:0,total_tokens:0}})}else{let e=await h.json(),t=e.content?.[0]?.text||"";n={id:e.id,object:"chat.completion",created:Math.floor(Date.now()/1e3),model:d,choices:[{index:0,message:{role:"assistant",content:t},finish_reason:e.stop_reason?.toLowerCase()||"stop"}],usage:{prompt_tokens:e.usage?.input_tokens,completion_tokens:e.usage?.output_tokens,total_tokens:(e.usage?.input_tokens||0)+(e.usage?.output_tokens||0)}}}}else if(e?.toLowerCase()==="deepseek"){let{custom_api_config_id:e,role:t,...l}=o,c={...l,model:d,messages:o.messages,stream:o.stream};Object.keys(c).forEach(e=>void 0===c[e]&&delete c[e]);let u={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`,"User-Agent":"RoKey/1.0 (Orchestration)"},body:JSON.stringify(c)},h=await m("https://api.deepseek.com/v1/chat/completions",u);if(p=new Date,a=h.status,!h.ok){let e=await h.json().catch(()=>({error:{message:h.statusText}}));throw i={message:`DeepSeek Error: ${e?.error?.message||h.statusText}`,status:h.status,provider_error:e}}if(o.stream&&h.body&&r){let e=h.body.getReader(),t=new TextDecoder,s="",o="";try{for(;;){let{done:a,value:i}=await e.read();if(a)break;let l=(o+=t.decode(i,{stream:!0})).split("\n");for(let e of(o=l.pop()||"",l))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),o=e.choices?.[0]?.delta?.content||"";o&&(s+=o,r(o)),e.usage&&(n={...n,usage:e.usage,choices:[{message:{content:s},finish_reason:e.choices?.[0]?.finish_reason||null}]})}catch(e){}}}}finally{e.releaseLock()}n||(n={choices:[{message:{content:s},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:0,total_tokens:0}})}else n=await h.json()}else if(e?.toLowerCase()==="xai"){let{custom_api_config_id:e,role:t,...l}=o,c={...l,model:d,messages:o.messages,stream:o.stream||!1};"number"==typeof o.temperature&&(c.temperature=o.temperature),"number"==typeof o.max_tokens&&(c.max_tokens=o.max_tokens),"number"==typeof o.top_p&&(c.top_p=o.top_p);let u={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`,"User-Agent":"RoKey/1.0 (Orchestration)"},body:JSON.stringify(c)},h=await m("https://api.x.ai/v1/chat/completions",u);if(p=new Date,a=h.status,!h.ok){let e=await h.json().catch(()=>({error:{message:h.statusText}}));throw i={message:`XAI Error: ${e?.error?.message||h.statusText}`,status:h.status,provider_error:e}}if(o.stream&&h.body&&r){let e=h.body.getReader(),t=new TextDecoder,s="",o="";try{for(;;){let{done:a,value:i}=await e.read();if(a)break;let l=(o+=t.decode(i,{stream:!0})).split("\n");for(let e of(o=l.pop()||"",l))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),o=e.choices?.[0]?.delta?.content||"";o&&(s+=o,r(o)),e.usage&&(n={...n,usage:e.usage,choices:[{message:{content:s},finish_reason:e.choices?.[0]?.finish_reason||null}]})}catch(e){}}}}finally{e.releaseLock()}n||(n={choices:[{message:{content:s},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:0,total_tokens:0}})}else n=await h.json()}else throw Error(`Unsupported provider: ${e}`);return{success:!0,response:void 0,responseData:n,status:a,error:i,llmRequestTimestamp:l,llmResponseTimestamp:p}}catch(e){return{success:!1,response:void 0,responseData:void 0,status:a||500,error:e,llmRequestTimestamp:l,llmResponseTimestamp:p}}}let g=c.z.object({executionId:c.z.string().uuid(),stepId:c.z.string().uuid()});async function _(e){let t=await (0,l.createSupabaseServerClientOnRequest)();try{let s,o=await e.json(),r=g.parse(o),{data:a,error:i}=await t.from("orchestration_steps").select("*").eq("id",r.stepId).eq("execution_id",r.executionId).single();if(i||!a)return n.NextResponse.json({error:"Step not found"},{status:404});if("pending"!==a.status)return n.NextResponse.json({error:`Step is in ${a.status} state, not pending`},{status:400});let l=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!l)return n.NextResponse.json({error:"Classification API key not configured"},{status:500});let c=new d.y(l,r.executionId);await t.from("orchestration_steps").update({status:"in_progress",started_at:new Date().toISOString()}).eq("id",a.id);let m={id:crypto.randomUUID(),execution_id:r.executionId,type:"moderator_assignment",timestamp:new Date().toISOString(),data:{message:(0,u.re)("moderator_assignment",a.role_id),step:{number:a.step_number,role:a.role_id,model:a.model_name}},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.tl)(r.executionId,m),await new Promise(e=>setTimeout(e,1e3));let _={id:crypto.randomUUID(),execution_id:r.executionId,type:"specialist_acknowledgment",timestamp:new Date().toISOString(),data:{message:(0,u.re)("specialist_acknowledgment",a.role_id),step:{number:a.step_number,role:a.role_id,model:a.model_name}},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.tl)(r.executionId,_),await new Promise(e=>setTimeout(e,800));let f={id:crypto.randomUUID(),execution_id:r.executionId,type:"step_started",timestamp:new Date().toISOString(),data:{commentary:c.generateLiveCommentary("step_started",{roleId:a.role_id}),step:{number:a.step_number,role:a.role_id,model:a.model_name,estimatedDuration:45e3}},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};if((0,u.tl)(r.executionId,f),!a.api_key_id)return await t.from("orchestration_steps").update({status:"failed",error_message:"No API key assigned to this step"}).eq("id",a.id),n.NextResponse.json({error:"No API key assigned to this step"},{status:400});let{data:y,error:w}=await t.from("api_keys").select("*").eq("id",a.api_key_id).single();if(w||!y)return await t.from("orchestration_steps").update({status:"failed",error_message:"API key not found"}).eq("id",a.id),n.NextResponse.json({error:"API key not found"},{status:404});let x=await (0,p.Y)(y.encrypted_api_key),I=a.prompt;if(I.includes("{{previousOutput}}")){let{data:e}=await t.from("orchestration_steps").select("response").eq("execution_id",a.execution_id).eq("step_number",a.step_number-1).single();I=e&&e.response?I.replace("{{previousOutput}}",e.response):I.replace("{{previousOutput}}","No previous output available")}let b=Date.now(),k=0,v=0,O=0;try{let e,t,o={custom_api_config_id:a.execution_id,messages:[{role:"user",content:I}],stream:!0,temperature:.7,max_tokens:2e3},i={id:crypto.randomUUID(),execution_id:r.executionId,type:"step_progress",timestamp:new Date().toISOString(),data:{commentary:c.generateLiveCommentary("step_progress",{roleId:a.role_id}),progress:.3,status:"Executing model call..."},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.tl)(r.executionId,i);let n="",l=e=>{n+=e;let t={id:crypto.randomUUID(),execution_id:r.executionId,type:"step_streaming",timestamp:new Date().toISOString(),data:{commentary:`💭 ${a.role_id} is thinking...`,progress:Math.min(80,n.length/500*100),status:"Streaming response...",partialResponse:n,deltaContent:e},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.tl)(r.executionId,t)};for(let s=1;s<=3;s++)try{if((e=await h(y.provider,y.predefined_model_id,x,o,l)).success)break;if(t=e.error,e.error?.retryable&&s<3){let t=Math.min(1e3*Math.pow(2,s-1),1e4),o={id:crypto.randomUUID(),execution_id:r.executionId,type:"step_progress",timestamp:new Date().toISOString(),data:{commentary:`🔄 ${a.role_id} encountered temporary overload. Retrying in ${Math.round(t/1e3)}s... (attempt ${s}/3)`,progress:.2,status:`Retrying due to: ${e.error.message}`},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.tl)(r.executionId,o),await new Promise(e=>setTimeout(e,t));continue}throw Error(e.error?.message||"Provider request failed")}catch(o){if(t=o,3===s)throw o;let e=Math.min(1e3*Math.pow(2,s-1),1e4);await new Promise(t=>setTimeout(t,e))}if(!e||!e.success)throw Error(t?.message||"All retry attempts failed");if(e.responseData?.choices?.[0]?.message?.content)s=e.responseData.choices[0].message.content;else if(e.responseData?.content?.[0]?.text)s=e.responseData.content[0].text;else throw Error("No valid response content found");k=e.responseData?.usage?.prompt_tokens||e.responseData?.usage?.input_tokens||0,v=e.responseData?.usage?.completion_tokens||e.responseData?.usage?.output_tokens||0,O=1e-6*k+2e-6*v}catch(s){await t.from("orchestration_steps").update({status:"failed",completed_at:new Date().toISOString(),duration_ms:Date.now()-b,error_message:s instanceof Error?s.message:"Unknown error"}).eq("id",a.id);let e={id:crypto.randomUUID(),execution_id:r.executionId,type:"step_failed",timestamp:new Date().toISOString(),data:{commentary:`❌ ${a.role_id} encountered an issue. Analyzing recovery options...`,error:s instanceof Error?s.message:"Unknown error",retryPlan:"Manual intervention required"},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};return(0,u.tl)(r.executionId,e),n.NextResponse.json({error:"Model execution failed",details:s instanceof Error?s.message:"Unknown error"},{status:500})}let S=Date.now()-b,$=await c.validateStepOutput(a.step_number,a.role_id,s,I,`Expected output for ${a.role_id} role`);await t.from("orchestration_steps").update({status:"completed",completed_at:new Date().toISOString(),duration_ms:S,tokens_in:k,tokens_out:v,cost:O,response:s,prompt:I}).eq("id",a.id);let D={id:crypto.randomUUID(),execution_id:r.executionId,type:"specialist_message",timestamp:new Date().toISOString(),data:{message:(0,u.re)("specialist_message",a.role_id),output:s.length>200?s.substring(0,200)+"...":s,fullOutput:s,duration:S,quality:$.quality},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.tl)(r.executionId,D),await new Promise(e=>setTimeout(e,1200));let T={id:crypto.randomUUID(),execution_id:r.executionId,type:"step_completed",timestamp:new Date().toISOString(),data:{commentary:c.generateLiveCommentary("step_completed",{roleId:a.role_id}),output:s,duration:S,tokens:{input:k,output:v},cost:O,quality:$.quality,validation:$},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.tl)(r.executionId,T);let{data:A}=await t.from("orchestration_executions").select("total_steps, created_at").eq("id",a.execution_id).single();if(A&&a.step_number===A.total_steps){let{data:e}=await t.from("orchestration_steps").select("step_number, role_id, response, prompt").eq("execution_id",a.execution_id).eq("status","completed").order("step_number",{ascending:!0});if(e&&e.length>0){let t=`/api/orchestration/synthesis-stream/${a.execution_id}`,s={id:crypto.randomUUID(),execution_id:a.execution_id,type:"synthesis_started",timestamp:new Date().toISOString(),data:{commentary:c.generateLiveCommentary("synthesis_started",{totalSteps:e.length}),steps:e.length,status:"Synthesizing all specialist outputs...",directStreamUrl:t,synthesisExecutionId:a.execution_id}};(0,u.tl)(a.execution_id,s)}else await t.from("orchestration_executions").update({status:"completed",completed_at:new Date().toISOString()}).eq("id",a.execution_id)}else if(A){await t.from("orchestration_steps").update({status:"pending"}).eq("execution_id",a.execution_id).eq("step_number",a.step_number+1);let{data:e}=await t.from("orchestration_steps").select("*").eq("execution_id",a.execution_id).eq("step_number",a.step_number+1).single();if(e){let t={id:crypto.randomUUID(),execution_id:a.execution_id,type:"handoff_message",timestamp:new Date().toISOString(),data:{message:(0,u.re)("handoff_message",a.role_id),fromRole:a.role_id,toRole:e.role_id,nextStep:{number:e.step_number,role:e.role_id,model:e.model_name}},step_number:a.step_number,role_id:a.role_id,model_name:a.model_name};(0,u.tl)(a.execution_id,t),setTimeout(()=>{fetch(`${process.env.NEXT_PUBLIC_APP_URL||"http://localhost:3000"}/api/orchestration/process-step`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({executionId:a.execution_id,stepId:e.id})}).catch(e=>{})},1500)}}return n.NextResponse.json({success:!0,step:{id:a.id,status:"completed",response:s,duration:S,tokens:{input:k,output:v},cost:O,quality:$.quality}})}catch(e){return n.NextResponse.json({error:"Internal server error",details:String(e)},{status:500})}}let f=new r.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orchestration/process-step/route",pathname:"/api/orchestration/process-step",filename:"route",bundlePath:"app/api/orchestration/process-step/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\orchestration\\process-step\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:y,workUnitAsyncStorage:w,serverHooks:x}=f;function I(){return(0,i.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:w})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},56534:(e,t,s)=>{s.d(t,{Y:()=>p,w:()=>l});let o="AES-GCM",r=process.env.ROKEY_ENCRYPTION_KEY;if(!r||64!==r.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");function a(e){let t=new Uint8Array(e.length/2);for(let s=0;s<e.length;s+=2)t[s/2]=parseInt(e.substr(s,2),16);return t}function i(e){return Array.from(e,e=>e.toString(16).padStart(2,"0")).join("")}let n=a(r);async function l(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=crypto.getRandomValues(new Uint8Array(12)),s=await crypto.subtle.importKey("raw",n,{name:o},!1,["encrypt"]),r=new TextEncoder().encode(e),a=new Uint8Array(await crypto.subtle.encrypt({name:o,iv:t},s,r)),l=a.slice(0,-16),p=a.slice(-16);return`${i(t)}:${i(p)}:${i(l)}`}async function p(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let s=a(t[0]),r=a(t[1]),i=a(t[2]);if(12!==s.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==r.length)throw Error("Invalid authTag length. Expected 16 bytes.");let l=await crypto.subtle.importKey("raw",n,{name:o},!1,["decrypt"]),p=new Uint8Array(i.length+r.length);p.set(i),p.set(r,i.length);let c=await crypto.subtle.decrypt({name:o,iv:s},l,p);return new TextDecoder().decode(c)}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),o=t.X(0,[4447,580,9398,3410,5697,367],()=>s(52639));module.exports=o})();