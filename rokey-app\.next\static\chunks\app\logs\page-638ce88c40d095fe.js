(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1524],{23405:(e,t,s)=>{"use strict";s.d(t,{f:()=>a.A});var a=s(74500)},43760:(e,t,s)=>{Promise.resolve().then(s.bind(s,66233))},66233:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var a=s(95155),r=s(12115);let l=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"}))});var n=s(26784),i=s(5279),o=s(15713);let d=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 7.5 7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5"}))}),c=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))});var m=s(82771);let x=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Zm3.75 11.625a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});var u=s(92975),h=s(10184),g=s(55233),p=s(71848),b=s(38456),y=s(83298);let f=[{label:"Timestamp",field:"request_timestamp",defaultSortOrder:"desc"},{label:"API Model",field:"custom_api_config_id"},{label:"Role Used",field:"role_used"},{label:"Provider",field:"llm_provider_name"},{label:"LLM Model",field:"llm_model_name"},{label:"Status",field:"status_code"},{label:"Latency (LLM)",field:"llm_provider_latency_ms"},{label:"Latency (RoKey)",field:"processing_duration_ms"},{label:"Input Tokens",field:"input_tokens"},{label:"Output Tokens",field:"output_tokens"}];function j(){let{user:e}=(0,y.R)(),[t,s]=(0,r.useState)([]),[j,v]=(0,r.useState)(null),[N,w]=(0,r.useState)(!0),[_,k]=(0,r.useState)(!0),[A,S]=(0,r.useState)(null),[C,L]=(0,r.useState)([]),E={startDate:"",endDate:"",customApiConfigId:"all",status:"all"},[M,P]=(0,r.useState)(E),[D,O]=(0,r.useState)(!1),[I,H]=(0,r.useState)({}),[Z,R]=(0,r.useState)(!1),[B,F]=(0,r.useState)(null),[V,q]=(0,r.useState)({sortBy:"request_timestamp",sortOrder:"desc"}),T=async()=>{k(!0);try{let e=await fetch("/api/custom-configs");if(!e.ok)throw Error("Failed to fetch API model configurations");let t=await e.json();L(t);let s={};t.forEach(e=>{s[e.id]=e.name}),H(s)}catch(e){S("Error fetching configurations: ".concat(e.message))}finally{k(!1)}},z=(0,r.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1?arguments[1]:void 0,a=arguments.length>2?arguments[2]:void 0;w(!0),S(null);try{let r={page:e.toString(),pageSize:"10",sortBy:a.sortBy,sortOrder:a.sortOrder};t.startDate&&(r.startDate=new Date(t.startDate).toISOString()),t.endDate&&(r.endDate=new Date(t.endDate).toISOString()),"all"!==t.customApiConfigId&&(r.customApiConfigId=t.customApiConfigId),"all"!==t.status&&(r.status=t.status);let l=await fetch("/api/logs?".concat(new URLSearchParams(r).toString()));if(!l.ok){let e=await l.json();throw Error(e.error||e.details||"Failed to fetch logs")}let n=await l.json();s(n.logs||[]),v(n.pagination||null)}catch(e){S(e.message),s([]),v(null)}finally{w(!1)}},[]);(0,r.useEffect)(()=>{if(e){let e=setTimeout(()=>{T(),z(1,M,V)},100);return()=>clearTimeout(e)}null===e&&(w(!1),k(!1))},[z,M,V,e]);let W=e=>{P(t=>({...t,[e.target.name]:e.target.value}))},U=e=>{e>0&&(!j||e<=j.totalPages)&&z(e,M,V)},K=e=>{let t=V.sortBy===e&&"asc"===V.sortOrder?"desc":"asc",s={sortBy:e,sortOrder:t};q(s),z(1,M,s)},G=e=>{F(e),R(!0)},J=e=>null===e?"bg-gray-600 text-gray-100":e>=200&&e<300?"bg-green-600 text-green-100":e>=400?"bg-red-600 text-red-100":"bg-yellow-500 text-yellow-100",Q=e=>{let{column:t}=e,s=V.sortBy===t.field;return(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:(0,a.jsxs)("button",{onClick:()=>K(t.field),className:"flex items-center space-x-2 hover:text-white transition-colors duration-200 group",children:[(0,a.jsx)("span",{children:t.label}),s?"asc"===V.sortOrder?(0,a.jsx)(o.A,{className:"h-4 w-4 text-cyan-400"}):(0,a.jsx)(n.A,{className:"h-4 w-4 text-cyan-400"}):(0,a.jsx)(d,{className:"h-4 w-4 text-gray-500 group-hover:text-gray-300"})]})})};return(0,a.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,a.jsx)("div",{className:"border-b border-gray-800/50",children:(0,a.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-8",children:(0,a.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Request Logs"})}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("button",{onClick:()=>O(!D),className:"px-4 py-2 rounded-lg border transition-all duration-200 flex items-center space-x-2 ".concat(D?"bg-cyan-500 text-white border-cyan-500":"bg-gray-900/50 text-gray-300 border-gray-700 hover:border-gray-600"),children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:D?"Hide Filters":"Show Filters"})]})})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h2",{className:"text-lg mb-2",children:(0,a.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400",children:"Monitor and analyze your API request history"})}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Track performance, debug issues, and optimize your routing configurations"})]})]})}),(0,a.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[A&&(0,a.jsx)("div",{className:"bg-red-900/20 border border-red-800 rounded-lg p-6 mb-8",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,a.jsx)("p",{className:"text-red-400",children:A})]})}),D&&(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 mb-8 animate-scale-in",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Filter Logs"}),(0,a.jsx)("p",{className:"text-gray-400 mt-1",children:"Narrow down your search results"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Real-time updates"})]})]})}),(0,a.jsxs)("form",{onSubmit:e=>{null==e||e.preventDefault(),z(1,M,V)},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[(0,a.jsx)(c,{className:"h-4 w-4 inline mr-1"}),"Start Date"]}),(0,a.jsx)("input",{type:"date",name:"startDate",value:M.startDate,onChange:W,className:"w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[(0,a.jsx)(c,{className:"h-4 w-4 inline mr-1"}),"End Date"]}),(0,a.jsx)("input",{type:"date",name:"endDate",value:M.endDate,onChange:W,className:"w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[(0,a.jsx)(l,{className:"h-4 w-4 inline mr-1"}),"Configuration"]}),(0,a.jsxs)("select",{name:"customApiConfigId",value:M.customApiConfigId,onChange:W,disabled:_,className:"w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Configurations"}),C.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[(0,a.jsx)(x,{className:"h-4 w-4 inline mr-1"}),"Status"]}),(0,a.jsxs)("select",{name:"status",value:M.status,onChange:W,className:"w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"all",children:"All Statuses"}),(0,a.jsx)("option",{value:"success",children:"Success (2xx)"}),(0,a.jsx)("option",{value:"error",children:"Error (4xx/5xx)"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsxs)("button",{type:"button",onClick:()=>{P(E),z(1,E,V)},className:"px-4 py-2 bg-gray-800/50 text-gray-300 border border-gray-700 rounded-lg hover:border-gray-600 transition-all duration-200 flex items-center space-x-2",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Clear Filters"})]}),(0,a.jsxs)("button",{type:"submit",className:"px-4 py-2 bg-cyan-500 text-white rounded-lg hover:bg-cyan-600 transition-all duration-200 flex items-center space-x-2",children:[(0,a.jsx)(x,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Apply Filters"})]})]})]})]}),N&&(0,a.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 border border-gray-800/50",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-800 rounded w-1/4"}),(0,a.jsx)("div",{className:"space-y-3",children:[...Array(8)].map((e,t)=>(0,a.jsx)("div",{className:"grid grid-cols-11 gap-4",children:[...Array(11)].map((e,t)=>(0,a.jsx)("div",{className:"h-4 bg-gray-800 rounded"},t))},t))})]})}),!N&&!t.length&&!A&&(0,a.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-12 border border-gray-800/50 text-center",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gray-800/50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-gray-700",children:(0,a.jsx)(u.A,{className:"h-8 w-8 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Logs Found"}),(0,a.jsx)("p",{className:"text-gray-400 mb-6",children:"No request logs match your criteria. Once you make requests to the unified API, they will appear here."})]})}),!N&&t.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full text-sm",children:[(0,a.jsx)("thead",{className:"bg-gray-800/50 border-b border-gray-700",children:(0,a.jsxs)("tr",{children:[f.map(e=>(0,a.jsx)(Q,{column:e},e.field)),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Multimodal"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-700 bg-gray-900/30",children:t.map((e,t)=>(0,a.jsxs)("tr",{className:"hover:bg-gray-800/30 transition-colors duration-200 animate-slide-in",style:{animationDelay:"".concat(50*t,"ms")},children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-white",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)("span",{children:new Date(e.request_timestamp).toLocaleString()})]})}),(0,a.jsx)("td",{className:"px-6 py-4 text-white",children:(0,a.jsx)("div",{className:"font-medium",children:e.custom_api_config_id?I[e.custom_api_config_id]||e.custom_api_config_id.substring(0,8)+"...":"N/A"})}),(0,a.jsx)("td",{className:"px-6 py-4 text-white",children:(0,a.jsx)("div",{className:"flex items-center",children:(()=>{let t=(0,b.gI)(e.role_used);return(0,a.jsx)("span",{className:(0,b.Rf)(t.type),children:t.text})})()})}),(0,a.jsx)("td",{className:"px-6 py-4 text-white",children:(0,a.jsx)("span",{className:"font-medium",children:(0,b.aU)(e.llm_provider_name)})}),(0,a.jsx)("td",{className:"px-6 py-4 text-white truncate max-w-xs",title:(0,b.Il)(e.llm_model_name),children:(0,a.jsx)("span",{className:"font-medium",children:(0,b.Il)(e.llm_model_name)})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat(J(e.status_code)),children:e.status_code||"N/A"})}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-white",children:null!==e.llm_provider_latency_ms?"".concat(e.llm_provider_latency_ms," ms"):"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-white",children:null!==e.processing_duration_ms?"".concat(e.processing_duration_ms," ms"):"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-white",children:null!==e.input_tokens?e.input_tokens.toLocaleString():"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-white",children:null!==e.output_tokens?e.output_tokens.toLocaleString():"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-center",children:e.is_multimodal?(0,a.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full inline-block"}):(0,a.jsx)("span",{className:"w-2 h-2 bg-gray-500 rounded-full inline-block"})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("button",{onClick:()=>G(e),className:"p-2 text-gray-400 hover:text-cyan-400 hover:bg-gray-800/50 rounded-lg transition-all duration-200",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})})]},e.id))})]})})}),j&&j.totalPages>1&&(0,a.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-4 border border-gray-800/50 mt-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-400",children:["Showing ",(0,a.jsx)("span",{className:"font-medium text-white",children:(j.currentPage-1)*j.pageSize+1}),t.length>0?" - ".concat(Math.min(j.currentPage*j.pageSize,j.totalCount)):""," ","of ",(0,a.jsx)("span",{className:"font-medium text-white",children:j.totalCount})," logs"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>U(j.currentPage-1),disabled:j.currentPage<=1||N,className:"px-3 py-1 text-sm bg-gray-800/50 text-gray-300 border border-gray-700 rounded hover:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:"Previous"}),(0,a.jsxs)("span",{className:"px-3 py-1 text-sm text-gray-400",children:["Page ",j.currentPage," of ",j.totalPages]}),(0,a.jsx)("button",{onClick:()=>U(j.currentPage+1),disabled:j.currentPage>=j.totalPages||N,className:"px-3 py-1 text-sm bg-gray-800/50 text-gray-300 border border-gray-700 rounded hover:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:"Next"})]})]})})]}),Z&&B&&(0,a.jsx)(p.A,{log:B,onClose:()=>{R(!1),F(null)},apiConfigNameMap:I})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8888,1459,5738,2396,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(43760)),_N_E=e.O()}]);