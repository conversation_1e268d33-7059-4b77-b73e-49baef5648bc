"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6703],{86973:(e,t,s)=>{s.d(t,{globalCache:()=>i});class a{set(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{ttl:a=3e5,tags:i=[],priority:c="medium",serialize:r=!1}=s,h={data:r?JSON.parse(JSON.stringify(t)):t,timestamp:Date.now(),ttl:a,accessCount:0,lastAccessed:Date.now(),tags:i,priority:c};this.cache.size>=this.maxSize&&this.evictLeastUsed(),this.cache.set(e,h)}get(e){let t=this.cache.get(e);return t?this.isExpired(t)?(this.cache.delete(e),null):(t.accessCount++,t.lastAccessed=Date.now(),t.data):null}getStale(e){let t=this.cache.get(e);if(!t)return{data:null,isStale:!1};let s=this.isExpired(t);return t.accessCount++,t.lastAccessed=Date.now(),{data:t.data,isStale:s}}has(e){let t=this.cache.get(e);return!!t&&(!this.isExpired(t)||(this.cache.delete(e),!1))}delete(e){return this.cache.delete(e)}invalidateByTags(e){let t=0;for(let[s,a]of this.cache.entries())a.tags.some(t=>e.includes(t))&&(this.cache.delete(s),t++);return t}clear(){this.cache.clear()}getStats(){let e=Array.from(this.cache.values()),t=Date.now();return{size:this.cache.size,maxSize:this.maxSize,hitRate:this.calculateHitRate(),averageAge:e.reduce((e,s)=>e+(t-s.timestamp),0)/e.length||0,totalAccesses:e.reduce((e,t)=>e+t.accessCount,0),expiredEntries:e.filter(e=>this.isExpired(e)).length,priorityDistribution:{high:e.filter(e=>"high"===e.priority).length,medium:e.filter(e=>"medium"===e.priority).length,low:e.filter(e=>"low"===e.priority).length}}}async preload(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=this.get(e);if(a)return this.backgroundRefresh(e,t,s),a;let i=await t();return this.set(e,i,s),i}async backgroundRefresh(e,t,s){try{let a=await t();this.set(e,a,s)}catch(e){}}isExpired(e){return Date.now()-e.timestamp>e.ttl}evictLeastUsed(){if(0===this.cache.size)return;let[e]=Array.from(this.cache.entries()).sort((e,t)=>{let[,s]=e,[,a]=t,i={low:0,medium:1,high:2},c=i[s.priority]-i[a.priority];return 0!==c?c:s.lastAccessed-a.lastAccessed})[0];this.cache.delete(e)}calculateHitRate(){let e=Array.from(this.cache.values()).reduce((e,t)=>e+t.accessCount,0);return e>0?this.cache.size/e*100:0}startCleanup(){this.cleanupInterval=setInterval(()=>{this.cleanup()},6e4)}cleanup(){Date.now();let e=[];for(let[t,s]of this.cache.entries())this.isExpired(s)&&e.push(t);e.forEach(e=>this.cache.delete(e)),e.length}destroy(){this.cleanupInterval&&clearInterval(this.cleanupInterval),this.cache.clear()}constructor(e=100){this.cache=new Map,this.cleanupInterval=null,this.maxSize=e,this.startCleanup()}}let i=new a(200)}}]);