"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9061],{33871:(e,t,n)=>{n.d(t,{pP:()=>M,iQ:()=>R,K7:()=>k,Rb:()=>H,Mk:()=>S});var r=n(38530),o=n(12115);function i(e){return e.nativeEvent=e,e.isDefaultPrevented=()=>e.defaultPrevented,e.isPropagationStopped=()=>e.cancelBubble,e.persist=()=>{},e}function s(e){let t=(0,o.useRef)({isFocused:!1,observer:null});(0,r.Nf)(()=>{let e=t.current;return()=>{e.observer&&(e.observer.disconnect(),e.observer=null)}},[]);let n=(0,r.Jt)(t=>{null==e||e(t)});return(0,o.useCallback)(e=>{if(e.target instanceof HTMLButtonElement||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement){t.current.isFocused=!0;let r=e.target;r.addEventListener("focusout",e=>{t.current.isFocused=!1,r.disabled&&n(i(e)),t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)},{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&r.disabled){var e;null==(e=t.current.observer)||e.disconnect();let n=r===document.activeElement?null:document.activeElement;r.dispatchEvent(new FocusEvent("blur",{relatedTarget:n})),r.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:n}))}}),t.current.observer.observe(r,{attributes:!0,attributeFilter:["disabled"]})}},[n])}new WeakMap,o.createContext({register:()=>{}}).displayName="PressResponderContext",n(82967),n(34896),n(70405),n(47650);var u=new WeakMap;function a(e){return"A"===e.tagName&&e.hasAttribute("href")}Symbol("linkClicked");let c=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function l(e,t){return"checkbox"===e.type||"radio"===e.type?" "===t:c.has(e.type)}n(95269);let d=null,f=new Set,p=new Map,v=!1,E=!1,b={Tab:!0,Escape:!0};function g(e,t){for(let n of f)n(e,t)}function h(e){v=!0,e.metaKey||!(0,r.Lz)()&&e.altKey||e.ctrlKey||"Control"===e.key||"Shift"===e.key||"Meta"===e.key||(d="keyboard",g("keyboard",e))}function T(e){d="pointer",("mousedown"===e.type||"pointerdown"===e.type)&&(v=!0,g("pointer",e))}function m(e){(0,r.YF)(e)&&(v=!0,d="virtual")}function y(e){e.target!==window&&e.target!==document&&e.isTrusted&&(v||E||(d="virtual",g("virtual",e)),v=!1,E=!1)}function w(){v=!1,E=!0}function L(e){if("undefined"==typeof window||"undefined"==typeof document||p.get((0,r.mD)(e)))return;let t=(0,r.mD)(e),n=(0,r.TW)(e),o=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){v=!0,o.apply(this,arguments)},n.addEventListener("keydown",h,!0),n.addEventListener("keyup",h,!0),n.addEventListener("click",m,!0),t.addEventListener("focus",y,!0),t.addEventListener("blur",w,!1),"undefined"!=typeof PointerEvent&&(n.addEventListener("pointerdown",T,!0),n.addEventListener("pointermove",T,!0),n.addEventListener("pointerup",T,!0)),t.addEventListener("beforeunload",()=>{F(e)},{once:!0}),p.set(t,{focus:o})}let F=(e,t)=>{let n=(0,r.mD)(e),o=(0,r.TW)(e);t&&o.removeEventListener("DOMContentLoaded",t),p.has(n)&&(n.HTMLElement.prototype.focus=p.get(n).focus,o.removeEventListener("keydown",h,!0),o.removeEventListener("keyup",h,!0),o.removeEventListener("click",m,!0),n.removeEventListener("focus",y,!0),n.removeEventListener("blur",w,!1),"undefined"!=typeof PointerEvent&&(o.removeEventListener("pointerdown",T,!0),o.removeEventListener("pointermove",T,!0),o.removeEventListener("pointerup",T,!0)),p.delete(n))};function M(){return"pointer"!==d}"undefined"!=typeof document&&function(e){let t,n=(0,r.TW)(void 0);"loading"!==n.readyState?L(void 0):(t=()=>{L(e)},n.addEventListener("DOMContentLoaded",t)),()=>F(e,t)}();let C=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function k(e,t,n){L(),(0,o.useEffect)(()=>{let t=(t,o)=>{(function(e,t,n){let o=(0,r.TW)(null==n?void 0:n.target),i="undefined"!=typeof window?(0,r.mD)(null==n?void 0:n.target).HTMLInputElement:HTMLInputElement,s="undefined"!=typeof window?(0,r.mD)(null==n?void 0:n.target).HTMLTextAreaElement:HTMLTextAreaElement,u="undefined"!=typeof window?(0,r.mD)(null==n?void 0:n.target).HTMLElement:HTMLElement,a="undefined"!=typeof window?(0,r.mD)(null==n?void 0:n.target).KeyboardEvent:KeyboardEvent;return!((e=e||o.activeElement instanceof i&&!C.has(o.activeElement.type)||o.activeElement instanceof s||o.activeElement instanceof u&&o.activeElement.isContentEditable)&&"keyboard"===t&&n instanceof a&&!b[n.key])})(!!(null==n?void 0:n.isTextInput),t,o)&&e(M())};return f.add(t),()=>{f.delete(t)}},t)}function R(e){let{isDisabled:t,onFocus:n,onBlur:i,onFocusChange:u}=e,a=(0,o.useCallback)(e=>{if(e.target===e.currentTarget)return i&&i(e),u&&u(!1),!0},[i,u]),c=s(a),l=(0,o.useCallback)(e=>{let t=(0,r.TW)(e.target),o=t?(0,r.bq)(t):(0,r.bq)();e.target===e.currentTarget&&o===(0,r.wt)(e.nativeEvent)&&(n&&n(e),u&&u(!0),c(e))},[u,n,c]);return{focusProps:{onFocus:!t&&(n||u||i)?l:void 0,onBlur:!t&&(i||u)?a:void 0}}}function H(e){let{isDisabled:t,onBlurWithin:n,onFocusWithin:u,onFocusWithinChange:a}=e,c=(0,o.useRef)({isFocusWithin:!1}),{addGlobalListener:l,removeAllGlobalListeners:d}=(0,r.A5)(),f=(0,o.useCallback)(e=>{e.currentTarget.contains(e.target)&&c.current.isFocusWithin&&!e.currentTarget.contains(e.relatedTarget)&&(c.current.isFocusWithin=!1,d(),n&&n(e),a&&a(!1))},[n,a,c,d]),p=s(f),v=(0,o.useCallback)(e=>{if(!e.currentTarget.contains(e.target))return;let t=(0,r.TW)(e.target),n=(0,r.bq)(t);if(!c.current.isFocusWithin&&n===(0,r.wt)(e.nativeEvent)){u&&u(e),a&&a(!0),c.current.isFocusWithin=!0,p(e);let n=e.currentTarget;l(t,"focus",e=>{if(c.current.isFocusWithin&&!(0,r.sD)(n,e.target)){let r=new t.defaultView.FocusEvent("blur",{relatedTarget:e.target});Object.defineProperty(r,"target",{value:n}),Object.defineProperty(r,"currentTarget",{value:n}),f(i(r))}},{capture:!0})}},[u,a,p,l,f]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:v,onBlur:f}}}let P=!1,W=0;function N(e){"touch"===e.pointerType&&(P=!0,setTimeout(()=>{P=!1},50))}function D(){if("undefined"!=typeof document)return"undefined"!=typeof PointerEvent&&document.addEventListener("pointerup",N),W++,()=>{--W>0||"undefined"!=typeof PointerEvent&&document.removeEventListener("pointerup",N)}}function S(e){let{onHoverStart:t,onHoverChange:n,onHoverEnd:i,isDisabled:s}=e,[u,a]=(0,o.useState)(!1),c=(0,o.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,o.useEffect)(D,[]);let{addGlobalListener:l,removeAllGlobalListeners:d}=(0,r.A5)(),{hoverProps:f,triggerHoverEnd:p}=(0,o.useMemo)(()=>{let e=(e,i)=>{if(c.pointerType=i,s||"touch"===i||c.isHovered||!e.currentTarget.contains(e.target))return;c.isHovered=!0;let u=e.currentTarget;c.target=u,l((0,r.TW)(e.target),"pointerover",e=>{c.isHovered&&c.target&&!(0,r.sD)(c.target,e.target)&&o(e,e.pointerType)},{capture:!0}),t&&t({type:"hoverstart",target:u,pointerType:i}),n&&n(!0),a(!0)},o=(e,t)=>{let r=c.target;c.pointerType="",c.target=null,"touch"!==t&&c.isHovered&&r&&(c.isHovered=!1,d(),i&&i({type:"hoverend",target:r,pointerType:t}),n&&n(!1),a(!1))},u={};return"undefined"!=typeof PointerEvent&&(u.onPointerEnter=t=>{P&&"mouse"===t.pointerType||e(t,t.pointerType)},u.onPointerLeave=e=>{!s&&e.currentTarget.contains(e.target)&&o(e,e.pointerType)}),{hoverProps:u,triggerHoverEnd:o}},[t,n,i,s,c,l,d]);return(0,o.useEffect)(()=>{s&&p({currentTarget:c.target},c.pointerType)},[s]),{hoverProps:f,isHovered:u}}},49061:(e,t,n)=>{n.d(t,{og:()=>f}),n(38530);var r=n(33871),o=n(12115);function i(e){return e[0].parentElement}function s(e,t){return!!e&&!!t&&t.some(t=>t.contains(e))}function u(e,t=!1){if(null==e||t){if(null!=e)try{e.focus()}catch{}}else try{$cgawC$focusSafely(e)}catch{}}function a(e,t,n){let r=(null==t?void 0:t.tabbable)?$cgawC$isTabbable:$cgawC$isFocusable,o=$cgawC$getOwnerDocument((null==e?void 0:e.nodeType)===Node.ELEMENT_NODE?e:null),i=$cgawC$createShadowTreeWalker(o,e||o,NodeFilter.SHOW_ELEMENT,{acceptNode(e){var o;return(null==t||null==(o=t.from)?void 0:o.contains(e))?NodeFilter.FILTER_REJECT:r(e)&&$645f2e67b85a24c9$export$e989c0fffaa6b27a(e)&&(!n||s(e,n))&&(!(null==t?void 0:t.accept)||t.accept(e))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});return(null==t?void 0:t.from)&&(i.currentNode=t.from),i}class c{get size(){return this.fastMap.size}getTreeNode(e){return this.fastMap.get(e)}addTreeNode(e,t,n){let r=this.fastMap.get(null!=t?t:null);if(!r)return;let o=new l({scopeRef:e});r.addChild(o),o.parent=r,this.fastMap.set(e,o),n&&(o.nodeToRestore=n)}addNode(e){this.fastMap.set(e.scopeRef,e)}removeTreeNode(e){if(null===e)return;let t=this.fastMap.get(e);if(!t)return;let n=t.parent;for(let e of this.traverse())e!==t&&t.nodeToRestore&&e.nodeToRestore&&t.scopeRef&&t.scopeRef.current&&s(e.nodeToRestore,t.scopeRef.current)&&(e.nodeToRestore=t.nodeToRestore);let r=t.children;n&&(n.removeChild(t),r.size>0&&r.forEach(e=>n&&n.addChild(e))),this.fastMap.delete(t.scopeRef)}*traverse(e=this.root){if(null!=e.scopeRef&&(yield e),e.children.size>0)for(let t of e.children)yield*this.traverse(t)}clone(){var e,t;let n=new c;for(let r of this.traverse())n.addTreeNode(r.scopeRef,null!=(t=null==(e=r.parent)?void 0:e.scopeRef)?t:null,r.nodeToRestore);return n}constructor(){this.fastMap=new Map,this.root=new l({scopeRef:null}),this.fastMap.set(null,this.root)}}class l{addChild(e){this.children.add(e),e.parent=this}removeChild(e){this.children.delete(e),e.parent=void 0}constructor(e){this.children=new Set,this.contain=!1,this.scopeRef=e.scopeRef}}let d=new c;function f(e={}){let{autoFocus:t=!1,isTextInput:n,within:i}=e,s=(0,o.useRef)({isFocused:!1,isFocusVisible:t||(0,r.pP)()}),[u,a]=(0,o.useState)(!1),[c,l]=(0,o.useState)(()=>s.current.isFocused&&s.current.isFocusVisible),d=(0,o.useCallback)(()=>l(s.current.isFocused&&s.current.isFocusVisible),[]),p=(0,o.useCallback)(e=>{s.current.isFocused=e,a(e),d()},[d]);(0,r.K7)(e=>{s.current.isFocusVisible=e,d()},[],{isTextInput:n});let{focusProps:v}=(0,r.iQ)({isDisabled:i,onFocusChange:p}),{focusWithinProps:E}=(0,r.Rb)({isDisabled:!i,onFocusWithinChange:p});return{isFocused:u,isFocusVisible:c,focusProps:i?E:v}}n(52596)},95269:(e,t,n)=>{var r=n(12115);let o={prefix:String(Math.round(1e10*Math.random())),current:0},i=r.createContext(o),s=r.createContext(!1);"undefined"!=typeof window&&window.document&&window.document.createElement;let u=new WeakMap;r.useId}}]);