"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/domutils";
exports.ids = ["vendor-chunks/domutils"];
exports.modules = {

/***/ "(rsc)/../node_modules/domutils/lib/esm/feeds.js":
/*!*************************************************!*\
  !*** ../node_modules/domutils/lib/esm/feeds.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFeed: () => (/* binding */ getFeed)\n/* harmony export */ });\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/../node_modules/domutils/lib/esm/stringify.js\");\n/* harmony import */ var _legacy_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./legacy.js */ \"(rsc)/../node_modules/domutils/lib/esm/legacy.js\");\n\n\n/**\n * Get the feed object from the root of a DOM tree.\n *\n * @category Feeds\n * @param doc - The DOM to to extract the feed from.\n * @returns The feed.\n */\nfunction getFeed(doc) {\n    const feedRoot = getOneElement(isValidFeed, doc);\n    return !feedRoot\n        ? null\n        : feedRoot.name === \"feed\"\n            ? getAtomFeed(feedRoot)\n            : getRssFeed(feedRoot);\n}\n/**\n * Parse an Atom feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getAtomFeed(feedRoot) {\n    var _a;\n    const childs = feedRoot.children;\n    const feed = {\n        type: \"atom\",\n        items: (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(\"entry\", childs).map((item) => {\n            var _a;\n            const { children } = item;\n            const entry = { media: getMediaElements(children) };\n            addConditionally(entry, \"id\", \"id\", children);\n            addConditionally(entry, \"title\", \"title\", children);\n            const href = (_a = getOneElement(\"link\", children)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n            if (href) {\n                entry.link = href;\n            }\n            const description = fetch(\"summary\", children) || fetch(\"content\", children);\n            if (description) {\n                entry.description = description;\n            }\n            const pubDate = fetch(\"updated\", children);\n            if (pubDate) {\n                entry.pubDate = new Date(pubDate);\n            }\n            return entry;\n        }),\n    };\n    addConditionally(feed, \"id\", \"id\", childs);\n    addConditionally(feed, \"title\", \"title\", childs);\n    const href = (_a = getOneElement(\"link\", childs)) === null || _a === void 0 ? void 0 : _a.attribs[\"href\"];\n    if (href) {\n        feed.link = href;\n    }\n    addConditionally(feed, \"description\", \"subtitle\", childs);\n    const updated = fetch(\"updated\", childs);\n    if (updated) {\n        feed.updated = new Date(updated);\n    }\n    addConditionally(feed, \"author\", \"email\", childs, true);\n    return feed;\n}\n/**\n * Parse a RSS feed.\n *\n * @param feedRoot The root of the feed.\n * @returns The parsed feed.\n */\nfunction getRssFeed(feedRoot) {\n    var _a, _b;\n    const childs = (_b = (_a = getOneElement(\"channel\", feedRoot.children)) === null || _a === void 0 ? void 0 : _a.children) !== null && _b !== void 0 ? _b : [];\n    const feed = {\n        type: feedRoot.name.substr(0, 3),\n        id: \"\",\n        items: (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(\"item\", feedRoot.children).map((item) => {\n            const { children } = item;\n            const entry = { media: getMediaElements(children) };\n            addConditionally(entry, \"id\", \"guid\", children);\n            addConditionally(entry, \"title\", \"title\", children);\n            addConditionally(entry, \"link\", \"link\", children);\n            addConditionally(entry, \"description\", \"description\", children);\n            const pubDate = fetch(\"pubDate\", children) || fetch(\"dc:date\", children);\n            if (pubDate)\n                entry.pubDate = new Date(pubDate);\n            return entry;\n        }),\n    };\n    addConditionally(feed, \"title\", \"title\", childs);\n    addConditionally(feed, \"link\", \"link\", childs);\n    addConditionally(feed, \"description\", \"description\", childs);\n    const updated = fetch(\"lastBuildDate\", childs);\n    if (updated) {\n        feed.updated = new Date(updated);\n    }\n    addConditionally(feed, \"author\", \"managingEditor\", childs, true);\n    return feed;\n}\nconst MEDIA_KEYS_STRING = [\"url\", \"type\", \"lang\"];\nconst MEDIA_KEYS_INT = [\n    \"fileSize\",\n    \"bitrate\",\n    \"framerate\",\n    \"samplingrate\",\n    \"channels\",\n    \"duration\",\n    \"height\",\n    \"width\",\n];\n/**\n * Get all media elements of a feed item.\n *\n * @param where Nodes to search in.\n * @returns Media elements.\n */\nfunction getMediaElements(where) {\n    return (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(\"media:content\", where).map((elem) => {\n        const { attribs } = elem;\n        const media = {\n            medium: attribs[\"medium\"],\n            isDefault: !!attribs[\"isDefault\"],\n        };\n        for (const attrib of MEDIA_KEYS_STRING) {\n            if (attribs[attrib]) {\n                media[attrib] = attribs[attrib];\n            }\n        }\n        for (const attrib of MEDIA_KEYS_INT) {\n            if (attribs[attrib]) {\n                media[attrib] = parseInt(attribs[attrib], 10);\n            }\n        }\n        if (attribs[\"expression\"]) {\n            media.expression = attribs[\"expression\"];\n        }\n        return media;\n    });\n}\n/**\n * Get one element by tag name.\n *\n * @param tagName Tag name to look for\n * @param node Node to search in\n * @returns The element or null\n */\nfunction getOneElement(tagName, node) {\n    return (0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(tagName, node, true, 1)[0];\n}\n/**\n * Get the text content of an element with a certain tag name.\n *\n * @param tagName Tag name to look for.\n * @param where Node to search in.\n * @param recurse Whether to recurse into child nodes.\n * @returns The text content of the element.\n */\nfunction fetch(tagName, where, recurse = false) {\n    return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_0__.textContent)((0,_legacy_js__WEBPACK_IMPORTED_MODULE_1__.getElementsByTagName)(tagName, where, recurse, 1)).trim();\n}\n/**\n * Adds a property to an object if it has a value.\n *\n * @param obj Object to be extended\n * @param prop Property name\n * @param tagName Tag name that contains the conditionally added property\n * @param where Element to search for the property\n * @param recurse Whether to recurse into child nodes.\n */\nfunction addConditionally(obj, prop, tagName, where, recurse = false) {\n    const val = fetch(tagName, where, recurse);\n    if (val)\n        obj[prop] = val;\n}\n/**\n * Checks if an element is a feed root node.\n *\n * @param value The name of the element to check.\n * @returns Whether an element is a feed root node.\n */\nfunction isValidFeed(value) {\n    return value === \"rss\" || value === \"feed\" || value === \"rdf:RDF\";\n}\n//# sourceMappingURL=feeds.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/domutils/lib/esm/feeds.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/domutils/lib/esm/helpers.js":
/*!***************************************************!*\
  !*** ../node_modules/domutils/lib/esm/helpers.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentPosition: () => (/* binding */ DocumentPosition),\n/* harmony export */   compareDocumentPosition: () => (/* binding */ compareDocumentPosition),\n/* harmony export */   removeSubsets: () => (/* binding */ removeSubsets),\n/* harmony export */   uniqueSort: () => (/* binding */ uniqueSort)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/../node_modules/domhandler/lib/esm/index.js\");\n\n/**\n * Given an array of nodes, remove any member that is contained by another\n * member.\n *\n * @category Helpers\n * @param nodes Nodes to filter.\n * @returns Remaining nodes that aren't contained by other nodes.\n */\nfunction removeSubsets(nodes) {\n    let idx = nodes.length;\n    /*\n     * Check if each node (or one of its ancestors) is already contained in the\n     * array.\n     */\n    while (--idx >= 0) {\n        const node = nodes[idx];\n        /*\n         * Remove the node if it is not unique.\n         * We are going through the array from the end, so we only\n         * have to check nodes that preceed the node under consideration in the array.\n         */\n        if (idx > 0 && nodes.lastIndexOf(node, idx - 1) >= 0) {\n            nodes.splice(idx, 1);\n            continue;\n        }\n        for (let ancestor = node.parent; ancestor; ancestor = ancestor.parent) {\n            if (nodes.includes(ancestor)) {\n                nodes.splice(idx, 1);\n                break;\n            }\n        }\n    }\n    return nodes;\n}\n/**\n * @category Helpers\n * @see {@link http://dom.spec.whatwg.org/#dom-node-comparedocumentposition}\n */\nvar DocumentPosition;\n(function (DocumentPosition) {\n    DocumentPosition[DocumentPosition[\"DISCONNECTED\"] = 1] = \"DISCONNECTED\";\n    DocumentPosition[DocumentPosition[\"PRECEDING\"] = 2] = \"PRECEDING\";\n    DocumentPosition[DocumentPosition[\"FOLLOWING\"] = 4] = \"FOLLOWING\";\n    DocumentPosition[DocumentPosition[\"CONTAINS\"] = 8] = \"CONTAINS\";\n    DocumentPosition[DocumentPosition[\"CONTAINED_BY\"] = 16] = \"CONTAINED_BY\";\n})(DocumentPosition || (DocumentPosition = {}));\n/**\n * Compare the position of one node against another node in any other document,\n * returning a bitmask with the values from {@link DocumentPosition}.\n *\n * Document order:\n * > There is an ordering, document order, defined on all the nodes in the\n * > document corresponding to the order in which the first character of the\n * > XML representation of each node occurs in the XML representation of the\n * > document after expansion of general entities. Thus, the document element\n * > node will be the first node. Element nodes occur before their children.\n * > Thus, document order orders element nodes in order of the occurrence of\n * > their start-tag in the XML (after expansion of entities). The attribute\n * > nodes of an element occur after the element and before its children. The\n * > relative order of attribute nodes is implementation-dependent.\n *\n * Source:\n * http://www.w3.org/TR/DOM-Level-3-Core/glossary.html#dt-document-order\n *\n * @category Helpers\n * @param nodeA The first node to use in the comparison\n * @param nodeB The second node to use in the comparison\n * @returns A bitmask describing the input nodes' relative position.\n *\n * See http://dom.spec.whatwg.org/#dom-node-comparedocumentposition for\n * a description of these values.\n */\nfunction compareDocumentPosition(nodeA, nodeB) {\n    const aParents = [];\n    const bParents = [];\n    if (nodeA === nodeB) {\n        return 0;\n    }\n    let current = (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(nodeA) ? nodeA : nodeA.parent;\n    while (current) {\n        aParents.unshift(current);\n        current = current.parent;\n    }\n    current = (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(nodeB) ? nodeB : nodeB.parent;\n    while (current) {\n        bParents.unshift(current);\n        current = current.parent;\n    }\n    const maxIdx = Math.min(aParents.length, bParents.length);\n    let idx = 0;\n    while (idx < maxIdx && aParents[idx] === bParents[idx]) {\n        idx++;\n    }\n    if (idx === 0) {\n        return DocumentPosition.DISCONNECTED;\n    }\n    const sharedParent = aParents[idx - 1];\n    const siblings = sharedParent.children;\n    const aSibling = aParents[idx];\n    const bSibling = bParents[idx];\n    if (siblings.indexOf(aSibling) > siblings.indexOf(bSibling)) {\n        if (sharedParent === nodeB) {\n            return DocumentPosition.FOLLOWING | DocumentPosition.CONTAINED_BY;\n        }\n        return DocumentPosition.FOLLOWING;\n    }\n    if (sharedParent === nodeA) {\n        return DocumentPosition.PRECEDING | DocumentPosition.CONTAINS;\n    }\n    return DocumentPosition.PRECEDING;\n}\n/**\n * Sort an array of nodes based on their relative position in the document,\n * removing any duplicate nodes. If the array contains nodes that do not belong\n * to the same document, sort order is unspecified.\n *\n * @category Helpers\n * @param nodes Array of DOM nodes.\n * @returns Collection of unique nodes, sorted in document order.\n */\nfunction uniqueSort(nodes) {\n    nodes = nodes.filter((node, i, arr) => !arr.includes(node, i + 1));\n    nodes.sort((a, b) => {\n        const relative = compareDocumentPosition(a, b);\n        if (relative & DocumentPosition.PRECEDING) {\n            return -1;\n        }\n        else if (relative & DocumentPosition.FOLLOWING) {\n            return 1;\n        }\n        return 0;\n    });\n    return nodes;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/domutils/lib/esm/helpers.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/domutils/lib/esm/index.js":
/*!*************************************************!*\
  !*** ../node_modules/domutils/lib/esm/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocumentPosition: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.DocumentPosition),\n/* harmony export */   append: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.append),\n/* harmony export */   appendChild: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.appendChild),\n/* harmony export */   compareDocumentPosition: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.compareDocumentPosition),\n/* harmony export */   existsOne: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.existsOne),\n/* harmony export */   filter: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.filter),\n/* harmony export */   find: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.find),\n/* harmony export */   findAll: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.findAll),\n/* harmony export */   findOne: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.findOne),\n/* harmony export */   findOneChild: () => (/* reexport safe */ _querying_js__WEBPACK_IMPORTED_MODULE_3__.findOneChild),\n/* harmony export */   getAttributeValue: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getAttributeValue),\n/* harmony export */   getChildren: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getChildren),\n/* harmony export */   getElementById: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementById),\n/* harmony export */   getElements: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElements),\n/* harmony export */   getElementsByClassName: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementsByClassName),\n/* harmony export */   getElementsByTagName: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementsByTagName),\n/* harmony export */   getElementsByTagType: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.getElementsByTagType),\n/* harmony export */   getFeed: () => (/* reexport safe */ _feeds_js__WEBPACK_IMPORTED_MODULE_6__.getFeed),\n/* harmony export */   getInnerHTML: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.getInnerHTML),\n/* harmony export */   getName: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getName),\n/* harmony export */   getOuterHTML: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.getOuterHTML),\n/* harmony export */   getParent: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getParent),\n/* harmony export */   getSiblings: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.getSiblings),\n/* harmony export */   getText: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.getText),\n/* harmony export */   hasAttrib: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.hasAttrib),\n/* harmony export */   hasChildren: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.hasChildren),\n/* harmony export */   innerText: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.innerText),\n/* harmony export */   isCDATA: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isCDATA),\n/* harmony export */   isComment: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isComment),\n/* harmony export */   isDocument: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isDocument),\n/* harmony export */   isTag: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isTag),\n/* harmony export */   isText: () => (/* reexport safe */ domhandler__WEBPACK_IMPORTED_MODULE_7__.isText),\n/* harmony export */   nextElementSibling: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.nextElementSibling),\n/* harmony export */   prepend: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.prepend),\n/* harmony export */   prependChild: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.prependChild),\n/* harmony export */   prevElementSibling: () => (/* reexport safe */ _traversal_js__WEBPACK_IMPORTED_MODULE_1__.prevElementSibling),\n/* harmony export */   removeElement: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.removeElement),\n/* harmony export */   removeSubsets: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.removeSubsets),\n/* harmony export */   replaceElement: () => (/* reexport safe */ _manipulation_js__WEBPACK_IMPORTED_MODULE_2__.replaceElement),\n/* harmony export */   testElement: () => (/* reexport safe */ _legacy_js__WEBPACK_IMPORTED_MODULE_4__.testElement),\n/* harmony export */   textContent: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_0__.textContent),\n/* harmony export */   uniqueSort: () => (/* reexport safe */ _helpers_js__WEBPACK_IMPORTED_MODULE_5__.uniqueSort)\n/* harmony export */ });\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/../node_modules/domutils/lib/esm/stringify.js\");\n/* harmony import */ var _traversal_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./traversal.js */ \"(rsc)/../node_modules/domutils/lib/esm/traversal.js\");\n/* harmony import */ var _manipulation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./manipulation.js */ \"(rsc)/../node_modules/domutils/lib/esm/manipulation.js\");\n/* harmony import */ var _querying_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./querying.js */ \"(rsc)/../node_modules/domutils/lib/esm/querying.js\");\n/* harmony import */ var _legacy_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./legacy.js */ \"(rsc)/../node_modules/domutils/lib/esm/legacy.js\");\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./helpers.js */ \"(rsc)/../node_modules/domutils/lib/esm/helpers.js\");\n/* harmony import */ var _feeds_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./feeds.js */ \"(rsc)/../node_modules/domutils/lib/esm/feeds.js\");\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! domhandler */ \"(rsc)/../node_modules/domhandler/lib/esm/index.js\");\n\n\n\n\n\n\n\n/** @deprecated Use these methods from `domhandler` directly. */\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL2RvbXV0aWxzL2xpYi9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBK0I7QUFDQTtBQUNHO0FBQ0o7QUFDRjtBQUNDO0FBQ0Y7QUFDM0I7QUFDeUY7QUFDekYiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXG5vZGVfbW9kdWxlc1xcZG9tdXRpbHNcXGxpYlxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9zdHJpbmdpZnkuanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3RyYXZlcnNhbC5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vbWFuaXB1bGF0aW9uLmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9xdWVyeWluZy5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vbGVnYWN5LmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9oZWxwZXJzLmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9mZWVkcy5qc1wiO1xuLyoqIEBkZXByZWNhdGVkIFVzZSB0aGVzZSBtZXRob2RzIGZyb20gYGRvbWhhbmRsZXJgIGRpcmVjdGx5LiAqL1xuZXhwb3J0IHsgaXNUYWcsIGlzQ0RBVEEsIGlzVGV4dCwgaXNDb21tZW50LCBpc0RvY3VtZW50LCBoYXNDaGlsZHJlbiwgfSBmcm9tIFwiZG9taGFuZGxlclwiO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/domutils/lib/esm/index.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/domutils/lib/esm/legacy.js":
/*!**************************************************!*\
  !*** ../node_modules/domutils/lib/esm/legacy.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getElementById: () => (/* binding */ getElementById),\n/* harmony export */   getElements: () => (/* binding */ getElements),\n/* harmony export */   getElementsByClassName: () => (/* binding */ getElementsByClassName),\n/* harmony export */   getElementsByTagName: () => (/* binding */ getElementsByTagName),\n/* harmony export */   getElementsByTagType: () => (/* binding */ getElementsByTagType),\n/* harmony export */   testElement: () => (/* binding */ testElement)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/../node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var _querying_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./querying.js */ \"(rsc)/../node_modules/domutils/lib/esm/querying.js\");\n\n\n/**\n * A map of functions to check nodes against.\n */\nconst Checks = {\n    tag_name(name) {\n        if (typeof name === \"function\") {\n            return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && name(elem.name);\n        }\n        else if (name === \"*\") {\n            return domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag;\n        }\n        return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && elem.name === name;\n    },\n    tag_type(type) {\n        if (typeof type === \"function\") {\n            return (elem) => type(elem.type);\n        }\n        return (elem) => elem.type === type;\n    },\n    tag_contains(data) {\n        if (typeof data === \"function\") {\n            return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(elem) && data(elem.data);\n        }\n        return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(elem) && elem.data === data;\n    },\n};\n/**\n * Returns a function to check whether a node has an attribute with a particular\n * value.\n *\n * @param attrib Attribute to check.\n * @param value Attribute value to look for.\n * @returns A function to check whether the a node has an attribute with a\n *   particular value.\n */\nfunction getAttribCheck(attrib, value) {\n    if (typeof value === \"function\") {\n        return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && value(elem.attribs[attrib]);\n    }\n    return (elem) => (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && elem.attribs[attrib] === value;\n}\n/**\n * Returns a function that returns `true` if either of the input functions\n * returns `true` for a node.\n *\n * @param a First function to combine.\n * @param b Second function to combine.\n * @returns A function taking a node and returning `true` if either of the input\n *   functions returns `true` for the node.\n */\nfunction combineFuncs(a, b) {\n    return (elem) => a(elem) || b(elem);\n}\n/**\n * Returns a function that executes all checks in `options` and returns `true`\n * if any of them match a node.\n *\n * @param options An object describing nodes to look for.\n * @returns A function that executes all checks in `options` and returns `true`\n *   if any of them match a node.\n */\nfunction compileTest(options) {\n    const funcs = Object.keys(options).map((key) => {\n        const value = options[key];\n        return Object.prototype.hasOwnProperty.call(Checks, key)\n            ? Checks[key](value)\n            : getAttribCheck(key, value);\n    });\n    return funcs.length === 0 ? null : funcs.reduce(combineFuncs);\n}\n/**\n * Checks whether a node matches the description in `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param node The element to test.\n * @returns Whether the element matches the description in `options`.\n */\nfunction testElement(options, node) {\n    const test = compileTest(options);\n    return test ? test(node) : true;\n}\n/**\n * Returns all nodes that match `options`.\n *\n * @category Legacy Query Functions\n * @param options An object describing nodes to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes that match `options`.\n */\nfunction getElements(options, nodes, recurse, limit = Infinity) {\n    const test = compileTest(options);\n    return test ? (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(test, nodes, recurse, limit) : [];\n}\n/**\n * Returns the node with the supplied ID.\n *\n * @category Legacy Query Functions\n * @param id The unique ID attribute value to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @returns The node with the supplied ID.\n */\nfunction getElementById(id, nodes, recurse = true) {\n    if (!Array.isArray(nodes))\n        nodes = [nodes];\n    return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.findOne)(getAttribCheck(\"id\", id), nodes, recurse);\n}\n/**\n * Returns all nodes with the supplied `tagName`.\n *\n * @category Legacy Query Functions\n * @param tagName Tag name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `tagName`.\n */\nfunction getElementsByTagName(tagName, nodes, recurse = true, limit = Infinity) {\n    return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(Checks[\"tag_name\"](tagName), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `className`.\n *\n * @category Legacy Query Functions\n * @param className Class name to search for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `className`.\n */\nfunction getElementsByClassName(className, nodes, recurse = true, limit = Infinity) {\n    return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(getAttribCheck(\"class\", className), nodes, recurse, limit);\n}\n/**\n * Returns all nodes with the supplied `type`.\n *\n * @category Legacy Query Functions\n * @param type Element type to look for.\n * @param nodes Nodes to search through.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes with the supplied `type`.\n */\nfunction getElementsByTagType(type, nodes, recurse = true, limit = Infinity) {\n    return (0,_querying_js__WEBPACK_IMPORTED_MODULE_1__.filter)(Checks[\"tag_type\"](type), nodes, recurse, limit);\n}\n//# sourceMappingURL=legacy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/domutils/lib/esm/legacy.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/domutils/lib/esm/manipulation.js":
/*!********************************************************!*\
  !*** ../node_modules/domutils/lib/esm/manipulation.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   append: () => (/* binding */ append),\n/* harmony export */   appendChild: () => (/* binding */ appendChild),\n/* harmony export */   prepend: () => (/* binding */ prepend),\n/* harmony export */   prependChild: () => (/* binding */ prependChild),\n/* harmony export */   removeElement: () => (/* binding */ removeElement),\n/* harmony export */   replaceElement: () => (/* binding */ replaceElement)\n/* harmony export */ });\n/**\n * Remove an element from the dom\n *\n * @category Manipulation\n * @param elem The element to be removed\n */\nfunction removeElement(elem) {\n    if (elem.prev)\n        elem.prev.next = elem.next;\n    if (elem.next)\n        elem.next.prev = elem.prev;\n    if (elem.parent) {\n        const childs = elem.parent.children;\n        const childsIndex = childs.lastIndexOf(elem);\n        if (childsIndex >= 0) {\n            childs.splice(childsIndex, 1);\n        }\n    }\n    elem.next = null;\n    elem.prev = null;\n    elem.parent = null;\n}\n/**\n * Replace an element in the dom\n *\n * @category Manipulation\n * @param elem The element to be replaced\n * @param replacement The element to be added\n */\nfunction replaceElement(elem, replacement) {\n    const prev = (replacement.prev = elem.prev);\n    if (prev) {\n        prev.next = replacement;\n    }\n    const next = (replacement.next = elem.next);\n    if (next) {\n        next.prev = replacement;\n    }\n    const parent = (replacement.parent = elem.parent);\n    if (parent) {\n        const childs = parent.children;\n        childs[childs.lastIndexOf(elem)] = replacement;\n        elem.parent = null;\n    }\n}\n/**\n * Append a child to an element.\n *\n * @category Manipulation\n * @param parent The element to append to.\n * @param child The element to be added as a child.\n */\nfunction appendChild(parent, child) {\n    removeElement(child);\n    child.next = null;\n    child.parent = parent;\n    if (parent.children.push(child) > 1) {\n        const sibling = parent.children[parent.children.length - 2];\n        sibling.next = child;\n        child.prev = sibling;\n    }\n    else {\n        child.prev = null;\n    }\n}\n/**\n * Append an element after another.\n *\n * @category Manipulation\n * @param elem The element to append after.\n * @param next The element be added.\n */\nfunction append(elem, next) {\n    removeElement(next);\n    const { parent } = elem;\n    const currNext = elem.next;\n    next.next = currNext;\n    next.prev = elem;\n    elem.next = next;\n    next.parent = parent;\n    if (currNext) {\n        currNext.prev = next;\n        if (parent) {\n            const childs = parent.children;\n            childs.splice(childs.lastIndexOf(currNext), 0, next);\n        }\n    }\n    else if (parent) {\n        parent.children.push(next);\n    }\n}\n/**\n * Prepend a child to an element.\n *\n * @category Manipulation\n * @param parent The element to prepend before.\n * @param child The element to be added as a child.\n */\nfunction prependChild(parent, child) {\n    removeElement(child);\n    child.parent = parent;\n    child.prev = null;\n    if (parent.children.unshift(child) !== 1) {\n        const sibling = parent.children[1];\n        sibling.prev = child;\n        child.next = sibling;\n    }\n    else {\n        child.next = null;\n    }\n}\n/**\n * Prepend an element before another.\n *\n * @category Manipulation\n * @param elem The element to prepend before.\n * @param prev The element be added.\n */\nfunction prepend(elem, prev) {\n    removeElement(prev);\n    const { parent } = elem;\n    if (parent) {\n        const childs = parent.children;\n        childs.splice(childs.indexOf(elem), 0, prev);\n    }\n    if (elem.prev) {\n        elem.prev.next = prev;\n    }\n    prev.parent = parent;\n    prev.prev = elem.prev;\n    prev.next = elem;\n    elem.prev = prev;\n}\n//# sourceMappingURL=manipulation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/domutils/lib/esm/manipulation.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/domutils/lib/esm/querying.js":
/*!****************************************************!*\
  !*** ../node_modules/domutils/lib/esm/querying.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   existsOne: () => (/* binding */ existsOne),\n/* harmony export */   filter: () => (/* binding */ filter),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   findAll: () => (/* binding */ findAll),\n/* harmony export */   findOne: () => (/* binding */ findOne),\n/* harmony export */   findOneChild: () => (/* binding */ findOneChild)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/../node_modules/domhandler/lib/esm/index.js\");\n\n/**\n * Search a node and its children for nodes passing a test function. If `node` is not an array, it will be wrapped in one.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param node Node to search. Will be included in the result set if it matches.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction filter(test, node, recurse = true, limit = Infinity) {\n    return find(test, Array.isArray(node) ? node : [node], recurse, limit);\n}\n/**\n * Search an array of nodes and their children for nodes passing a test function.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @param recurse Also consider child nodes.\n * @param limit Maximum number of nodes to return.\n * @returns All nodes passing `test`.\n */\nfunction find(test, nodes, recurse, limit) {\n    const result = [];\n    /** Stack of the arrays we are looking at. */\n    const nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n    /** Stack of the indices within the arrays. */\n    const indexStack = [0];\n    for (;;) {\n        // First, check if the current array has any more elements to look at.\n        if (indexStack[0] >= nodeStack[0].length) {\n            // If we have no more arrays to look at, we are done.\n            if (indexStack.length === 1) {\n                return result;\n            }\n            // Otherwise, remove the current array from the stack.\n            nodeStack.shift();\n            indexStack.shift();\n            // Loop back to the start to continue with the next array.\n            continue;\n        }\n        const elem = nodeStack[0][indexStack[0]++];\n        if (test(elem)) {\n            result.push(elem);\n            if (--limit <= 0)\n                return result;\n        }\n        if (recurse && (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) && elem.children.length > 0) {\n            /*\n             * Add the children to the stack. We are depth-first, so this is\n             * the next array we look at.\n             */\n            indexStack.unshift(0);\n            nodeStack.unshift(elem.children);\n        }\n    }\n}\n/**\n * Finds the first element inside of an array that matches a test function. This is an alias for `Array.prototype.find`.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns The first node in the array that passes `test`.\n * @deprecated Use `Array.prototype.find` directly.\n */\nfunction findOneChild(test, nodes) {\n    return nodes.find(test);\n}\n/**\n * Finds one element in a tree that passes a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Node or array of nodes to search.\n * @param recurse Also consider child nodes.\n * @returns The first node that passes `test`.\n */\nfunction findOne(test, nodes, recurse = true) {\n    const searchedNodes = Array.isArray(nodes) ? nodes : [nodes];\n    for (let i = 0; i < searchedNodes.length; i++) {\n        const node = searchedNodes[i];\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(node) && test(node)) {\n            return node;\n        }\n        if (recurse && (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && node.children.length > 0) {\n            const found = findOne(test, node.children, true);\n            if (found)\n                return found;\n        }\n    }\n    return null;\n}\n/**\n * Checks if a tree of nodes contains at least one node passing a test.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns Whether a tree of nodes contains at least one node passing the test.\n */\nfunction existsOne(test, nodes) {\n    return (Array.isArray(nodes) ? nodes : [nodes]).some((node) => ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(node) && test(node)) ||\n        ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && existsOne(test, node.children)));\n}\n/**\n * Search an array of nodes and their children for elements passing a test function.\n *\n * Same as `find`, but limited to elements and with less options, leading to reduced complexity.\n *\n * @category Querying\n * @param test Function to test nodes on.\n * @param nodes Array of nodes to search.\n * @returns All nodes passing `test`.\n */\nfunction findAll(test, nodes) {\n    const result = [];\n    const nodeStack = [Array.isArray(nodes) ? nodes : [nodes]];\n    const indexStack = [0];\n    for (;;) {\n        if (indexStack[0] >= nodeStack[0].length) {\n            if (nodeStack.length === 1) {\n                return result;\n            }\n            // Otherwise, remove the current array from the stack.\n            nodeStack.shift();\n            indexStack.shift();\n            // Loop back to the start to continue with the next array.\n            continue;\n        }\n        const elem = nodeStack[0][indexStack[0]++];\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(elem) && test(elem))\n            result.push(elem);\n        if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) && elem.children.length > 0) {\n            indexStack.unshift(0);\n            nodeStack.unshift(elem.children);\n        }\n    }\n}\n//# sourceMappingURL=querying.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/domutils/lib/esm/querying.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/domutils/lib/esm/stringify.js":
/*!*****************************************************!*\
  !*** ../node_modules/domutils/lib/esm/stringify.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getInnerHTML: () => (/* binding */ getInnerHTML),\n/* harmony export */   getOuterHTML: () => (/* binding */ getOuterHTML),\n/* harmony export */   getText: () => (/* binding */ getText),\n/* harmony export */   innerText: () => (/* binding */ innerText),\n/* harmony export */   textContent: () => (/* binding */ textContent)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/../node_modules/domhandler/lib/esm/index.js\");\n/* harmony import */ var dom_serializer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dom-serializer */ \"(rsc)/../node_modules/dom-serializer/lib/esm/index.js\");\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! domelementtype */ \"(rsc)/../node_modules/domelementtype/lib/esm/index.js\");\n\n\n\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the outer HTML of.\n * @param options Options for serialization.\n * @returns `node`'s outer HTML.\n */\nfunction getOuterHTML(node, options) {\n    return (0,dom_serializer__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(node, options);\n}\n/**\n * @category Stringify\n * @deprecated Use the `dom-serializer` module directly.\n * @param node Node to get the inner HTML of.\n * @param options Options for serialization.\n * @returns `node`'s inner HTML.\n */\nfunction getInnerHTML(node, options) {\n    return (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node)\n        ? node.children.map((node) => getOuterHTML(node, options)).join(\"\")\n        : \"\";\n}\n/**\n * Get a node's inner text. Same as `textContent`, but inserts newlines for `<br>` tags. Ignores comments.\n *\n * @category Stringify\n * @deprecated Use `textContent` instead.\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n */\nfunction getText(node) {\n    if (Array.isArray(node))\n        return node.map(getText).join(\"\");\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(node))\n        return node.name === \"br\" ? \"\\n\" : getText(node.children);\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isCDATA)(node))\n        return getText(node.children);\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(node))\n        return node.data;\n    return \"\";\n}\n/**\n * Get a node's text content. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the text content of.\n * @returns `node`'s text content.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/textContent}\n */\nfunction textContent(node) {\n    if (Array.isArray(node))\n        return node.map(textContent).join(\"\");\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isComment)(node)) {\n        return textContent(node.children);\n    }\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(node))\n        return node.data;\n    return \"\";\n}\n/**\n * Get a node's inner text, ignoring `<script>` and `<style>` tags. Ignores comments.\n *\n * @category Stringify\n * @param node Node to get the inner text of.\n * @returns `node`'s inner text.\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/Node/innerText}\n */\nfunction innerText(node) {\n    if (Array.isArray(node))\n        return node.map(innerText).join(\"\");\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(node) && (node.type === domelementtype__WEBPACK_IMPORTED_MODULE_2__.ElementType.Tag || (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isCDATA)(node))) {\n        return innerText(node.children);\n    }\n    if ((0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isText)(node))\n        return node.data;\n    return \"\";\n}\n//# sourceMappingURL=stringify.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/domutils/lib/esm/stringify.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/domutils/lib/esm/traversal.js":
/*!*****************************************************!*\
  !*** ../node_modules/domutils/lib/esm/traversal.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAttributeValue: () => (/* binding */ getAttributeValue),\n/* harmony export */   getChildren: () => (/* binding */ getChildren),\n/* harmony export */   getName: () => (/* binding */ getName),\n/* harmony export */   getParent: () => (/* binding */ getParent),\n/* harmony export */   getSiblings: () => (/* binding */ getSiblings),\n/* harmony export */   hasAttrib: () => (/* binding */ hasAttrib),\n/* harmony export */   nextElementSibling: () => (/* binding */ nextElementSibling),\n/* harmony export */   prevElementSibling: () => (/* binding */ prevElementSibling)\n/* harmony export */ });\n/* harmony import */ var domhandler__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domhandler */ \"(rsc)/../node_modules/domhandler/lib/esm/index.js\");\n\n/**\n * Get a node's children.\n *\n * @category Traversal\n * @param elem Node to get the children of.\n * @returns `elem`'s children, or an empty array.\n */\nfunction getChildren(elem) {\n    return (0,domhandler__WEBPACK_IMPORTED_MODULE_0__.hasChildren)(elem) ? elem.children : [];\n}\n/**\n * Get a node's parent.\n *\n * @category Traversal\n * @param elem Node to get the parent of.\n * @returns `elem`'s parent node, or `null` if `elem` is a root node.\n */\nfunction getParent(elem) {\n    return elem.parent || null;\n}\n/**\n * Gets an elements siblings, including the element itself.\n *\n * Attempts to get the children through the element's parent first. If we don't\n * have a parent (the element is a root node), we walk the element's `prev` &\n * `next` to get all remaining nodes.\n *\n * @category Traversal\n * @param elem Element to get the siblings of.\n * @returns `elem`'s siblings, including `elem`.\n */\nfunction getSiblings(elem) {\n    const parent = getParent(elem);\n    if (parent != null)\n        return getChildren(parent);\n    const siblings = [elem];\n    let { prev, next } = elem;\n    while (prev != null) {\n        siblings.unshift(prev);\n        ({ prev } = prev);\n    }\n    while (next != null) {\n        siblings.push(next);\n        ({ next } = next);\n    }\n    return siblings;\n}\n/**\n * Gets an attribute from an element.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to retrieve.\n * @returns The element's attribute value, or `undefined`.\n */\nfunction getAttributeValue(elem, name) {\n    var _a;\n    return (_a = elem.attribs) === null || _a === void 0 ? void 0 : _a[name];\n}\n/**\n * Checks whether an element has an attribute.\n *\n * @category Traversal\n * @param elem Element to check.\n * @param name Attribute name to look for.\n * @returns Returns whether `elem` has the attribute `name`.\n */\nfunction hasAttrib(elem, name) {\n    return (elem.attribs != null &&\n        Object.prototype.hasOwnProperty.call(elem.attribs, name) &&\n        elem.attribs[name] != null);\n}\n/**\n * Get the tag name of an element.\n *\n * @category Traversal\n * @param elem The element to get the name for.\n * @returns The tag name of `elem`.\n */\nfunction getName(elem) {\n    return elem.name;\n}\n/**\n * Returns the next element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the next sibling of.\n * @returns `elem`'s next sibling that is a tag, or `null` if there is no next\n * sibling.\n */\nfunction nextElementSibling(elem) {\n    let { next } = elem;\n    while (next !== null && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(next))\n        ({ next } = next);\n    return next;\n}\n/**\n * Returns the previous element sibling of a node.\n *\n * @category Traversal\n * @param elem The element to get the previous sibling of.\n * @returns `elem`'s previous sibling that is a tag, or `null` if there is no\n * previous sibling.\n */\nfunction prevElementSibling(elem) {\n    let { prev } = elem;\n    while (prev !== null && !(0,domhandler__WEBPACK_IMPORTED_MODULE_0__.isTag)(prev))\n        ({ prev } = prev);\n    return prev;\n}\n//# sourceMappingURL=traversal.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzL2RvbXV0aWxzL2xpYi9lc20vdHJhdmVyc2FsLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFpRDtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsV0FBVyx1REFBVztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLGFBQWE7QUFDdkI7QUFDQTtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFVBQVUsT0FBTztBQUNqQiw2QkFBNkIsaURBQUs7QUFDbEMsV0FBVyxPQUFPO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxVQUFVLE9BQU87QUFDakIsNkJBQTZCLGlEQUFLO0FBQ2xDLFdBQVcsT0FBTztBQUNsQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXG5vZGVfbW9kdWxlc1xcZG9tdXRpbHNcXGxpYlxcZXNtXFx0cmF2ZXJzYWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNUYWcsIGhhc0NoaWxkcmVuLCB9IGZyb20gXCJkb21oYW5kbGVyXCI7XG4vKipcbiAqIEdldCBhIG5vZGUncyBjaGlsZHJlbi5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2FsXG4gKiBAcGFyYW0gZWxlbSBOb2RlIHRvIGdldCB0aGUgY2hpbGRyZW4gb2YuXG4gKiBAcmV0dXJucyBgZWxlbWAncyBjaGlsZHJlbiwgb3IgYW4gZW1wdHkgYXJyYXkuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRDaGlsZHJlbihlbGVtKSB7XG4gICAgcmV0dXJuIGhhc0NoaWxkcmVuKGVsZW0pID8gZWxlbS5jaGlsZHJlbiA6IFtdO1xufVxuLyoqXG4gKiBHZXQgYSBub2RlJ3MgcGFyZW50LlxuICpcbiAqIEBjYXRlZ29yeSBUcmF2ZXJzYWxcbiAqIEBwYXJhbSBlbGVtIE5vZGUgdG8gZ2V0IHRoZSBwYXJlbnQgb2YuXG4gKiBAcmV0dXJucyBgZWxlbWAncyBwYXJlbnQgbm9kZSwgb3IgYG51bGxgIGlmIGBlbGVtYCBpcyBhIHJvb3Qgbm9kZS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldFBhcmVudChlbGVtKSB7XG4gICAgcmV0dXJuIGVsZW0ucGFyZW50IHx8IG51bGw7XG59XG4vKipcbiAqIEdldHMgYW4gZWxlbWVudHMgc2libGluZ3MsIGluY2x1ZGluZyB0aGUgZWxlbWVudCBpdHNlbGYuXG4gKlxuICogQXR0ZW1wdHMgdG8gZ2V0IHRoZSBjaGlsZHJlbiB0aHJvdWdoIHRoZSBlbGVtZW50J3MgcGFyZW50IGZpcnN0LiBJZiB3ZSBkb24ndFxuICogaGF2ZSBhIHBhcmVudCAodGhlIGVsZW1lbnQgaXMgYSByb290IG5vZGUpLCB3ZSB3YWxrIHRoZSBlbGVtZW50J3MgYHByZXZgICZcbiAqIGBuZXh0YCB0byBnZXQgYWxsIHJlbWFpbmluZyBub2Rlcy5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2FsXG4gKiBAcGFyYW0gZWxlbSBFbGVtZW50IHRvIGdldCB0aGUgc2libGluZ3Mgb2YuXG4gKiBAcmV0dXJucyBgZWxlbWAncyBzaWJsaW5ncywgaW5jbHVkaW5nIGBlbGVtYC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldFNpYmxpbmdzKGVsZW0pIHtcbiAgICBjb25zdCBwYXJlbnQgPSBnZXRQYXJlbnQoZWxlbSk7XG4gICAgaWYgKHBhcmVudCAhPSBudWxsKVxuICAgICAgICByZXR1cm4gZ2V0Q2hpbGRyZW4ocGFyZW50KTtcbiAgICBjb25zdCBzaWJsaW5ncyA9IFtlbGVtXTtcbiAgICBsZXQgeyBwcmV2LCBuZXh0IH0gPSBlbGVtO1xuICAgIHdoaWxlIChwcmV2ICE9IG51bGwpIHtcbiAgICAgICAgc2libGluZ3MudW5zaGlmdChwcmV2KTtcbiAgICAgICAgKHsgcHJldiB9ID0gcHJldik7XG4gICAgfVxuICAgIHdoaWxlIChuZXh0ICE9IG51bGwpIHtcbiAgICAgICAgc2libGluZ3MucHVzaChuZXh0KTtcbiAgICAgICAgKHsgbmV4dCB9ID0gbmV4dCk7XG4gICAgfVxuICAgIHJldHVybiBzaWJsaW5ncztcbn1cbi8qKlxuICogR2V0cyBhbiBhdHRyaWJ1dGUgZnJvbSBhbiBlbGVtZW50LlxuICpcbiAqIEBjYXRlZ29yeSBUcmF2ZXJzYWxcbiAqIEBwYXJhbSBlbGVtIEVsZW1lbnQgdG8gY2hlY2suXG4gKiBAcGFyYW0gbmFtZSBBdHRyaWJ1dGUgbmFtZSB0byByZXRyaWV2ZS5cbiAqIEByZXR1cm5zIFRoZSBlbGVtZW50J3MgYXR0cmlidXRlIHZhbHVlLCBvciBgdW5kZWZpbmVkYC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGdldEF0dHJpYnV0ZVZhbHVlKGVsZW0sIG5hbWUpIHtcbiAgICB2YXIgX2E7XG4gICAgcmV0dXJuIChfYSA9IGVsZW0uYXR0cmlicykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hW25hbWVdO1xufVxuLyoqXG4gKiBDaGVja3Mgd2hldGhlciBhbiBlbGVtZW50IGhhcyBhbiBhdHRyaWJ1dGUuXG4gKlxuICogQGNhdGVnb3J5IFRyYXZlcnNhbFxuICogQHBhcmFtIGVsZW0gRWxlbWVudCB0byBjaGVjay5cbiAqIEBwYXJhbSBuYW1lIEF0dHJpYnV0ZSBuYW1lIHRvIGxvb2sgZm9yLlxuICogQHJldHVybnMgUmV0dXJucyB3aGV0aGVyIGBlbGVtYCBoYXMgdGhlIGF0dHJpYnV0ZSBgbmFtZWAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoYXNBdHRyaWIoZWxlbSwgbmFtZSkge1xuICAgIHJldHVybiAoZWxlbS5hdHRyaWJzICE9IG51bGwgJiZcbiAgICAgICAgT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGVsZW0uYXR0cmlicywgbmFtZSkgJiZcbiAgICAgICAgZWxlbS5hdHRyaWJzW25hbWVdICE9IG51bGwpO1xufVxuLyoqXG4gKiBHZXQgdGhlIHRhZyBuYW1lIG9mIGFuIGVsZW1lbnQuXG4gKlxuICogQGNhdGVnb3J5IFRyYXZlcnNhbFxuICogQHBhcmFtIGVsZW0gVGhlIGVsZW1lbnQgdG8gZ2V0IHRoZSBuYW1lIGZvci5cbiAqIEByZXR1cm5zIFRoZSB0YWcgbmFtZSBvZiBgZWxlbWAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXROYW1lKGVsZW0pIHtcbiAgICByZXR1cm4gZWxlbS5uYW1lO1xufVxuLyoqXG4gKiBSZXR1cm5zIHRoZSBuZXh0IGVsZW1lbnQgc2libGluZyBvZiBhIG5vZGUuXG4gKlxuICogQGNhdGVnb3J5IFRyYXZlcnNhbFxuICogQHBhcmFtIGVsZW0gVGhlIGVsZW1lbnQgdG8gZ2V0IHRoZSBuZXh0IHNpYmxpbmcgb2YuXG4gKiBAcmV0dXJucyBgZWxlbWAncyBuZXh0IHNpYmxpbmcgdGhhdCBpcyBhIHRhZywgb3IgYG51bGxgIGlmIHRoZXJlIGlzIG5vIG5leHRcbiAqIHNpYmxpbmcuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBuZXh0RWxlbWVudFNpYmxpbmcoZWxlbSkge1xuICAgIGxldCB7IG5leHQgfSA9IGVsZW07XG4gICAgd2hpbGUgKG5leHQgIT09IG51bGwgJiYgIWlzVGFnKG5leHQpKVxuICAgICAgICAoeyBuZXh0IH0gPSBuZXh0KTtcbiAgICByZXR1cm4gbmV4dDtcbn1cbi8qKlxuICogUmV0dXJucyB0aGUgcHJldmlvdXMgZWxlbWVudCBzaWJsaW5nIG9mIGEgbm9kZS5cbiAqXG4gKiBAY2F0ZWdvcnkgVHJhdmVyc2FsXG4gKiBAcGFyYW0gZWxlbSBUaGUgZWxlbWVudCB0byBnZXQgdGhlIHByZXZpb3VzIHNpYmxpbmcgb2YuXG4gKiBAcmV0dXJucyBgZWxlbWAncyBwcmV2aW91cyBzaWJsaW5nIHRoYXQgaXMgYSB0YWcsIG9yIGBudWxsYCBpZiB0aGVyZSBpcyBub1xuICogcHJldmlvdXMgc2libGluZy5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHByZXZFbGVtZW50U2libGluZyhlbGVtKSB7XG4gICAgbGV0IHsgcHJldiB9ID0gZWxlbTtcbiAgICB3aGlsZSAocHJldiAhPT0gbnVsbCAmJiAhaXNUYWcocHJldikpXG4gICAgICAgICh7IHByZXYgfSA9IHByZXYpO1xuICAgIHJldHVybiBwcmV2O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHJhdmVyc2FsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/domutils/lib/esm/traversal.js\n");

/***/ })

};
;