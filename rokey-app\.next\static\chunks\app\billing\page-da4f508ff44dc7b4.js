(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7522],{38060:(e,s,a)=>{Promise.resolve().then(a.bind(a,54837))},54837:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>v});var t=a(95155),r=a(12115),l=a(35695),i=a(15713),n=a(72227),c=a(6865),d=a(40710),o=a(18593),m=a(55628),x=a(52589),u=a(13741),g=a(56671),h=a(83298),p=a(80377),b=a(5585),f=a(87162),y=a(75518);let j=[{id:"free",name:"Free",price:0,interval:"forever",features:[{name:"Strict fallback routing only",included:!0},{name:"Basic analytics",included:!0},{name:"No custom roles",included:!1},{name:"Configurations",included:!0,limit:"1 max"},{name:"API keys per config",included:!0,limit:"3 max"},{name:"User-generated API keys",included:!0,limit:"3 max"}]},{id:"starter",name:"Starter",price:19,interval:"month",popular:!0,features:[{name:"All routing strategies",included:!0},{name:"Advanced analytics",included:!0},{name:"Custom roles",included:!0,limit:"Up to 3 roles"},{name:"Configurations",included:!0,limit:"5 max"},{name:"API keys per config",included:!0,limit:"15 max"},{name:"User-generated API keys",included:!0,limit:"50 max"},{name:"Browsing tasks",included:!0,limit:"15/month"}]},{id:"professional",name:"Professional",price:49,interval:"month",features:[{name:"Everything in Starter",included:!0},{name:"Unlimited configurations",included:!0},{name:"Unlimited API keys per config",included:!0},{name:"Unlimited user-generated API keys",included:!0},{name:"Knowledge base documents",included:!0,limit:"5 documents"},{name:"Priority support",included:!0}]},{id:"enterprise",name:"Enterprise",price:149,interval:"month",features:[{name:"Everything in Professional",included:!0},{name:"Unlimited knowledge base documents",included:!0},{name:"Advanced semantic caching",included:!0},{name:"Custom integrations",included:!0},{name:"Dedicated support + phone",included:!0},{name:"SLA guarantee",included:!0}]}];function v(){(0,l.useRouter)();let e=(0,l.useSearchParams)(),{user:s,subscriptionStatus:a,refreshStatus:v,createCheckoutSession:N,openCustomerPortal:w}=(0,h.R)(),k=(0,f.Z)(),[P,A]=(0,r.useState)(!1),[C,S]=(0,r.useState)(!1),[_,T]=(0,r.useState)(""),[R,U]=(0,r.useState)(""),[E,F]=(0,r.useState)(null),B=j.find(e=>e.id===(null==a?void 0:a.tier))||j[0],D=(0,r.useMemo)(()=>{if((null==a?void 0:a.tier)==="free"||!(null==a?void 0:a.currentPeriodEnd))return null;let e=new Date(a.currentPeriodEnd),s=new Date,t=Math.ceil((e.getTime()-s.getTime())/864e5);return t>0?t:null},[null==a?void 0:a.tier,null==a?void 0:a.currentPeriodEnd]),I=(e,s)=>{let a={free:0,starter:1,professional:2,enterprise:3};return a[s]>a[e]};(0,r.useEffect)(()=>{let a=e.get("portal_return"),t=e.get("prev_tier");if("true"===a){let e=new URL(window.location.href);e.searchParams.delete("portal_return"),e.searchParams.delete("prev_tier"),window.history.replaceState({},"",e.toString()),(async()=>{let e=0;for(await new Promise(e=>setTimeout(e,3e3));e<8;)try{await v(),await new Promise(e=>setTimeout(e,500));let a=await fetch("/api/stripe/subscription-status?userId=".concat(null==s?void 0:s.id,"&_t=").concat(Date.now()),{cache:"no-store",headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}});if(a.ok){let s=(await a.json()).tier||"free";if(s!==t){if(I(t||"free",s)){g.oR.success("Plan upgraded successfully!");let e=()=>{(0,b.A)({particleCount:100,spread:70,origin:{y:.6}})};e(),setTimeout(e,500),setTimeout(e,1e3)}else g.oR.success("Billing settings updated successfully!");break}if(e>=3){g.oR.success("Billing settings updated successfully!");break}}++e<8&&await new Promise(e=>setTimeout(e,2e3))}catch(s){++e>=8?(g.oR.error("Refreshing page to update subscription status..."),setTimeout(()=>{window.location.reload()},2e3)):await new Promise(e=>setTimeout(e,2e3))}})()}},[e,v,null==s?void 0:s.id]);let $=async()=>{try{A(!0);let e=(null==a?void 0:a.tier)||"free",s="".concat(window.location.origin,"/billing?portal_return=true&prev_tier=").concat(e);await w(s)}catch(e){e.message.includes("No configuration provided")||e.message.includes("default configuration has not been created")?g.oR.error("Billing portal is being set up. Please contact support for plan changes."):g.oR.error("Failed to open billing portal. Please try again."),A(!1)}},L=async()=>{if(!_.trim())return void g.oR.error("Please select a reason for cancellation");A(!0);try{var e;let a={user_email:(null==s?void 0:s.email)||"Unknown",user_name:(null==s||null==(e=s.user_metadata)?void 0:e.first_name)||"User",current_plan:B.name,cancel_reason:_,additional_feedback:R,cancel_date:new Date().toLocaleDateString()};await y.Ay.send("service_2xtn7iv","template_pg7e1af",a,"lm7-ATth2Cql60KIN"),await new Promise(e=>setTimeout(e,1500)),g.oR.success("Subscription cancelled successfully. We've sent your feedback to our team."),S(!1),T(""),U(""),await v()}catch(e){g.oR.error("Failed to cancel subscription. Please contact support.")}finally{A(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,t.jsx)("div",{className:"border-b border-gray-800/50",children:(0,t.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Billing & Plans"}),(0,t.jsx)("div",{className:"flex items-center space-x-1",children:(0,t.jsx)("div",{className:"px-3 py-1 text-sm bg-cyan-500 text-white rounded",children:"Subscription"})})]}),(0,t.jsx)("div",{className:"flex items-center space-x-4"})]})})}),(0,t.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6 hover:shadow-lg transition-all duration-200",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-2 bg-cyan-500/10 rounded-lg",children:(0,t.jsx)(d.A,{className:"h-5 w-5 text-cyan-400"})}),(0,t.jsx)("h2",{className:"text-lg font-semibold text-white",children:"Current Plan"})]}),(null==a?void 0:a.tier)!=="free"&&(0,t.jsx)(u.$,{variant:"outline",onClick:()=>S(!0),className:"text-red-400 border-red-500/20 hover:bg-red-500/10 hover:border-red-500/40 transition-all duration-200 text-sm",size:"sm",children:"Cancel Subscription"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4 mb-3",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-white",children:B.name}),B.popular&&(0,t.jsx)("span",{className:"px-2 py-1 text-xs bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full font-semibold",children:"⭐ Popular"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"text-3xl font-bold text-white",children:0===B.price?(0,t.jsx)("span",{className:"text-green-400",children:"Free"}):(0,t.jsxs)("span",{children:["$",B.price,(0,t.jsxs)("span",{className:"text-gray-400 text-lg font-normal",children:["/",B.interval]})]})}),0===B.price&&(0,t.jsx)("p",{className:"text-green-400 font-medium",children:"Forever free"})]})]}),(0,t.jsx)("div",{className:"flex flex-col justify-center",children:D&&(0,t.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-300 mb-1",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Next Billing"})]}),(0,t.jsxs)("div",{className:"text-white font-semibold",children:[D," days"]})]})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-500/10 rounded-lg",children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-blue-400"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Plan Features"})]}),(0,t.jsx)("div",{className:"space-y-4",children:B.features.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-3 py-2",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:e.included?(0,t.jsx)("div",{className:"p-1 bg-green-500/10 rounded-full",children:(0,t.jsx)(c.A,{className:"h-4 w-4 text-green-400"})}):(0,t.jsx)("div",{className:"p-1 bg-gray-700/50 rounded-full",children:(0,t.jsx)(x.A,{className:"h-4 w-4 text-gray-500"})})}),(0,t.jsxs)("span",{className:"text-sm ".concat(e.included?"text-gray-300":"text-gray-500"),children:[e.name,e.limit&&(0,t.jsxs)("span",{className:"text-gray-400 font-medium",children:[" (",e.limit,")"]})]})]},s))})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,t.jsx)("div",{className:"p-2 bg-orange-500/10 rounded-lg",children:(0,t.jsx)(d.A,{className:"h-5 w-5 text-orange-400"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Manage Subscription"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center p-4 bg-gray-800/30 rounded-lg",children:[(0,t.jsxs)("div",{className:"inline-flex items-center gap-2 bg-green-500/10 text-green-400 px-3 py-1 rounded-full text-sm font-semibold mb-3 border border-green-500/20",children:[(0,t.jsx)(c.A,{className:"h-4 w-4"}),"Active Plan"]}),(0,t.jsx)("h4",{className:"text-xl font-bold text-white mb-1",children:B.name}),(0,t.jsx)("div",{className:"text-2xl font-bold text-white",children:0===B.price?(0,t.jsx)("span",{className:"text-green-400",children:"Free"}):(0,t.jsxs)("span",{children:["$",B.price,(0,t.jsxs)("span",{className:"text-gray-400 text-base font-normal",children:["/",B.interval]})]})})]}),(0,t.jsx)(u.$,{onClick:$,disabled:P,className:"w-full bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl transition-all duration-200",size:"lg",children:(null==a?void 0:a.tier)==="free"?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.A,{className:"h-5 w-5 mr-2"}),"Upgrade Plan"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"h-5 w-5 mr-2"}),"Manage Subscription"]})}),(0,t.jsx)("p",{className:"text-center text-sm text-gray-400",children:(null==a?void 0:a.tier)==="free"?"Unlock advanced features and higher limits":"Change plans, update billing, or cancel anytime"})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,t.jsx)("div",{className:"p-2 bg-green-500/10 rounded-lg",children:(0,t.jsx)(d.A,{className:"h-5 w-5 text-green-400"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Billing Details"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-700/30",children:[(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"Plan"}),(0,t.jsx)("span",{className:"font-medium text-white",children:B.name})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-700/30",children:[(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"Billing Cycle"}),(0,t.jsx)("span",{className:"font-medium text-white",children:0===B.price?"N/A":"Monthly"})]}),D&&(0,t.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-700/30",children:[(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"Next Billing"}),(0,t.jsx)("span",{className:"font-medium text-white",children:new Date(Date.now()+24*D*36e5).toLocaleDateString()})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"Status"}),(0,t.jsx)("span",{className:"px-2 py-1 text-xs rounded-full font-medium ".concat((null==a?void 0:a.tier)==="free"?"bg-gray-700/50 text-gray-300":"bg-green-500/10 text-green-400"),children:(null==a?void 0:a.tier)==="free"?"Free Plan":"Active"})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,t.jsx)("div",{className:"p-2 bg-purple-500/10 rounded-lg",children:(0,t.jsx)(m.A,{className:"h-5 w-5 text-purple-400"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Support"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-gray-300 text-sm",children:"Need help with your subscription or billing? We're here to assist you."}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(u.$,{variant:"outline",className:"w-full justify-start text-gray-300 border-gray-600 hover:bg-gray-800/50 hover:border-gray-500 text-sm",onClick:()=>window.open("/contact","_blank"),children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Contact Support"]}),(0,t.jsx)("div",{className:"text-xs text-gray-400 text-center",children:"Response time: Usually within 24 hours"})]})]})]})]}),C&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,t.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-xl shadow-2xl max-w-md w-full p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,t.jsx)("div",{className:"p-2 bg-red-500/10 rounded-lg",children:(0,t.jsx)(m.A,{className:"h-5 w-5 text-red-400"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Cancel Subscription"})]}),(0,t.jsx)("p",{className:"text-gray-300 mb-6 text-sm",children:"We're sorry to see you go! Please help us improve by telling us why you're cancelling."}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"Reason for cancellation *"}),(0,t.jsxs)("select",{value:_,onChange:e=>T(e.target.value),className:"w-full p-3 border border-gray-600 rounded-lg bg-gray-800 text-white focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-sm",children:[(0,t.jsx)("option",{value:"",children:"Select a reason..."}),["Too expensive","Not using enough features","Found a better alternative","Technical issues","Poor customer support","Missing features I need","Temporary financial constraints","Other"].map(e=>(0,t.jsx)("option",{value:e,children:e},e))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"Additional feedback (optional)"}),(0,t.jsx)("textarea",{value:R,onChange:e=>U(e.target.value),placeholder:"Tell us more about your experience or what we could do better...",rows:3,className:"w-full p-3 border border-gray-600 rounded-lg bg-gray-800 text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-sm"})]})]}),(0,t.jsxs)("div",{className:"flex gap-3 mt-6",children:[(0,t.jsx)(u.$,{variant:"outline",onClick:()=>S(!1),className:"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50 text-sm",children:"Keep Subscription"}),(0,t.jsx)(u.$,{onClick:L,disabled:P||!_.trim(),className:"flex-1 bg-red-600 hover:bg-red-700 text-white text-sm",children:P?"Cancelling...":"Cancel Subscription"})]})]})}),(0,t.jsx)(p.A,{isOpen:k.isOpen,onClose:k.hideConfirmation,onConfirm:k.onConfirm,title:k.title,message:k.message,confirmText:k.confirmText,cancelText:k.cancelText,type:k.type,isLoading:k.isLoading})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,5738,2396,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(38060)),_N_E=e.O()}]);