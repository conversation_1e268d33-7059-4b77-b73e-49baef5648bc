{"name": "fast-deep-equal", "version": "2.0.1", "description": "Fast deep equal", "main": "index.js", "scripts": {"eslint": "eslint *.js benchmark spec", "test-spec": "mocha spec/*.spec.js -R spec", "test-cov": "nyc npm run test-spec", "test-ts": "tsc --target ES5 --noImplicitAny index.d.ts", "test": "npm run eslint && npm run test-ts && npm run test-cov"}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/fast-deep-equal.git"}, "keywords": ["fast", "equal", "deep-equal"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/epoberezkin/fast-deep-equal/issues"}, "homepage": "https://github.com/epoberezkin/fast-deep-equal#readme", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^2.13.1", "deep-eql": "latest", "deep-equal": "latest", "eslint": "^4.0.0", "lodash": "latest", "mocha": "^3.4.2", "nano-equal": "latest", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "ramda": "latest", "shallow-equal-fuzzy": "latest", "typescript": "^2.6.1", "underscore": "latest"}, "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "files": ["index.js", "index.d.ts"], "types": "index.d.ts"}