"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1826],{54547:(e,s,a)=>{a.d(s,{z:()=>k});var t=a(95155),r=a(12115),i=a(13741),l=a(75898),c=a(56671),n=a(26126),d=a(87266),o=a(574);function m(e){let{apiKey:s,onRevoke:a}=e,r=async e=>{try{await navigator.clipboard.writeText(e),c.oR.success("API key copied to clipboard")}catch(e){c.oR.error("Failed to copy API key")}},l=s.expires_at&&new Date(s.expires_at)<new Date,m="active"===s.status&&!l;return(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 transition-all duration-200 hover:border-gray-700/50 ".concat(m?"":"opacity-75"),children:[(0,t.jsx)("div",{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:s.key_name}),(0,t.jsxs)("p",{className:"text-sm text-gray-400",children:["Configuration: ",s.custom_api_configs.name]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(n.E,{className:(e=>{switch(e){case"active":return"bg-green-900/30 text-green-300 border-green-500/50";case"inactive":return"bg-yellow-900/30 text-yellow-300 border-yellow-500/50";case"revoked":return"bg-red-900/30 text-red-300 border-red-500/50";default:return"bg-gray-800/50 text-gray-300 border-gray-600/50"}})(s.status),children:s.status}),l&&(0,t.jsx)(n.E,{className:"bg-red-900/30 text-red-300 border-red-500/50",children:"Expired"})]})]})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"API Key (Masked)"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-800/50 rounded-lg border border-gray-700",children:[(0,t.jsxs)("code",{className:"flex-1 text-sm font-mono text-gray-300",children:[s.key_prefix,"_","*".repeat(28),"string"==typeof s.masked_key?s.masked_key.slice(-4):"xxxx"]}),(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>r("".concat(s.key_prefix,"_").concat("*".repeat(28)).concat("string"==typeof s.masked_key?s.masked_key.slice(-4):"xxxx")),className:"h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-700/50",title:"Copy masked key (for reference only)",children:(0,t.jsx)(d.QR,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-xs text-amber-400 bg-amber-900/20 p-2 rounded",children:[(0,t.jsx)("span",{children:"⚠️"}),(0,t.jsx)("span",{children:"Full API key was only shown once during creation for security. Save it securely when creating new keys."})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Permissions"}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[s.permissions.chat&&(0,t.jsx)(n.E,{variant:"secondary",className:"text-xs bg-blue-900/30 text-blue-300 border-blue-500/50",children:"Chat Completions"}),s.permissions.streaming&&(0,t.jsx)(n.E,{variant:"secondary",className:"text-xs bg-green-900/30 text-green-300 border-green-500/50",children:"Streaming"}),s.permissions.all_models&&(0,t.jsx)(n.E,{variant:"secondary",className:"text-xs bg-purple-900/30 text-purple-300 border-purple-500/50",children:"All Models"})]})]}),(s.allowed_ips.length>0||s.allowed_domains.length>0)&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-300 flex items-center gap-1",children:[(0,t.jsx)(d.ek,{className:"h-4 w-4"}),"Security Restrictions"]}),(0,t.jsxs)("div",{className:"space-y-1 text-xs",children:[s.allowed_ips.length>0&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-gray-400",children:[(0,t.jsx)("span",{className:"font-medium",children:"IPs:"}),(0,t.jsx)("span",{children:s.allowed_ips.join(", ")})]}),s.allowed_domains.length>0&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-gray-400",children:[(0,t.jsx)(d.qz,{className:"h-3 w-3"}),(0,t.jsx)("span",{className:"font-medium",children:"Domains:"}),(0,t.jsx)("span",{children:s.allowed_domains.join(", ")})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-300 flex items-center gap-1",children:[(0,t.jsx)(d.Il,{className:"h-4 w-4"}),"Usage Statistics"]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Total Requests:"}),(0,t.jsx)("span",{className:"ml-2 font-semibold text-white",children:s.total_requests.toLocaleString()})]}),s.last_used_at&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Last Used:"}),(0,t.jsx)("span",{className:"ml-2 font-semibold text-white",children:(0,o.m)(new Date(s.last_used_at),{addSuffix:!0})})]})]})]}),s.expires_at&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-300 flex items-center gap-1",children:[(0,t.jsx)(d.Vv,{className:"h-4 w-4"}),"Expiration"]}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("span",{className:"font-semibold ".concat(l?"text-red-400":"text-white"),children:[new Date(s.expires_at).toLocaleDateString()," at"," ",new Date(s.expires_at).toLocaleTimeString()]}),!l&&(0,t.jsxs)("span",{className:"ml-2 text-gray-400",children:["(",(0,o.m)(new Date(s.expires_at),{addSuffix:!0}),")"]})]})]}),(0,t.jsx)("div",{className:"flex items-center justify-end pt-2 border-t border-gray-700",children:"revoked"!==s.status&&(0,t.jsxs)(i.$,{variant:"destructive",size:"sm",onClick:()=>a(s.id),className:"text-xs",children:[(0,t.jsx)(d.TB,{className:"h-3 w-3 mr-1"}),"Revoke"]})})]})]})}var x=a(54165),y=a(93915),h=a(85057),p=a(55365),g=a(31547);function u(e){let{open:s,onOpenChange:a,onCreateApiKey:l,configName:n,creating:d,subscriptionTier:o}=e,[m,u]=(0,r.useState)("form"),[j,f]=(0,r.useState)(null),[N,k]=(0,r.useState)(!0),[v,b]=(0,r.useState)(!1),[w,_]=(0,r.useState)({key_name:"",expires_at:""}),A=async e=>{if(e.preventDefault(),!w.key_name.trim())return void c.oR.error("Please enter a name for your API key");try{let e=await l({key_name:w.key_name.trim(),expires_at:w.expires_at||void 0});f(e),u("success")}catch(e){}},I=async()=>{if(null==j?void 0:j.api_key)try{await navigator.clipboard.writeText(j.api_key),b(!0),c.oR.success("API key copied to clipboard"),setTimeout(()=>b(!1),2e3)}catch(e){c.oR.error("Failed to copy API key")}},P=()=>{"form"===m&&(u("form"),f(null),k(!0),_({key_name:"",expires_at:""}),a(!1))};return"success"===m&&j?(0,t.jsx)(x.lG,{open:s,onOpenChange:()=>{},modal:!0,children:(0,t.jsxs)(x.Cf,{className:"max-w-lg",children:[(0,t.jsxs)(x.c7,{className:"text-center space-y-3",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(g.Uz,{className:"h-8 w-8 text-green-600"})}),(0,t.jsx)(x.L3,{className:"text-2xl font-bold text-gray-900",children:"API Key Created Successfully!"}),(0,t.jsx)(x.rr,{className:"text-gray-600",children:"Save your API key now - this is the only time you'll see it in full."})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[(0,t.jsxs)(p.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(g.hc,{className:"h-4 w-4 text-red-600"}),(0,t.jsxs)(p.TN,{className:"text-red-800 font-medium",children:[(0,t.jsx)("strong",{children:"Important:"})," This is the only time you'll see the full API key. Make sure to copy and store it securely."]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(h.J,{className:"text-sm font-medium text-gray-700",children:"Your API Key"}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,t.jsx)("code",{className:"flex-1 text-sm font-mono text-gray-900 break-all select-all",children:N?j.api_key:"".concat(j.key_prefix,"_").concat("*".repeat(28)).concat(j.api_key.slice(-4))}),(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>k(!N),className:"h-8 w-8 p-0",title:N?"Hide key":"Show key",children:N?(0,t.jsx)(g.X_,{className:"h-4 w-4"}):(0,t.jsx)(g.kU,{className:"h-4 w-4"})}),(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:I,className:"h-8 w-8 p-0 ".concat(v?"text-green-600":""),title:"Copy to clipboard",children:v?(0,t.jsx)("span",{className:"text-xs",children:"✓"}):(0,t.jsx)(g.QR,{className:"h-4 w-4"})})]})]})}),(0,t.jsx)(i.$,{onClick:I,variant:"outline",className:"w-full",disabled:v,children:v?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"text-green-600 mr-2",children:"✓"}),"Copied!"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.QR,{className:"h-4 w-4 mr-2"}),"Copy API Key"]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(h.J,{className:"text-gray-600",children:"Key Name"}),(0,t.jsx)("p",{className:"font-medium text-gray-900",children:j.key_name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(h.J,{className:"text-gray-600",children:"Created"}),(0,t.jsx)("p",{className:"font-medium text-gray-900",children:new Date(j.created_at).toLocaleString()})]})]})]}),(0,t.jsx)(x.Es,{className:"pt-6",children:(0,t.jsx)(i.$,{onClick:()=>{u("form"),f(null),k(!0),b(!1),_({key_name:"",expires_at:""}),a(!1)},className:"w-full",children:"I've Saved My API Key"})})]})}):(0,t.jsx)(x.lG,{open:s,onOpenChange:P,children:(0,t.jsxs)(x.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(x.c7,{children:[(0,t.jsxs)(x.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(g.Uz,{className:"h-5 w-5"}),"Create API Key"]}),(0,t.jsxs)(x.rr,{children:["Create a new API key for programmatic access to ",n]})]}),(0,t.jsxs)("form",{onSubmit:A,className:"space-y-6",children:[(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"key_name",children:"API Key Name *"}),(0,t.jsx)(y.pd,{id:"key_name",value:w.key_name,onChange:e=>_(s=>({...s,key_name:e.target.value})),placeholder:"e.g., Production API Key",required:!0,className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"A descriptive name to help you identify this API key"})]})}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(h.J,{htmlFor:"expires_at",children:"Expiration Date (Optional)"}),(0,t.jsx)(y.pd,{id:"expires_at",type:"datetime-local",value:w.expires_at,onChange:e=>_(s=>({...s,expires_at:e.target.value})),min:new Date().toISOString().slice(0,16),className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Leave empty for no expiration"})]})}),(0,t.jsxs)(x.Es,{children:[(0,t.jsx)(i.$,{type:"button",variant:"outline",onClick:P,children:"Cancel"}),(0,t.jsx)(i.$,{type:"submit",disabled:d,children:d?"Creating...":"Create API Key"})]})]})]})})}var j=a(6875),f=a(80377),N=a(87162);function k(e){let{configId:s,configName:a}=e,[n,d]=(0,r.useState)([]),[o,x]=(0,r.useState)(!0),[y,h]=(0,r.useState)(!1),[p,g]=(0,r.useState)(!1),[k,v]=(0,r.useState)(null),b=(0,N.Z)(),w=async()=>{try{x(!0);let e=await fetch("/api/user-api-keys?config_id=".concat(s));if(!e.ok)throw Error("Failed to fetch API keys");let a=await e.json();d(a.api_keys||[])}catch(e){c.oR.error("Failed to load API keys")}finally{x(!1)}},_=async e=>{try{let s=await fetch("/api/user/subscription-tier"),a=s.ok?await s.json():null,t=(null==a?void 0:a.tier)||"starter",r={free:3,starter:50,professional:999999,enterprise:999999},i=void 0!==e?e:n.length;v({tier:t,keyLimit:r[t]||r.free,currentCount:i})}catch(s){v({tier:"free",keyLimit:3,currentCount:void 0!==e?e:n.length})}};(0,r.useEffect)(()=>{w()},[s]),(0,r.useEffect)(()=>{n.length>=0&&_()},[n]);let A=async e=>{try{h(!0);let t=await fetch("/api/user-api-keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,custom_api_config_id:s})});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to create API key")}let r=await t.json();c.oR.success("API key created successfully!");let i={...r,custom_api_configs:{id:s,name:a}};return d(e=>{let s=[i,...e];return _(s.length),s}),await w(),r}catch(e){throw c.oR.error(e.message||"Failed to create API key"),e}finally{h(!1)}},I=async e=>{let s=n.find(s=>s.id===e),a=(null==s?void 0:s.key_name)||"this API key";b.showConfirmation({title:"Revoke API Key",message:'Are you sure you want to revoke "'.concat(a,'"? This action cannot be undone and will immediately disable the key.'),confirmText:"Revoke Key",cancelText:"Cancel",type:"danger"},async()=>{try{let s=await fetch("/api/user-api-keys/".concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to revoke API key")}d(s=>s.map(s=>s.id===e?{...s,status:"revoked"}:s)),c.oR.success("API key revoked successfully")}catch(e){throw c.oR.error(e.message||"Failed to revoke API key"),e}})},P=!k||k.currentCount<k.keyLimit;return o?(0,t.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(l.e9,{className:"h-6 w-6 animate-spin mr-2 text-gray-400"}),(0,t.jsx)("span",{className:"text-gray-400",children:"Loading API keys..."})]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold flex items-center gap-2 text-white",children:[(0,t.jsx)(l.Uz,{className:"h-6 w-6"}),"API Keys"]}),(0,t.jsxs)("p",{className:"text-gray-400 mt-1",children:["Generate API keys for programmatic access to ",a]})]}),P?(0,t.jsxs)(i.$,{onClick:()=>g(!0),className:"flex items-center gap-2",children:[(0,t.jsx)(l.FW,{className:"h-4 w-4"}),"Create API Key"]}):(0,t.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,t.jsxs)(i.$,{disabled:!0,className:"flex items-center gap-2 opacity-50",children:[(0,t.jsx)(l.FW,{className:"h-4 w-4"}),"Create API Key"]}),(0,t.jsx)("p",{className:"text-xs text-orange-400 font-medium",children:(null==k?void 0:k.tier)==="free"?"Upgrade to Starter plan for more API keys":"API key limit reached - upgrade for unlimited keys"})]})]}),(null==k?void 0:k.tier)==="free"&&(0,t.jsx)(j.L,{message:"Unlock intelligent routing and more API keys",variant:"compact",className:"mb-4"}),(0,t.jsx)(j.p,{className:"mb-4"}),0===n.length?(0,t.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg text-center py-8",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)(l.Uz,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2 text-white",children:"No API Keys"}),(0,t.jsx)("p",{className:"text-gray-400 mb-4",children:"Create your first API key to start using the RouKey API programmatically."}),P?(0,t.jsxs)(i.$,{onClick:()=>g(!0),children:[(0,t.jsx)(l.FW,{className:"h-4 w-4 mr-2"}),"Create Your First API Key"]}):(0,t.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,t.jsxs)(i.$,{disabled:!0,className:"opacity-50",children:[(0,t.jsx)(l.FW,{className:"h-4 w-4 mr-2"}),"Create Your First API Key"]}),(0,t.jsx)("p",{className:"text-xs text-orange-400 font-medium",children:(null==k?void 0:k.tier)==="free"?"Upgrade to Starter plan to create API keys":"API key limit reached - upgrade for unlimited keys"})]})]})}):(0,t.jsx)("div",{className:"grid gap-4",children:n.map(e=>(0,t.jsx)(m,{apiKey:e,onRevoke:I},e.id))}),(0,t.jsx)(u,{open:p,onOpenChange:e=>{g(e)},onCreateApiKey:A,configName:a,creating:y,subscriptionTier:(null==k?void 0:k.tier)||"starter"}),(0,t.jsx)(f.A,{isOpen:b.isOpen,onClose:b.hideConfirmation,onConfirm:b.onConfirm,title:b.title,message:b.message,confirmText:b.confirmText,cancelText:b.cancelText,type:b.type,isLoading:b.isLoading})]})}}}]);