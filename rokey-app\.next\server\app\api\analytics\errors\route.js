(()=>{var e={};e.id=4748,e.ids=[1489,4748],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{H:()=>n,Q:()=>u,createSupabaseServerClientOnRequest:()=>i});var s=r(34386),a=r(39398),o=r(44999);async function i(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function u(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}function n(){return(0,a.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77976:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>c});var a=r(96559),o=r(48088),i=r(37719),u=r(32190),n=r(2507);async function c(e){try{let t=(0,n.Q)(e),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),o=parseInt(a.get("days")||"30"),i=new Date;i.setDate(i.getDate()-o);let{data:c,count:p}=await t.from("request_logs").select("id",{count:"exact"}).eq("user_id",r.id).gte("request_timestamp",i.toISOString()),{data:d,count:l}=await t.from("request_logs").select("id, status_code, error_message, error_source, request_timestamp",{count:"exact"}).eq("user_id",r.id).gte("request_timestamp",i.toISOString()).gte("status_code",400),m=p||0,g=l||0,h=m>0?g/m*100:0,I={},q={},x=[];d&&d.forEach(e=>{let t=e.status_code;I[t]=(I[t]||0)+1;let r=e.error_source||"unknown";q[r]=(q[r]||0)+1,e.error_message&&x.push({message:e.error_message,source:e.error_source,statusCode:e.status_code,timestamp:e.request_timestamp})});let S=Object.entries(I).sort(([,e],[,t])=>t-e).slice(0,10).map(([e,t])=>({statusCode:parseInt(e),count:t,description:({400:"Bad Request",401:"Unauthorized",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",409:"Conflict",422:"Unprocessable Entity",429:"Too Many Requests",500:"Internal Server Error",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout"})[parseInt(e)]||"Unknown Error"})),y=Object.entries(q).sort(([,e],[,t])=>t-e).slice(0,10).map(([e,t])=>({source:e,count:t})),w={};d&&d.forEach(e=>{let t=new Date(e.request_timestamp).toISOString().split("T")[0];w[t]=(w[t]||0)+1});let _=[];for(let e=o-1;e>=0;e--){let t=new Date;t.setDate(t.getDate()-e);let r=t.toISOString().split("T")[0];_.push({date:r,errors:w[r]||0})}let f=x.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()).slice(0,20),v=new Date(i);v.setDate(v.getDate()-o);let{count:O}=await t.from("request_logs").select("id",{count:"exact"}).eq("user_id",r.id).gte("request_timestamp",v.toISOString()).lt("request_timestamp",i.toISOString()),{count:R}=await t.from("request_logs").select("id",{count:"exact"}).eq("user_id",r.id).gte("request_timestamp",v.toISOString()).lt("request_timestamp",i.toISOString()).gte("status_code",400),D=O||0;return u.NextResponse.json({success:!0,data:{totalRequests:p||0,failedRequests:l||0,errorRate:h,errorRateTrend:h-(D>0?(R||0)/D*100:0),statusCodeBreakdown:S,errorSourceBreakdown:y,errorTimeSeriesData:_,recentErrors:f,period:`${o} days`,startDate:i.toISOString(),endDate:new Date().toISOString()}})}catch(e){return u.NextResponse.json({error:"Failed to fetch errors analytics",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/analytics/errors/route",pathname:"/api/analytics/errors",filename:"route",bundlePath:"app/api/analytics/errors/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\analytics\\errors\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=p;function g(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(77976));module.exports=s})();