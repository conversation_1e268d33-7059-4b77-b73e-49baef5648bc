(()=>{var e={};e.id=1745,e.ids=[1745],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15341:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>L});var a=t(60687),r=t(43210),l=t(16189),n=t(11016),i=t(45700);let o=r.forwardRef(function({title:e,titleId:s,...t},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?r.createElement("title",{id:s},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181"}))});var c=t(30922),d=t(66524);let x=r.forwardRef(function({title:e,titleId:s,...t},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?r.createElement("title",{id:s},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});var m=t(59168);let h=r.forwardRef(function({title:e,titleId:s,...t},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?r.createElement("title",{id:s},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.602H7.923a3.375 3.375 0 0 0-3.285 2.602l-2.268 9.64a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3m16.5 0h.008v.008h-.008v-.008Zm-3 0h.008v.008h-.008v-.008Z"}))});var p=t(61245),g=t(45994),u=t(50515),b=t(17712),j=t(31082),y=t(68589),f=t(70143),v=t(97450),N=t(81521),w=t(74461),k=t(14566);let C=e=>e>=1e6?`${(e/1e6).toFixed(1)}M`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString(),S=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:4}).format(e),A=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},R=(e,s)=>{if(0===s)return{percentage:0,isPositive:e>0};let t=(e-s)/s*100;return{percentage:Math.abs(t),isPositive:t>=0}},M=({title:e,value:s,trend:t,icon:r,subtitle:l})=>(0,a.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-400 mb-1",children:e}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-white",children:"number"==typeof s?C(s):s}),t&&(0,a.jsxs)("span",{className:`text-sm px-2 py-1 rounded-md flex items-center space-x-1 ${t.isPositive?"text-green-400 bg-green-400/10":"text-red-400 bg-red-400/10"}`,children:[t.isPositive?(0,a.jsx)(i.A,{className:"w-3 h-3"}):(0,a.jsx)(o,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[t.percentage.toFixed(1),"%"]})]})]}),l&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:l})]}),(0,a.jsx)("div",{className:"text-gray-500",children:r})]})});function E(){(0,l.useRouter)();let e=(0,n.R)(),[s,t]=(0,r.useState)(null),[E,L]=(0,r.useState)(null),[P,q]=(0,r.useState)([]),[$,_]=(0,r.useState)([]),[T,I]=(0,r.useState)(!0),[F,D]=(0,r.useState)(null),[K,B]=(0,r.useState)(null),[O,U]=(0,r.useState)(null),[H,G]=(0,r.useState)(null),[W,Z]=(0,r.useState)(null),[z,V]=(0,r.useState)({}),[Q,X]=(0,r.useState)("30"),[Y,J]=(0,r.useState)(""),[ee,es]=(0,r.useState)("overview");(0,r.useCallback)(async()=>{if(!z.users){V(e=>({...e,users:!0}));try{let e=await fetch(`/api/analytics/users?days=${Q}`);if(e.ok){let s=await e.json();B(s.data)}}catch(e){}finally{V(e=>({...e,users:!1}))}}},[Q,z.users]),(0,r.useCallback)(async()=>{if(!z.errors){V(e=>({...e,errors:!0}));try{let e=await fetch(`/api/analytics/errors?days=${Q}`);if(e.ok){let s=await e.json();U(s.data)}}catch(e){}finally{V(e=>({...e,errors:!1}))}}},[Q,z.errors]),(0,r.useCallback)(async()=>{if(!z.cache){V(e=>({...e,cache:!0}));try{let e=await fetch(`/api/analytics/cache?days=${Q}`);if(e.ok){let s=await e.json();G(s.data)}}catch(e){}finally{V(e=>({...e,cache:!1}))}}},[Q,z.cache]),(0,r.useCallback)(async()=>{if(!z.metadata){V(e=>({...e,metadata:!0}));try{let e=await fetch(`/api/analytics/metadata?days=${Q}`);if(e.ok){let s=await e.json();Z(s.data)}}catch(e){}finally{V(e=>({...e,metadata:!1}))}}},[Q,z.metadata]);let et=(0,r.useCallback)(async()=>{try{I(!0),D(null);try{let e=await fetch("/api/analytics/summary?groupBy=provider");if(401===e.status)throw Error("Authentication required. Please log in to view analytics.")}catch(e){}let e=new URLSearchParams,s=new Date;s.setDate(s.getDate()-parseInt(Q)),e.append("startDate",s.toISOString()),Y&&e.append("customApiConfigId",Y);let a=await fetch(`/api/analytics/summary?${e.toString()}&groupBy=provider`);if(!a.ok){let e=await a.text();if(401===a.status)throw Error("Authentication required. Please log in to view analytics.");throw Error(`Failed to fetch analytics data: ${a.status} ${e}`)}let r=await a.json();if(t(r),r.summary?.total_requests>0)try{let s=new URLSearchParams,t=new Date;t.setDate(t.getDate()-2*parseInt(Q));let a=new Date;a.setDate(a.getDate()-parseInt(Q)),s.append("startDate",t.toISOString()),s.append("endDate",a.toISOString()),Y&&s.append("customApiConfigId",Y);let[r,l]=await Promise.all([fetch(`/api/analytics/summary?${s.toString()}&groupBy=provider`),fetch(`/api/analytics/summary?${e.toString()}&groupBy=day`)]),n=r.ok?await r.json():null,i=l.ok?await l.json():null;if(L(n),i?.grouped_data){let e=i.grouped_data.map(e=>({date:e.period||e.name,cost:e.cost||0,requests:e.requests||0,tokens:(e.input_tokens||0)+(e.output_tokens||0),latency:e.avg_latency||0}));q(e)}}catch(e){}}catch(e){D(e.message)}finally{I(!1)}},[Q,Y]);if(e?.loading!==!1||T)return(0,a.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white",children:(0,a.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"animate-pulse space-y-8",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-800 rounded w-1/3"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg p-6 border border-gray-800",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-800 rounded w-1/2 mb-4"}),(0,a.jsx)("div",{className:"h-8 bg-gray-800 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-800 rounded w-1/3"})]},s))})]}),(0,a.jsxs)("div",{className:"fixed bottom-4 right-4 bg-gray-800 rounded-lg p-3 flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-300",children:e?.loading!==!1?"Authenticating...":"Loading analytics..."})]})]})});if(F)return(0,a.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white",children:(0,a.jsx)("div",{className:"w-full px-6 py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold mb-4",children:"Analytics"}),(0,a.jsxs)("div",{className:"bg-red-900/20 border border-red-800 rounded-lg p-6 max-w-md mx-auto",children:[(0,a.jsxs)("p",{className:"text-red-400 mb-4",children:["Error loading analytics: ",F]}),(0,a.jsx)("button",{onClick:et,className:"px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 transition-colors",children:"Retry"})]})]})})});let ea=s?.summary,er=E?.summary,el=er?R(ea?.total_cost||0,er.total_cost):void 0,en=er?R(ea?.total_requests||0,er.total_requests):void 0,ei=er?R(ea?.average_latency||0,er.average_latency||0):void 0,eo=er?R(ea?.success_rate||0,er.success_rate):void 0;return(0,a.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,a.jsx)("div",{className:"border-b border-gray-800/50",children:(0,a.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-8",children:(0,a.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Analytics"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,a.jsx)(c.A,{className:"w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search Filter",className:"bg-transparent border-none outline-none text-gray-400 placeholder-gray-500 w-32"})]}),(0,a.jsxs)("select",{value:Q,onChange:e=>X(e.target.value),className:"px-3 py-1 bg-gray-800 border border-gray-700 rounded text-sm text-white focus:outline-none focus:border-cyan-500",children:[(0,a.jsx)("option",{value:"7",children:"Last 7 days"}),(0,a.jsx)("option",{value:"30",children:"Last 30 days"}),(0,a.jsx)("option",{value:"90",children:"Last 90 days"})]})]})]})})}),(0,a.jsx)("div",{className:"border-b border-gray-800/50",children:(0,a.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,a.jsxs)("button",{onClick:()=>es("overview"),className:`flex items-center space-x-2 py-4 border-b-2 transition-colors ${"overview"===ee?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"}`,children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Overview"})]}),(0,a.jsxs)("button",{onClick:()=>es("users"),className:`flex items-center space-x-2 py-4 border-b-2 transition-colors ${"users"===ee?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"}`,children:[(0,a.jsx)(x,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Users"})]}),(0,a.jsxs)("button",{onClick:()=>es("errors"),className:`flex items-center space-x-2 py-4 border-b-2 transition-colors ${"errors"===ee?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"}`,children:[(0,a.jsx)(m.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Errors"})]}),(0,a.jsxs)("button",{onClick:()=>es("cache"),className:`flex items-center space-x-2 py-4 border-b-2 transition-colors ${"cache"===ee?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"}`,children:[(0,a.jsx)(h,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Cache"})]}),(0,a.jsxs)("button",{onClick:()=>es("metadata"),className:`flex items-center space-x-2 py-4 border-b-2 transition-colors ${"metadata"===ee?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"}`,children:[(0,a.jsx)(p.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Metadata"})]})]})})}),(0,a.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:["overview"===ee&&(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(M,{title:"Total Request Made",value:ea?.total_requests||0,trend:en,icon:(0,a.jsx)(g.A,{className:"w-6 h-6"})}),(0,a.jsx)(M,{title:"Average Latency",value:`${Math.round(ea?.average_latency||0)}ms`,trend:ei,icon:(0,a.jsx)(u.A,{className:"w-6 h-6"})}),(0,a.jsx)(M,{title:"User Feedback",value:`${(ea?.success_rate||0).toFixed(1)}%`,trend:eo,icon:(0,a.jsx)(b.A,{className:"w-6 h-6"})}),(0,a.jsx)(M,{title:"Total Cost",value:S(ea?.total_cost||0),trend:el,icon:(0,a.jsx)(j.A,{className:"w-6 h-6"})})]}),(!ea||0===ea.total_requests)&&!T&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(g.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Analytics Data Yet"}),(0,a.jsx)("p",{className:"text-gray-400 mb-4",children:"Start making API requests to see your analytics data here."}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-6",children:"Analytics will appear once you begin using your API configurations."}),!1]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Cost"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-white",children:S(ea?.total_cost||0)}),el&&(0,a.jsxs)("span",{className:`text-sm px-2 py-1 rounded flex items-center space-x-1 ${el.isPositive?"text-red-400 bg-red-400/10":"text-green-400 bg-green-400/10"}`,children:[el.isPositive?(0,a.jsx)(i.A,{className:"w-3 h-3"}):(0,a.jsx)(o,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[el.percentage.toFixed(1),"%"]})]})]})]}),(0,a.jsx)("div",{className:"text-gray-500",children:(0,a.jsx)(j.A,{className:"w-6 h-6"})})]}),(0,a.jsx)("div",{className:"h-48 relative bg-gray-800/50 rounded",children:P.length>0?(0,a.jsx)("div",{className:"absolute inset-4",children:(0,a.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 400 120",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"costGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#10b981",stopOpacity:"0.3"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#10b981",stopOpacity:"0"})]})}),[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)("line",{x1:"0",y1:24*s,x2:"400",y2:24*s,stroke:"#374151",strokeWidth:"0.5",opacity:"0.3"},s)),(0,a.jsx)("polyline",{fill:"none",stroke:"#10b981",strokeWidth:"2",points:P.map((e,s)=>{let t=s/Math.max(P.length-1,1)*400,a=Math.max(...P.map(e=>e.cost)),r=120-e.cost/a*100;return`${t},${r}`}).join(" ")}),(0,a.jsx)("polygon",{fill:"url(#costGradient)",points:`0,120 ${P.map((e,s)=>{let t=s/Math.max(P.length-1,1)*400,a=Math.max(...P.map(e=>e.cost)),r=120-e.cost/a*100;return`${t},${r}`}).join(" ")} 400,120`})]})}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(g.A,{className:"h-8 w-8 mx-auto mb-2 text-gray-500"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No data available"})]})})})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Latency"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsxs)("span",{className:"text-3xl font-bold text-white",children:[Math.round(ea?.average_latency||0),"ms"]}),ei&&(0,a.jsxs)("span",{className:`text-sm px-2 py-1 rounded flex items-center space-x-1 ${ei.isPositive?"text-red-400 bg-red-400/10":"text-green-400 bg-green-400/10"}`,children:[ei.isPositive?(0,a.jsx)(i.A,{className:"w-3 h-3"}):(0,a.jsx)(o,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[ei.percentage.toFixed(1),"%"]})]})]})]}),(0,a.jsx)("div",{className:"text-gray-500",children:(0,a.jsx)(u.A,{className:"w-6 h-6"})})]}),(0,a.jsx)("div",{className:"h-48 relative bg-gray-800/50 rounded",children:P.length>0?(0,a.jsx)("div",{className:"absolute inset-4",children:(0,a.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 400 120",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"latencyGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#f59e0b",stopOpacity:"0.3"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#f59e0b",stopOpacity:"0"})]})}),[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)("line",{x1:"0",y1:24*s,x2:"400",y2:24*s,stroke:"#374151",strokeWidth:"0.5",opacity:"0.3"},s)),(0,a.jsx)("polyline",{fill:"none",stroke:"#f59e0b",strokeWidth:"2",points:P.map((e,s)=>{let t=s/Math.max(P.length-1,1)*400,a=Math.max(...P.map(e=>e.latency||0)),r=120-(e.latency||0)/Math.max(a,1)*100;return`${t},${r}`}).join(" ")}),(0,a.jsx)("polygon",{fill:"url(#latencyGradient)",points:`0,120 ${P.map((e,s)=>{let t=s/Math.max(P.length-1,1)*400,a=Math.max(...P.map(e=>e.latency||0)),r=120-(e.latency||0)/Math.max(a,1)*100;return`${t},${r}`}).join(" ")} 400,120`})]})}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(u.A,{className:"h-8 w-8 mx-auto mb-2 text-gray-500"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No data available"})]})})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Tokens Used"}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"March 28"})]}),(0,a.jsx)("div",{className:"text-gray-500",children:(0,a.jsx)(y.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-white",children:C(ea?.total_tokens||0)}),(0,a.jsxs)("span",{className:"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1",children:[(0,a.jsx)(i.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"8.39%"})]})]}),(0,a.jsx)("div",{className:"h-32 relative bg-gray-800/50 rounded",children:(0,a.jsxs)("div",{className:"absolute inset-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-4 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-gray-400",children:"Input Token"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-gray-400",children:"Output Token"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-gray-400",children:"Total Token"})]})]}),(0,a.jsx)("div",{className:"relative h-16",children:[...Array(20)].map((e,s)=>(0,a.jsx)("div",{className:`absolute w-1 h-1 rounded-full ${s%3==0?"bg-yellow-500":s%3==1?"bg-green-500":"bg-blue-500"}`,style:{left:`${90*Math.random()}%`,top:`${80*Math.random()}%`}},s))})]})})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Requests"})}),(0,a.jsx)("div",{className:"text-gray-500",children:(0,a.jsx)(f.A,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-white",children:C(ea?.total_requests||0)}),(0,a.jsxs)("span",{className:"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1",children:[(0,a.jsx)(i.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"3.39%"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:s?.grouped_data.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-20 text-sm text-gray-400 truncate",children:e.name}),(0,a.jsx)("div",{className:"flex-1 mx-3",children:(0,a.jsx)("div",{className:"h-2 bg-gray-800 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:`h-full rounded-full ${0===s?"bg-pink-500":1===s?"bg-purple-500":2===s?"bg-cyan-500":3===s?"bg-green-500":"bg-yellow-500"}`,style:{width:`${e.requests/(ea?.total_requests||1)*100}%`}})})}),(0,a.jsx)("div",{className:"text-sm text-gray-400 w-12 text-right",children:C(e.requests)})]},e.name))})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Unique Users"})}),(0,a.jsx)("div",{className:"text-gray-500",children:(0,a.jsx)(x,{className:"w-6 h-6"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,a.jsx)("span",{className:"text-3xl font-bold text-white",children:ea?.successful_requests||0}),(0,a.jsxs)("span",{className:"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1",children:[(0,a.jsx)(i.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"3.39%"})]})]}),(0,a.jsx)("div",{className:"h-32 relative bg-gray-800/50 rounded",children:(0,a.jsx)("div",{className:"absolute inset-4",children:(0,a.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 200 80",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{id:"waveGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,a.jsx)("stop",{offset:"0%",stopColor:"#8b5cf6",stopOpacity:"0.3"}),(0,a.jsx)("stop",{offset:"100%",stopColor:"#8b5cf6",stopOpacity:"0"})]})}),(0,a.jsx)("path",{d:"M0,40 Q50,20 100,40 T200,40",fill:"none",stroke:"#8b5cf6",strokeWidth:"2"}),(0,a.jsx)("path",{d:"M0,40 Q50,20 100,40 T200,40 L200,80 L0,80 Z",fill:"url(#waveGradient)"})]})})})]})]})]}),"users"===ee&&(0,a.jsx)("div",{className:"space-y-6",children:z.users?(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"}),(0,a.jsx)("span",{className:"ml-3 text-gray-400",children:"Loading users analytics..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Active Users"}),(0,a.jsx)(x,{className:"w-6 h-6 text-gray-400"})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:K?.activeUsers||0}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Users with API activity"})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"API Keys Generated"}),(0,a.jsx)(v.A,{className:"w-6 h-6 text-gray-400"})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:C(K?.totalApiKeys||0)}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"User-generated API keys"})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Total Requests"}),(0,a.jsx)(g.A,{className:"w-6 h-6 text-gray-400"})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:C(K?.totalRequests||0)}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"API requests made"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"User Profile"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Subscription Tier"}),(0,a.jsx)("span",{className:"text-white capitalize",children:K?.userProfile?.tier||"Free"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Account Status"}),(0,a.jsx)("span",{className:"text-green-400 capitalize",children:K?.userProfile?.status||"Active"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Success Rate"}),(0,a.jsxs)("span",{className:"text-white",children:[K?.successRate?.toFixed(1)||0,"%"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Total Configs"}),(0,a.jsx)("span",{className:"text-white",children:K?.totalConfigs||0})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Top Models Used"}),(0,a.jsx)("div",{className:"space-y-3",children:K?.topModels?.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 truncate",children:e.model}),(0,a.jsx)("span",{className:"text-white",children:e.count})]},e.model))||(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"No model usage data available"})})]})]}),K?.timeSeriesData&&K.timeSeriesData.length>0&&(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Activity Timeline"}),(0,a.jsx)("div",{className:"h-64 flex items-end space-x-2",children:K.timeSeriesData.map((e,s)=>(0,a.jsxs)("div",{className:"flex-1 flex flex-col items-center",children:[(0,a.jsx)("div",{className:"w-full bg-cyan-500 rounded-t",style:{height:`${Math.max(e.requests/Math.max(...K.timeSeriesData.map(e=>e.requests))*200,2)}px`}}),(0,a.jsx)("span",{className:"text-xs text-gray-500 mt-2 transform -rotate-45",children:new Date(e.date).toLocaleDateString("en-US",{month:"short",day:"numeric"})})]},e.date))})]})]})}),"errors"===ee&&(0,a.jsx)("div",{className:"space-y-6",children:z.errors?(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"}),(0,a.jsx)("span",{className:"ml-3 text-gray-400",children:"Loading error analytics..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Error Rate"}),(0,a.jsx)(m.A,{className:"w-6 h-6 text-red-400"})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:O?.errorRate?`${O.errorRate.toFixed(2)}%`:"0%"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Failed requests percentage"}),O?.errorRateTrend!==void 0&&(0,a.jsxs)("span",{className:`text-xs px-2 py-1 rounded ${O.errorRateTrend>0?"bg-red-500/20 text-red-400":"bg-green-500/20 text-green-400"}`,children:[O.errorRateTrend>0?"+":"",O.errorRateTrend.toFixed(2),"%"]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Total Errors"}),(0,a.jsx)(N.A,{className:"w-6 h-6 text-red-400"})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:C(O?.failedRequests||0)}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Failed requests count"})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Total Requests"}),(0,a.jsx)(g.A,{className:"w-6 h-6 text-gray-400"})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:C(O?.totalRequests||0)}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"All requests processed"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Top Error Status Codes"}),(0,a.jsx)("div",{className:"space-y-3",children:O?.statusCodeBreakdown?.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-white",children:e.statusCode}),(0,a.jsx)("span",{className:"text-gray-400 text-sm ml-2",children:e.description})]}),(0,a.jsx)("span",{className:"text-red-400",children:e.count})]},e.statusCode))||(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"No error data available"})})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Error Sources"}),(0,a.jsx)("div",{className:"space-y-3",children:O?.errorSourceBreakdown?.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 capitalize",children:e.source}),(0,a.jsx)("span",{className:"text-red-400",children:e.count})]},e.source))||(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"No error source data available"})})]})]}),O?.recentErrors&&O.recentErrors.length>0&&(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Errors"}),(0,a.jsx)("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:O.recentErrors.slice(0,10).map((e,s)=>(0,a.jsx)("div",{className:"border-l-2 border-red-500 pl-4 py-2",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-white text-sm font-medium",children:e.message}),(0,a.jsxs)("p",{className:"text-gray-400 text-xs mt-1",children:["Source: ",e.source," | Status: ",e.statusCode]})]}),(0,a.jsx)("span",{className:"text-gray-500 text-xs",children:new Date(e.timestamp).toLocaleString()})]})},s))})]})]})}),"cache"===ee&&(0,a.jsx)("div",{className:"space-y-6",children:z.cache?(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"}),(0,a.jsx)("span",{className:"ml-3 text-gray-400",children:"Loading cache analytics..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Cache Hit Rate"}),(0,a.jsx)(h,{className:"w-6 h-6 text-green-400"})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:H?.cacheHitRate?`${H.cacheHitRate.toFixed(1)}%`:"0%"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Semantic cache efficiency"}),H?.hitRateTrend!==void 0&&(0,a.jsxs)("span",{className:`text-xs px-2 py-1 rounded ${H.hitRateTrend>0?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"}`,children:[H.hitRateTrend>0?"+":"",H.hitRateTrend.toFixed(1),"%"]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Cache Entries"}),(0,a.jsx)(w.A,{className:"w-6 h-6 text-blue-400"})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:C(H?.totalCacheEntries||0)}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Stored cache responses"})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Cost Savings"}),(0,a.jsx)(j.A,{className:"w-6 h-6 text-green-400"})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:S(H?.totalCostSaved||0)}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Saved through caching"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Cache Performance"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Total Hits"}),(0,a.jsx)("span",{className:"text-green-400",children:C(H?.totalHits||0)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Total Misses"}),(0,a.jsx)("span",{className:"text-red-400",children:C(H?.totalMisses||0)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Avg Response Time Saved"}),(0,a.jsxs)("span",{className:"text-white",children:[H?.averageResponseTimeSaved?.toFixed(0)||0,"ms"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Storage Used"}),(0,a.jsx)("span",{className:"text-white",children:A(H?.totalStorageBytes||0)})]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Cache by Provider"}),(0,a.jsx)("div",{className:"space-y-3",children:H?.cacheByProvider?.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:e.provider}),(0,a.jsx)("span",{className:"text-white",children:e.count})]},e.provider))||(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"No cache data available"})})]})]}),H?.topCachedModels&&H.topCachedModels.length>0&&(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Most Cached Models"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:H.topCachedModels.slice(0,6).map(e=>(0,a.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-white font-medium truncate",children:e.model}),(0,a.jsxs)("div",{className:"text-cyan-400 text-sm",children:[e.count," cached responses"]})]},e.model))})]})]})}),"metadata"===ee&&(0,a.jsx)("div",{className:"space-y-6",children:z.metadata?(0,a.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"}),(0,a.jsx)("span",{className:"ml-3 text-gray-400",children:"Loading metadata analytics..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Total Configurations"}),(0,a.jsx)(k.A,{className:"w-6 h-6 text-orange-400"})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:W?.totalConfigs||0}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Active API configurations"})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Unique Models"}),(0,a.jsx)(y.A,{className:"w-6 h-6 text-green-400"})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:W?.uniqueModels||0}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Different models used"})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"API Keys"}),(0,a.jsx)(v.A,{className:"w-6 h-6 text-cyan-400"})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:(W?.totalApiKeys||0)+(W?.totalUserApiKeys||0)}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Total API keys configured"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Routing Strategies"}),(0,a.jsx)("div",{className:"space-y-3",children:W?.routingStrategyBreakdown?.map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:e.strategy}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-white",children:e.count}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["(",e.percentage.toFixed(1),"%)"]})]})]},e.strategy))||(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"No routing strategy data available"})})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Performance Metrics"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Avg Processing Time"}),(0,a.jsxs)("span",{className:"text-white",children:[W?.averageProcessingTime?.toFixed(0)||0,"ms"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Avg Request Size"}),(0,a.jsx)("span",{className:"text-white",children:A(W?.averageRequestSize||0)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Avg Response Size"}),(0,a.jsx)("span",{className:"text-white",children:A(W?.averageResponseSize||0)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Config Age"}),(0,a.jsxs)("span",{className:"text-white",children:[W?.averageConfigAge?.toFixed(0)||0," days"]})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Top Models"}),(0,a.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:W?.topModels?.slice(0,10).map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center py-1",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm truncate",children:e.model}),(0,a.jsx)("span",{className:"text-white text-sm",children:e.count})]},e.model))||(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"No model usage data available"})})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Temperature Distribution"}),(0,a.jsx)("div",{className:"space-y-3",children:W?.temperatureDistribution?.map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:e.range}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-white",children:e.count}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["(",e.percentage.toFixed(1),"%)"]})]})]},e.range))||(0,a.jsx)("p",{className:"text-gray-500 text-sm",children:"No temperature data available"})})]})]})]})})]})]})}function L(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-[#040716] text-white",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-8",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-800 rounded w-1/3"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsxs)("div",{className:"bg-gray-900 rounded-lg p-6 border border-gray-800",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-800 rounded w-1/2 mb-4"}),(0,a.jsx)("div",{className:"h-8 bg-gray-800 rounded w-3/4 mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-800 rounded w-1/3"})]},s))})]})})}),children:(0,a.jsx)(E,{})})}},17712:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(43210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 0 1-.825-.242m9.345-8.334a2.126 2.126 0 0 0-.476-.095 48.64 48.64 0 0 0-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0 0 11.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"}))})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19153:(e,s,t)=>{Promise.resolve().then(t.bind(t,15341))},21031:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\analytics\\page.tsx","default")},23425:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=t(65239),r=t(48088),l=t(88170),n=t.n(l),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let c={children:["",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21031)),"C:\\RoKey App\\rokey-app\\src\\app\\analytics\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,28044)),"C:\\RoKey App\\rokey-app\\src\\app\\analytics\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\RoKey App\\rokey-app\\src\\app\\analytics\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/analytics/page",pathname:"/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28044:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(37413),r=t(47417);function l(){return(0,a.jsx)(r.AnalyticsSkeleton,{})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31082:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(43210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40877:(e,s,t)=>{Promise.resolve().then(t.bind(t,47417))},45700:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(43210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))})},47417:(e,s,t)=>{"use strict";t.d(s,{AnalyticsSkeleton:()=>o,ConfigSelectorSkeleton:()=>l,MessageSkeleton:()=>r,MyModelsSkeleton:()=>n,RoutingSetupSkeleton:()=>i});var a=t(12907);(0,a.registerClientReference)(function(){throw Error("Attempted to call LoadingSkeleton() from the server but LoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LoadingSkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call ChatHistorySkeleton() from the server but ChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ChatHistorySkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call EnhancedChatHistorySkeleton() from the server but EnhancedChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","EnhancedChatHistorySkeleton");let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call MessageSkeleton() from the server but MessageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MessageSkeleton"),l=(0,a.registerClientReference)(function(){throw Error("Attempted to call ConfigSelectorSkeleton() from the server but ConfigSelectorSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ConfigSelectorSkeleton");(0,a.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","DashboardSkeleton");let n=(0,a.registerClientReference)(function(){throw Error("Attempted to call MyModelsSkeleton() from the server but MyModelsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MyModelsSkeleton"),i=(0,a.registerClientReference)(function(){throw Error("Attempted to call RoutingSetupSkeleton() from the server but RoutingSetupSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","RoutingSetupSkeleton");(0,a.registerClientReference)(function(){throw Error("Attempted to call TrainingSkeleton() from the server but TrainingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","TrainingSkeleton");let o=(0,a.registerClientReference)(function(){throw Error("Attempted to call AnalyticsSkeleton() from the server but AnalyticsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","AnalyticsSkeleton");(0,a.registerClientReference)(function(){throw Error("Attempted to call PlaygroundSkeleton() from the server but PlaygroundSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","PlaygroundSkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call LogsSkeleton() from the server but LogsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LogsSkeleton"),(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LoadingSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","default")},50515:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(43210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},54001:(e,s,t)=>{Promise.resolve().then(t.bind(t,21031))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64845:(e,s,t)=>{Promise.resolve().then(t.bind(t,35291))},66524:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(43210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},68589:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(43210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))})},70143:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(43210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))})},74075:e=>{"use strict";e.exports=require("zlib")},74461:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(43210);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"}))})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,5449,4912],()=>t(23425));module.exports=a})();