"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/utils/streamingUtils.ts":
/*!*************************************!*\
  !*** ./src/utils/streamingUtils.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERFORMANCE_THRESHOLDS: () => (/* binding */ PERFORMANCE_THRESHOLDS),\n/* harmony export */   createFirstTokenTrackingStream: () => (/* binding */ createFirstTokenTrackingStream),\n/* harmony export */   estimateTokenCount: () => (/* binding */ estimateTokenCount),\n/* harmony export */   estimateUsageFromContent: () => (/* binding */ estimateUsageFromContent),\n/* harmony export */   evaluatePerformance: () => (/* binding */ evaluatePerformance),\n/* harmony export */   extractUsageFromStreamChunk: () => (/* binding */ extractUsageFromStreamChunk),\n/* harmony export */   getProviderModelFromContext: () => (/* binding */ getProviderModelFromContext),\n/* harmony export */   logStreamingPerformance: () => (/* binding */ logStreamingPerformance)\n/* harmony export */ });\n// Streaming utilities for first token tracking, performance monitoring, and cost calculation\n// Interface for streaming usage data\nfunction createFirstTokenTrackingStream(originalStream, provider, model, onUsageExtracted) {\n    const reader = originalStream.getReader();\n    const decoder = new TextDecoder();\n    const encoder = new TextEncoder();\n    return new ReadableStream({\n        async start (controller) {\n            let firstTokenSent = false;\n            let accumulatedContent = '';\n            const streamStartTime = Date.now();\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        // Final opportunity to extract usage data if not found in chunks\n                        if (onUsageExtracted && !firstTokenSent) {\n                            // Fallback: estimate tokens from accumulated content\n                            const estimatedUsage = estimateUsageFromContent(accumulatedContent, provider);\n                            console.log(\"\\uD83D\\uDCB0 [\".concat(provider, \" Cost] Fallback token estimation: \").concat(estimatedUsage.totalTokens, \" tokens\"));\n                            onUsageExtracted(estimatedUsage);\n                        }\n                        controller.close();\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    // Check if this chunk contains actual content (first token)\n                    if (!firstTokenSent && chunk.includes('delta')) {\n                        try {\n                            // Parse SSE data to check for content\n                            const lines = chunk.split('\\n');\n                            for (const line of lines){\n                                if (line.startsWith('data: ') && !line.includes('[DONE]')) {\n                                    const jsonData = line.substring(6);\n                                    try {\n                                        var _parsed_choices__delta, _parsed_choices_, _parsed_choices;\n                                        const parsed = JSON.parse(jsonData);\n                                        if ((_parsed_choices = parsed.choices) === null || _parsed_choices === void 0 ? void 0 : (_parsed_choices_ = _parsed_choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content) {\n                                            const firstTokenTime = Date.now() - streamStartTime;\n                                            console.log(\"\\uD83D\\uDE80 \".concat(provider.toUpperCase(), \" FIRST TOKEN: \").concat(firstTokenTime, \"ms (\").concat(model, \")\"));\n                                            firstTokenSent = true;\n                                            // Accumulate content for fallback estimation\n                                            accumulatedContent += parsed.choices[0].delta.content;\n                                            break;\n                                        }\n                                    } catch (e) {\n                                    // Ignore JSON parse errors for individual chunks\n                                    }\n                                }\n                            }\n                        } catch (e) {\n                            // Ignore parsing errors, just track timing\n                            if (!firstTokenSent) {\n                                const firstTokenTime = Date.now() - streamStartTime;\n                                console.log(\"\\uD83D\\uDE80 \".concat(provider.toUpperCase(), \" FIRST TOKEN: \").concat(firstTokenTime, \"ms (\").concat(model, \") [fallback detection]\"));\n                                firstTokenSent = true;\n                            }\n                        }\n                    }\n                    // Extract usage data from streaming chunks\n                    if (onUsageExtracted && chunk.includes('usage')) {\n                        try {\n                            const usageData = extractUsageFromStreamChunk(chunk, provider);\n                            if (usageData) {\n                                console.log(\"\\uD83D\\uDCB0 [\".concat(provider, \" Cost] Usage data extracted from stream: \").concat(usageData.totalTokens, \" tokens\"));\n                                onUsageExtracted(usageData);\n                            }\n                        } catch (e) {\n                        // Ignore usage extraction errors\n                        }\n                    }\n                    // Continue accumulating content for fallback\n                    if (chunk.includes('delta')) {\n                        try {\n                            const lines = chunk.split('\\n');\n                            for (const line of lines){\n                                if (line.startsWith('data: ') && !line.includes('[DONE]')) {\n                                    const jsonData = line.substring(6);\n                                    try {\n                                        var _parsed_choices__delta1, _parsed_choices_1, _parsed_choices1;\n                                        const parsed = JSON.parse(jsonData);\n                                        if ((_parsed_choices1 = parsed.choices) === null || _parsed_choices1 === void 0 ? void 0 : (_parsed_choices_1 = _parsed_choices1[0]) === null || _parsed_choices_1 === void 0 ? void 0 : (_parsed_choices__delta1 = _parsed_choices_1.delta) === null || _parsed_choices__delta1 === void 0 ? void 0 : _parsed_choices__delta1.content) {\n                                            accumulatedContent += parsed.choices[0].delta.content;\n                                        }\n                                    } catch (e) {\n                                    // Ignore JSON parse errors\n                                    }\n                                }\n                            }\n                        } catch (e) {\n                        // Ignore parsing errors\n                        }\n                    }\n                    // Forward the chunk unchanged\n                    controller.enqueue(value);\n                }\n            } catch (error) {\n                console.error(\"[\".concat(provider, \" Stream Tracking] Error:\"), error);\n                // Phase 1 Optimization: Graceful error handling for connection resets\n                if (error instanceof Error && (error.message.includes('aborted') || error.message.includes('ECONNRESET'))) {\n                    console.log(\"[\".concat(provider, \" Stream] Connection reset detected - closing stream gracefully\"));\n                    controller.close();\n                } else {\n                    controller.error(error);\n                }\n            }\n        }\n    });\n}\n// Enhanced logging for streaming performance\nfunction logStreamingPerformance(provider, model, metrics) {\n    console.log(\"\\uD83D\\uDCCA STREAMING PERFORMANCE: \".concat(provider, \"/\").concat(model));\n    if (metrics.timeToFirstToken !== undefined) {\n        console.log(\"   ⏱️ Time to First Token: \".concat(metrics.timeToFirstToken.toFixed(1), \"ms\"));\n        // Performance categories\n        if (metrics.timeToFirstToken < 500) {\n            console.log(\"   ⚡ EXCELLENT first token performance\");\n        } else if (metrics.timeToFirstToken < 1000) {\n            console.log(\"   ✅ GOOD first token performance\");\n        } else if (metrics.timeToFirstToken < 2000) {\n            console.log(\"   ⚠️ SLOW first token performance\");\n        } else {\n            console.log(\"   \\uD83D\\uDC0C VERY SLOW first token performance\");\n        }\n    }\n    if (metrics.totalStreamTime !== undefined) {\n        console.log(\"   \\uD83D\\uDD04 Total Stream Time: \".concat(metrics.totalStreamTime.toFixed(1), \"ms\"));\n    }\n    if (metrics.totalTokens !== undefined) {\n        console.log(\"   \\uD83C\\uDFAF Total Tokens: \".concat(metrics.totalTokens));\n    }\n    if (metrics.averageTokenLatency !== undefined) {\n        console.log(\"   \\uD83D\\uDCC8 Avg Token Latency: \".concat(metrics.averageTokenLatency.toFixed(1), \"ms/token\"));\n    }\n}\n// Utility to extract provider and model from request context\nfunction getProviderModelFromContext(providerName, modelId) {\n    return {\n        provider: providerName || 'unknown',\n        model: modelId || 'unknown'\n    };\n}\n// Extract usage data from streaming chunk\nfunction extractUsageFromStreamChunk(chunk, provider) {\n    try {\n        const lines = chunk.split('\\n');\n        for (const line of lines){\n            if (line.startsWith('data: ') && !line.includes('[DONE]')) {\n                const jsonData = line.substring(6);\n                try {\n                    const parsed = JSON.parse(jsonData);\n                    // Check for usage data in the chunk\n                    if (parsed.usage) {\n                        const usage = parsed.usage;\n                        // Handle different provider formats\n                        let promptTokens = 0;\n                        let completionTokens = 0;\n                        let totalTokens = 0;\n                        let cost;\n                        if (provider.toLowerCase() === 'openrouter') {\n                            // OpenRouter format\n                            promptTokens = usage.prompt_tokens || 0;\n                            completionTokens = usage.completion_tokens || 0;\n                            totalTokens = usage.total_tokens || promptTokens + completionTokens;\n                            cost = usage.cost ? usage.cost * 0.000001 : undefined; // Convert credits to USD\n                        } else {\n                            // Standard OpenAI format (Google, Anthropic, xAI)\n                            promptTokens = usage.prompt_tokens || usage.input_tokens || 0;\n                            completionTokens = usage.completion_tokens || usage.output_tokens || 0;\n                            totalTokens = usage.total_tokens || promptTokens + completionTokens;\n                        }\n                        if (totalTokens > 0) {\n                            return {\n                                promptTokens,\n                                completionTokens,\n                                totalTokens,\n                                cost\n                            };\n                        }\n                    }\n                } catch (e) {\n                // Ignore JSON parse errors for individual chunks\n                }\n            }\n        }\n    } catch (e) {\n    // Ignore parsing errors\n    }\n    return null;\n}\n// Estimate usage from accumulated content (fallback)\nfunction estimateUsageFromContent(content, provider, promptText) {\n    // More accurate token estimation based on provider\n    let tokensPerChar = 0.25; // Default: 4 chars per token\n    // Provider-specific token ratios (based on empirical data)\n    switch(provider.toLowerCase()){\n        case 'openrouter':\n        case 'openai':\n            tokensPerChar = 0.25; // ~4 chars per token\n            break;\n        case 'google':\n            tokensPerChar = 0.22; // ~4.5 chars per token (slightly more efficient)\n            break;\n        case 'anthropic':\n            tokensPerChar = 0.26; // ~3.8 chars per token\n            break;\n        case 'xai':\n            tokensPerChar = 0.25; // Similar to OpenAI\n            break;\n    }\n    const completionTokens = Math.ceil(content.length * tokensPerChar);\n    const promptTokens = promptText ? Math.ceil(promptText.length * tokensPerChar) : Math.ceil(completionTokens * 0.3); // Estimate 30% of completion\n    const totalTokens = promptTokens + completionTokens;\n    return {\n        promptTokens,\n        completionTokens,\n        totalTokens\n    };\n}\n// Simple token counter for rough estimation (legacy function)\nfunction estimateTokenCount(text) {\n    // Rough estimation: 1 token ≈ 4 characters for English text\n    // This is a simplified approach, real tokenization would be more accurate\n    return Math.ceil(text.length / 4);\n}\n// Performance thresholds for different providers\nconst PERFORMANCE_THRESHOLDS = {\n    EXCELLENT_FIRST_TOKEN: 500,\n    GOOD_FIRST_TOKEN: 1000,\n    SLOW_FIRST_TOKEN: 2000,\n    // Anything above 2000ms is considered very slow\n    EXCELLENT_TOTAL: 3000,\n    GOOD_TOTAL: 5000,\n    SLOW_TOTAL: 10000,\n    TARGET_TOKEN_LATENCY: 50\n};\n// Check if performance meets targets\nfunction evaluatePerformance(metrics) {\n    const firstTokenGrade = !metrics.timeToFirstToken ? 'very_slow' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN ? 'excellent' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN ? 'good' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN ? 'slow' : 'very_slow';\n    const totalTimeGrade = !metrics.totalStreamTime ? 'very_slow' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.EXCELLENT_TOTAL ? 'excellent' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.GOOD_TOTAL ? 'good' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.SLOW_TOTAL ? 'slow' : 'very_slow';\n    const tokenLatencyGrade = !metrics.averageTokenLatency ? 'very_slow' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY ? 'excellent' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 2 ? 'good' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 4 ? 'slow' : 'very_slow';\n    // Overall grade is the worst of the three\n    const grades = [\n        firstTokenGrade,\n        totalTimeGrade,\n        tokenLatencyGrade\n    ];\n    const gradeOrder = [\n        'excellent',\n        'good',\n        'slow',\n        'very_slow'\n    ];\n    const overallGrade = grades.reduce((worst, current)=>{\n        return gradeOrder.indexOf(current) > gradeOrder.indexOf(worst) ? current : worst;\n    }, 'excellent');\n    return {\n        firstTokenGrade,\n        totalTimeGrade,\n        tokenLatencyGrade,\n        overallGrade\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/streamingUtils.ts\n"));

/***/ })

});