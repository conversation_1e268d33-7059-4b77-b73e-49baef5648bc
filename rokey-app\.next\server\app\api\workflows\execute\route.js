"use strict";(()=>{var e={};e.id=4016,e.ids=[4016],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},38249:(e,t,r)=>{r.d(t,{P:()=>u});var s=r(39398),o=r(55511),a=r.n(o);let i=(0,s.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);class n{static getInstance(){return n.instance||(n.instance=new n),n.instance}async saveWorkflow(e,t,r,s,o,n={}){try{let u=a().randomUUID(),l={id:u,user_id:e,name:t,description:r,nodes:s,edges:o,settings:n,is_active:!0,is_template:!1,version:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},{data:d,error:_}=await i.from("manual_build_workflows").insert(l).select().single();if(_)throw Error(`Failed to save workflow: ${_.message}`);let w=await this.generateWorkflowAPIKey(u,e,t);return{workflow:d,apiKey:w}}catch(e){throw e}}async updateWorkflow(e,t,r){try{let{data:s}=await i.from("manual_build_workflows").select("version").eq("id",e).eq("user_id",t).single(),o={...r,updated_at:new Date().toISOString(),version:(s?.version||0)+1},{data:a,error:n}=await i.from("manual_build_workflows").update(o).eq("id",e).eq("user_id",t).select().single();if(n)throw Error(`Failed to update workflow: ${n.message}`);return a}catch(e){throw e}}async getUserWorkflows(e){try{let{data:t,error:r}=await i.from("manual_build_workflows").select("*").eq("user_id",e).eq("is_template",!1).order("updated_at",{ascending:!1});if(r)throw Error(`Failed to fetch workflows: ${r.message}`);return t}catch(e){throw e}}async getWorkflow(e,t){try{let{data:r,error:s}=await i.from("manual_build_workflows").select("*").eq("id",e).eq("user_id",t).single();if(s){if("PGRST116"===s.code)return null;throw Error(`Failed to fetch workflow: ${s.message}`)}return r}catch(e){throw e}}async deleteWorkflow(e,t){try{await i.from("workflow_api_keys").delete().eq("workflow_id",e).eq("user_id",t);let{error:r}=await i.from("manual_build_workflows").delete().eq("id",e).eq("user_id",t);if(r)throw Error(`Failed to delete workflow: ${r.message}`)}catch(e){throw e}}async generateWorkflowAPIKey(e,t,r){try{let s="rk_wf",o=a().randomBytes(32).toString("hex"),n=`${s}_${o}`,u=a().createHash("sha256").update(n).digest("hex"),l=o.slice(-8),d=a().createHash("md5").update(l).digest("hex"),_={id:a().randomUUID(),workflow_id:e,user_id:t,key_name:`${r} API Key`,key_prefix:s,key_hash:u,encrypted_key_suffix:d,permissions:{execute:!0,read_logs:!0,read_status:!0},status:"active",total_requests:0,created_at:new Date().toISOString()},{error:w}=await i.from("workflow_api_keys").insert(_);if(w)throw Error(`Failed to create API key: ${w.message}`);return n}catch(e){throw e}}async getWorkflowAPIKey(e,t){try{let{data:r,error:s}=await i.from("workflow_api_keys").select("*").eq("workflow_id",e).eq("user_id",t).single();if(s){if("PGRST116"===s.code)return null;throw Error(`Failed to fetch API key: ${s.message}`)}return r}catch(e){throw e}}async validateWorkflowAPIKey(e){try{let t=a().createHash("sha256").update(e).digest("hex"),{data:r,error:s}=await i.from("workflow_api_keys").select("workflow_id, user_id, status").eq("key_hash",t).eq("status","active").single();if(s||!r)return{valid:!1};let{data:o}=await i.from("workflow_api_keys").select("total_requests").eq("key_hash",t).single(),n=(o?.total_requests||0)+1;return await i.from("workflow_api_keys").update({last_used_at:new Date().toISOString(),total_requests:n}).eq("key_hash",t),{valid:!0,workflowId:r.workflow_id,userId:r.user_id}}catch(e){return{valid:!1}}}async getWorkflowTemplates(e){try{let t=i.from("manual_build_workflows").select("*").eq("is_template",!0);e&&(t=t.eq("template_category",e));let{data:r,error:s}=await t.order("created_at",{ascending:!1});if(s)throw Error(`Failed to fetch templates: ${s.message}`);return r}catch(e){throw e}}}let u=n.getInstance()},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},82279:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>k,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>m,POST:()=>f});var o=r(96559),a=r(48088),i=r(37719),n=r(32190),u=r(38249),l=r(11719),d=r(62480),_=r(39398),w=r(55511),p=r.n(w);async function c(e,t,r,s,o){try{let a=(0,_.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),i={workflow_id:e,api_key_id:r,user_id:t,predefined_model_id:o.models_used?.join(", ")||null,role_requested:null,role_used:"workflow_execution",ip_address:s.headers.get("x-forwarded-for")||s.headers.get("x-real-ip")||null,request_timestamp:o.request_timestamp.toISOString(),response_timestamp:o.response_timestamp.toISOString(),status_code:o.status_code,request_payload_summary:{input_length:o.input_text?.length||0,execution_id:o.execution_id,workflow_type:"manual_build"},response_payload_summary:{output_length:o.output_text?.length||0,models_used:o.models_used||[],providers_used:o.providers_used||[]},error_message:o.error_message||null,error_source:o.error_message?"workflow_execution":null,llm_model_name:o.models_used?.join(", ")||null,llm_provider_name:o.providers_used?.join(", ")||null,llm_provider_status_code:o.status_code,processing_duration_ms:o.processing_duration_ms,cost:o.total_cost||null,input_tokens:o.total_input_tokens||null,output_tokens:o.total_output_tokens||null,is_multimodal:!1},{error:n}=await a.from("request_logs").insert(i)}catch(e){}}async function f(e){try{let t,r,s,o=e.headers.get("X-API-Key");if(!o)return n.NextResponse.json({error:"API key required in X-API-Key header"},{status:401});let a=await u.P.validateWorkflowAPIKey(o);if(!a.valid||!a.workflowId||!a.userId)return n.NextResponse.json({error:"Invalid or expired API key"},{status:401});let{input:i,options:_={}}=await e.json();if(!i)return n.NextResponse.json({error:"Input is required"},{status:400});let w=await u.P.getWorkflow(a.workflowId,a.userId);if(!w||!w.is_active)return n.NextResponse.json({error:"Workflow not found or inactive"},{status:404});let f=p().randomUUID(),m=new Date,k=Date.now();await d.a.startExecution(f,w.id,a.userId,w.nodes.length);let g=null;try{let o=l.J.getInstance();return s=await o.executeWorkflow(w.id,a.userId,w.nodes,w.edges,i),t=new Date,r=Date.now()-k,await d.a.completeExecution(f,s,Date.now()),setImmediate(async()=>{await c(w.id,a.userId,"",e,{status_code:200,request_timestamp:m,response_timestamp:t,processing_duration_ms:r,input_text:"string"==typeof i?i:JSON.stringify(i),output_text:"string"==typeof s?s:JSON.stringify(s),execution_id:f,models_used:[],providers_used:[],total_cost:0,total_input_tokens:0,total_output_tokens:0})}),n.NextResponse.json({success:!0,execution_id:f,workflow_id:w.id,workflow_name:w.name,result:s,executed_at:new Date().toISOString(),execution_time_ms:r})}catch(s){throw g=s instanceof Error?s:Error("Unknown execution error"),t=new Date,r=Date.now()-k,await d.a.failExecution(f,g.message,{error:g}),setImmediate(async()=>{await c(w.id,a.userId,"",e,{status_code:500,request_timestamp:m,response_timestamp:t,processing_duration_ms:r,input_text:"string"==typeof i?i:JSON.stringify(i),error_message:g?.message||"Unknown execution error",execution_id:f,models_used:[],providers_used:[]})}),g}}catch(t){return setImmediate(async()=>{try{let r=(0,_.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),s={workflow_id:null,api_key_id:null,user_id:null,predefined_model_id:null,role_requested:null,role_used:"workflow_execution_failed",ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:new Date().toISOString(),response_timestamp:new Date().toISOString(),status_code:500,request_payload_summary:{note:"Early workflow execution error"},response_payload_summary:null,error_message:t instanceof Error?t.message:"Unknown workflow execution error",error_source:"workflow_api",processing_duration_ms:0},{error:o}=await r.from("request_logs").insert(s)}catch(e){}}),n.NextResponse.json({error:"Workflow execution failed",details:t instanceof Error?t.message:"Unknown error"},{status:500})}}async function m(e){try{let t=e.headers.get("X-API-Key");if(!t)return n.NextResponse.json({error:"API key required in X-API-Key header"},{status:401});let r=await u.P.validateWorkflowAPIKey(t);if(!r.valid||!r.workflowId||!r.userId)return n.NextResponse.json({error:"Invalid or expired API key"},{status:401});let{searchParams:s}=new URL(e.url),o=s.get("execution_id");if(o){let e=d.a.getExecutionStatus(o);if(!e)return n.NextResponse.json({error:"Execution not found"},{status:404});return n.NextResponse.json({execution_id:o,status:e.status,progress:e.progress,current_node:e.currentNodeId,logs:e.logs,result:e.result,error:e.error,timestamp:e.timestamp})}{let e=await u.P.getWorkflow(r.workflowId,r.userId);if(!e)return n.NextResponse.json({error:"Workflow not found"},{status:404});let t=await d.a.getExecutionHistory(e.id,r.userId,10);return n.NextResponse.json({workflow:{id:e.id,name:e.name,description:e.description,is_active:e.is_active,version:e.version,created_at:e.created_at,updated_at:e.updated_at},recent_executions:t.map(e=>({id:e.id,status:e.status,started_at:e.started_at,completed_at:e.completed_at,execution_time_ms:e.execution_time_ms,nodes_executed:e.nodes_executed,nodes_total:e.nodes_total}))})}}catch(e){return n.NextResponse.json({error:"Failed to get workflow status",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let k=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/workflows/execute/route",pathname:"/api/workflows/execute",filename:"route",bundlePath:"app/api/workflows/execute/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\workflows\\execute\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:x,serverHooks:h}=k;function y(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:x})}},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410,750,2186,4100],()=>r(82279));module.exports=s})();