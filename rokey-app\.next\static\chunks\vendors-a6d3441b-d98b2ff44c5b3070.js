"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3362],{78:(t,e,n)=>{n.d(e,{w:()=>M});var i=n(52290),o=n(21448),r=n(43891),s=n(66698),a=n(51442),l=n(26953),u=n(51586),c=n(78588),h=n(64200),d=n(81786),f=n(94198),m=n(33757),p=n(68212),v=n(33991),g=n(76333),y=n(61665);function x(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function P(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}function w(t,e,n){return{min:E(t,e),max:E(t,n)}}function E(t,e){return"number"==typeof t?t:t[e]||0}let A=new WeakMap;class C{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,d.ge)(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new y.Q(t,{onSessionStart:t=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor((0,u.e)(t).point)},onStart:(t,e)=>{let{drag:n,dragPropagation:i,onDragStart:o}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=(0,r.Wp)(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),(0,f.X)(t=>{let e=this.getAxisMotionValue(t).get()||0;if(r.rq.test(e)){let{projection:n}=this.visualElement;if(n&&n.layout){let i=n.layout.layoutBox[t];i&&(e=(0,h.CQ)(i)*(parseFloat(e)/100))}}this.originPoint[t]=e}),o&&r.Gt.postRender(()=>o(t,e)),(0,g.g)(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:n,dragDirectionLock:i,onDirectionLock:o,onDrag:r}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}(s),null!==this.currentDirection&&o&&o(this.currentDirection);return}this.updateAxis("x",e.point,s),this.updateAxis("y",e.point,s),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>(0,f.X)(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:(0,p.s)(this.visualElement)})}stop(t,e){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:i}=e;this.startAnimation(i);let{onDragEnd:o}=this.getProps();o&&r.Gt.postRender(()=>o(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){let{drag:i}=this.getProps();if(!n||!S(t,i,this.currentDirection))return;let o=this.getAxisMotionValue(t),s=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?(0,r.k$)(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?(0,r.k$)(n,t,i.max):Math.min(t,n)),t}(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&(0,v.X)(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(t,{top:e,left:n,bottom:i,right:o}){return{x:x(t.x,n,o),y:x(t.y,e,i)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:w(t,"left","right"),y:w(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&(0,f.X)(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!(0,v.X)(e))return!1;let i=e.current;(0,o.V1)(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=(0,m.L)(i,r.root,this.visualElement.getTransformPagePoint()),a=(t=r.layout.layoutBox,{x:P(t.x,s.x),y:P(t.y,s.y)});if(n){let t=n((0,c.pA)(a));this.hasMutatedConstraints=!!t,t&&(a=(0,c.FY)(t))}return a}startAnimation(t){let{drag:e,dragMomentum:n,dragElastic:i,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all((0,f.X)(s=>{if(!S(s,e,this.currentDirection))return;let l=a&&a[s]||{};r&&(l={min:0,max:0});let u={type:"inertia",velocity:n?t[s]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...o,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(t,e){let n=this.getAxisMotionValue(t);return(0,g.g)(this.visualElement,t),n.start((0,s.f)(t,n,0,e,this.visualElement,!1))}stopAnimation(){(0,f.X)(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){(0,f.X)(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps();return n[e]||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){(0,f.X)(e=>{let{drag:n}=this.getProps();if(!S(e,n,this.currentDirection))return;let{projection:i}=this.visualElement,o=this.getAxisMotionValue(e);if(i&&i.layout){let{min:n,max:s}=i.layout.layoutBox[e];o.set(t[e]-(0,r.k$)(n,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!(0,v.X)(e)||!n||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};(0,f.X)(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let n=e.get();i[t]=function(t,e){let n=.5,i=(0,h.CQ)(t),r=(0,h.CQ)(e);return r>i?n=(0,o.qB)(e.min,e.max-i,t.min):i>r&&(n=(0,o.qB)(t.min,t.max-r,e.min)),(0,o.qE)(0,1,n)}({min:n,max:n},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),(0,f.X)(e=>{if(!S(e,t,null))return;let n=this.getAxisMotionValue(e),{min:o,max:s}=this.constraints[e];n.set((0,r.k$)(o,s,i[e]))})}addListeners(){if(!this.visualElement.current)return;A.set(this.visualElement,this);let t=this.visualElement.current,e=(0,l.h)(t,"pointerdown",t=>{let{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),n=()=>{let{dragConstraints:t}=this.getProps();(0,v.X)(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",n);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r.Gt.read(n);let s=(0,a.k)(window,"resize",()=>this.scalePositionWithinConstraints()),u=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&((0,f.X)(e=>{let n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),e(),o(),u&&u()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:r=.35,dragMomentum:s=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:o,dragElastic:r,dragMomentum:s}}}function S(t,e,n){return(!0===e||e===t)&&(null===n||n===t)}class M extends i.X{constructor(t){super(t),this.removeGroupControls=o.lQ,this.removeListeners=o.lQ,this.controls=new C(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||o.lQ}unmount(){this.removeGroupControls(),this.removeListeners()}}},198:(t,e,n)=>{n(21448),n(18802),n(78660)},671:(t,e,n)=>{n.d(e,{B1:()=>C,C0:()=>d,Dz:()=>g,Jx:()=>a,LI:()=>r,PG:()=>m,RI:()=>s,Sg:()=>f,T9:()=>o,TV:()=>v,WJ:()=>x,_3:()=>h,bV:()=>E,jk:()=>i,lP:()=>w,nI:()=>A,qE:()=>c,sq:()=>p,w7:()=>y});let i=Math.min,o=Math.max,r=Math.round,s=Math.floor,a=t=>({x:t,y:t}),l={left:"right",right:"left",bottom:"top",top:"bottom"},u={start:"end",end:"start"};function c(t,e,n){return o(t,i(e,n))}function h(t,e){return"function"==typeof t?t(e):t}function d(t){return t.split("-")[0]}function f(t){return t.split("-")[1]}function m(t){return"x"===t?"y":"x"}function p(t){return"y"===t?"height":"width"}function v(t){return["top","bottom"].includes(d(t))?"y":"x"}function g(t){return m(v(t))}function y(t,e,n){void 0===n&&(n=!1);let i=f(t),o=g(t),r=p(o),s="x"===o?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return e.reference[r]>e.floating[r]&&(s=E(s)),[s,E(s)]}function x(t){let e=E(t);return[P(t),e,P(e)]}function P(t){return t.replace(/start|end/g,t=>u[t])}function w(t,e,n,i){let o=f(t),r=function(t,e,n){let i=["left","right"],o=["right","left"];switch(t){case"top":case"bottom":if(n)return e?o:i;return e?i:o;case"left":case"right":return e?["top","bottom"]:["bottom","top"];default:return[]}}(d(t),"start"===n,i);return o&&(r=r.map(t=>t+"-"+o),e&&(r=r.concat(r.map(P)))),r}function E(t){return t.replace(/left|right|bottom|top/g,t=>l[t])}function A(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function C(t){let{x:e,y:n,width:i,height:o}=t;return{width:i,height:o,top:n,left:e,right:e+i,bottom:n+o,x:e,y:n}}},1265:(t,e,n)=>{n(43891),n(21448),n(31788),n(46926);let i=new Set},2736:(t,e,n)=>{n(95155),n(21448),n(12115),n(82885),n(43050)},2999:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(12115).createContext)({})},5910:(t,e,n)=>{n.d(e,{p:()=>i});let i=t=>Array.isArray(t)},6340:(t,e,n)=>{n.d(e,{N:()=>i});function i(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}},16242:(t,e,n)=>{n(82885);class i{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,e){this.componentControls.forEach(n=>{n.start(t.nativeEvent||t,e)})}}},19209:(t,e,n)=>{n.d(e,{Y:()=>c,q:()=>v});var i=n(95155),o=n(21448),r=n(12115);let s=(0,r.createContext)(null);var a=n(36545),l=n(82885),u=n(43891);let c=(0,r.forwardRef)(function(t,e){let{children:n,as:c="ul",axis:f="y",onReorder:m,values:p,...v}=t,g=(0,l.M)(()=>a.P[c]),y=[],x=(0,r.useRef)(!1);return(0,o.V1)(!!p,"Reorder.Group must be provided a values prop"),(0,r.useEffect)(()=>{x.current=!1}),(0,i.jsx)(g,{...v,ref:e,ignoreStrict:!0,children:(0,i.jsx)(s.Provider,{value:{axis:f,registerItem:(t,e)=>{let n=y.findIndex(e=>t===e.value);-1!==n?y[n].layout=e[f]:y.push({value:t,layout:e[f]}),y.sort(d)},updateOrder:(t,e,n)=>{if(x.current)return;let i=function(t,e,n,i){if(!i)return t;let r=t.findIndex(t=>t.value===e);if(-1===r)return t;let s=i>0?1:-1,a=t[r+s];if(!a)return t;let l=t[r],c=a.layout,h=(0,u.k$)(c.min,c.max,.5);return 1===s&&l.layout.max+n>h||-1===s&&l.layout.min+n<h?(0,o.Pe)(t,r,r+s):t}(y,t,e,n);y!==i&&(x.current=!0,m(i.map(h).filter(t=>-1!==p.indexOf(t))))}},children:n})})});function h(t){return t.value}function d(t,e){return t.layout.min-e.layout.min}var f=n(8619),m=n(62094);function p(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(0,u.SS)(t)?t:(0,f.d)(e)}let v=(0,r.forwardRef)(function(t,e){let{children:n,style:u={},value:c,as:h="li",onDrag:d,layout:f=!0,...v}=t,g=(0,l.M)(()=>a.P[h]),y=(0,r.useContext)(s),x={x:p(u.x),y:p(u.y)},P=(0,m.G)([x.x,x.y],t=>{let[e,n]=t;return e||n?1:"unset"});(0,o.V1)(!!y,"Reorder.Item must be a child of Reorder.Group");let{axis:w,registerItem:E,updateOrder:A}=y;return(0,i.jsx)(g,{drag:w,...v,dragSnapToOrigin:!0,style:{...u,x:x.x,y:x.y,zIndex:P},layout:f,onDrag:(t,e)=>{let{velocity:n}=e;n[w]&&A(c,x[w].get(),n[w]),d&&d(t,e)},onLayoutMeasure:t=>E(c,t),ref:e,ignoreStrict:!0,children:n})})},19578:(t,e,n)=>{n.d(e,{$:()=>l});var i=n(43891),o=n(18802),r=n(76333),s=n(46926),a=n(66698);function l(t,e,{delay:n=0,transitionOverride:u,type:c}={}){let{transition:h=t.getDefaultTransition(),transitionEnd:d,...f}=e;u&&(h=u);let m=[],p=c&&t.animationState&&t.animationState.getState()[c];for(let e in f){let o=t.getValue(e,t.latestValues[e]??null),l=f[e];if(void 0===l||p&&function({protectedKeys:t,needsAnimating:e},n){let i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}(p,e))continue;let u={delay:n,...(0,i.rU)(h||{},e)},c=o.get();if(void 0!==c&&!o.isAnimating&&!Array.isArray(l)&&l===c&&!u.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let n=(0,s.P)(t);if(n){let t=window.MotionHandoffAnimation(n,e,i.Gt);null!==t&&(u.startTime=t,d=!0)}}(0,r.g)(t,e),o.start((0,a.f)(e,o,l,t.shouldReduceMotion&&i.$y.has(e)?{type:!1}:u,t,d));let v=o.animation;v&&m.push(v)}return d&&Promise.all(m).then(()=>{i.Gt.update(()=>{d&&(0,o.U)(t,d)})}),m}},19624:(t,e,n)=>{n.d(e,{c:()=>s});var i=n(21448),o=n(51442),r=n(52290);class s extends r.X{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,i.Fs)((0,o.k)(this.node.current,"focus",()=>this.onFocus()),(0,o.k)(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},19726:(t,e,n)=>{n.d(e,{e:()=>a});var i=n(43891),o=n(51586),r=n(52290);function s(t,e,n){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===n);let s=r["onHover"+n];s&&i.Gt.postRender(()=>s(e,(0,o.e)(e)))}class a extends r.X{mount(){let{current:t}=this.node;t&&(this.unmount=(0,i.PT)(t,(t,e)=>(s(this.node,e,"Start"),t=>s(this.node,t,"End"))))}unmount(){}}},24132:(t,e,n)=>{n.d(e,{z:()=>a});var i=n(12115),o=n(2999),r=n(19253),s=n(65305);function a(t){let{initial:e,animate:n}=function(t,e){if((0,r.e)(t)){let{initial:e,animate:n}=t;return{initial:!1===e||(0,s.w)(e)?e:void 0,animate:(0,s.w)(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,(0,i.useContext)(o.A));return(0,i.useMemo)(()=>({initial:e,animate:n}),[l(e),l(n)])}function l(t){return Array.isArray(t)?t.join(" "):t}},25214:(t,e,n)=>{n.d(e,{Y:()=>i});let i=(0,n(12115).createContext)({strict:!1})},26953:(t,e,n)=>{n.d(e,{h:()=>r});var i=n(51442),o=n(51586);function r(t,e,n,r){return(0,i.k)(t,e,(0,o.F)(n),r)}},31788:(t,e,n)=>{n.d(e,{n:()=>i});let i="data-"+(0,n(78450).I)("framerAppearId")},32082:(t,e,n)=>{n.d(e,{xQ:()=>r});var i=n(12115),o=n(80845);function r(t=!0){let e=(0,i.useContext)(o.t);if(null===e)return[!0,null];let{isPresent:n,onExitComplete:s,register:a}=e,l=(0,i.useId)();(0,i.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,i.useCallback)(()=>t&&s&&s(l),[l,s,t]);return!n&&s?[!1,u]:[!0]}},35580:(t,e,n)=>{n.d(e,{z:()=>r});var i=n(43891),o=n(66698);function r(t,e,n){let r=(0,i.SS)(t)?t:(0,i.OQ)(t);return r.start((0,o.f)("",r,e,n)),r.animation}},36464:(t,e,n)=>{var i=n(43891),o=n(21448);function r(t){return"object"==typeof t&&!Array.isArray(t)}let s=t=>"number"==typeof t;var a=n(65511),l=n(19578),u=n(75245),c=n(13513),h=n(60728);function d(t){let e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=(0,i.xZ)(t)&&!(0,i.h1)(t)?new h.l(e):new u.M(e);n.mount(t),a.C.set(t,n)}function f(t){let e=new c.K({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),a.C.set(t,e)}var m=n(35580)},38160:(t,e,n)=>{n.d(e,{f:()=>c});var i=n(43891),o=n(21448),r=n(26953),s=n(52290),a=n(68212),l=n(61665);let u=t=>(e,n)=>{t&&i.Gt.postRender(()=>t(e,n))};class c extends s.X{constructor(){super(...arguments),this.removePointerDownListener=o.lQ}onPointerDown(t){this.session=new l.Q(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:(0,a.s)(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:o}=this.node.getProps();return{onSessionStart:u(t),onStart:u(e),onMove:n,onEnd:(t,e)=>{delete this.session,o&&i.Gt.postRender(()=>o(t,e))}}}mount(){this.removePointerDownListener=(0,r.h)(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},39126:(t,e,n)=>{(0,n(12115).createContext)(null)},43050:(t,e,n)=>{n(95155),n(12115),n(90869),n(39126),n(39174),n(80131)},46926:(t,e,n)=>{n.d(e,{P:()=>o});var i=n(31788);function o(t){return t.props[i.n]}},49441:(t,e,n)=>{n.d(e,{H:()=>a});var i=n(43891),o=n(51586),r=n(52290);function s(t,e,n){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===n);let s=r["onTap"+("End"===n?"":n)];s&&i.Gt.postRender(()=>s(e,(0,o.e)(e)))}class a extends r.X{mount(){let{current:t}=this.node;t&&(this.unmount=(0,i.c$)(t,(t,e)=>(s(this.node,e,"Start"),(t,{success:e})=>s(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},49489:(t,e,n)=>{n(21448)},51251:(t,e,n)=>{n(95155),n(12115),n(25214),n(9480)},51442:(t,e,n)=>{n.d(e,{k:()=>i});function i(t,e,n,o={passive:!0}){return t.addEventListener(e,n,o),()=>t.removeEventListener(e,n)}},51508:(t,e,n)=>{n.d(e,{Q:()=>i});let i=(0,n(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},51586:(t,e,n)=>{n.d(e,{F:()=>r,e:()=>o});var i=n(43891);function o(t){return{point:{x:t.pageX,y:t.pageY}}}let r=t=>e=>(0,i.Mc)(e)&&t(e,o(e))},55539:(t,e,n)=>{n(43891),n(21448)},56787:(t,e,n)=>{n(95155),n(12115),n(51508),n(99776),n(82885)},60760:(t,e,n)=>{n(95155);var i=n(12115);n(90869),n(82885),n(97494),n(80845);var o=n(43891);n(51508),i.Component,n(32082)},61665:(t,e,n)=>{n.d(e,{Q:()=>l});var i=n(43891),o=n(21448),r=n(26953),s=n(51586),a=n(2986);class l{constructor(t,e,{transformPagePoint:n,contextWindow:l,dragSnapToOrigin:c=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=h(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=(0,a.w)(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;let{point:o}=t,{timestamp:r}=i.uv;this.history.push({...o,timestamp:r});let{onStart:s,onMove:l}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=u(e,this.transformPagePoint),i.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:n,onSessionEnd:i,resumeAnimation:o}=this.handlers;if(this.dragSnapToOrigin&&o&&o(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=h("pointercancel"===t.type?this.lastMoveEventInfo:u(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,r),i&&i(t,r)},!(0,i.Mc)(t))return;this.dragSnapToOrigin=c,this.handlers=e,this.transformPagePoint=n,this.contextWindow=l||window;let d=u((0,s.e)(t),this.transformPagePoint),{point:f}=d,{timestamp:m}=i.uv;this.history=[{...f,timestamp:m}];let{onSessionStart:p}=e;p&&p(t,h(d,this.history)),this.removeListeners=(0,o.Fs)((0,r.h)(this.contextWindow,"pointermove",this.handlePointerMove),(0,r.h)(this.contextWindow,"pointerup",this.handlePointerUp),(0,r.h)(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,i.WG)(this.updatePoint)}}function u(t,e){return e?{point:e(t.point)}:t}function c(t,e){return{x:t.x-e.x,y:t.y-e.y}}function h({point:t},e){return{point:t,delta:c(t,d(e)),offset:c(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null,r=d(t);for(;n>=0&&(i=t[n],!(r.timestamp-i.timestamp>(0,o.fD)(.1)));)n--;if(!i)return{x:0,y:0};let s=(0,o.Xu)(r.timestamp-i.timestamp);if(0===s)return{x:0,y:0};let a={x:(r.x-i.x)/s,y:(r.y-i.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function d(t){return t[t.length-1]}},66698:(t,e,n)=>{n.d(e,{f:()=>h});var i=n(43891),o=n(21448);let r=t=>null!==t,s={type:"spring",stiffness:500,damping:25,restSpeed:10},a=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),l={type:"keyframes",duration:.8},u={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},c=(t,{keyframes:e})=>e.length>2?l:i.fu.has(t)?t.startsWith("scale")?a(e[1]):s:u,h=(t,e,n,s={},a,l)=>u=>{let h=(0,i.rU)(s,t)||{},d=h.delay||s.delay||0,{elapsed:f=0}=s;f-=(0,o.fD)(d);let m={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...h,delay:-f,onUpdate:t=>{e.set(t),h.onUpdate&&h.onUpdate(t)},onComplete:()=>{u(),h.onComplete&&h.onComplete()},name:t,motionValue:e,element:l?void 0:a};!function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:o,repeat:r,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(h)&&Object.assign(m,c(t,m)),m.duration&&(m.duration=(0,o.fD)(m.duration)),m.repeatDelay&&(m.repeatDelay=(0,o.fD)(m.repeatDelay)),void 0!==m.from&&(m.keyframes[0]=m.from);let p=!1;if(!1!==m.type&&(0!==m.duration||m.repeatDelay)||(m.duration=0,0===m.delay&&(p=!0)),(o.W9.instantAnimations||o.W9.skipAnimations)&&(p=!0,m.duration=0,m.delay=0),m.allowFlatten=!h.type&&!h.ease,p&&!l&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:n="loop"},i){let o=t.filter(r),s=e&&"loop"!==n&&e%2==1?0:o.length-1;return o[s]}(m.keyframes,h);if(void 0!==t)return void i.Gt.update(()=>{m.onUpdate(t),m.onComplete()})}return h.isSync?new i.sb(m):new i.AT(m)}},70797:(t,e,n)=>{n.d(e,{N:()=>i});let i=(0,n(12115).createContext)({})},71492:(t,e,n)=>{n(82885),n(86811),n(36464)},78660:(t,e,n)=>{n.d(e,{_:()=>a});var i=n(20419),o=n(19578);function r(t,e,n={}){let a=(0,i.K)(t,e,"exit"===n.type?t.presenceContext?.custom:void 0),{transition:l=t.getDefaultTransition()||{}}=a||{};n.transitionOverride&&(l=n.transitionOverride);let u=a?()=>Promise.all((0,o.$)(t,a,n)):()=>Promise.resolve(),c=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:o=0,staggerChildren:a,staggerDirection:u}=l;return function(t,e,n=0,i=0,o=1,a){let l=[],u=(t.variantChildren.size-1)*i,c=1===o?(t=0)=>t*i:(t=0)=>u-t*i;return Array.from(t.variantChildren).sort(s).forEach((t,i)=>{t.notify("AnimationStart",e),l.push(r(t,e,{...a,delay:n+c(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(l)}(t,e,o+i,a,u,n)}:()=>Promise.resolve(),{when:h}=l;if(!h)return Promise.all([u(),c(n.delay)]);{let[t,e]="beforeChildren"===h?[u,c]:[c,u];return t().then(()=>e())}}function s(t,e){return t.sortNodePosition(e)}function a(t,e,n={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>r(t,e,n)));else if("string"==typeof e)s=r(t,e,n);else{let r="function"==typeof e?(0,i.K)(t,e,n.custom):e;s=Promise.all((0,o.$)(t,r,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}},80845:(t,e,n)=>{n.d(e,{t:()=>i});let i=(0,n(12115).createContext)(null)},86301:(t,e,n)=>{function i(){return"undefined"!=typeof window}function o(t){return a(t)?(t.nodeName||"").toLowerCase():"#document"}function r(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function s(t){var e;return null==(e=(a(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function a(t){return!!i()&&(t instanceof Node||t instanceof r(t).Node)}function l(t){return!!i()&&(t instanceof Element||t instanceof r(t).Element)}function u(t){return!!i()&&(t instanceof HTMLElement||t instanceof r(t).HTMLElement)}function c(t){return!!i()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof r(t).ShadowRoot)}function h(t){let{overflow:e,overflowX:n,overflowY:i,display:o}=y(t);return/auto|scroll|overlay|hidden|clip/.test(e+i+n)&&!["inline","contents"].includes(o)}function d(t){return["table","td","th"].includes(o(t))}function f(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch(t){return!1}})}function m(t){let e=v(),n=l(t)?y(t):t;return["transform","translate","scale","rotate","perspective"].some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(t=>(n.willChange||"").includes(t))||["paint","layout","strict","content"].some(t=>(n.contain||"").includes(t))}function p(t){let e=P(t);for(;u(e)&&!g(e);){if(m(e))return e;if(f(e))break;e=P(e)}return null}function v(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function g(t){return["html","body","#document"].includes(o(t))}function y(t){return r(t).getComputedStyle(t)}function x(t){return l(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function P(t){if("html"===o(t))return t;let e=t.assignedSlot||t.parentNode||c(t)&&t.host||s(t);return c(e)?e.host:e}function w(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}n.d(e,{$4:()=>P,CP:()=>x,L9:()=>y,Lv:()=>d,Tc:()=>v,Tf:()=>f,ZU:()=>h,_m:()=>w,ep:()=>s,eu:()=>g,gJ:()=>p,mq:()=>o,sQ:()=>m,sb:()=>u,v9:()=>function t(e,n,i){var o;void 0===n&&(n=[]),void 0===i&&(i=!0);let s=function t(e){let n=P(e);return g(n)?e.ownerDocument?e.ownerDocument.body:e.body:u(n)&&h(n)?n:t(n)}(e),a=s===(null==(o=e.ownerDocument)?void 0:o.body),l=r(s);if(a){let e=w(l);return n.concat(l,l.visualViewport||[],h(s)?s:[],e&&i?t(e):[])}return n.concat(s,t(s,[],i))},vq:()=>l,zk:()=>r})},88558:(t,e,n)=>{n(12115);var i=n(96488),o=n(81786),r=n(40956);n(82885),n(78660);let s=()=>({});r.B,(0,i.T)({scrapeMotionValuesFromProps:s,createRenderState:s})},90693:(t,e,n)=>{n(12115),n(80845)},90869:(t,e,n)=>{n.d(e,{L:()=>i});let i=(0,n(12115).createContext)({})},93810:(t,e,n)=>{n(12115),n(51442)},98663:(t,e,n)=>{n(82885),n(97494),n(198)},98828:(t,e,n)=>{n(82885),n(86811),n(55539)}}]);