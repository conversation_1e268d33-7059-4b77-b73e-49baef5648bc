"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6213],{4993:(e,t,n)=>{var r=n(12115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},u=r.useSyncExternalStore,i=r.useRef,a=r.useEffect,l=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,f){var d=i(null);if(null===d.current){var s={hasValue:!1,value:null};d.current=s}else s=d.current;var v=u(e,(d=l(function(){function e(e){if(!a){if(a=!0,u=e,e=r(e),void 0!==f&&s.hasValue){var t=s.value;if(f(t,e))return i=t}return i=e}if(t=i,o(u,e))return t;var n=r(e);return void 0!==f&&f(t,n)?(u=e,t):(u=e,i=n)}var u,i,a=!1,l=void 0===n?null:n;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]},[t,n,r,f]))[0],d[1]);return a(function(){s.hasValue=!0,s.value=v},[v]),c(v),v}},25095:(e,t,n)=>{var r="undefined"==typeof Element,o=r?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,u=!r&&Element.prototype.getRootNode?function(e){var t;return null==e||null==(t=e.getRootNode)?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},i=function e(t,n){void 0===n&&(n=!0);var r,o=null==t||null==(r=t.getAttribute)?void 0:r.call(t,"inert");return""===o||"true"===o||n&&t&&e(t.parentNode)},a=function(e){var t,n=null==e||null==(t=e.getAttribute)?void 0:t.call(e,"contenteditable");return""===n||"true"===n},l=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},c=function(e){if(!e)throw Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||a(e))&&!l(e)?0:e.tabIndex},f=function(e,t){var n=c(e);return n<0&&t&&!l(e)?0:n},d=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},s=function(e){return"INPUT"===e.tagName},v=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]},m=function(e){if(!e.name)return!0;var t,n=e.form||u(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var o=v(t,e.form);return!o||o===e},p=function(e){return s(e)&&"radio"===e.type&&!m(e)},h=function(e){var t,n,r,o,i,a,l,c=e&&u(e),f=null==(t=c)?void 0:t.host,d=!1;if(c&&c!==e)for(d=!!(null!=(n=f)&&null!=(r=n.ownerDocument)&&r.contains(f)||null!=e&&null!=(o=e.ownerDocument)&&o.contains(e));!d&&f;)d=!!(null!=(a=f=null==(i=c=u(f))?void 0:i.host)&&null!=(l=a.ownerDocument)&&l.contains(f));return d},g=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},y=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var i=o.call(e,"details>summary:first-of-type")?e.parentElement:e;if(o.call(i,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return g(e)}else{if("function"==typeof r){for(var a=e;e;){var l=e.parentElement,c=u(e);if(l&&!l.shadowRoot&&!0===r(l))return g(e);e=e.assignedSlot?e.assignedSlot:l||c===e.ownerDocument?l:c.host}e=a}if(h(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},S=function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!o.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1},E=function(e,t){return!(t.disabled||i(t)||s(t)&&"hidden"===t.type||y(t,e)||"DETAILS"===t.tagName&&Array.prototype.slice.apply(t.children).some(function(e){return"SUMMARY"===e.tagName})||S(t))}},39611:(e,t,n)=>{e.exports=n(4993)}}]);