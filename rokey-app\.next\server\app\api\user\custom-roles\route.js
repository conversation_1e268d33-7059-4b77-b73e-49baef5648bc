(()=>{var e={};e.id=1923,e.ids=[1489,1923],e.modules={2507:(e,s,t)=>{"use strict";t.d(s,{H:()=>u,Q:()=>a,createSupabaseServerClientOnRequest:()=>i});var r=t(34386),n=t(39398),o=t(44999);async function i(){let e=await (0,o.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:s=>e.get(s)?.value,set(s,t,r){try{e.set({name:s,value:t,...r})}catch(e){}},remove(s,t){try{e.set({name:s,value:"",...t})}catch(e){}}}})}function a(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:s=>e.cookies.get(s)?.value,set(e,s,t){},remove(e,s){}}})}function u(){return(0,n.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13422:(e,s,t)=>{"use strict";t.r(s),t.d(s,{patchFetch:()=>_,routeModule:()=>I,serverHooks:()=>E,workAsyncStorage:()=>R,workUnitAsyncStorage:()=>g});var r={};t.r(r),t.d(r,{GET:()=>d,POST:()=>m});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),u=t(2507),c=t(43156),l=t(45697);let p=l.z.object({role_id:l.z.string().trim().min(1,"Role ID is required").max(30,"Role ID must be 30 characters or less").regex(/^[a-zA-Z0-9_]+$/,"Role ID can only contain letters, numbers, and underscores. No spaces or special characters."),name:l.z.string().trim().min(1,"Name is required").max(100,"Name must be 100 characters or less"),description:l.z.string().trim().max(500,"Description must be 500 characters or less").optional().nullable()});async function m(e){let s,t=await (0,u.createSupabaseServerClientOnRequest)(),{data:{user:r},error:n}=await t.auth.getUser();if(n||!r)return a.NextResponse.json({error:"Unauthorized. Please log in."},{status:401});let o=r.id;try{s=await e.json()}catch(e){return a.NextResponse.json({error:"Invalid JSON request body."},{status:400})}let i=p.safeParse(s);if(!i.success)return a.NextResponse.json({error:"Invalid request body.",issues:i.error.flatten().fieldErrors},{status:400});let{role_id:l,name:m,description:d}=i.data,{data:I}=await t.from("subscriptions").select("tier").eq("user_id",o).eq("status","active").single(),R=I?.tier||"free";if(!(0,c.Nu)(R,"custom_roles"))return a.NextResponse.json({error:`Custom roles are not available on the ${R} plan. Please upgrade to create custom roles.`},{status:403});let{count:g}=await t.from("user_custom_roles").select("*",{count:"exact",head:!0}).eq("user_id",o),E=(0,c.zX)(R).limits.maxCustomRoles;if(999999!==E&&(g||0)>=E)return a.NextResponse.json({error:`You have reached the maximum number of custom roles (${E}) for your ${R} plan. Please upgrade to create more custom roles.`},{status:403});try{let{data:e,error:s}=await t.from("user_custom_roles").insert({user_id:o,role_id:l,name:m,description:d}).select().single();if(s){if("23505"===s.code)return a.NextResponse.json({error:`You already have a custom role with ID '${l}'. Role IDs must be unique per user.`},{status:409});return a.NextResponse.json({error:"Failed to create custom role.",details:s.message},{status:500})}return a.NextResponse.json(e,{status:201})}catch(e){return a.NextResponse.json({error:"An unexpected server error occurred.",details:e.message},{status:500})}}async function d(e){let s=await (0,u.createSupabaseServerClientOnRequest)(),{data:{user:t},error:r}=await s.auth.getUser();if(r||!t)return a.NextResponse.json({error:"Unauthorized. Please log in."},{status:401});let n=t.id;try{let{data:e,error:t}=await s.from("user_custom_roles").select("*").eq("user_id",n).order("name",{ascending:!0});if(t)return a.NextResponse.json({error:"Failed to fetch custom roles.",details:t.message},{status:500});return a.NextResponse.json(e||[],{status:200})}catch(e){return a.NextResponse.json({error:"An unexpected server error occurred.",details:e.message},{status:500})}}let I=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/user/custom-roles/route",pathname:"/api/user/custom-roles",filename:"route",bundlePath:"app/api/user/custom-roles/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\user\\custom-roles\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:R,workUnitAsyncStorage:g,serverHooks:E}=I;function _(){return(0,i.patchFetch)({workAsyncStorage:R,workUnitAsyncStorage:g})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},43156:(e,s,t)=>{"use strict";t.d(s,{Nu:()=>a,iK:()=>i,zX:()=>o});var r=t(94473);let n={free:{name:"Free",price:"$0",priceId:r.Dm.FREE,productId:r.Zu.FREE,features:["Unlimited API requests","1 Custom Configuration","3 API Keys per config","All 300+ AI models","Strict fallback routing only","Basic analytics only","No custom roles, basic router only","Limited logs","Community support"],limits:{configurations:1,apiKeysPerConfig:3,apiRequests:999999,canUseAdvancedRouting:!1,canUseCustomRoles:!1,maxCustomRoles:0,canUsePromptEngineering:!1,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},starter:{name:"Starter",price:"$20",priceId:r.Dm.STARTER,productId:r.Zu.STARTER,features:["Unlimited API requests","15 Custom Configurations","5 API Keys per config","All 300+ AI models","Intelligent routing strategies","Up to 3 custom roles","Intelligent role routing","Prompt engineering (no file upload)","Enhanced logs and analytics","Community support"],limits:{configurations:15,apiKeysPerConfig:5,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:3,canUsePromptEngineering:!0,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1}},professional:{name:"Professional",price:"$50",priceId:r.Dm.PROFESSIONAL,productId:r.Zu.PROFESSIONAL,features:["Unlimited API requests","Unlimited Custom Configurations","Unlimited API Keys per config","All 300+ AI models","All advanced routing strategies","Unlimited custom roles","Prompt engineering + Knowledge base (5 documents)","Semantic caching","Advanced analytics and logging","Priority email support"],limits:{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:5,canUseSemanticCaching:!0}},enterprise:{name:"Enterprise",price:"$149",priceId:r.Dm.ENTERPRISE,productId:r.Zu.ENTERPRISE,features:["Unlimited API requests","Unlimited configurations","Unlimited API keys","All 300+ models + priority access","All routing strategies","Unlimited custom roles","All features + priority support","Unlimited knowledge base documents","Advanced semantic caching","Custom integrations","Dedicated support + phone","SLA guarantee"],limits:{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:999999,canUseSemanticCaching:!0}}};function o(e){return n[e]}function i(e,s,t){let r=n[e].limits;switch(s){case"create_config":return t<r.configurations;case"create_api_key":return t<r.apiKeysPerConfig;default:return!0}}function a(e,s){let t=n[e].limits;switch(s){case"custom_roles":return t.canUseCustomRoles;case"knowledge_base":return t.canUseKnowledgeBase;case"advanced_routing":return t.canUseAdvancedRouting;case"prompt_engineering":return t.canUsePromptEngineering;case"semantic_caching":return t.canUseSemanticCaching;case"configurations":return t.configurations>0;default:return!1}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function s(e){var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s}s.keys=()=>[],s.resolve=s,s.id=51906,e.exports=s},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94473:(e,s,t)=>{"use strict";t.d(s,{Dm:()=>n,Lj:()=>r,Zu:()=>o});let r={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},n={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},o={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,r.publishableKey&&r.publishableKey.substring(0,20),r.secretKey&&r.secretKey.substring(0,20),r.webhookSecret&&r.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,580,9398,3410,5697],()=>t(13422));module.exports=r})();