(()=>{var e={};e.id=5105,e.ids=[5105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16405:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,80559)),"C:\\RoKey App\\rokey-app\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\dashboard\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31082:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},45700:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))})},50515:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},51385:(e,t,s)=>{Promise.resolve().then(s.bind(s,80559))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57891:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},58061:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(60687),a=s(43210),i=s(16189),n=s(45994),l=s(31082),o=s(58089),d=s(68589),c=s(45700),m=s(57891),u=s(86297),p=s(61245),h=s(50515),x=s(59168),g=s(11016);function y(){let e=(0,i.useRouter)(),{user:t}=(0,g.R)(),[s,y]=(0,a.useState)(null),[v,b]=(0,a.useState)(!1),[f,w]=(0,a.useState)(!0),[j,N]=(0,a.useState)(null),[k,A]=(0,a.useState)([]),[_,C]=(0,a.useState)([{name:"API Gateway",status:"operational"},{name:"Routing Engine",status:"operational"},{name:"Analytics",status:"degraded"}]),q=(0,a.useCallback)(async()=>{try{f&&b(!0);let e=new Date;e.setDate(e.getDate()-30);let t=await fetch(`/api/analytics/summary?startDate=${e.toISOString()}&groupBy=day`);if(!t.ok)throw Error("Failed to fetch analytics data");let s=await t.json();y(s)}catch(e){N(e.message)}finally{f&&b(!1)}},[f]),E=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:6}).format(e),R=e=>new Intl.NumberFormat("en-US").format(e),P=async()=>{try{let e=await fetch("/api/activity?limit=10");if(!e.ok)throw Error("Failed to fetch recent activity");let t=(await e.json()).activities.map(e=>({id:e.id,action:e.action,model:e.model,time:e.time,status:e.status,details:e.details}));A(t)}catch(e){A([{id:"1",action:"System initialized",model:"RoKey",time:"Just now",status:"info"}])}},L=t?.user_metadata?.first_name||t?.user_metadata?.full_name?.split(" ")[0]||"there",M=s?[{name:"Total Requests",value:R(s.summary.total_requests),change:"Last 30 days",changeType:"neutral",icon:n.A},{name:"Total Cost",value:E(s.summary.total_cost),change:`${E(s.summary.average_cost_per_request)} avg/request`,changeType:"neutral",icon:l.A},{name:"Success Rate",value:`${s.summary.success_rate.toFixed(1)}%`,change:`${R(s.summary.successful_requests)} successful`,changeType:s.summary.success_rate>=95?"positive":"negative",icon:o.A},{name:"Total Tokens",value:R(s.summary.total_tokens),change:`${R(s.summary.total_input_tokens)} in, ${R(s.summary.total_output_tokens)} out`,changeType:"neutral",icon:d.A}]:[];return v&&f?(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)("div",{className:"card p-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-3/4 mb-2"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3"})]},t))})]}):j?(0,r.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:(0,r.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"animate-slide-in",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold mb-2",children:(0,r.jsxs)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400",children:["Welcome ",L,"! \uD83D\uDC4B"]})}),(0,r.jsx)("p",{className:"text-gray-400 text-lg",children:"Here's what's happening with your LLM infrastructure today."})]}),(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 text-center mt-8",children:[(0,r.jsxs)("p",{className:"text-red-400 mb-4",children:["Error loading analytics data: ",j]}),(0,r.jsx)("button",{onClick:q,className:"btn-primary",children:"Retry"})]})]})}):(0,r.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:(0,r.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8",children:[(0,r.jsxs)("div",{className:"animate-slide-in",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold mb-2",children:(0,r.jsxs)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400",children:["Welcome ",L,"! \uD83D\uDC4B"]})}),(0,r.jsx)("p",{className:"text-gray-400 text-lg",children:"Here's what's happening with your LLM infrastructure today."})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-slide-in",children:M.map((e,t)=>(0,r.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200",style:{animationDelay:`${100*t}ms`},children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-gray-400 mb-1",children:e.name}),(0,r.jsx)("p",{className:"text-3xl font-bold text-white mt-2",children:e.value}),(0,r.jsxs)("p",{className:`text-sm mt-2 flex items-center ${"positive"===e.changeType?"text-green-400":"negative"===e.changeType?"text-red-400":"text-gray-500"}`,children:["neutral"!==e.changeType&&(0,r.jsx)(c.A,{className:`h-4 w-4 mr-1 ${"negative"===e.changeType?"rotate-180":""}`}),e.change]})]}),(0,r.jsx)("div",{className:"text-gray-500",children:(0,r.jsx)(e.icon,{className:"h-6 w-6"})})]})},e.name))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 animate-slide-in",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{onClick:()=>{e.push("/my-models")},className:"btn-primary w-full justify-center hover:scale-105 transition-transform duration-200",children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-3"}),"Add New Model"]}),(0,r.jsxs)("button",{onClick:()=>{e.push("/playground")},className:"btn-secondary-dark w-full justify-center hover:scale-105 transition-transform duration-200",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 mr-3"}),"Test in Playground"]}),(0,r.jsxs)("button",{onClick:()=>{e.push("/logs")},className:"btn-secondary-dark w-full justify-center hover:scale-105 transition-transform duration-200",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 mr-3"}),"View Logs"]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 animate-slide-in",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"System Status"}),(0,r.jsx)("div",{className:"space-y-4",children:_.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:`w-3 h-3 rounded-full mr-3 ${"operational"===e.status?"bg-green-500":"degraded"===e.status?"bg-yellow-500":"bg-red-500"}`}),(0,r.jsx)("span",{className:"text-gray-300",children:e.name})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("span",{className:`text-sm font-medium ${"operational"===e.status?"text-green-400":"degraded"===e.status?"text-yellow-400":"text-red-400"}`,children:"operational"===e.status?"Operational":"degraded"===e.status?"Degraded":"Down"}),e.lastChecked&&(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.lastChecked})]})]},e.name))})]})]}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 animate-slide-in",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Recent Activity"}),(0,r.jsx)("button",{onClick:P,className:"text-orange-400 hover:text-orange-300 text-sm font-medium",children:"Refresh"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[0===k.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(h.A,{className:"h-12 w-12 text-gray-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-400",children:"No recent activity"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"Activity will appear here as you use the API"})]}):k.slice(-4).map(e=>(0,r.jsxs)("div",{className:"flex items-start p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-colors duration-200 group overflow-hidden",children:[(0,r.jsx)("div",{className:`w-3 h-3 rounded-full mr-4 ${"success"===e.status?"bg-green-500":"warning"===e.status?"bg-yellow-500":"error"===e.status?"bg-red-500":"bg-blue-500"}`}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-white font-medium break-words",children:e.action}),(0,r.jsxs)("p",{className:"text-gray-400 text-sm break-words",children:[e.model," • ",e.time]}),e.details&&(0,r.jsx)("p",{className:"text-gray-500 text-xs mt-1 line-clamp-2 leading-relaxed",title:e.details,children:e.details})]}),(0,r.jsx)("div",{className:"text-gray-400 group-hover:text-gray-300",children:"error"===e.status?(0,r.jsx)(x.A,{className:"h-5 w-5 text-red-400"}):(0,r.jsx)(d.A,{className:"h-5 w-5"})})]},e.id)),k.length>4&&(0,r.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,r.jsxs)("button",{onClick:()=>{window.location.href="/logs"},className:"text-sm text-orange-600 hover:text-orange-700 font-medium flex items-center justify-center w-full py-2 hover:bg-orange-50 rounded-lg transition-colors",children:["View All Activity (",k.length,")",(0,r.jsx)("svg",{className:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})]})})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68589:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\dashboard\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},87833:(e,t,s)=>{Promise.resolve().then(s.bind(s,58061))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,5449,4912],()=>s(16405));module.exports=r})();