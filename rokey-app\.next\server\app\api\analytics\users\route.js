(()=>{var e={};e.id=5527,e.ids=[1489,5527],e.modules={2507:(e,t,s)=>{"use strict";s.d(t,{H:()=>n,Q:()=>u,createSupabaseServerClientOnRequest:()=>o});var r=s(34386),i=s(39398),a=s(44999);async function o(){let e=await (0,a.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){try{e.set({name:t,value:s,...r})}catch(e){}},remove(t,s){try{e.set({name:t,value:"",...s})}catch(e){}}}})}function u(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,s){},remove(e,t){}}})}function n(){return(0,i.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73604:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>_,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var r={};s.r(r),s.d(r,{GET:()=>c});var i=s(96559),a=s(48088),o=s(37719),u=s(32190),n=s(2507);async function c(e){try{let t=(0,n.Q)(e),{data:{user:s},error:r}=await t.auth.getUser();if(r||!s)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:i}=new URL(e.url),a=parseInt(i.get("days")||"30"),o=new Date;o.setDate(o.getDate()-a);let{data:c}=await t.from("user_profiles").select("subscription_tier, subscription_status, created_at").eq("id",s.id).single(),{data:p}=await t.from("request_logs").select("user_id").eq("user_id",s.id).gte("request_timestamp",o.toISOString()).limit(1),l=p&&p.length>0?1:0,{data:d,count:m}=await t.from("user_generated_api_keys").select("id",{count:"exact"}).eq("user_id",s.id),{data:_,count:g}=await t.from("custom_api_configs").select("id",{count:"exact"}).eq("user_id",s.id),{data:h}=await t.from("request_logs").select("request_timestamp").eq("user_id",s.id).gte("request_timestamp",o.toISOString()).order("request_timestamp",{ascending:!0}),q={};h&&h.forEach(e=>{let t=new Date(e.request_timestamp).toISOString().split("T")[0];q[t]=(q[t]||0)+1});let f=[];for(let e=a-1;e>=0;e--){let t=new Date;t.setDate(t.getDate()-e);let s=t.toISOString().split("T")[0];f.push({date:s,requests:q[s]||0})}let{data:I,count:x}=await t.from("request_logs").select("id",{count:"exact"}).eq("user_id",s.id).gte("request_timestamp",o.toISOString()),{data:y,count:S}=await t.from("request_logs").select("id",{count:"exact"}).eq("user_id",s.id).gte("request_timestamp",o.toISOString()).gte("status_code",200).lt("status_code",300),w=x||0,v=S||0,O={free:+(c?.subscription_tier==="free"),starter:+(c?.subscription_tier==="starter"),professional:+(c?.subscription_tier==="professional"),enterprise:+(c?.subscription_tier==="enterprise")},{data:b}=await t.from("request_logs").select("llm_model_name").eq("user_id",s.id).gte("request_timestamp",o.toISOString()).not("llm_model_name","is",null),R={};b&&b.forEach(e=>{let t=e.llm_model_name;R[t]=(R[t]||0)+1});let j=Object.entries(R).sort(([,e],[,t])=>t-e).slice(0,5).map(([e,t])=>({model:e,count:t}));return u.NextResponse.json({success:!0,data:{activeUsers:l,totalApiKeys:m||0,totalConfigs:g||0,totalRequests:x||0,successfulRequests:S||0,successRate:w>0?v/w*100:0,userProfile:{tier:c?.subscription_tier||"free",status:c?.subscription_status||"active",createdAt:c?.created_at},subscriptionBreakdown:O,timeSeriesData:f,topModels:j,period:`${a} days`,startDate:o.toISOString(),endDate:new Date().toISOString()}})}catch(e){return u.NextResponse.json({error:"Failed to fetch users analytics",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/analytics/users/route",pathname:"/api/analytics/users",filename:"route",bundlePath:"app/api/analytics/users/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\analytics\\users\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:m}=p;function _(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,9398,3410],()=>s(73604));module.exports=r})();