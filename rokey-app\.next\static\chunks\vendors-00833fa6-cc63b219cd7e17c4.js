"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9420],{41093:(e,t,i)=>{i.d(t,{UE:()=>q,ll:()=>R,rD:()=>V,__:()=>k,UU:()=>j,cY:()=>P,BN:()=>E,Ej:()=>_});var n=i(671);function r(e,t,i){let r,{reference:l,floating:o}=e,a=(0,n.TV)(t),s=(0,n.Dz)(t),c=(0,n.sq)(s),f=(0,n.C0)(t),u="y"===a,d=l.x+l.width/2-o.width/2,h=l.y+l.height/2-o.height/2,m=l[c]/2-o[c]/2;switch(f){case"top":r={x:d,y:l.y-o.height};break;case"bottom":r={x:d,y:l.y+l.height};break;case"right":r={x:l.x+l.width,y:h};break;case"left":r={x:l.x-o.width,y:h};break;default:r={x:l.x,y:l.y}}switch((0,n.Sg)(t)){case"start":r[s]-=m*(i&&u?-1:1);break;case"end":r[s]+=m*(i&&u?-1:1)}return r}let l=async(e,t,i)=>{let{placement:n="bottom",strategy:l="absolute",middleware:o=[],platform:a}=i,s=o.filter(Boolean),c=await (null==a.isRTL?void 0:a.isRTL(t)),f=await a.getElementRects({reference:e,floating:t,strategy:l}),{x:u,y:d}=r(f,n,c),h=n,m={},p=0;for(let i=0;i<s.length;i++){let{name:o,fn:g}=s[i],{x:y,y:w,data:v,reset:b}=await g({x:u,y:d,initialPlacement:n,placement:h,strategy:l,middlewareData:m,rects:f,platform:a,elements:{reference:e,floating:t}});u=null!=y?y:u,d=null!=w?w:d,m={...m,[o]:{...m[o],...v}},b&&p<=50&&(p++,"object"==typeof b&&(b.placement&&(h=b.placement),b.rects&&(f=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:l}):b.rects),{x:u,y:d}=r(f,h,c)),i=-1)}return{x:u,y:d,placement:h,strategy:l,middlewareData:m}};async function o(e,t){var i;void 0===t&&(t={});let{x:r,y:l,platform:o,rects:a,elements:s,strategy:c}=e,{boundary:f="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:h=!1,padding:m=0}=(0,n._3)(t,e),p=(0,n.nI)(m),g=s[h?"floating"===d?"reference":"floating":d],y=(0,n.B1)(await o.getClippingRect({element:null==(i=await (null==o.isElement?void 0:o.isElement(g)))||i?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(s.floating)),boundary:f,rootBoundary:u,strategy:c})),w="floating"===d?{x:r,y:l,width:a.floating.width,height:a.floating.height}:a.reference,v=await (null==o.getOffsetParent?void 0:o.getOffsetParent(s.floating)),b=await (null==o.isElement?void 0:o.isElement(v))&&await (null==o.getScale?void 0:o.getScale(v))||{x:1,y:1},x=(0,n.B1)(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:w,offsetParent:v,strategy:c}):w);return{top:(y.top-x.top+p.top)/b.y,bottom:(x.bottom-y.bottom+p.bottom)/b.y,left:(y.left-x.left+p.left)/b.x,right:(x.right-y.right+p.right)/b.x}}async function a(e,t){let{placement:i,platform:r,elements:l}=e,o=await (null==r.isRTL?void 0:r.isRTL(l.floating)),a=(0,n.C0)(i),s=(0,n.Sg)(i),c="y"===(0,n.TV)(i),f=["left","top"].includes(a)?-1:1,u=o&&c?-1:1,d=(0,n._3)(t,e),{mainAxis:h,crossAxis:m,alignmentAxis:p}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof p&&(m="end"===s?-1*p:p),c?{x:m*u,y:h*f}:{x:h*f,y:m*u}}var s=i(86301);function c(e){let t=(0,s.L9)(e),i=parseFloat(t.width)||0,r=parseFloat(t.height)||0,l=(0,s.sb)(e),o=l?e.offsetWidth:i,a=l?e.offsetHeight:r,c=(0,n.LI)(i)!==o||(0,n.LI)(r)!==a;return c&&(i=o,r=a),{width:i,height:r,$:c}}function f(e){return(0,s.vq)(e)?e:e.contextElement}function u(e){let t=f(e);if(!(0,s.sb)(t))return(0,n.Jx)(1);let i=t.getBoundingClientRect(),{width:r,height:l,$:o}=c(t),a=(o?(0,n.LI)(i.width):i.width)/r,u=(o?(0,n.LI)(i.height):i.height)/l;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let d=(0,n.Jx)(0);function h(e){let t=(0,s.zk)(e);return(0,s.Tc)()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:d}function m(e,t,i,r){var l;void 0===t&&(t=!1),void 0===i&&(i=!1);let o=e.getBoundingClientRect(),a=f(e),c=(0,n.Jx)(1);t&&(r?(0,s.vq)(r)&&(c=u(r)):c=u(e));let d=(void 0===(l=i)&&(l=!1),r&&(!l||r===(0,s.zk)(a))&&l)?h(a):(0,n.Jx)(0),m=(o.left+d.x)/c.x,p=(o.top+d.y)/c.y,g=o.width/c.x,y=o.height/c.y;if(a){let e=(0,s.zk)(a),t=r&&(0,s.vq)(r)?(0,s.zk)(r):r,i=e,n=(0,s._m)(i);for(;n&&r&&t!==i;){let e=u(n),t=n.getBoundingClientRect(),r=(0,s.L9)(n),l=t.left+(n.clientLeft+parseFloat(r.paddingLeft))*e.x,o=t.top+(n.clientTop+parseFloat(r.paddingTop))*e.y;m*=e.x,p*=e.y,g*=e.x,y*=e.y,m+=l,p+=o,i=(0,s.zk)(n),n=(0,s._m)(i)}}return(0,n.B1)({width:g,height:y,x:m,y:p})}function p(e,t){let i=(0,s.CP)(e).scrollLeft;return t?t.left+i:m((0,s.ep)(e)).left+i}function g(e,t,i){void 0===i&&(i=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(i?0:p(e,n)),y:n.top+t.scrollTop}}function y(e,t,i){let r;if("viewport"===t)r=function(e,t){let i=(0,s.zk)(e),n=(0,s.ep)(e),r=i.visualViewport,l=n.clientWidth,o=n.clientHeight,a=0,c=0;if(r){l=r.width,o=r.height;let e=(0,s.Tc)();(!e||e&&"fixed"===t)&&(a=r.offsetLeft,c=r.offsetTop)}return{width:l,height:o,x:a,y:c}}(e,i);else if("document"===t)r=function(e){let t=(0,s.ep)(e),i=(0,s.CP)(e),r=e.ownerDocument.body,l=(0,n.T9)(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=(0,n.T9)(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-i.scrollLeft+p(e),c=-i.scrollTop;return"rtl"===(0,s.L9)(r).direction&&(a+=(0,n.T9)(t.clientWidth,r.clientWidth)-l),{width:l,height:o,x:a,y:c}}((0,s.ep)(e));else if((0,s.vq)(t))r=function(e,t){let i=m(e,!0,"fixed"===t),r=i.top+e.clientTop,l=i.left+e.clientLeft,o=(0,s.sb)(e)?u(e):(0,n.Jx)(1),a=e.clientWidth*o.x,c=e.clientHeight*o.y;return{width:a,height:c,x:l*o.x,y:r*o.y}}(t,i);else{let i=h(e);r={x:t.x-i.x,y:t.y-i.y,width:t.width,height:t.height}}return(0,n.B1)(r)}function w(e){return"static"===(0,s.L9)(e).position}function v(e,t){if(!(0,s.sb)(e)||"fixed"===(0,s.L9)(e).position)return null;if(t)return t(e);let i=e.offsetParent;return(0,s.ep)(e)===i&&(i=i.ownerDocument.body),i}function b(e,t){let i=(0,s.zk)(e);if((0,s.Tf)(e))return i;if(!(0,s.sb)(e)){let t=(0,s.$4)(e);for(;t&&!(0,s.eu)(t);){if((0,s.vq)(t)&&!w(t))return t;t=(0,s.$4)(t)}return i}let n=v(e,t);for(;n&&(0,s.Lv)(n)&&w(n);)n=v(n,t);return n&&(0,s.eu)(n)&&w(n)&&!(0,s.sQ)(n)?i:n||(0,s.gJ)(e)||i}let x=async function(e){let t=this.getOffsetParent||b,i=this.getDimensions,r=await i(e.floating);return{reference:function(e,t,i){let r=(0,s.sb)(t),l=(0,s.ep)(t),o="fixed"===i,a=m(e,!0,o,t),c={scrollLeft:0,scrollTop:0},f=(0,n.Jx)(0);if(r||!r&&!o)if(("body"!==(0,s.mq)(t)||(0,s.ZU)(l))&&(c=(0,s.CP)(t)),r){let e=m(t,!0,o,t);f.x=e.x+t.clientLeft,f.y=e.y+t.clientTop}else l&&(f.x=p(l));o&&!r&&l&&(f.x=p(l));let u=!l||r||o?(0,n.Jx)(0):g(l,c);return{x:a.left+c.scrollLeft-f.x-u.x,y:a.top+c.scrollTop-f.y-u.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},T={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:i,offsetParent:r,strategy:l}=e,o="fixed"===l,a=(0,s.ep)(r),c=!!t&&(0,s.Tf)(t.floating);if(r===a||c&&o)return i;let f={scrollLeft:0,scrollTop:0},d=(0,n.Jx)(1),h=(0,n.Jx)(0),p=(0,s.sb)(r);if((p||!p&&!o)&&(("body"!==(0,s.mq)(r)||(0,s.ZU)(a))&&(f=(0,s.CP)(r)),(0,s.sb)(r))){let e=m(r);d=u(r),h.x=e.x+r.clientLeft,h.y=e.y+r.clientTop}let y=!a||p||o?(0,n.Jx)(0):g(a,f,!0);return{width:i.width*d.x,height:i.height*d.y,x:i.x*d.x-f.scrollLeft*d.x+h.x+y.x,y:i.y*d.y-f.scrollTop*d.y+h.y+y.y}},getDocumentElement:s.ep,getClippingRect:function(e){let{element:t,boundary:i,rootBoundary:r,strategy:l}=e,o=[..."clippingAncestors"===i?(0,s.Tf)(t)?[]:function(e,t){let i=t.get(e);if(i)return i;let n=(0,s.v9)(e,[],!1).filter(e=>(0,s.vq)(e)&&"body"!==(0,s.mq)(e)),r=null,l="fixed"===(0,s.L9)(e).position,o=l?(0,s.$4)(e):e;for(;(0,s.vq)(o)&&!(0,s.eu)(o);){let t=(0,s.L9)(o),i=(0,s.sQ)(o);i||"fixed"!==t.position||(r=null),(l?!i&&!r:!i&&"static"===t.position&&!!r&&["absolute","fixed"].includes(r.position)||(0,s.ZU)(o)&&!i&&function e(t,i){let n=(0,s.$4)(t);return!(n===i||!(0,s.vq)(n)||(0,s.eu)(n))&&("fixed"===(0,s.L9)(n).position||e(n,i))}(e,o))?n=n.filter(e=>e!==o):r=t,o=(0,s.$4)(o)}return t.set(e,n),n}(t,this._c):[].concat(i),r],a=o[0],c=o.reduce((e,i)=>{let r=y(t,i,l);return e.top=(0,n.T9)(r.top,e.top),e.right=(0,n.jk)(r.right,e.right),e.bottom=(0,n.jk)(r.bottom,e.bottom),e.left=(0,n.T9)(r.left,e.left),e},y(t,a,l));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:b,getElementRects:x,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:i}=c(e);return{width:t,height:i}},getScale:u,isElement:s.vq,isRTL:function(e){return"rtl"===(0,s.L9)(e).direction}};function L(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function R(e,t,i,r){let l;void 0===r&&(r={});let{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,h=f(e),p=o||a?[...h?(0,s.v9)(h):[],...(0,s.v9)(t)]:[];p.forEach(e=>{o&&e.addEventListener("scroll",i,{passive:!0}),a&&e.addEventListener("resize",i)});let g=h&&u?function(e,t){let i,r=null,l=(0,s.ep)(e);function o(){var e;clearTimeout(i),null==(e=r)||e.disconnect(),r=null}return!function a(s,c){void 0===s&&(s=!1),void 0===c&&(c=1),o();let f=e.getBoundingClientRect(),{left:u,top:d,width:h,height:m}=f;if(s||t(),!h||!m)return;let p=(0,n.RI)(d),g=(0,n.RI)(l.clientWidth-(u+h)),y={rootMargin:-p+"px "+-g+"px "+-(0,n.RI)(l.clientHeight-(d+m))+"px "+-(0,n.RI)(u)+"px",threshold:(0,n.T9)(0,(0,n.jk)(1,c))||1},w=!0;function v(t){let n=t[0].intersectionRatio;if(n!==c){if(!w)return a();n?a(!1,n):i=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==n||L(f,e.getBoundingClientRect())||a(),w=!1}try{r=new IntersectionObserver(v,{...y,root:l.ownerDocument})}catch(e){r=new IntersectionObserver(v,y)}r.observe(e)}(!0),o}(h,i):null,y=-1,w=null;c&&(w=new ResizeObserver(e=>{let[n]=e;n&&n.target===h&&w&&(w.unobserve(t),cancelAnimationFrame(y),y=requestAnimationFrame(()=>{var e;null==(e=w)||e.observe(t)})),i()}),h&&!d&&w.observe(h),w.observe(t));let v=d?m(e):null;return d&&function t(){let n=m(e);v&&!L(v,n)&&i(),v=n,l=requestAnimationFrame(t)}(),i(),()=>{var e;p.forEach(e=>{o&&e.removeEventListener("scroll",i),a&&e.removeEventListener("resize",i)}),null==g||g(),null==(e=w)||e.disconnect(),w=null,d&&cancelAnimationFrame(l)}}let k=o,P=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var i,n;let{x:r,y:l,placement:o,middlewareData:s}=t,c=await a(t,e);return o===(null==(i=s.offset)?void 0:i.placement)&&null!=(n=s.arrow)&&n.alignmentOffset?{}:{x:r+c.x,y:l+c.y,data:{...c,placement:o}}}}},E=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:i,y:r,placement:l}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:c={fn:e=>{let{x:t,y:i}=e;return{x:t,y:i}}},...f}=(0,n._3)(e,t),u={x:i,y:r},d=await o(t,f),h=(0,n.TV)((0,n.C0)(l)),m=(0,n.PG)(h),p=u[m],g=u[h];if(a){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",i=p+d[e],r=p-d[t];p=(0,n.qE)(i,p,r)}if(s){let e="y"===h?"top":"left",t="y"===h?"bottom":"right",i=g+d[e],r=g-d[t];g=(0,n.qE)(i,g,r)}let y=c.fn({...t,[m]:p,[h]:g});return{...y,data:{x:y.x-i,y:y.y-r,enabled:{[m]:a,[h]:s}}}}}},j=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var i,r,l,a,s;let{placement:c,middlewareData:f,rects:u,initialPlacement:d,platform:h,elements:m}=t,{mainAxis:p=!0,crossAxis:g=!0,fallbackPlacements:y,fallbackStrategy:w="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:b=!0,...x}=(0,n._3)(e,t);if(null!=(i=f.arrow)&&i.alignmentOffset)return{};let T=(0,n.C0)(c),L=(0,n.TV)(d),R=(0,n.C0)(d)===d,k=await (null==h.isRTL?void 0:h.isRTL(m.floating)),P=y||(R||!b?[(0,n.bV)(d)]:(0,n.WJ)(d)),E="none"!==v;!y&&E&&P.push(...(0,n.lP)(d,b,v,k));let j=[d,...P],_=await o(t,x),q=[],V=(null==(r=f.flip)?void 0:r.overflows)||[];if(p&&q.push(_[T]),g){let e=(0,n.w7)(c,u,k);q.push(_[e[0]],_[e[1]])}if(V=[...V,{placement:c,overflows:q}],!q.every(e=>e<=0)){let e=((null==(l=f.flip)?void 0:l.index)||0)+1,t=j[e];if(t&&("alignment"!==g||L===(0,n.TV)(t)||V.every(e=>e.overflows[0]>0&&(0,n.TV)(e.placement)===L)))return{data:{index:e,overflows:V},reset:{placement:t}};let i=null==(a=V.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:a.placement;if(!i)switch(w){case"bestFit":{let e=null==(s=V.filter(e=>{if(E){let t=(0,n.TV)(e.placement);return t===L||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:s[0];e&&(i=e);break}case"initialPlacement":i=d}if(c!==i)return{reset:{placement:i}}}return{}}}},_=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var i,r;let l,a,{placement:s,rects:c,platform:f,elements:u}=t,{apply:d=()=>{},...h}=(0,n._3)(e,t),m=await o(t,h),p=(0,n.C0)(s),g=(0,n.Sg)(s),y="y"===(0,n.TV)(s),{width:w,height:v}=c.floating;"top"===p||"bottom"===p?(l=p,a=g===(await (null==f.isRTL?void 0:f.isRTL(u.floating))?"start":"end")?"left":"right"):(a=p,l="end"===g?"top":"bottom");let b=v-m.top-m.bottom,x=w-m.left-m.right,T=(0,n.jk)(v-m[l],b),L=(0,n.jk)(w-m[a],x),R=!t.middlewareData.shift,k=T,P=L;if(null!=(i=t.middlewareData.shift)&&i.enabled.x&&(P=x),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=b),R&&!g){let e=(0,n.T9)(m.left,0),t=(0,n.T9)(m.right,0),i=(0,n.T9)(m.top,0),r=(0,n.T9)(m.bottom,0);y?P=w-2*(0!==e||0!==t?e+t:(0,n.T9)(m.left,m.right)):k=v-2*(0!==i||0!==r?i+r:(0,n.T9)(m.top,m.bottom))}await d({...t,availableWidth:P,availableHeight:k});let E=await f.getDimensions(u.floating);return w!==E.width||v!==E.height?{reset:{rects:!0}}:{}}}},q=e=>({name:"arrow",options:e,async fn(t){let{x:i,y:r,placement:l,rects:o,platform:a,elements:s,middlewareData:c}=t,{element:f,padding:u=0}=(0,n._3)(e,t)||{};if(null==f)return{};let d=(0,n.nI)(u),h={x:i,y:r},m=(0,n.Dz)(l),p=(0,n.sq)(m),g=await a.getDimensions(f),y="y"===m,w=y?"clientHeight":"clientWidth",v=o.reference[p]+o.reference[m]-h[m]-o.floating[p],b=h[m]-o.reference[m],x=await (null==a.getOffsetParent?void 0:a.getOffsetParent(f)),T=x?x[w]:0;T&&await (null==a.isElement?void 0:a.isElement(x))||(T=s.floating[w]||o.floating[p]);let L=T/2-g[p]/2-1,R=(0,n.jk)(d[y?"top":"left"],L),k=(0,n.jk)(d[y?"bottom":"right"],L),P=T-g[p]-k,E=T/2-g[p]/2+(v/2-b/2),j=(0,n.qE)(R,E,P),_=!c.arrow&&null!=(0,n.Sg)(l)&&E!==j&&o.reference[p]/2-(E<R?R:k)-g[p]/2<0,q=_?E<R?E-R:E-P:0;return{[m]:h[m]+q,data:{[m]:j,centerOffset:E-j-q,..._&&{alignmentOffset:q}},reset:_}}}),V=(e,t,i)=>{let n=new Map,r={platform:T,...i},o={...r.platform,_c:n};return l(e,t,{...r,platform:o})}},75518:(e,t,i)=>{i.d(t,{Ay:()=>T});class n{constructor(e=0,t="Network Error"){this.status=e,this.text=t}}let r={origin:"https://api.emailjs.com",blockHeadless:!1,storageProvider:(()=>{if("undefined"!=typeof localStorage)return{get:e=>Promise.resolve(localStorage.getItem(e)),set:(e,t)=>Promise.resolve(localStorage.setItem(e,t)),remove:e=>Promise.resolve(localStorage.removeItem(e))}})()},l=e=>e?"string"==typeof e?{publicKey:e}:"[object Object]"===e.toString()?e:{}:{},o=async(e,t,i={})=>{let l=await fetch(r.origin+e,{method:"POST",headers:i,body:t}),o=await l.text(),a=new n(l.status,o);if(l.ok)return a;throw a},a=(e,t,i)=>{if(!e||"string"!=typeof e)throw"The public key is required. Visit https://dashboard.emailjs.com/admin/account";if(!t||"string"!=typeof t)throw"The service ID is required. Visit https://dashboard.emailjs.com/admin";if(!i||"string"!=typeof i)throw"The template ID is required. Visit https://dashboard.emailjs.com/admin/templates"},s=e=>{if(e&&"[object Object]"!==e.toString())throw"The template params have to be the object. Visit https://www.emailjs.com/docs/sdk/send/"},c=e=>e.webdriver||!e.languages||0===e.languages.length,f=()=>new n(451,"Unavailable For Headless Browser"),u=(e,t)=>{if(!Array.isArray(e))throw"The BlockList list has to be an array";if("string"!=typeof t)throw"The BlockList watchVariable has to be a string"},d=e=>!e.list?.length||!e.watchVariable,h=(e,t)=>e instanceof FormData?e.get(t):e[t],m=(e,t)=>{if(d(e))return!1;u(e.list,e.watchVariable);let i=h(t,e.watchVariable);return"string"==typeof i&&e.list.includes(i)},p=()=>new n(403,"Forbidden"),g=(e,t)=>{if("number"!=typeof e||e<0)throw"The LimitRate throttle has to be a positive number";if(t&&"string"!=typeof t)throw"The LimitRate ID has to be a non-empty string"},y=async(e,t,i)=>{let n=Number(await i.get(e)||0);return t-Date.now()+n},w=async(e,t,i)=>{if(!t.throttle||!i)return!1;g(t.throttle,t.id);let n=t.id||e;return await y(n,t.throttle,i)>0||(await i.set(n,Date.now().toString()),!1)},v=()=>new n(429,"Too Many Requests"),b=e=>{if(!e||"FORM"!==e.nodeName)throw"The 3rd parameter is expected to be the HTML form element or the style selector of the form"},x=e=>"string"==typeof e?document.querySelector(e):e,T={init:(e,t="https://api.emailjs.com")=>{if(!e)return;let i=l(e);r.publicKey=i.publicKey,r.blockHeadless=i.blockHeadless,r.storageProvider=i.storageProvider,r.blockList=i.blockList,r.limitRate=i.limitRate,r.origin=i.origin||t},send:async(e,t,i,n)=>{let u=l(n),d=u.publicKey||r.publicKey,h=u.blockHeadless||r.blockHeadless,g=u.storageProvider||r.storageProvider,y={...r.blockList,...u.blockList},b={...r.limitRate,...u.limitRate};return h&&c(navigator)?Promise.reject(f()):(a(d,e,t),s(i),i&&m(y,i))?Promise.reject(p()):await w(location.pathname,b,g)?Promise.reject(v()):o("/api/v1.0/email/send",JSON.stringify({lib_version:"4.4.1",user_id:d,service_id:e,template_id:t,template_params:i}),{"Content-type":"application/json"})},sendForm:async(e,t,i,n)=>{let s=l(n),u=s.publicKey||r.publicKey,d=s.blockHeadless||r.blockHeadless,h=r.storageProvider||s.storageProvider,g={...r.blockList,...s.blockList},y={...r.limitRate,...s.limitRate};if(d&&c(navigator))return Promise.reject(f());let T=x(i);a(u,e,t),b(T);let L=new FormData(T);return m(g,L)?Promise.reject(p()):await w(location.pathname,y,h)?Promise.reject(v()):(L.append("lib_version","4.4.1"),L.append("service_id",e),L.append("template_id",t),L.append("user_id",u),o("/api/v1.0/email/send-form",L))},EmailJSResponseStatus:n}}}]);