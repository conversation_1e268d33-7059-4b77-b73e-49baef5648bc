(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6703,9558],{10222:()=>{},30347:()=>{},42714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return a}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function s(e){return["async","defer","noModule"].includes(e)}function a(e,t){for(let[a,i]of Object.entries(t)){if(!t.hasOwnProperty(a)||n.includes(a)||void 0===i)continue;let l=r[a]||a.toLowerCase();"SCRIPT"===e.tagName&&s(l)?e[l]=!!i:e.setAttribute(l,String(i)),(!1===i||"SCRIPT"===e.tagName&&s(l)&&(!i||"false"===i))&&(e.setAttribute(l,""),e.removeAttribute(l))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48031:(e,t,r)=>{"use strict";r.d(t,{SpeedInsights:()=>f});var n=r(12115),s=r(35695),a=r(87358),i=()=>{window.si||(window.si=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];(window.siq=window.siq||[]).push(t)})};function l(){return"development"===function(){return"production"}()}function c(e){return new RegExp("/".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"(?=[/?#]|$)"))}function o(e){(0,n.useEffect)(()=>{var t;e.beforeSend&&(null==(t=window.si)||t.call(window,"beforeSend",e.beforeSend))},[e.beforeSend]);let t=(0,n.useRef)(null);return(0,n.useEffect)(()=>{if(t.current)e.route&&t.current(e.route);else{var r,n;let s=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("undefined"==typeof window||null===t.route)return null;i();let r=t.scriptSrc?t.scriptSrc:l()?"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js":t.dsn?"https://va.vercel-scripts.com/v1/speed-insights/script.js":t.basePath?"".concat(t.basePath,"/speed-insights/script.js"):"/_vercel/speed-insights/script.js";if(document.head.querySelector('script[src*="'.concat(r,'"]')))return null;t.beforeSend&&(null==(e=window.si)||e.call(window,"beforeSend",t.beforeSend));let n=document.createElement("script");return n.src=r,n.defer=!0,n.dataset.sdkn="@vercel/speed-insights"+(t.framework?"/".concat(t.framework):""),n.dataset.sdkv="1.2.0",t.sampleRate&&(n.dataset.sampleRate=t.sampleRate.toString()),t.route&&(n.dataset.route=t.route),t.endpoint?n.dataset.endpoint=t.endpoint:t.basePath&&(n.dataset.endpoint="".concat(t.basePath,"/speed-insights/vitals")),t.dsn&&(n.dataset.dsn=t.dsn),l()&&!1===t.debug&&(n.dataset.debug="false"),n.onerror=()=>{},document.head.appendChild(n),{setRoute:e=>{n.dataset.route=null!=e?e:void 0}}}({framework:null!=(r=e.framework)?r:"react",basePath:null!=(n=e.basePath)?n:function(){if(void 0!==a&&void 0!==a.env)return a.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...e});s&&(t.current=s.setRoute)}},[e.route]),null}var u=()=>{let e=(0,s.useParams)(),t=(0,s.useSearchParams)()||new URLSearchParams,r=(0,s.usePathname)();return e?function(e,t){if(!e||!t)return e;let r=e;try{let e=Object.entries(t);for(let[t,n]of e)if(!Array.isArray(n)){let e=c(n);e.test(r)&&(r=r.replace(e,"/[".concat(t,"]")))}for(let[t,n]of e)if(Array.isArray(n)){let e=c(n.join("/"));e.test(r)&&(r=r.replace(e,"/[...".concat(t,"]")))}return r}catch(t){return e}}(r,Object.keys(e).length?e:Object.fromEntries(t.entries())):null};function d(e){let t=u();return n.createElement(o,{route:t,...e,framework:"next",basePath:function(){if(void 0!==a&&void 0!==a.env)return a.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH}()})}function f(e){return n.createElement(n.Suspense,{fallback:null},n.createElement(d,{...e}))}},69243:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},handleClientScriptLoad:function(){return m},initScriptLoader:function(){return b}});let n=r(38466),s=r(93011),a=r(95155),i=n._(r(47650)),l=s._(r(12115)),c=r(82830),o=r(42714),u=r(92374),d=new Map,f=new Set,h=e=>{if(i.default.preinit)return void e.forEach(e=>{i.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},p=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:s=null,dangerouslySetInnerHTML:a,children:i="",strategy:l="afterInteractive",onError:c,stylesheets:u}=e,p=r||t;if(p&&f.has(p))return;if(d.has(t)){f.add(p),d.get(t).then(n,c);return}let m=()=>{s&&s(),f.add(p)},b=document.createElement("script"),v=new Promise((e,t)=>{b.addEventListener("load",function(t){e(),n&&n.call(this,t),m()}),b.addEventListener("error",function(e){t(e)})}).catch(function(e){c&&c(e)});a?(b.innerHTML=a.__html||"",m()):i?(b.textContent="string"==typeof i?i:Array.isArray(i)?i.join(""):"",m()):t&&(b.src=t,d.set(t,v)),(0,o.setAttributesFromProps)(b,e),"worker"===l&&b.setAttribute("type","text/partytown"),b.setAttribute("data-nscript",l),u&&h(u),document.body.appendChild(b)};function m(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,u.requestIdleCallback)(()=>p(e))}):p(e)}function b(e){e.forEach(m),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function v(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:s=null,strategy:o="afterInteractive",onError:d,stylesheets:h,...m}=e,{updateScripts:b,scripts:v,getIsSsr:g,appDir:y,nonce:_}=(0,l.useContext)(c.HeadManagerContext),w=(0,l.useRef)(!1);(0,l.useEffect)(()=>{let e=t||r;w.current||(s&&e&&f.has(e)&&s(),w.current=!0)},[s,t,r]);let S=(0,l.useRef)(!1);if((0,l.useEffect)(()=>{if(!S.current){if("afterInteractive"===o)p(e);else"lazyOnload"===o&&("complete"===document.readyState?(0,u.requestIdleCallback)(()=>p(e)):window.addEventListener("load",()=>{(0,u.requestIdleCallback)(()=>p(e))}));S.current=!0}},[e,o]),("beforeInteractive"===o||"worker"===o)&&(b?(v[o]=(v[o]||[]).concat([{id:t,src:r,onLoad:n,onReady:s,onError:d,...m}]),b(v)):g&&g()?f.add(t||r):g&&!g()&&p(e)),y){if(h&&h.forEach(e=>{i.default.preinit(e,{as:"style"})}),"beforeInteractive"===o)if(!r)return m.dangerouslySetInnerHTML&&(m.children=m.dangerouslySetInnerHTML.__html,delete m.dangerouslySetInnerHTML),(0,a.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...m,id:t}])+")"}});else return i.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:_,crossOrigin:m.crossOrigin}:{as:"script",nonce:_,crossOrigin:m.crossOrigin}),(0,a.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...m,id:t}])+")"}});"afterInteractive"===o&&r&&i.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:_,crossOrigin:m.crossOrigin}:{as:"script",nonce:_,crossOrigin:m.crossOrigin})}return null}Object.defineProperty(v,"__nextScript",{value:!0});let g=v;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74309:(e,t,r)=>{Promise.resolve().then(r.bind(r,48031)),Promise.resolve().then(r.t.bind(r,69243,23)),Promise.resolve().then(r.t.bind(r,80527,23)),Promise.resolve().then(r.t.bind(r,30347,23)),Promise.resolve().then(r.bind(r,35462)),Promise.resolve().then(r.bind(r,99030)),Promise.resolve().then(r.bind(r,52469)),Promise.resolve().then(r.bind(r,38050)),Promise.resolve().then(r.t.bind(r,10222,23))},80527:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},86973:(e,t,r)=>{"use strict";r.d(t,{globalCache:()=>s});class n{set(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{ttl:n=3e5,tags:s=[],priority:a="medium",serialize:i=!1}=r,l={data:i?JSON.parse(JSON.stringify(t)):t,timestamp:Date.now(),ttl:n,accessCount:0,lastAccessed:Date.now(),tags:s,priority:a};this.cache.size>=this.maxSize&&this.evictLeastUsed(),this.cache.set(e,l)}get(e){let t=this.cache.get(e);return t?this.isExpired(t)?(this.cache.delete(e),null):(t.accessCount++,t.lastAccessed=Date.now(),t.data):null}getStale(e){let t=this.cache.get(e);if(!t)return{data:null,isStale:!1};let r=this.isExpired(t);return t.accessCount++,t.lastAccessed=Date.now(),{data:t.data,isStale:r}}has(e){let t=this.cache.get(e);return!!t&&(!this.isExpired(t)||(this.cache.delete(e),!1))}delete(e){return this.cache.delete(e)}invalidateByTags(e){let t=0;for(let[r,n]of this.cache.entries())n.tags.some(t=>e.includes(t))&&(this.cache.delete(r),t++);return t}clear(){this.cache.clear()}getStats(){let e=Array.from(this.cache.values()),t=Date.now();return{size:this.cache.size,maxSize:this.maxSize,hitRate:this.calculateHitRate(),averageAge:e.reduce((e,r)=>e+(t-r.timestamp),0)/e.length||0,totalAccesses:e.reduce((e,t)=>e+t.accessCount,0),expiredEntries:e.filter(e=>this.isExpired(e)).length,priorityDistribution:{high:e.filter(e=>"high"===e.priority).length,medium:e.filter(e=>"medium"===e.priority).length,low:e.filter(e=>"low"===e.priority).length}}}async preload(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=this.get(e);if(n)return this.backgroundRefresh(e,t,r),n;let s=await t();return this.set(e,s,r),s}async backgroundRefresh(e,t,r){try{let n=await t();this.set(e,n,r)}catch(e){}}isExpired(e){return Date.now()-e.timestamp>e.ttl}evictLeastUsed(){if(0===this.cache.size)return;let[e]=Array.from(this.cache.entries()).sort((e,t)=>{let[,r]=e,[,n]=t,s={low:0,medium:1,high:2},a=s[r.priority]-s[n.priority];return 0!==a?a:r.lastAccessed-n.lastAccessed})[0];this.cache.delete(e)}calculateHitRate(){let e=Array.from(this.cache.values()).reduce((e,t)=>e+t.accessCount,0);return e>0?this.cache.size/e*100:0}startCleanup(){this.cleanupInterval=setInterval(()=>{this.cleanup()},6e4)}cleanup(){Date.now();let e=[];for(let[t,r]of this.cache.entries())this.isExpired(r)&&e.push(t);e.forEach(e=>this.cache.delete(e)),e.length}destroy(){this.cleanupInterval&&clearInterval(this.cleanupInterval),this.cache.clear()}constructor(e=100){this.cache=new Map,this.cleanupInterval=null,this.maxSize=e,this.startCleanup()}}let s=new n(200)},92374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}},e=>{var t=t=>e(e.s=t);e.O(0,[2098,7690,1911,8888,1459,5738,2396,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(74309)),_N_E=e.O()}]);