(()=>{var e={};e.id=5771,e.ids=[1489,5771],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{H:()=>u,Q:()=>o,createSupabaseServerClientOnRequest:()=>n});var s=r(34386),i=r(39398),a=r(44999);async function n(){let e=await (0,a.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function o(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}function u(){return(0,i.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},96627:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{POST:()=>p});var i=r(96559),a=r(48088),n=r(37719),o=r(32190),u=r(2507);async function p(e){let t=await (0,u.createSupabaseServerClientOnRequest)(),{data:{user:s},error:i}=await t.auth.getUser();if(i||!s)return o.NextResponse.json({error:"Unauthorized: You must be logged in to create training jobs."},{status:401});try{let i,{custom_api_config_id:a,name:n,description:u,training_data:p,parameters:c}=await e.json();if(!a||!n)return o.NextResponse.json({error:"Missing required fields: custom_api_config_id, name"},{status:400});let{data:d,error:l}=await t.from("training_jobs").select("id, name, description, status, training_data, parameters, created_at, updated_at").eq("custom_api_config_id",a).eq("status","completed").order("created_at",{ascending:!1}).limit(1).maybeSingle();if(l)return o.NextResponse.json({error:"Failed to check existing training job",details:l.message},{status:500});let g=!1;if(d){let{data:e,error:r}=await t.from("training_jobs").update({name:n,description:u,training_data:p,parameters:c,updated_at:new Date().toISOString()}).eq("id",d.id).select().single();if(r)return o.NextResponse.json({error:"Failed to update training job",details:r.message},{status:500});i=e,g=!1}else{let{data:e,error:r}=await t.from("training_jobs").insert({custom_api_config_id:a,name:n,description:u,training_data:p,parameters:c,status:"completed",progress_percentage:100,started_at:new Date().toISOString(),completed_at:new Date().toISOString(),user_id:s.id}).select().single();if(r)return o.NextResponse.json({error:"Failed to create training job",details:r.message},{status:500});i=e,g=!0}try{let{trainingDataCache:e}=await r.e(2842).then(r.bind(r,2842));e.invalidate(a)}catch(e){}return o.NextResponse.json({id:i.id,custom_api_config_id:i.custom_api_config_id,name:i.name,description:i.description,status:i.status,training_data:i.training_data,parameters:i.parameters,created_at:i.created_at,updated_at:i.updated_at,operation:g?"created":"updated",message:g?"New training job created successfully":"Existing training job updated successfully - all files preserved"},{status:g?201:200})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/training/jobs/upsert/route",pathname:"/api/training/jobs/upsert",filename:"route",bundlePath:"app/api/training/jobs/upsert/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\training\\jobs\\upsert\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:g}=c;function m(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(96627));module.exports=s})();