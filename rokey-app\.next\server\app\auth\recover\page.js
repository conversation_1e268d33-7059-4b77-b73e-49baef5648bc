(()=>{var e={};e.id=4586,e.ids=[4586],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4154:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\recover\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\auth\\recover\\page.tsx","default")},6671:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["auth",{children:["recover",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4154)),"C:\\RoKey App\\rokey-app\\src\\app\\auth\\recover\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\RoKey App\\rokey-app\\src\\app\\auth\\recover\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/recover/page",pathname:"/auth/recover",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37961:(e,t,r)=>{Promise.resolve().then(r.bind(r,59972))},44725:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},47689:(e,t,r)=>{Promise.resolve().then(r.bind(r,4154))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59972:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687),a=r(43210),n=r(52535),i=r(85814),o=r.n(i),l=r(44725);function c(){let[e,t]=(0,a.useState)(""),[r,i]=(0,a.useState)(!1),[c,d]=(0,a.useState)(null),[u,p]=(0,a.useState)(""),m=async t=>{t.preventDefault(),i(!0),p(""),d(null);try{let t=await fetch("/api/auth/check-pending-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})}),r=await t.json();if(!t.ok)throw Error(r.error||"Failed to check account status");d(r)}catch(e){p(e instanceof Error?e.message:"Failed to check account status")}finally{i(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)(o(),{href:"/auth/signin",className:"inline-flex items-center text-gray-600 hover:text-gray-800 mb-8 transition-colors",children:[(0,s.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"Back to Sign In"]}),(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Account Recovery"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Can't remember if you have an account or need to complete a payment? Enter your email to check your account status."})]}),(0,s.jsxs)("form",{onSubmit:m,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,s.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>t(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent transition-all",placeholder:"Enter your email address"})]}),(0,s.jsx)("button",{type:"submit",disabled:r,className:"w-full bg-[#ff6b35] text-white py-3 px-4 rounded-xl font-medium hover:bg-[#e55a2b] focus:ring-2 focus:ring-[#ff6b35] focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:r?"Checking...":"Check Account Status"})]}),u&&(0,s.jsx)(n.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-6 p-4 bg-red-50 border border-red-200 rounded-xl",children:(0,s.jsx)("p",{className:"text-red-600 text-sm",children:u})}),c&&(0,s.jsx)(n.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-6 space-y-4",children:c.exists?c.hasPendingPayment?(0,s.jsxs)("div",{className:"p-4 bg-amber-50 border border-amber-200 rounded-xl",children:[(0,s.jsx)("p",{className:"text-amber-700 text-sm mb-3",children:c.message}),(0,s.jsx)(o(),{href:c.signInUrl,className:"inline-block bg-amber-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-amber-700 transition-colors",children:"Complete Payment"})]}):c.hasActiveSubscription?(0,s.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-xl",children:[(0,s.jsx)("p",{className:"text-green-700 text-sm mb-3",children:c.message}),(0,s.jsx)(o(),{href:c.signInUrl,className:"inline-block bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors",children:"Sign In"})]}):(0,s.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-xl",children:[(0,s.jsx)("p",{className:"text-blue-700 text-sm mb-3",children:c.message}),(0,s.jsx)(o(),{href:c.signInUrl,className:"inline-block bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors",children:"Sign In"})]}):(0,s.jsxs)("div",{className:"p-4 bg-gray-50 border border-gray-200 rounded-xl",children:[(0,s.jsx)("p",{className:"text-gray-700 text-sm mb-3",children:c.message}),(0,s.jsx)(o(),{href:"/auth/signup",className:"inline-block bg-[#ff6b35] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#e55a2b] transition-colors",children:"Create Account"})]})}),(0,s.jsx)("div",{className:"mt-8 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Need help? Contact us at"," ",(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-[#ff6b35] hover:underline",children:"<EMAIL>"})]})})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5449,2535,4912],()=>r(6671));module.exports=s})();