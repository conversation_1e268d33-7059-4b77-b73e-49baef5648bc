(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4150],{574:(e,a,s)=>{"use strict";s.d(a,{m:()=>r.m});var r=s(76804)},5966:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>E});var r=s(95155),t=s(12115),l=s(35695),i=s(75922),o=s(47225),n=s(32461),d=s(6865),c=s(89959),m=s(37186),u=(s(55628),s(65529)),x=s(67695),g=s(94038),h=s(61316),p=s(85037),b=s(57765),f=s(8246),y=s(31151),j=s(52589),v=s(55424),w=s(80377),N=s(87162),_=s(28003),k=s(79958),C=s(53951),A=s(99323),S=s(54547),I=s(60993);let P=i.MG.map(e=>({value:e.id,label:e.name}));function E(){var e,a;let s=(0,l.useParams)().configId,E=(0,N.Z)(),D=(0,A.bu)(),T=(null==D?void 0:D.navigateOptimistically)||(e=>{window.location.href=e}),{getCachedData:R,isCached:F,clearCache:M}=(0,_._)(),{createHoverPrefetch:K}=(0,C.c)(),[L,O]=(0,t.useState)(null),[z,U]=(0,t.useState)(!0),[G,B]=(0,t.useState)(!1),[J,q]=(0,t.useState)((null==(e=P[0])?void 0:e.value)||"openai"),[W,H]=(0,t.useState)(""),[V,Q]=(0,t.useState)(""),[X,Z]=(0,t.useState)(""),[Y,$]=(0,t.useState)(1),[ee,ea]=(0,t.useState)(!1),[es,er]=(0,t.useState)(null),[et,el]=(0,t.useState)(null),[ei,eo]=(0,t.useState)(null),[en,ed]=(0,t.useState)(!1),[ec,em]=(0,t.useState)(null),[eu,ex]=(0,t.useState)([]),[eg,eh]=(0,t.useState)(!0),[ep,eb]=(0,t.useState)(null),[ef,ey]=(0,t.useState)(null),[ej,ev]=(0,t.useState)(null),[ew,eN]=(0,t.useState)(null),[e_,ek]=(0,t.useState)(1),[eC,eA]=(0,t.useState)(""),[eS,eI]=(0,t.useState)(!1),[eP,eE]=(0,t.useState)([]),[eD,eT]=(0,t.useState)(!1),[eR,eF]=(0,t.useState)(null),[eM,eK]=(0,t.useState)(!1),[eL,eO]=(0,t.useState)(""),[ez,eU]=(0,t.useState)(""),[eG,eB]=(0,t.useState)(""),[eJ,eq]=(0,t.useState)(!1),[eW,eH]=(0,t.useState)(null),[eV,eQ]=(0,t.useState)(null),[eX,eZ]=(0,t.useState)("provider-keys"),[eY,e$]=(0,t.useState)(!1),[e0,e5]=(0,t.useState)([]),[e2,e4]=(0,t.useState)(!1),e1=(0,t.useCallback)(async()=>{if(!s)return;let e=R(s);if(e&&e.configDetails){O(e.configDetails),void 0!==e.configDetails.browsing_enabled&&e$(e.configDetails.browsing_enabled),e.configDetails.browsing_models&&e5(e.configDetails.browsing_models),U(!1);return}F(s)||B(!0),U(!0),er(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let a=await e.json();throw Error(a.error||"Failed to fetch configurations list")}let a=(await e.json()).find(e=>e.id===s);if(!a)throw Error("Configuration not found in the list.");O(a),void 0!==a.browsing_enabled&&e$(a.browsing_enabled),a.browsing_models&&e5(a.browsing_models)}catch(e){er("Error loading model configuration: ".concat(e.message)),O(null)}finally{U(!1),B(!1)}},[s,R,F]);(0,t.useEffect)(()=>{e1()},[e1]);let e3=(0,t.useCallback)(async()=>{let e=R(s);if(e&&e.models){eo(e.models),ed(!1);return}ed(!0),em(null),eo(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),a=await e.json();if(!e.ok)throw Error(a.error||"Failed to fetch models from database.");a.models?eo(a.models):eo([])}catch(e){em("Error fetching models: ".concat(e.message)),eo([])}finally{ed(!1)}},[s,R]);(0,t.useEffect)(()=>{s&&e3()},[s,e3]);let e6=(0,t.useCallback)(async()=>{let e=R(s);if(e&&e.userCustomRoles){eE(e.userCustomRoles),eT(!1);return}eT(!0),eF(null);try{let e=await fetch("/api/user/custom-roles");if(e.ok){let a=await e.json();eE(a)}else{let a;try{a=await e.json()}catch(s){a={error:await e.text().catch(()=>"HTTP error ".concat(e.status))}}let s=a.error||(a.issues?JSON.stringify(a.issues):"Failed to fetch custom roles (status: ".concat(e.status,")"));if(401===e.status)eF(s);else throw Error(s);eE([])}}catch(e){eF(e.message),eE([])}finally{eT(!1)}},[]),e8=(0,t.useCallback)(async()=>{if(!s||!eP)return;let e=R(s);if(e&&e.apiKeys&&void 0!==e.defaultChatKeyId){let a=e.apiKeys.map(async a=>{let s=await fetch("/api/keys/".concat(a.id,"/roles")),r=[];return s.ok&&(r=(await s.json()).map(e=>{let a=(0,o.Dc)(e.role_name);if(a)return a;let s=eP.find(a=>a.role_id===e.role_name);return s?{id:s.role_id,name:s.name,description:s.description||void 0}:null}).filter(Boolean)),{...a,assigned_roles:r,is_default_general_chat_model:e.defaultChatKeyId===a.id}});ex(await Promise.all(a)),ey(e.defaultChatKeyId),eh(!1);return}eh(!0),er(e=>e&&e.startsWith("Error loading model configuration:")?e:null),el(null);try{let e=await fetch("/api/keys?custom_config_id=".concat(s));if(!e.ok){let a=await e.json();throw Error(a.error||"Failed to fetch API keys")}let a=await e.json(),r=await fetch("/api/custom-configs/".concat(s,"/default-chat-key"));r.ok;let t=200===r.status?await r.json():null;ey((null==t?void 0:t.id)||null);let l=a.map(async e=>{let a=await fetch("/api/keys/".concat(e.id,"/roles")),s=[];return a.ok&&(s=(await a.json()).map(e=>{let a=(0,o.Dc)(e.role_name);if(a)return a;let s=eP.find(a=>a.role_id===e.role_name);return s?{id:s.role_id,name:s.name,description:s.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:s,is_default_general_chat_model:(null==t?void 0:t.id)===e.id}}),i=await Promise.all(l);ex(i)}catch(e){er(a=>a?"".concat(a,"; ").concat(e.message):e.message)}finally{eh(!1)}},[s,eP]);(0,t.useEffect)(()=>{L&&e6()},[L,e6]),(0,t.useEffect)(()=>{L&&eP&&e8()},[L,eP,e8]);let e9=(0,t.useMemo)(()=>{if(ei){let e=i.MG.find(e=>e.id===J);if(!e)return[];if("openrouter"===e.id)return ei.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,a)=>(e.label||"").localeCompare(a.label||""));if("deepseek"===e.id){let e=[];return ei.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),ei.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,a)=>(e.label||"").localeCompare(a.label||""))}return ei.filter(a=>a.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,a)=>(e.label||"").localeCompare(a.label||""))}return[]},[ei,J]),e7=(0,t.useMemo)(()=>{if(ei&&ew){let e=i.MG.find(e=>e.id===ew.provider);if(!e)return[];if("openrouter"===e.id)return ei.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,a)=>(e.label||"").localeCompare(a.label||""));if("deepseek"===e.id){let e=[];return ei.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),ei.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,a)=>(e.label||"").localeCompare(a.label||""))}return ei.filter(a=>a.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,a)=>(e.label||"").localeCompare(a.label||""))}return[]},[ei,ew]);(0,t.useCallback)(e=>{if(!ei)return[];let a=i.MG.find(a=>a.id===e);if(!a)return[];if("openrouter"===a.id)return ei.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,a)=>(e.label||"").localeCompare(a.label||""));if("deepseek"===a.id){let e=["deepseek-chat","deepseek-reasoner"];return ei.filter(a=>"deepseek"===a.provider_id&&e.some(e=>a.id.includes(e))).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,a)=>(e.label||"").localeCompare(a.label||""))}return ei.filter(e=>e.provider_id===a.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,a)=>(e.label||"").localeCompare(a.label||""))},[ei]),(0,t.useEffect)(()=>{e9.length>0?H(e9[0].value):H("")},[e9,J]),(0,t.useEffect)(()=>{J&&e3()},[J,e3]);let ae=async e=>{if(e.preventDefault(),!s)return void er("Configuration ID is missing.");if(eu.some(e=>e.predefined_model_id===W))return void er("This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.");ea(!0),er(null),el(null);let a=[...eu];try{var r;let e=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({custom_api_config_id:s,provider:J,predefined_model_id:W,api_key_raw:V,label:X,temperature:Y})}),a=await e.json();if(!e.ok)throw Error(a.details||a.error||"Failed to save API key");let t={id:a.id,custom_api_config_id:s,provider:J,predefined_model_id:W,label:X,temperature:Y,status:"active",created_at:new Date().toISOString(),last_used_at:null,is_default_general_chat_model:!1,assigned_roles:[]};ex(e=>[...e,t]),M(s),el('API key "'.concat(X,'" saved successfully!')),q((null==(r=P[0])?void 0:r.value)||"openai"),Q(""),Z(""),$(1),e9.length>0&&H(e9[0].value)}catch(e){ex(a),er("Save Key Error: ".concat(e.message))}finally{ea(!1)}},aa=e=>{eN(e),ek(e.temperature||1),eA(e.predefined_model_id)},as=async()=>{if(!ew)return;if(eu.some(e=>e.id!==ew.id&&e.predefined_model_id===eC))return void er("This model is already configured in this setup. Each model can only be used once per configuration.");eI(!0),er(null),el(null);let e=[...eu];ex(e=>e.map(e=>e.id===ew.id?{...e,temperature:e_,predefined_model_id:eC}:e));try{let a=await fetch("/api/keys?id=".concat(ew.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({temperature:e_,predefined_model_id:eC})}),r=await a.json();if(!a.ok)throw ex(e),Error(r.details||r.error||"Failed to update API key");M(s),el('API key "'.concat(ew.label,'" updated successfully!')),eN(null)}catch(e){er("Update Key Error: ".concat(e.message))}finally{eI(!1)}},ar=(e,a)=>{E.showConfirmation({title:"Delete API Key",message:'Are you sure you want to delete the API key "'.concat(a,'"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{eb(e),er(null),el(null);let r=[...eu],t=eu.find(a=>a.id===e);ex(a=>a.filter(a=>a.id!==e)),(null==t?void 0:t.is_default_general_chat_model)&&ey(null);try{let t=await fetch("/api/keys/".concat(e),{method:"DELETE"}),l=await t.json();if(!t.ok){if(ex(r),ey(ef),404===t.status){ex(a=>a.filter(a=>a.id!==e)),el('API key "'.concat(a,'" was already deleted.'));return}throw Error(l.details||l.error||"Failed to delete API key")}M(s),el('API key "'.concat(a,'" deleted successfully!'))}catch(e){throw er("Delete Key Error: ".concat(e.message)),e}finally{eb(null)}})},at=async e=>{if(!s)return;er(null),el(null);let a=[...eu];ex(a=>a.map(a=>({...a,is_default_general_chat_model:a.id===e}))),ey(e);try{let r=await fetch("/api/custom-configs/".concat(s,"/default-key-handler/").concat(e),{method:"PUT"}),t=await r.json();if(!r.ok)throw ex(a.map(e=>({...e}))),ey(ef),Error(t.details||t.error||"Failed to set default chat key");M(s),el(t.message||"Default general chat key updated!")}catch(e){er("Set Default Error: ".concat(e.message))}},al=async(e,a,s)=>{er(null),el(null);let r="/api/keys/".concat(e.id,"/roles"),t=[...o.p2.map(e=>({...e,isCustom:!1})),...eP.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].find(e=>e.id===a)||{id:a,name:a,description:""},l=eu.map(e=>({...e,assigned_roles:[...e.assigned_roles.map(e=>({...e}))]})),i=null;ej&&ej.id===e.id&&(i={...ej,assigned_roles:[...ej.assigned_roles.map(e=>({...e}))]}),ex(r=>r.map(r=>{if(r.id===e.id){let e=s?r.assigned_roles.filter(e=>e.id!==a):[...r.assigned_roles,t];return{...r,assigned_roles:e}}return r})),ej&&ej.id===e.id&&ev(e=>{if(!e)return null;let r=s?e.assigned_roles.filter(e=>e.id!==a):[...e.assigned_roles,t];return{...e,assigned_roles:r}});try{let o;o=s?await fetch("".concat(r,"/").concat(a),{method:"DELETE"}):await fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_name:a})});let n=await o.json();if(!o.ok){if(ex(l),i)ev(i);else if(ej&&ej.id===e.id){let a=l.find(a=>a.id===e.id);a&&ev(a)}let a=409===o.status&&n.error?n.error:n.details||n.error||(s?"Failed to unassign role":"Failed to assign role");throw Error(a)}el(n.message||"Role '".concat(t.name,"' ").concat(s?"unassigned":"assigned"," successfully."))}catch(e){er("Role Update Error: ".concat(e.message))}},ai=async()=>{if(!eL.trim()||eL.trim().length>30||!/^[a-zA-Z0-9_]+$/.test(eL.trim()))return void eH("Role ID is required (max 30 chars, letters, numbers, underscores only).");if(o.p2.some(e=>e.id.toLowerCase()===eL.trim().toLowerCase())||eP.some(e=>e.role_id.toLowerCase()===eL.trim().toLowerCase()))return void eH("This Role ID is already in use (either predefined or as one of your custom roles).");if(!ez.trim())return void eH("Role Name is required.");eH(null),eq(!0);try{let e=await fetch("/api/user/custom-roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_id:eL.trim(),name:ez.trim(),description:eG.trim()})});if(!e.ok){let a;try{a=await e.json()}catch(r){let s=await e.text().catch(()=>"HTTP status ".concat(e.status));a={error:"Server error, could not parse response.",details:s}}let s=a.error||"Failed to create custom role.";if(a.details)s+=" (Details: ".concat(a.details,")");else if(a.issues){let e=Object.entries(a.issues).map(e=>{let[a,s]=e;return"".concat(a,": ").concat(s.join(", "))}).join("; ");s+=" (Issues: ".concat(e,")")}throw Error(s)}let a=await e.json();eO(""),eU(""),eB(""),M(s);let r={id:a.id,role_id:a.role_id,name:a.name,description:a.description,user_id:a.user_id,created_at:a.created_at,updated_at:a.updated_at};eE(e=>[...e,r]),el("Custom role '".concat(a.name,"' created successfully! It is now available globally."))}catch(e){eH(e.message)}finally{eq(!1)}},ao=(e,a)=>{e&&E.showConfirmation({title:"Delete Custom Role",message:'Are you sure you want to delete the custom role "'.concat(a,"\"? This will unassign it from all API keys where it's currently used. This action cannot be undone."),confirmText:"Delete Role",cancelText:"Cancel",type:"danger"},async()=>{eQ(e),eF(null),eH(null),el(null);try{let r=await fetch("/api/user/custom-roles/".concat(e),{method:"DELETE"}),t=await r.json();if(!r.ok)throw Error(t.error||"Failed to delete custom role");eE(a=>a.filter(a=>a.id!==e)),M(s),el(t.message||'Global custom role "'.concat(a,'" deleted successfully.')),s&&e8()}catch(e){throw eF("Error deleting role: ".concat(e.message)),e}finally{eQ(null)}})};return G&&!F(s)?(0,r.jsx)(k.A,{}):z&&!L?(0,r.jsx)(k._,{}):(0,r.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:(0,r.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("button",{onClick:()=>T("/my-models"),className:"text-orange-400 hover:text-orange-300 inline-flex items-center mb-6 transition-colors duration-200 group",children:[(0,r.jsx)(n.A,{className:"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform"}),"Back to My API Models"]}),(0,r.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[(0,r.jsx)("div",{className:"flex-1",children:L?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg",children:(0,r.jsx)(m.A,{className:"h-6 w-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white",children:L.name}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Model Configuration"})]})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-300 bg-gray-800/50 px-4 py-2 rounded-lg w-fit",children:[(0,r.jsx)("span",{className:"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"}),"ID: ",L.id]})]}):es&&!z?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-900/50 rounded-2xl flex items-center justify-center mr-4",children:(0,r.jsx)(j.A,{className:"h-6 w-6 text-red-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-red-400",children:"Configuration Error"}),(0,r.jsx)("p",{className:"text-red-300 mt-1",children:es.replace("Error loading model configuration: ","")})]})]}):(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-800/50 rounded-2xl flex items-center justify-center mr-4",children:(0,r.jsx)(c.A,{className:"h-6 w-6 text-gray-400 animate-pulse"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Loading Configuration..."}),(0,r.jsx)("p",{className:"text-gray-400 mt-1",children:"Please wait while we fetch your model details"})]})]})}),L&&(0,r.jsx)("div",{className:"flex flex-col sm:flex-row gap-3",children:(0,r.jsxs)("button",{onClick:()=>T("/routing-setup/".concat(s,"?from=model-config")),className:"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group",...K(s),children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200"}),"Advanced Routing Setup"]})})]})}),et&&(0,r.jsx)("div",{className:"bg-green-900/50 backdrop-blur-sm border border-green-800/50 rounded-lg p-4 mb-6 animate-slide-in",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 text-green-400"}),(0,r.jsx)("p",{className:"text-green-300 font-medium",children:et})]})}),es&&(0,r.jsx)("div",{className:"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4 mb-6 animate-slide-in",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-red-400"}),(0,r.jsx)("p",{className:"text-red-300 font-medium",children:es})]})})]}),L&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-2",children:(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("button",{onClick:()=>eZ("provider-keys"),className:"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ".concat("provider-keys"===eX?"bg-orange-500 text-white shadow-md":"text-gray-400 hover:text-white hover:bg-gray-800/50"),children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Provider API Keys"})]})}),(0,r.jsx)("button",{onClick:()=>eZ("user-api-keys"),className:"flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ".concat("user-api-keys"===eX?"bg-orange-500 text-white shadow-md":"text-gray-400 hover:text-white hover:bg-gray-800/50"),children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Generated API Keys"})]})}),!1]})}),"provider-keys"===eX&&(0,r.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-5 gap-8",children:[(0,r.jsx)("div",{className:"xl:col-span-2",children:(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 sticky top-8",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,r.jsx)(b.A,{className:"h-5 w-5 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white",children:"Add Provider API Key"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"Configure new provider key"})]})]}),(0,r.jsxs)("form",{onSubmit:ae,className:"space-y-5",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,r.jsx)("select",{id:"provider",value:J,onChange:e=>{q(e.target.value)},className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm",children:P.map(e=>(0,r.jsx)("option",{value:e.value,className:"bg-gray-800 text-white",children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"apiKeyRaw",className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key"}),(0,r.jsx)("input",{id:"apiKeyRaw",type:"password",value:V,onChange:e=>Q(e.target.value),className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm",placeholder:"Enter your API key"}),en&&null===ei&&(0,r.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),ec&&(0,r.jsx)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:ec})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"predefinedModelId",className:"block text-sm font-medium text-gray-300 mb-2",children:"Model Variant"}),(0,r.jsx)("select",{id:"predefinedModelId",value:W,onChange:e=>H(e.target.value),disabled:!e9.length,className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm disabled:bg-gray-800/30 disabled:text-gray-500",children:e9.length>0?e9.map(e=>(0,r.jsx)("option",{value:e.value,className:"bg-gray-800 text-white",children:e.label},e.value)):(0,r.jsx)("option",{value:"",disabled:!0,className:"bg-gray-800 text-gray-400",children:null===ei&&en?"Loading models...":"Select a provider first"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-300 mb-2",children:"Label"}),(0,r.jsx)("input",{type:"text",id:"label",value:X,onChange:e=>Z(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm",placeholder:"e.g., My OpenAI GPT-4o Key #1"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature",(0,r.jsx)("span",{className:"text-xs text-gray-400 ml-1",children:"(0.0 - 2.0)"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:Y,onChange:e=>$(parseFloat(e.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:Y,onChange:e=>$(Math.min(2,Math.max(0,parseFloat(e.target.value)||0))),className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,r.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]})]}),(0,r.jsx)("button",{type:"submit",disabled:ee||!W||""===W||!V.trim()||!X.trim(),className:"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm",children:ee?(0,r.jsxs)("span",{className:"flex items-center justify-center",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2 animate-pulse"}),"Saving..."]}):(0,r.jsxs)("span",{className:"flex items-center justify-center",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Add API Key"]})})]}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-blue-900/50 backdrop-blur-sm border border-blue-800/50 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(x.A,{className:"h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-blue-300 mb-1",children:"Key Configuration Rules"}),(0,r.jsxs)("div",{className:"text-xs text-blue-200 space-y-1",children:[(0,r.jsxs)("p",{children:["✅ ",(0,r.jsx)("strong",{children:"Same API key, different models:"})," Allowed"]}),(0,r.jsxs)("p",{children:["✅ ",(0,r.jsx)("strong",{children:"Different API keys, same model:"})," Allowed"]}),(0,r.jsxs)("p",{children:["❌ ",(0,r.jsx)("strong",{children:"Same model twice:"})," Not allowed in one configuration"]})]})]})]})})]})}),(0,r.jsx)("div",{className:"xl:col-span-3",children:(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,r.jsx)(g.A,{className:"h-5 w-5 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white",children:"API Keys & Roles"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"Manage existing keys"})]})]}),eg&&(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Loading API keys..."})]}),!eg&&0===eu.length&&(!es||es&&es.startsWith("Error loading model configuration:"))&&(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)(g.A,{className:"h-6 w-6 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-1",children:"No API Keys"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Add your first key using the form"})]}),!eg&&eu.length>0&&(0,r.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:eu.map((e,a)=>(0,r.jsx)("div",{className:"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 hover:border-gray-600/50 transition-all duration-200 animate-slide-in",style:{animationDelay:"".concat(50*a,"ms")},children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-white truncate mr-2",children:e.label}),e.is_default_general_chat_model&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/30 text-green-300 border border-green-500/50 flex-shrink-0",children:[(0,r.jsx)(f.A,{className:"h-3 w-3 mr-1"}),"Default"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("p",{className:"text-xs text-gray-200 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:[e.provider," (",e.predefined_model_id,")"]}),(0,r.jsxs)("p",{className:"text-xs text-orange-300 bg-orange-900/30 px-2 py-1 rounded-lg border border-orange-500/50",children:["Temp: ",e.temperature]})]}),(0,r.jsx)(I.sU,{feature:"custom_roles",fallback:(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:(0,r.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:"Roles available on Starter plan+"})}),children:(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:e.assigned_roles.length>0?e.assigned_roles.map(e=>(0,r.jsx)("span",{className:"inline-block whitespace-nowrap rounded-full bg-orange-900/30 px-2 py-1 text-xs font-medium text-orange-300 border border-orange-500/50",children:e.name},e.id)):(0,r.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:"No roles"})})})]}),!e.is_default_general_chat_model&&(0,r.jsx)("button",{onClick:()=>at(e.id),className:"text-xs bg-gray-700/50 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white py-1 px-2 rounded-lg mt-2 transition-colors","data-tooltip-id":"global-tooltip","data-tooltip-content":"Set as default chat model",children:"Set Default"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 ml-2 flex-shrink-0",children:[(0,r.jsx)("button",{onClick:()=>aa(e),disabled:ep===e.id,className:"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Edit Model & Settings",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})}),(0,r.jsx)(I.sU,{feature:"custom_roles",fallback:(0,r.jsx)("button",{disabled:!0,className:"p-2 text-gray-400 cursor-not-allowed rounded-lg opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Role management requires Starter plan or higher",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),children:(0,r.jsx)("button",{onClick:()=>ev(e),disabled:ep===e.id,className:"p-2 text-orange-400 hover:text-orange-300 hover:bg-orange-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Manage Roles",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})})}),(0,r.jsx)("button",{onClick:()=>ar(e.id,e.label),disabled:ep===e.id,className:"p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Delete Key",children:ep===e.id?(0,r.jsx)(y.A,{className:"h-4 w-4 animate-pulse"}):(0,r.jsx)(y.A,{className:"h-4 w-4"})})]})]})},e.id))}),!eg&&es&&!es.startsWith("Error loading model configuration:")&&(0,r.jsx)("div",{className:"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-red-400"}),(0,r.jsxs)("p",{className:"text-red-300 font-medium text-sm",children:["Could not load API keys/roles: ",es]})]})})]})})]}),"user-api-keys"===eX&&(0,r.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:(0,r.jsx)(S.z,{configId:s,configName:L.name})}),"browsing-config"===eX&&!1]}),ej&&(()=>{if(!ej)return null;let e=[...o.p2.map(e=>({...e,isCustom:!1})),...eP.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].sort((e,a)=>e.name.localeCompare(a.name));return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg max-h-[90vh] flex flex-col",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-white",children:["Manage Roles for: ",(0,r.jsx)("span",{className:"text-orange-400",children:ej.label})]}),(0,r.jsx)("button",{onClick:()=>{ev(null),eK(!1),eH(null)},className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,r.jsx)(j.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[eR&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,r.jsxs)("p",{className:"text-red-800 text-sm",children:["Error with custom roles: ",eR]})}),(0,r.jsxs)(I.sU,{feature:"custom_roles",customMessage:"Custom roles are available starting with the Starter plan. Create specialized roles to organize your API keys by task type and optimize routing for different use cases.",children:[(0,r.jsx)("div",{className:"flex justify-end mb-4",children:(0,r.jsxs)("button",{onClick:()=>eK(!eM),className:"btn-primary text-sm inline-flex items-center",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),eM?"Cancel New Role":"Create New Custom Role"]})}),eM&&(0,r.jsxs)("div",{className:"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 mb-4",children:[(0,r.jsx)("h3",{className:"text-md font-medium text-white mb-3",children:"Create New Custom Role for this Configuration"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"newCustomRoleId",className:"block text-sm font-medium text-gray-300 mb-1",children:"Role ID (short, no spaces, max 30 chars)"}),(0,r.jsx)("input",{type:"text",id:"newCustomRoleId",value:eL,onChange:e=>eO(e.target.value.replace(/\s/g,"")),className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:30,placeholder:"e.g., my_blog_writer"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"newCustomRoleName",className:"block text-sm font-medium text-gray-300 mb-1",children:"Display Name (max 100 chars)"}),(0,r.jsx)("input",{type:"text",id:"newCustomRoleName",value:ez,onChange:e=>eU(e.target.value),className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:100,placeholder:"e.g., My Awesome Blog Writer"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"newCustomRoleDescription",className:"block text-sm font-medium text-gray-300 mb-1",children:"Description (optional, max 500 chars)"}),(0,r.jsx)("textarea",{id:"newCustomRoleDescription",value:eG,onChange:e=>eB(e.target.value),rows:2,className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:500,placeholder:"Optional: Describe what this role is for..."})]}),eW&&(0,r.jsx)("div",{className:"bg-red-900/50 border border-red-800/50 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-red-300 text-sm",children:eW})}),(0,r.jsx)("button",{onClick:ai,disabled:eJ,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:eJ?"Saving Role...":"Save Custom Role"})]})]})]})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-300 mb-3",children:"Select roles to assign:"}),(0,r.jsxs)("div",{className:"overflow-y-auto space-y-2",style:{maxHeight:"calc(90vh - 350px)"},children:[eD&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm ml-2",children:"Loading custom roles..."})]}),e.map(e=>{let a=ej.assigned_roles.some(a=>a.id===e.id);return(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ".concat(a?"bg-orange-500/20 border-orange-500/30 shadow-sm":"bg-gray-800/50 border-gray-700/50 hover:border-gray-600/50 hover:shadow-sm"),children:[(0,r.jsxs)("label",{htmlFor:"role-".concat(e.id),className:"flex items-center cursor-pointer flex-grow",children:[(0,r.jsx)("input",{type:"checkbox",id:"role-".concat(e.id),checked:a,onChange:()=>al(ej,e.id,a),className:"h-4 w-4 text-orange-500 border-gray-600 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer bg-gray-700"}),(0,r.jsx)("span",{className:"ml-3 text-sm font-medium ".concat(a?"text-orange-300":"text-white"),children:e.name}),e.isCustom&&(0,r.jsx)("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-500/20 text-blue-300",children:"Custom"})]}),e.isCustom&&e.databaseId&&(0,r.jsx)("button",{onClick:()=>ao(e.databaseId,e.name),disabled:eV===e.databaseId,className:"p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-900/30 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2",title:"Delete this custom role",children:eV===e.databaseId?(0,r.jsx)(m.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(y.A,{className:"h-4 w-4"})})]},e.id)})]})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg",children:(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{onClick:()=>{ev(null),eK(!1),eH(null)},className:"btn-secondary",children:"Done"})})})]})})})(),ew&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Edit API Key"}),(0,r.jsx)("button",{onClick:()=>eN(null),className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,r.jsx)(j.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:ew.label}),(0,r.jsxs)("p",{className:"text-sm text-gray-400",children:["Current: ",ew.provider," (",ew.predefined_model_id,")"]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,r.jsx)("div",{className:"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-300",children:(null==(a=i.MG.find(e=>e.id===ew.provider))?void 0:a.name)||ew.provider}),(0,r.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Provider cannot be changed"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"editModelId",className:"block text-sm font-medium text-gray-300 mb-2",children:"Model"}),(0,r.jsx)("select",{id:"editModelId",value:eC,onChange:e=>eA(e.target.value),disabled:!e7.length,className:"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-200 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-800/30",children:e7.length>0?e7.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value)):(0,r.jsx)("option",{value:"",disabled:!0,children:en?"Loading models...":"No models available"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"editTemperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature: ",e_]}),(0,r.jsx)("input",{type:"range",id:"editTemperature",min:"0",max:"2",step:"0.1",value:e_,onChange:e=>ek(parseFloat(e.target.value)),className:"slider-orange w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,r.jsx)("span",{children:"0.0 (Focused)"}),(0,r.jsx)("span",{children:"1.0 (Balanced)"}),(0,r.jsx)("span",{children:"2.0 (Creative)"})]})]}),(0,r.jsx)("div",{className:"bg-gray-800/50 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied."})})]})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:()=>eN(null),className:"btn-secondary",disabled:eS,children:"Cancel"}),(0,r.jsx)("button",{onClick:as,disabled:eS,className:"btn-primary",children:eS?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):"Save Changes"})]})})]})}),!L&&!z&&!es&&(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Model Not Found"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-8",children:"This API Model configuration could not be found or may have been deleted."}),(0,r.jsxs)("button",{onClick:()=>T("/my-models"),className:"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Return to My API Models"]})]}),(0,r.jsx)(w.A,{isOpen:E.isOpen,onClose:E.hideConfirmation,onConfirm:E.onConfirm,title:E.title,message:E.message,confirmText:E.confirmText,cancelText:E.cancelText,type:E.type,isLoading:E.isLoading}),(0,r.jsx)(v.m_,{id:"global-tooltip"})]})})}},31547:(e,a,s)=>{"use strict";s.d(a,{QR:()=>t.A,Uz:()=>o.A,X_:()=>i.A,hc:()=>r.A,kU:()=>l.A});var r=s(1243),t=s(24357),l=s(92657),i=s(78749),o=s(69803)},44469:(e,a,s)=>{Promise.resolve().then(s.bind(s,5966))},75898:(e,a,s)=>{"use strict";s.d(a,{FW:()=>t.A,Uz:()=>r.A,e9:()=>l.A});var r=s(69803),t=s(84616),l=s(53904)},76288:(e,a,s)=>{"use strict";s.d(a,{X:()=>r.A});var r=s(54416)},87266:(e,a,s)=>{"use strict";s.d(a,{Il:()=>r.A,QR:()=>l.A,TB:()=>n.A,Vv:()=>t.A,ek:()=>o.A,qz:()=>i.A});var r=s(79397),t=s(69074),l=s(24357),i=s(34869),o=s(75525),n=s(62525)}},e=>{var a=a=>e(e.s=a);e.O(0,[8888,1459,7874,5738,2396,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,3020,6642,7706,7544,2138,4518,9248,2324,7358],()=>a(44469)),_N_E=e.O()}]);