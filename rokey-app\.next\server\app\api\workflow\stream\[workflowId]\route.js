(()=>{var e={};e.id=467,e.ids=[467,1489],e.modules={1804:(e,t,r)=>{"use strict";r.d(t,{IH:()=>c,JC:()=>i});var s=r(39398);let o=new Map;function n(e){o.get(e)&&o.delete(e)}function a(e,t){let r=o.get(e);if(r)try{let e=new TextEncoder,s=`id: ${t.id}
event: ${t.type}
data: ${JSON.stringify(t)}

`;r.controller.enqueue(e.encode(s)),r.lastEventId=t.id}catch(t){o.delete(e)}}function i(e,t,r,s,n){let i={id:crypto.randomUUID(),workflowId:e,executionId:n,type:r,timestamp:new Date().toISOString(),data:s,userId:t};Array.from(o.entries()).filter(([t,r])=>r.workflowId===e).forEach(([e,t])=>{a(e,i)}),u(i).catch(e=>{})}async function u(e){try{let t=(0,s.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);await t.from("workflow_execution_logs").insert({execution_id:e.executionId||e.workflowId,workflow_id:e.workflowId,node_id:e.data?.nodeId||"system",node_type:e.data?.nodeType||"system",log_level:e.type.includes("failed")?"error":"info",message:e.data?.message||`Workflow event: ${e.type}`,data:e.data,duration_ms:e.data?.duration,created_at:e.timestamp})}catch(e){}}function c(e,t){let r=crypto.randomUUID();return new TextEncoder,new ReadableStream({start(s){let n={workflowId:e,userId:t,controller:s,connectedAt:new Date().toISOString()};o.set(r,n);let i={id:crypto.randomUUID(),workflowId:e,type:"connection_established",timestamp:new Date().toISOString(),data:{message:"\uD83D\uDD17 Connected to workflow real-time updates",workflowId:e,connectionId:r},userId:t};a(r,i)},cancel(){n(r)}})}setInterval(function(){let e=Date.now();for(let[t,r]of o.entries())e-new Date(r.connectedAt).getTime()>18e5&&n(t)},3e5)},2507:(e,t,r)=>{"use strict";r.d(t,{H:()=>u,Q:()=>i,createSupabaseServerClientOnRequest:()=>a});var s=r(34386),o=r(39398),n=r(44999);async function a(){let e=await (0,n.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function i(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}function u(){return(0,o.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13107:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>w,serverHooks:()=>m,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>I});var s={};r.r(s),r.d(s,{GET:()=>d,OPTIONS:()=>p,POST:()=>l});var o=r(96559),n=r(48088),a=r(37719),i=r(32190),u=r(2507),c=r(1804);async function d(e,{params:t}){let{workflowId:r}=await t;if(!r)return i.NextResponse.json({error:"Workflow ID is required"},{status:400});try{let e=await (0,u.createSupabaseServerClientOnRequest)(),{data:{user:t},error:s}=await e.auth.getUser();if(s||!t)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{data:o,error:n}=await e.from("manual_build_workflows").select("id, user_id, name, is_active").eq("id",r).eq("user_id",t.id).single();if(n||!o)return i.NextResponse.json({error:"Workflow not found or access denied"},{status:404});let a=(0,c.IH)(r,t.id);return setTimeout(()=>{(0,c.JC)(r,t.id,"connection_established",{message:`🔗 Connected to workflow: ${o.name}`,workflow:{id:o.id,name:o.name,isActive:o.is_active},timestamp:new Date().toISOString()})},100),new Response(a,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Headers":"Cache-Control","X-Accel-Buffering":"no"}})}catch(e){return i.NextResponse.json({error:"Internal server error"},{status:500})}}async function l(e,{params:t}){let{workflowId:r}=await t;if(!r)return i.NextResponse.json({error:"Workflow ID is required"},{status:400});try{let t=await (0,u.createSupabaseServerClientOnRequest)(),{data:{user:s},error:o}=await t.auth.getUser();if(o||!s)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{eventType:n,data:a,executionId:d}=await e.json();if(!n)return i.NextResponse.json({error:"Event type is required"},{status:400});let{data:l,error:p}=await t.from("manual_build_workflows").select("id, user_id").eq("id",r).eq("user_id",s.id).single();if(p||!l)return i.NextResponse.json({error:"Workflow not found or access denied"},{status:404});return(0,c.JC)(r,s.id,n,a,d),i.NextResponse.json({success:!0,message:"Event emitted successfully",eventType:n,workflowId:r})}catch(e){return i.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(){return new Response(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let w=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/workflow/stream/[workflowId]/route",pathname:"/api/workflow/stream/[workflowId]",filename:"route",bundlePath:"app/api/workflow/stream/[workflowId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\workflow\\stream\\[workflowId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:I,serverHooks:m}=w;function h(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:I})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(13107));module.exports=s})();