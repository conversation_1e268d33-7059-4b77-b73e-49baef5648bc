(()=>{var e={};e.id=9890,e.ids=[9890],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},22614:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>P,routeModule:()=>R,serverHooks:()=>S,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>I});var t={};s.r(t),s.d(t,{POST:()=>d});var i=s(96559),n=s(48088),o=s(37719),a=s(32190),u=s(64745),p=s(39398),c=s(94473);let E=new u.A(c.Lj.secretKey,{apiVersion:"2025-02-24.acacia"}),_=(0,p.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function d(e){try{let r,{priceId:s,userId:t,userEmail:i,tier:n,signup:o,pendingUserData:u}=await e.json();if(o){if(!s||!i||!n||!u)return a.NextResponse.json({error:"Missing required fields for signup: priceId, userEmail, tier, pendingUserData"},{status:400})}else if(!s||!t||!i||!n)return a.NextResponse.json({error:"Missing required fields: priceId, userId, userEmail, tier"},{status:400});if(!["starter","professional","enterprise"].includes(n))return a.NextResponse.json({error:"Invalid tier. Must be starter, professional, or enterprise"},{status:400});let p=function(e){switch(e){case"free":return c.Dm.FREE;case"starter":return c.Dm.STARTER;case"professional":return c.Dm.PROFESSIONAL;case"enterprise":return c.Dm.ENTERPRISE;default:throw Error(`Invalid tier: ${e}`)}}(n);if(s!==p)return a.NextResponse.json({error:"Price ID does not match selected tier"},{status:400});if(!o&&t){let{data:e}=await _.auth.admin.getUserById(t),r=e?.user?.user_metadata?.payment_status;if("pending"===r);else{let{data:e}=await _.from("subscriptions").select("*").eq("user_id",t).eq("status","active").single();if(e)return a.NextResponse.json({error:"User already has an active subscription"},{status:400})}}let d=await E.customers.list({email:i,limit:1}),R={customer:(d.data.length>0?d.data[0]:await E.customers.create({email:i,metadata:{user_id:t}})).id,payment_method_types:["card"],line_items:[{price:s,quantity:1}],mode:"subscription",success_url:`https://roukey.online/api/stripe/payment-success?session_id={CHECKOUT_SESSION_ID}&plan=${n}`,cancel_url:`https://roukey.online/pricing?plan=${n}&payment_cancelled=true`,metadata:{user_id:t||"pending_signup",tier:n,signup:o?"true":"false",pending_user_data:o?JSON.stringify(u):void 0},subscription_data:{metadata:{user_id:t||"pending_signup",tier:n,signup:o?"true":"false",pending_user_data:o?JSON.stringify(u):void 0}},allow_promotion_codes:!0,billing_address_collection:"required",customer_update:{address:"auto",name:"auto"}},l=await E.checkout.sessions.create(R);return a.NextResponse.json({sessionId:l.id,url:l.url})}catch(e){if(e instanceof u.A.errors.StripeError)return a.NextResponse.json({error:`Stripe error: ${e.message}`},{status:400});return a.NextResponse.json({error:"Internal server error"},{status:500})}}let R=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/stripe/create-checkout-session/route",pathname:"/api/stripe/create-checkout-session",filename:"route",bundlePath:"app/api/stripe/create-checkout-session/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\stripe\\create-checkout-session\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:I,serverHooks:S}=R;function P(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:I})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94473:(e,r,s)=>{"use strict";s.d(r,{Dm:()=>i,Lj:()=>t,Zu:()=>n});let t={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},i={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},n={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,t.publishableKey&&t.publishableKey.substring(0,20),t.secretKey&&t.secretKey.substring(0,20),t.webhookSecret&&t.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580,9398,4745],()=>s(22614));module.exports=t})();