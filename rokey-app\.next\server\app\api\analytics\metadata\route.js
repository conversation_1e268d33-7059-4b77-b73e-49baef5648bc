(()=>{var e={};e.id=4222,e.ids=[1489,4222],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{H:()=>u,Q:()=>n,createSupabaseServerClientOnRequest:()=>o});var s=r(34386),a=r(39398),i=r(44999);async function o(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function n(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}function u(){return(0,a.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67493:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>c});var a=r(96559),i=r(48088),o=r(37719),n=r(32190),u=r(2507);async function c(e){try{let t=(0,u.Q)(e),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),i=parseInt(a.get("days")||"30"),o=new Date;o.setDate(o.getDate()-i);let{data:c,count:l}=await t.from("custom_api_configs").select("id, name, routing_strategy, routing_strategy_params, browsing_enabled, created_at",{count:"exact"}).eq("user_id",r.id),d={};c&&c.forEach(e=>{let t=e.routing_strategy||"none";d[t]=(d[t]||0)+1});let m=Object.entries(d).map(([e,t])=>({strategy:p(e),count:t,percentage:(l||0)>0?t/(l||0)*100:0})),{data:g}=await t.from("request_logs").select("llm_model_name, llm_provider_name").eq("user_id",r.id).gte("request_timestamp",o.toISOString()).not("llm_model_name","is",null),_={},h={};g&&g.forEach(e=>{let t=e.llm_model_name,r=e.llm_provider_name;_[t]=(_[t]||0)+1,r&&(h[r]=(h[r]||0)+1)});let y=Object.entries(_).sort(([,e],[,t])=>t-e).slice(0,15).map(([e,t])=>({model:e,count:t})),f=Object.entries(h).sort(([,e],[,t])=>t-e).slice(0,10).map(([e,t])=>({provider:e,count:t})),{data:x,count:I}=await t.from("api_keys").select("provider, predefined_model_id, temperature, created_at",{count:"exact"}).eq("user_id",r.id),q={"Very Low (0-0.4)":0,"Low (0.4-0.8)":0,"Medium (0.8-1.2)":0,"High (1.2-1.6)":0,"Very High (1.6-2.0)":0};x&&x.forEach(e=>{let t=e.temperature||.7;t<.4?q["Very Low (0-0.4)"]++:t<.8?q["Low (0.4-0.8)"]++:t<1.2?q["Medium (0.8-1.2)"]++:t<1.6?q["High (1.2-1.6)"]++:q["Very High (1.6-2.0)"]++});let w=Object.entries(q).map(([e,t])=>({range:e,count:t,percentage:(I||0)>0?t/(I||0)*100:0})),{data:O,count:b}=await t.from("user_generated_api_keys").select("permissions, status, total_requests, created_at",{count:"exact"}).eq("user_id",r.id),j={};O&&O.forEach(e=>{let t=e.status||"unknown";j[t]=(j[t]||0)+1});let k=Object.entries(j).map(([e,t])=>({status:e,count:t,percentage:(b||0)>0?t/(b||0)*100:0})),{data:v}=await t.from("request_logs").select("request_payload_summary, response_payload_summary, processing_duration_ms").eq("user_id",r.id).gte("request_timestamp",o.toISOString()).limit(1e3),S=0,R=0,M=[],E=[];v&&v.forEach(e=>{if(e.processing_duration_ms&&(S+=e.processing_duration_ms,R++),e.request_payload_summary){let t=JSON.stringify(e.request_payload_summary).length;M.push(t)}if(e.response_payload_summary){let t=JSON.stringify(e.response_payload_summary).length;E.push(t)}});let C=R>0?S/R:0,z=M.length>0?M.reduce((e,t)=>e+t,0)/M.length:0,D=E.length>0?E.reduce((e,t)=>e+t,0)/E.length:0,{data:J}=await t.from("routing_quality_metrics").select("routing_strategy").eq("user_id",r.id).gte("created_at",o.toISOString()),N={};J&&J.forEach(e=>{let t=e.routing_strategy||"unknown";N[t]=(N[t]||0)+1});let L=Object.entries(N).map(([e,t])=>({strategy:p(e),count:t})).sort((e,t)=>t.count-e.count),U=[];if(c){let e=new Date;c.forEach(t=>{let r=new Date(t.created_at),s=Math.floor((e.getTime()-r.getTime())/864e5);U.push(s)})}let A=U.length>0?U.reduce((e,t)=>e+t,0)/U.length:0;return n.NextResponse.json({success:!0,data:{totalConfigs:l||0,totalApiKeys:I||0,totalUserApiKeys:b||0,averageConfigAge:A,routingStrategyBreakdown:m,roleStrategyUsage:L,topModels:y,topProviders:f,uniqueModels:Object.keys(_).length,uniqueProviders:Object.keys(h).length,temperatureDistribution:w,userApiKeyStatusBreakdown:k,averageProcessingTime:C,averageRequestSize:z,averageResponseSize:D,period:`${i} days`,startDate:o.toISOString(),endDate:new Date().toISOString()}})}catch(e){return n.NextResponse.json({error:"Failed to fetch metadata analytics",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}function p(e){return({none:"No Routing",intelligent_role:"Intelligent Role",complexity_round_robin:"Complexity Round Robin",auto_optimal:"Auto Optimal",strict_fallback:"Strict Fallback",cost_optimized:"Cost Optimized",ab_routing:"A/B Testing",unknown:"Unknown"})[e]||e}let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/analytics/metadata/route",pathname:"/api/analytics/metadata",filename:"route",bundlePath:"app/api/analytics/metadata/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\analytics\\metadata\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:m,serverHooks:g}=l;function _(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:m})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(67493));module.exports=s})();