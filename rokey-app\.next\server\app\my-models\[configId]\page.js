(()=>{var e={};e.id=4150,e.ids=[4150],e.modules={2643:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var a=r(60687),n=r(43210),s=r(9776);let i=(0,n.forwardRef)(({className:e="",variant:t="default",size:r="default",loading:n=!1,icon:i,iconPosition:o="left",children:l,disabled:d,...c},u)=>{let m={default:"h-5 w-5",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",icon:"h-5 w-5"},p=d||n;return(0,a.jsxs)("button",{ref:u,className:`inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed ${{default:"bg-orange-600 text-white hover:bg-orange-700",primary:"bg-orange-600 text-white hover:bg-orange-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500",destructive:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500",danger:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500"}[t]} ${{default:"px-4 py-2.5 text-sm",sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-6 py-3 text-base",icon:"h-10 w-10"}[r]} ${e}`,disabled:p,...c,children:[n&&(0,a.jsx)(s.Ay,{size:"lg"===r?"md":"sm",className:"mr-2"}),!n&&i&&"left"===o&&(0,a.jsx)("span",{className:`${m[r]} mr-2`,children:i}),l,!n&&i&&"right"===o&&(0,a.jsx)("span",{className:`${m[r]} ml-2`,children:i})]})});i.displayName="Button"},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8730:(e,t,r)=>{"use strict";r.d(t,{TL:()=>i});var a=r(43210),n=r(98599),s=r(60687);function i(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...s}=e;if(a.isValidElement(r)){var i;let e,o,l=(i=r,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let a in t){let n=e[a],s=t[a];/^on[A-Z]/.test(a)?n&&s?r[a]=(...e)=>{let t=s(...e);return n(...e),t}:n&&(r[a]=n):"style"===a?r[a]={...n,...s}:"className"===a&&(r[a]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==a.Fragment&&(d.ref=t?(0,n.t)(t,l):l),a.cloneElement(r,d)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:n,...i}=e,o=a.Children.toArray(n),d=o.find(l);if(d){let e=d.props.children,n=o.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,s.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var o=Symbol("radix.slottable");function l(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},11997:e=>{"use strict";e.exports=require("punycode")},14163:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>o});var a=r(43210),n=r(51215),s=r(8730),i=r(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),n=a.forwardRef((e,a)=>{let{asChild:n,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(n?r:t,{...s,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function l(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20218:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\my-models\\\\[configId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\[configId]\\page.tsx","default")},23051:(e,t,r)=>{Promise.resolve().then(r.bind(r,45100))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43985:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=function(){for(var e,t,r=0,a="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,a,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var s=t.length;for(r=0;r<s;r++)t[r]&&(a=e(t[r]))&&(n&&(n+=" "),n+=a)}else for(a in t)t[a]&&(n&&(n+=" "),n+=a);return n}(e))&&(a&&(a+=" "),a+=t);return a},s=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:o}=t,l=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==o?void 0:o[e];if(null===t)return null;let s=a(t)||a(n);return i[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return n(e,l,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...d}[t]):({...o,...d})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},45100:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>rM});var a,n,s,i=r(60687),o=r(43210),l=r.t(o,2),d=r(16189),c=r(66368),u=r(62525),m=r(81521),p=r(71031),f=r(51426),h=r(26403),g=r(44725),x=r(95753),y=r(58089),v=r(97450),b=r(70143),w=r(57891),j=r(99127),N=r(50942),k=r(71178),_=(r(59168),r(78488)),C=r(69662);let E={core:!1,base:!1};function S({css:e,id:t="react-tooltip-base-styles",type:r="base",ref:a}){var n,s;if(!e||"undefined"==typeof document||E[r]||"core"===r&&"undefined"!=typeof process&&(null==(n=null==process?void 0:process.env)?void 0:n.REACT_TOOLTIP_DISABLE_CORE_STYLES)||"base"!==r&&"undefined"!=typeof process&&(null==(s=null==process?void 0:process.env)?void 0:s.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;"core"===r&&(t="react-tooltip-core-styles"),a||(a={});let{insertAt:i}=a;if(document.getElementById(t))return;let o=document.head||document.getElementsByTagName("head")[0],l=document.createElement("style");l.id=t,l.type="text/css","top"===i&&o.firstChild?o.insertBefore(l,o.firstChild):o.appendChild(l),l.styleSheet?l.styleSheet.cssText=e:l.appendChild(document.createTextNode(e)),E[r]=!0}let A=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:r=null,place:a="top",offset:n=10,strategy:s="absolute",middlewares:i=[(0,_.cY)(Number(n)),(0,_.UU)({fallbackAxisSideDirection:"start"}),(0,_.BN)({padding:5})],border:o,arrowSize:l=8})=>e&&null!==t?r?(i.push((0,_.UE)({element:r,padding:5})),(0,_.rD)(e,t,{placement:a,strategy:s,middleware:i}).then(({x:e,y:t,placement:r,middlewareData:a})=>{var n,s;let i={left:`${e}px`,top:`${t}px`,border:o},{x:d,y:c}=null!=(n=a.arrow)?n:{x:0,y:0},u=null!=(s=({top:"bottom",right:"left",bottom:"top",left:"right"})[r.split("-")[0]])?s:"bottom",m=0;if(o){let e=`${o}`.match(/(\d+)px/);m=(null==e?void 0:e[1])?Number(e[1]):1}return{tooltipStyles:i,tooltipArrowStyles:{left:null!=d?`${d}px`:"",top:null!=c?`${c}px`:"",right:"",bottom:"",...o&&{borderBottom:o,borderRight:o},[u]:`-${l/2+m}px`},place:r}})):(0,_.rD)(e,t,{placement:"bottom",strategy:s,middleware:i}).then(({x:e,y:t,placement:r})=>({tooltipStyles:{left:`${e}px`,top:`${t}px`},tooltipArrowStyles:{},place:r})):{tooltipStyles:{},tooltipArrowStyles:{},place:a},P=(e,t)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(e,t),R=(e,t,r)=>{let a=null,n=function(...n){let s=()=>{a=null,r||e.apply(this,n)};r&&!a&&(e.apply(this,n),a=setTimeout(s,t)),r||(a&&clearTimeout(a),a=setTimeout(s,t))};return n.cancel=()=>{a&&(clearTimeout(a),a=null)},n},M=e=>null!==e&&!Array.isArray(e)&&"object"==typeof e,T=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((e,r)=>T(e,t[r]));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!M(e)||!M(t))return e===t;let r=Object.keys(e),a=Object.keys(t);return r.length===a.length&&r.every(r=>T(e[r],t[r]))},I=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;let t=getComputedStyle(e);return["overflow","overflow-x","overflow-y"].some(e=>{let r=t.getPropertyValue(e);return"auto"===r||"scroll"===r})},D=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(I(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},O="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,$=e=>{e.current&&(clearTimeout(e.current),e.current=null)},L={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},F=(0,o.createContext)({getTooltipData:()=>L});function W(e="DEFAULT_TOOLTIP_ID"){return(0,o.useContext)(F).getTooltipData(e)}var z={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},K={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};let q=({forwardRef:e,id:t,className:r,classNameArrow:a,variant:n="dark",anchorId:s,anchorSelect:i,place:l="top",offset:d=10,events:c=["hover"],openOnClick:u=!1,positionStrategy:m="absolute",middlewares:p,wrapper:f,delayShow:h=0,delayHide:g=0,float:x=!1,hidden:y=!1,noArrow:v=!1,clickable:b=!1,closeOnEsc:w=!1,closeOnScroll:j=!1,closeOnResize:N=!1,openEvents:k,closeEvents:E,globalCloseEvents:S,imperativeModeOnly:P,style:M,position:I,afterShow:L,afterHide:F,disableTooltip:q,content:B,contentWrapperRef:U,isOpen:G,defaultIsOpen:H=!1,setIsOpen:X,activeAnchor:V,setActiveAnchor:Y,border:J,opacity:Z,arrowColor:Q,arrowSize:ee=8,role:et="tooltip"})=>{var er;let ea=(0,o.useRef)(null),en=(0,o.useRef)(null),es=(0,o.useRef)(null),ei=(0,o.useRef)(null),eo=(0,o.useRef)(null),[el,ed]=(0,o.useState)({tooltipStyles:{},tooltipArrowStyles:{},place:l}),[ec,eu]=(0,o.useState)(!1),[em,ep]=(0,o.useState)(!1),[ef,eh]=(0,o.useState)(null),eg=(0,o.useRef)(!1),ex=(0,o.useRef)(null),{anchorRefs:ey,setActiveAnchor:ev}=W(t),eb=(0,o.useRef)(!1),[ew,ej]=(0,o.useState)([]),eN=(0,o.useRef)(!1),ek=u||c.includes("click"),e_=ek||(null==k?void 0:k.click)||(null==k?void 0:k.dblclick)||(null==k?void 0:k.mousedown),eC=k?{...k}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!k&&ek&&Object.assign(eC,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});let eE=E?{...E}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!E&&ek&&Object.assign(eE,{mouseleave:!1,blur:!1,mouseout:!1});let eS=S?{...S}:{escape:w||!1,scroll:j||!1,resize:N||!1,clickOutsideAnchor:e_||!1};P&&(Object.assign(eC,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(eE,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(eS,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),O(()=>(eN.current=!0,()=>{eN.current=!1}),[]);let eA=e=>{eN.current&&(e&&ep(!0),setTimeout(()=>{eN.current&&(null==X||X(e),void 0===G&&eu(e))},10))};(0,o.useEffect)(()=>{if(void 0===G)return()=>null;G&&ep(!0);let e=setTimeout(()=>{eu(G)},10);return()=>{clearTimeout(e)}},[G]),(0,o.useEffect)(()=>{ec!==eg.current&&(($(eo),eg.current=ec,ec)?null==L||L():eo.current=setTimeout(()=>{ep(!1),eh(null),null==F||F()},(e=>{let t=e.match(/^([\d.]+)(ms|s)$/);if(!t)return 0;let[,r,a]=t;return Number(r)*("ms"===a?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"))+25))},[ec]);let eP=e=>{ed(t=>T(t,e)?t:e)},eR=(e=h)=>{$(es),em?eA(!0):es.current=setTimeout(()=>{eA(!0)},e)},eM=(e=g)=>{$(ei),ei.current=setTimeout(()=>{eb.current||eA(!1)},e)},eT=e=>{var t;if(!e)return;let r=null!=(t=e.currentTarget)?t:e.target;if(!(null==r?void 0:r.isConnected))return Y(null),void ev({current:null});h?eR():eA(!0),Y(r),ev({current:r}),$(ei)},eI=()=>{b?eM(g||100):g?eM():eA(!1),$(es)},eD=({x:e,y:t})=>{var r;A({place:null!=(r=null==ef?void 0:ef.place)?r:l,offset:d,elementReference:{getBoundingClientRect:()=>({x:e,y:t,width:0,height:0,top:t,left:e,right:e,bottom:t})},tooltipReference:ea.current,tooltipArrowReference:en.current,strategy:m,middlewares:p,border:J,arrowSize:ee}).then(e=>{eP(e)})},eO=e=>{if(!e)return;let t={x:e.clientX,y:e.clientY};eD(t),ex.current=t},e$=e=>{var t;if(!ec)return;let r=e.target;r.isConnected&&(null==(t=ea.current)||!t.contains(r))&&([document.querySelector(`[id='${s}']`),...ew].some(e=>null==e?void 0:e.contains(r))||(eA(!1),$(es)))},eL=R(eT,50,!0),eF=R(eI,50,!0),eW=e=>{eF.cancel(),eL(e)},ez=()=>{eL.cancel(),eF()},eK=(0,o.useCallback)(()=>{var e,t;let r=null!=(e=null==ef?void 0:ef.position)?e:I;r?eD(r):x?ex.current&&eD(ex.current):(null==V?void 0:V.isConnected)&&A({place:null!=(t=null==ef?void 0:ef.place)?t:l,offset:d,elementReference:V,tooltipReference:ea.current,tooltipArrowReference:en.current,strategy:m,middlewares:p,border:J,arrowSize:ee}).then(e=>{eN.current&&eP(e)})},[ec,V,B,M,l,null==ef?void 0:ef.place,d,m,I,null==ef?void 0:ef.position,x,ee]);(0,o.useEffect)(()=>{var e,t;let r=new Set(ey);ew.forEach(e=>{(null==q?void 0:q(e))||r.add({current:e})});let a=document.querySelector(`[id='${s}']`);!a||(null==q?void 0:q(a))||r.add({current:a});let n=()=>{eA(!1)},i=D(V),o=D(ea.current);eS.scroll&&(window.addEventListener("scroll",n),null==i||i.addEventListener("scroll",n),null==o||o.addEventListener("scroll",n));let l=null;eS.resize?window.addEventListener("resize",n):V&&ea.current&&(l=(0,_.ll)(V,ea.current,eK,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));let d=e=>{"Escape"===e.key&&eA(!1)};eS.escape&&window.addEventListener("keydown",d),eS.clickOutsideAnchor&&window.addEventListener("click",e$);let c=[],u=e=>!!((null==e?void 0:e.target)&&(null==V?void 0:V.contains(e.target))),m=e=>{ec&&u(e)||eT(e)},p=e=>{ec&&u(e)&&eI()},f=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],h=["click","dblclick","mousedown","mouseup"];Object.entries(eC).forEach(([e,t])=>{t&&(f.includes(e)?c.push({event:e,listener:eW}):h.includes(e)&&c.push({event:e,listener:m}))}),Object.entries(eE).forEach(([e,t])=>{t&&(f.includes(e)?c.push({event:e,listener:ez}):h.includes(e)&&c.push({event:e,listener:p}))}),x&&c.push({event:"pointermove",listener:eO});let g=()=>{eb.current=!0},y=()=>{eb.current=!1,eI()},v=b&&(eE.mouseout||eE.mouseleave);return v&&(null==(e=ea.current)||e.addEventListener("mouseover",g),null==(t=ea.current)||t.addEventListener("mouseout",y)),c.forEach(({event:e,listener:t})=>{r.forEach(r=>{var a;null==(a=r.current)||a.addEventListener(e,t)})}),()=>{var e,t;eS.scroll&&(window.removeEventListener("scroll",n),null==i||i.removeEventListener("scroll",n),null==o||o.removeEventListener("scroll",n)),eS.resize?window.removeEventListener("resize",n):null==l||l(),eS.clickOutsideAnchor&&window.removeEventListener("click",e$),eS.escape&&window.removeEventListener("keydown",d),v&&(null==(e=ea.current)||e.removeEventListener("mouseover",g),null==(t=ea.current)||t.removeEventListener("mouseout",y)),c.forEach(({event:e,listener:t})=>{r.forEach(r=>{var a;null==(a=r.current)||a.removeEventListener(e,t)})})}},[V,eK,em,ey,ew,k,E,S,ek,h,g]),(0,o.useEffect)(()=>{var e,r;let a=null!=(r=null!=(e=null==ef?void 0:ef.anchorSelect)?e:i)?r:"";!a&&t&&(a=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`);let n=new MutationObserver(e=>{let r=[],n=[];e.forEach(e=>{if("attributes"===e.type&&"data-tooltip-id"===e.attributeName&&(e.target.getAttribute("data-tooltip-id")===t?r.push(e.target):e.oldValue===t&&n.push(e.target)),"childList"===e.type){if(V){let t=[...e.removedNodes].filter(e=>1===e.nodeType);if(a)try{n.push(...t.filter(e=>e.matches(a))),n.push(...t.flatMap(e=>[...e.querySelectorAll(a)]))}catch(e){}t.some(e=>{var t;return!!(null==(t=null==e?void 0:e.contains)?void 0:t.call(e,V))&&(ep(!1),eA(!1),Y(null),$(es),$(ei),!0)})}if(a)try{let t=[...e.addedNodes].filter(e=>1===e.nodeType);r.push(...t.filter(e=>e.matches(a))),r.push(...t.flatMap(e=>[...e.querySelectorAll(a)]))}catch(e){}}}),(r.length||n.length)&&ej(e=>[...e.filter(e=>!n.includes(e)),...r])});return n.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{n.disconnect()}},[t,i,null==ef?void 0:ef.anchorSelect,V]),(0,o.useEffect)(()=>{eK()},[eK]),(0,o.useEffect)(()=>{if(!(null==U?void 0:U.current))return()=>null;let e=new ResizeObserver(()=>{setTimeout(()=>eK())});return e.observe(U.current),()=>{e.disconnect()}},[B,null==U?void 0:U.current]),(0,o.useEffect)(()=>{var e;let t=document.querySelector(`[id='${s}']`),r=[...ew,t];V&&r.includes(V)||Y(null!=(e=ew[0])?e:t)},[s,ew,V]),(0,o.useEffect)(()=>(H&&eA(!0),()=>{$(es),$(ei)}),[]),(0,o.useEffect)(()=>{var e;let r=null!=(e=null==ef?void 0:ef.anchorSelect)?e:i;if(!r&&t&&(r=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`),r)try{let e=Array.from(document.querySelectorAll(r));ej(e)}catch(e){ej([])}},[t,i,null==ef?void 0:ef.anchorSelect]),(0,o.useEffect)(()=>{es.current&&($(es),eR(h))},[h]);let eq=null!=(er=null==ef?void 0:ef.content)?er:B,eB=ec&&Object.keys(el.tooltipStyles).length>0;return(0,o.useImperativeHandle)(e,()=>({open:e=>{if(null==e?void 0:e.anchorSelect)try{document.querySelector(e.anchorSelect)}catch(t){return void console.warn(`[react-tooltip] "${e.anchorSelect}" is not a valid CSS selector`)}eh(null!=e?e:null),(null==e?void 0:e.delay)?eR(e.delay):eA(!0)},close:e=>{(null==e?void 0:e.delay)?eM(e.delay):eA(!1)},activeAnchor:V,place:el.place,isOpen:!!(em&&!y&&eq&&eB)})),em&&!y&&eq?o.createElement(f,{id:t,role:et,className:C("react-tooltip",z.tooltip,K.tooltip,K[n],r,`react-tooltip__place-${el.place}`,z[eB?"show":"closing"],eB?"react-tooltip__show":"react-tooltip__closing","fixed"===m&&z.fixed,b&&z.clickable),onTransitionEnd:e=>{$(eo),ec||"opacity"!==e.propertyName||(ep(!1),eh(null),null==F||F())},style:{...M,...el.tooltipStyles,opacity:void 0!==Z&&eB?Z:void 0},ref:ea},eq,o.createElement(f,{className:C("react-tooltip-arrow",z.arrow,K.arrow,a,v&&z.noArrow),style:{...el.tooltipArrowStyles,background:Q?`linear-gradient(to right bottom, transparent 50%, ${Q} 50%)`:void 0,"--rt-arrow-size":`${ee}px`},ref:en})):null},B=({content:e})=>o.createElement("span",{dangerouslySetInnerHTML:{__html:e}}),U=o.forwardRef(({id:e,anchorId:t,anchorSelect:r,content:a,html:n,render:s,className:i,classNameArrow:l,variant:d="dark",place:c="top",offset:u=10,wrapper:m="div",children:p=null,events:f=["hover"],openOnClick:h=!1,positionStrategy:g="absolute",middlewares:x,delayShow:y=0,delayHide:v=0,float:b=!1,hidden:w=!1,noArrow:j=!1,clickable:N=!1,closeOnEsc:k=!1,closeOnScroll:_=!1,closeOnResize:E=!1,openEvents:S,closeEvents:A,globalCloseEvents:R,imperativeModeOnly:M=!1,style:T,position:I,isOpen:D,defaultIsOpen:O=!1,disableStyleInjection:$=!1,border:L,opacity:F,arrowColor:z,arrowSize:K,setIsOpen:U,afterShow:G,afterHide:H,disableTooltip:X,role:V="tooltip"},Y)=>{let[J,Z]=(0,o.useState)(a),[Q,ee]=(0,o.useState)(n),[et,er]=(0,o.useState)(c),[ea,en]=(0,o.useState)(d),[es,ei]=(0,o.useState)(u),[eo,el]=(0,o.useState)(y),[ed,ec]=(0,o.useState)(v),[eu,em]=(0,o.useState)(b),[ep,ef]=(0,o.useState)(w),[eh,eg]=(0,o.useState)(m),[ex,ey]=(0,o.useState)(f),[ev,eb]=(0,o.useState)(g),[ew,ej]=(0,o.useState)(null),[eN,ek]=(0,o.useState)(null),e_=(0,o.useRef)($),{anchorRefs:eC,activeAnchor:eE}=W(e),eS=e=>null==e?void 0:e.getAttributeNames().reduce((t,r)=>{var a;return r.startsWith("data-tooltip-")&&(t[r.replace(/^data-tooltip-/,"")]=null!=(a=null==e?void 0:e.getAttribute(r))?a:null),t},{}),eA=e=>{let t={place:e=>{er(null!=e?e:c)},content:e=>{Z(null!=e?e:a)},html:e=>{ee(null!=e?e:n)},variant:e=>{en(null!=e?e:d)},offset:e=>{ei(null===e?u:Number(e))},wrapper:e=>{eg(null!=e?e:m)},events:e=>{let t=null==e?void 0:e.split(" ");ey(null!=t?t:f)},"position-strategy":e=>{eb(null!=e?e:g)},"delay-show":e=>{el(null===e?y:Number(e))},"delay-hide":e=>{ec(null===e?v:Number(e))},float:e=>{em(null===e?b:"true"===e)},hidden:e=>{ef(null===e?w:"true"===e)},"class-name":e=>{ej(e)}};Object.values(t).forEach(e=>e(null)),Object.entries(e).forEach(([e,r])=>{var a;null==(a=t[e])||a.call(t,r)})};(0,o.useEffect)(()=>{Z(a)},[a]),(0,o.useEffect)(()=>{ee(n)},[n]),(0,o.useEffect)(()=>{er(c)},[c]),(0,o.useEffect)(()=>{en(d)},[d]),(0,o.useEffect)(()=>{ei(u)},[u]),(0,o.useEffect)(()=>{el(y)},[y]),(0,o.useEffect)(()=>{ec(v)},[v]),(0,o.useEffect)(()=>{em(b)},[b]),(0,o.useEffect)(()=>{ef(w)},[w]),(0,o.useEffect)(()=>{eb(g)},[g]),(0,o.useEffect)(()=>{e_.current!==$&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[$]),(0,o.useEffect)(()=>{"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:"core"===$,disableBase:$}}))},[]),(0,o.useEffect)(()=>{var a;let n=new Set(eC),s=r;if(!s&&e&&(s=`[data-tooltip-id='${e.replace(/'/g,"\\'")}']`),s)try{document.querySelectorAll(s).forEach(e=>{n.add({current:e})})}catch(e){console.warn(`[react-tooltip] "${s}" is not a valid CSS selector`)}let i=document.querySelector(`[id='${t}']`);if(i&&n.add({current:i}),!n.size)return()=>null;let o=null!=(a=null!=eN?eN:i)?a:eE.current,l=new MutationObserver(e=>{e.forEach(e=>{var t;o&&"attributes"===e.type&&(null==(t=e.attributeName)?void 0:t.startsWith("data-tooltip-"))&&eA(eS(o))})});return o&&(eA(eS(o)),l.observe(o,{attributes:!0,childList:!1,subtree:!1})),()=>{l.disconnect()}},[eC,eE,eN,t,r]),(0,o.useEffect)(()=>{(null==T?void 0:T.border)&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),L&&!P("border",`${L}`)&&console.warn(`[react-tooltip] "${L}" is not a valid \`border\`.`),(null==T?void 0:T.opacity)&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),F&&!P("opacity",`${F}`)&&console.warn(`[react-tooltip] "${F}" is not a valid \`opacity\`.`)},[]);let eP=p,eR=(0,o.useRef)(null);if(s){let e=s({content:(null==eN?void 0:eN.getAttribute("data-tooltip-content"))||J||null,activeAnchor:eN});eP=e?o.createElement("div",{ref:eR,className:"react-tooltip-content-wrapper"},e):null}else J&&(eP=J);Q&&(eP=o.createElement(B,{content:Q}));let eM={forwardRef:Y,id:e,anchorId:t,anchorSelect:r,className:C(i,ew),classNameArrow:l,content:eP,contentWrapperRef:eR,place:et,variant:ea,offset:es,wrapper:eh,events:ex,openOnClick:h,positionStrategy:ev,middlewares:x,delayShow:eo,delayHide:ed,float:eu,hidden:ep,noArrow:j,clickable:N,closeOnEsc:k,closeOnScroll:_,closeOnResize:E,openEvents:S,closeEvents:A,globalCloseEvents:R,imperativeModeOnly:M,style:T,position:I,isOpen:D,defaultIsOpen:O,border:L,opacity:F,arrowColor:z,arrowSize:K,setIsOpen:U,afterShow:G,afterHide:H,disableTooltip:X,activeAnchor:eN,setActiveAnchor:e=>ek(e),role:V};return o.createElement(q,{...eM})});"undefined"!=typeof window&&window.addEventListener("react-tooltip-inject-styles",e=>{e.detail.disableCore||S({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s;--rt-arrow-size:8px}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit;z-index:-1}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),e.detail.disableBase||S({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:var(--rt-arrow-size);height:var(--rt-arrow-size)}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});var G=r(50181),H=r(20404),X=r(5097);function V(){return(0,i.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,i.jsx)("div",{className:"flex items-center justify-between",children:(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gray-700 rounded animate-pulse"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-48 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-64 animate-pulse"})]})]})}),(0,i.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,i.jsx)("div",{className:"h-6 bg-gray-700 rounded w-32 mb-6 animate-pulse"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-20 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-10 bg-gray-700 rounded animate-pulse"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-16 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-10 bg-gray-700 rounded animate-pulse"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-20 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-10 bg-gray-700 rounded animate-pulse"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-12 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-10 bg-gray-700 rounded animate-pulse"})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-24 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-6 bg-gray-700 rounded animate-pulse"})]})]}),(0,i.jsx)("div",{className:"mt-6",children:(0,i.jsx)("div",{className:"h-10 bg-gray-700 rounded w-32 animate-pulse"})})]}),(0,i.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsx)("div",{className:"h-6 bg-gray-700 rounded w-40 animate-pulse"}),(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-24 animate-pulse"})]}),(0,i.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,i.jsxs)("div",{className:"border border-gray-700/50 rounded-lg p-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-gray-700 rounded-lg animate-pulse"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-5 bg-gray-700 rounded w-32 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-48 animate-pulse"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-20 animate-pulse"}),(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-16 animate-pulse"}),(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-16 animate-pulse"})]})]}),(0,i.jsxs)("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-3 bg-gray-700 rounded w-16 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-20 animate-pulse"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-3 bg-gray-700 rounded w-12 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-16 animate-pulse"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-3 bg-gray-700 rounded w-20 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-24 animate-pulse"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-3 bg-gray-700 rounded w-14 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-18 animate-pulse"})]})]}),(0,i.jsxs)("div",{className:"mt-4",children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-28 mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:[1,2,3].map(e=>(0,i.jsx)("div",{className:"h-6 bg-gray-700 rounded-full w-20 animate-pulse"},e))})]})]},e))})]}),(0,i.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsx)("div",{className:"h-6 bg-gray-700 rounded w-36 animate-pulse"}),(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-28 animate-pulse"})]}),(0,i.jsx)("div",{className:"space-y-3",children:[1,2].map(e=>(0,i.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-700/50 rounded-lg",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-32 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-3 bg-gray-700 rounded w-48 animate-pulse"})]}),(0,i.jsx)("div",{className:"h-8 bg-gray-700 rounded w-16 animate-pulse"})]},e))})]})]})}function Y(){return(0,i.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gray-700 rounded animate-pulse"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"h-6 bg-gray-700 rounded w-40 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-56 animate-pulse"})]})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[1,2,3,4].map(e=>(0,i.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-4",children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gray-700 rounded animate-pulse"}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("div",{className:"h-4 bg-gray-700 rounded w-24 mb-1 animate-pulse"}),(0,i.jsx)("div",{className:"h-3 bg-gray-700 rounded w-32 animate-pulse"})]})]})},e))})]})}var J=r(60925),Z=r(36721),Q=r(2643),ee=r(62688);let et=(0,ee.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),er=(0,ee.A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]),ea=(0,ee.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var en=r(52581),es=r(43985);let ei=(0,es.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-orange-600 text-white hover:bg-orange-700",secondary:"border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700",outline:"text-gray-700 border-gray-300"}},defaultVariants:{variant:"default"}});function eo({className:e,variant:t,...r}){return(0,i.jsx)("div",{className:`${ei({variant:t})} ${e||""}`,...r})}let el=(0,ee.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),ed=(0,ee.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),ec=(0,ee.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),eu=(0,ee.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),em=(0,ee.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),ep=(0,ee.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),ef=Symbol.for("constructDateFrom");function eh(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&ef in e?e[ef](t):e instanceof Date?new e.constructor(t):new Date(t)}let eg={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function ex(e){return (t={})=>{let r=t.width?String(t.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}let ey={date:ex({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:ex({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:ex({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},ev={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function eb(e){return(t,r)=>{let a;if("formatting"===(r?.context?String(r.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,n=r?.width?String(r.width):t;a=e.formattingValues[n]||e.formattingValues[t]}else{let t=e.defaultWidth,n=r?.width?String(r.width):e.defaultWidth;a=e.values[n]||e.values[t]}return a[e.argumentCallback?e.argumentCallback(t):t]}}function ew(e){return(t,r={})=>{let a,n=r.width,s=n&&e.matchPatterns[n]||e.matchPatterns[e.defaultMatchWidth],i=t.match(s);if(!i)return null;let o=i[0],l=n&&e.parsePatterns[n]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(l)?function(e,t){for(let r=0;r<e.length;r++)if(t(e[r]))return r}(l,e=>e.test(o)):function(e,t){for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return r}(l,e=>e.test(o));return a=e.valueCallback?e.valueCallback(d):d,{value:a=r.valueCallback?r.valueCallback(a):a,rest:t.slice(o.length)}}}let ej={code:"en-US",formatDistance:(e,t,r)=>{let a,n=eg[e];if(a="string"==typeof n?n:1===t?n.one:n.other.replace("{{count}}",t.toString()),r?.addSuffix)if(r.comparison&&r.comparison>0)return"in "+a;else return a+" ago";return a},formatLong:ey,formatRelative:(e,t,r,a)=>ev[e],localize:{ordinalNumber:(e,t)=>{let r=Number(e),a=r%100;if(a>20||a<10)switch(a%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},era:eb({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:eb({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:eb({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:eb({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:eb({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(t,r={})=>{let a=t.match(e.matchPattern);if(!a)return null;let n=a[0],s=t.match(e.parsePattern);if(!s)return null;let i=e.valueCallback?e.valueCallback(s[0]):s[0];return{value:i=r.valueCallback?r.valueCallback(i):i,rest:t.slice(n.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:ew({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:ew({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:ew({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:ew({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:ew({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},eN={};function ek(e,t){return eh(t||e,e)}function e_(e){let t=ek(e),r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),e-r}function eC(e,...t){let r=eh.bind(null,e||t.find(e=>"object"==typeof e));return t.map(r)}function eE(e,t){let r=ek(e)-ek(t);return r<0?-1:r>0?1:r}function eS(e,t){return function(e,t,r){let a,n=r?.locale??eN.locale??ej,s=eE(e,t);if(isNaN(s))throw RangeError("Invalid time value");let i=Object.assign({},r,{addSuffix:r?.addSuffix,comparison:s}),[o,l]=eC(r?.in,...s>0?[t,e]:[e,t]),d=function(e,t,r){var a;return(a=void 0,e=>{let t=(a?Math[a]:Math.trunc)(e);return 0===t?0:t})((ek(e)-ek(t))/1e3)}(l,o),c=Math.round((d-(e_(l)-e_(o))/1e3)/60);if(c<2)if(r?.includeSeconds)if(d<5)return n.formatDistance("lessThanXSeconds",5,i);else if(d<10)return n.formatDistance("lessThanXSeconds",10,i);else if(d<20)return n.formatDistance("lessThanXSeconds",20,i);else if(d<40)return n.formatDistance("halfAMinute",0,i);else if(d<60)return n.formatDistance("lessThanXMinutes",1,i);else return n.formatDistance("xMinutes",1,i);else if(0===c)return n.formatDistance("lessThanXMinutes",1,i);else return n.formatDistance("xMinutes",c,i);if(c<45)return n.formatDistance("xMinutes",c,i);if(c<90)return n.formatDistance("aboutXHours",1,i);if(c<1440){let e=Math.round(c/60);return n.formatDistance("aboutXHours",e,i)}if(c<2520)return n.formatDistance("xDays",1,i);else if(c<43200){let e=Math.round(c/1440);return n.formatDistance("xDays",e,i)}else if(c<86400)return a=Math.round(c/43200),n.formatDistance("aboutXMonths",a,i);if((a=function(e,t,r){let[a,n,s]=eC(void 0,e,e,t),i=eE(n,s),o=Math.abs(function(e,t,r){let[a,n]=eC(void 0,e,t);return 12*(a.getFullYear()-n.getFullYear())+(a.getMonth()-n.getMonth())}(n,s));if(o<1)return 0;1===n.getMonth()&&n.getDate()>27&&n.setDate(30),n.setMonth(n.getMonth()-i*o);let l=eE(n,s)===-i;(function(e,t){let r=ek(e,void 0);return+function(e,t){let r=ek(e,t?.in);return r.setHours(23,59,59,999),r}(r,void 0)==+function(e,t){let r=ek(e,t?.in),a=r.getMonth();return r.setFullYear(r.getFullYear(),a+1,0),r.setHours(23,59,59,999),r}(r,t)})(a)&&1===o&&1===eE(a,s)&&(l=!1);let d=i*(o-l);return 0===d?0:d}(l,o))<12){let e=Math.round(c/43200);return n.formatDistance("xMonths",e,i)}{let e=a%12,t=Math.trunc(a/12);return e<3?n.formatDistance("aboutXYears",t,i):e<9?n.formatDistance("overXYears",t,i):n.formatDistance("almostXYears",t+1,i)}}(e,eh(e,Date.now()),t)}function eA({apiKey:e,onRevoke:t}){let r=async e=>{try{await navigator.clipboard.writeText(e),en.oR.success("API key copied to clipboard")}catch(e){en.oR.error("Failed to copy API key")}},a=e.expires_at&&new Date(e.expires_at)<new Date,n="active"===e.status&&!a;return(0,i.jsxs)("div",{className:`bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 transition-all duration-200 hover:border-gray-700/50 ${!n?"opacity-75":""}`,children:[(0,i.jsx)("div",{className:"pb-3",children:(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-white",children:e.key_name}),(0,i.jsxs)("p",{className:"text-sm text-gray-400",children:["Configuration: ",e.custom_api_configs.name]})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(eo,{className:(e=>{switch(e){case"active":return"bg-green-900/30 text-green-300 border-green-500/50";case"inactive":return"bg-yellow-900/30 text-yellow-300 border-yellow-500/50";case"revoked":return"bg-red-900/30 text-red-300 border-red-500/50";default:return"bg-gray-800/50 text-gray-300 border-gray-600/50"}})(e.status),children:e.status}),a&&(0,i.jsx)(eo,{className:"bg-red-900/30 text-red-300 border-red-500/50",children:"Expired"})]})]})}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"API Key (Masked)"}),(0,i.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-800/50 rounded-lg border border-gray-700",children:[(0,i.jsxs)("code",{className:"flex-1 text-sm font-mono text-gray-300",children:[e.key_prefix,"_","*".repeat(28),"string"==typeof e.masked_key?e.masked_key.slice(-4):"xxxx"]}),(0,i.jsx)(Q.$,{variant:"ghost",size:"sm",onClick:()=>r(`${e.key_prefix}_${"*".repeat(28)}${"string"==typeof e.masked_key?e.masked_key.slice(-4):"xxxx"}`),className:"h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-700/50",title:"Copy masked key (for reference only)",children:(0,i.jsx)(el,{className:"h-4 w-4"})})]}),(0,i.jsxs)("div",{className:"flex items-center gap-2 text-xs text-amber-400 bg-amber-900/20 p-2 rounded",children:[(0,i.jsx)("span",{children:"⚠️"}),(0,i.jsx)("span",{children:"Full API key was only shown once during creation for security. Save it securely when creating new keys."})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Permissions"}),(0,i.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.permissions.chat&&(0,i.jsx)(eo,{variant:"secondary",className:"text-xs bg-blue-900/30 text-blue-300 border-blue-500/50",children:"Chat Completions"}),e.permissions.streaming&&(0,i.jsx)(eo,{variant:"secondary",className:"text-xs bg-green-900/30 text-green-300 border-green-500/50",children:"Streaming"}),e.permissions.all_models&&(0,i.jsx)(eo,{variant:"secondary",className:"text-xs bg-purple-900/30 text-purple-300 border-purple-500/50",children:"All Models"})]})]}),(e.allowed_ips.length>0||e.allowed_domains.length>0)&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"text-sm font-medium text-gray-300 flex items-center gap-1",children:[(0,i.jsx)(ed,{className:"h-4 w-4"}),"Security Restrictions"]}),(0,i.jsxs)("div",{className:"space-y-1 text-xs",children:[e.allowed_ips.length>0&&(0,i.jsxs)("div",{className:"flex items-center gap-1 text-gray-400",children:[(0,i.jsx)("span",{className:"font-medium",children:"IPs:"}),(0,i.jsx)("span",{children:e.allowed_ips.join(", ")})]}),e.allowed_domains.length>0&&(0,i.jsxs)("div",{className:"flex items-center gap-1 text-gray-400",children:[(0,i.jsx)(ec,{className:"h-3 w-3"}),(0,i.jsx)("span",{className:"font-medium",children:"Domains:"}),(0,i.jsx)("span",{children:e.allowed_domains.join(", ")})]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"text-sm font-medium text-gray-300 flex items-center gap-1",children:[(0,i.jsx)(eu,{className:"h-4 w-4"}),"Usage Statistics"]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-400",children:"Total Requests:"}),(0,i.jsx)("span",{className:"ml-2 font-semibold text-white",children:e.total_requests.toLocaleString()})]}),e.last_used_at&&(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"text-gray-400",children:"Last Used:"}),(0,i.jsx)("span",{className:"ml-2 font-semibold text-white",children:eS(new Date(e.last_used_at),{addSuffix:!0})})]})]})]}),e.expires_at&&(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"text-sm font-medium text-gray-300 flex items-center gap-1",children:[(0,i.jsx)(em,{className:"h-4 w-4"}),"Expiration"]}),(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsxs)("span",{className:`font-semibold ${a?"text-red-400":"text-white"}`,children:[new Date(e.expires_at).toLocaleDateString()," at"," ",new Date(e.expires_at).toLocaleTimeString()]}),!a&&(0,i.jsxs)("span",{className:"ml-2 text-gray-400",children:["(",eS(new Date(e.expires_at),{addSuffix:!0}),")"]})]})]}),(0,i.jsx)("div",{className:"flex items-center justify-end pt-2 border-t border-gray-700",children:"revoked"!==e.status&&(0,i.jsxs)(Q.$,{variant:"destructive",size:"sm",onClick:()=>t(e.id),className:"text-xs",children:[(0,i.jsx)(ep,{className:"h-3 w-3 mr-1"}),"Revoke"]})})]})]})}function eP(e,t,{checkForDefaultPrevented:r=!0}={}){return function(a){if(e?.(a),!1===r||!a.defaultPrevented)return t?.(a)}}var eR=r(98599),eM=globalThis?.document?o.useLayoutEffect:()=>{},eT=l[" useId ".trim().toString()]||(()=>void 0),eI=0;function eD(e){let[t,r]=o.useState(eT());return eM(()=>{e||r(e=>e??String(eI++))},[e]),e||(t?`radix-${t}`:"")}var eO=l[" useInsertionEffect ".trim().toString()]||eM,e$=(Symbol("RADIX:SYNC_STATE"),r(14163));function eL(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var eF="dismissableLayer.update",eW=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ez=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:l,onInteractOutside:d,onDismiss:c,...u}=e,m=o.useContext(eW),[p,f]=o.useState(null),h=p?.ownerDocument??globalThis?.document,[,g]=o.useState({}),x=(0,eR.s)(t,e=>f(e)),y=Array.from(m.layers),[v]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),b=y.indexOf(v),w=p?y.indexOf(p):-1,j=m.layersWithOutsidePointerEventsDisabled.size>0,N=w>=b,k=function(e,t=globalThis?.document){let r=eL(e),a=o.useRef(!1),n=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let a=function(){eq("dismissableLayer.pointerDownOutside",r,s,{discrete:!0})},s={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",n.current),n.current=a,t.addEventListener("click",n.current,{once:!0})):a()}else t.removeEventListener("click",n.current);a.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",e),t.removeEventListener("click",n.current)}},[t,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,r=[...m.branches].some(e=>e.contains(t));N&&!r&&(s?.(e),d?.(e),e.defaultPrevented||c?.())},h),_=function(e,t=globalThis?.document){let r=eL(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&eq("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;![...m.branches].some(e=>e.contains(t))&&(l?.(e),d?.(e),e.defaultPrevented||c?.())},h);return!function(e,t=globalThis?.document){let r=eL(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{w===m.layers.size-1&&(a?.(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},h),o.useEffect(()=>{if(p)return r&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(n=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(p)),m.layers.add(p),eK(),()=>{r&&1===m.layersWithOutsidePointerEventsDisabled.size&&(h.body.style.pointerEvents=n)}},[p,h,r,m]),o.useEffect(()=>()=>{p&&(m.layers.delete(p),m.layersWithOutsidePointerEventsDisabled.delete(p),eK())},[p,m]),o.useEffect(()=>{let e=()=>g({});return document.addEventListener(eF,e),()=>document.removeEventListener(eF,e)},[]),(0,i.jsx)(e$.sG.div,{...u,ref:x,style:{pointerEvents:j?N?"auto":"none":void 0,...e.style},onFocusCapture:eP(e.onFocusCapture,_.onFocusCapture),onBlurCapture:eP(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:eP(e.onPointerDownCapture,k.onPointerDownCapture)})});function eK(){let e=new CustomEvent(eF);document.dispatchEvent(e)}function eq(e,t,r,{discrete:a}){let n=r.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&n.addEventListener(e,t,{once:!0}),a?(0,e$.hO)(n,s):n.dispatchEvent(s)}ez.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(eW),a=o.useRef(null),n=(0,eR.s)(t,a);return o.useEffect(()=>{let e=a.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,i.jsx)(e$.sG.div,{...e,ref:n})}).displayName="DismissableLayerBranch";var eB="focusScope.autoFocusOnMount",eU="focusScope.autoFocusOnUnmount",eG={bubbles:!1,cancelable:!0},eH=o.forwardRef((e,t)=>{let{loop:r=!1,trapped:a=!1,onMountAutoFocus:n,onUnmountAutoFocus:s,...l}=e,[d,c]=o.useState(null),u=eL(n),m=eL(s),p=o.useRef(null),f=(0,eR.s)(t,e=>c(e)),h=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(a){let e=function(e){if(h.paused||!d)return;let t=e.target;d.contains(t)?p.current=t:eY(p.current,{select:!0})},t=function(e){if(h.paused||!d)return;let t=e.relatedTarget;null!==t&&(d.contains(t)||eY(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&eY(d)});return d&&r.observe(d,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[a,d,h.paused]),o.useEffect(()=>{if(d){eJ.add(h);let e=document.activeElement;if(!d.contains(e)){let t=new CustomEvent(eB,eG);d.addEventListener(eB,u),d.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let a of e)if(eY(a,{select:t}),document.activeElement!==r)return}(eX(d).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&eY(d))}return()=>{d.removeEventListener(eB,u),setTimeout(()=>{let t=new CustomEvent(eU,eG);d.addEventListener(eU,m),d.dispatchEvent(t),t.defaultPrevented||eY(e??document.body,{select:!0}),d.removeEventListener(eU,m),eJ.remove(h)},0)}}},[d,u,m,h]);let g=o.useCallback(e=>{if(!r&&!a||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[a,s]=function(e){let t=eX(e);return[eV(t,e),eV(t.reverse(),e)]}(t);a&&s?e.shiftKey||n!==s?e.shiftKey&&n===a&&(e.preventDefault(),r&&eY(s,{select:!0})):(e.preventDefault(),r&&eY(a,{select:!0})):n===t&&e.preventDefault()}},[r,a,h.paused]);return(0,i.jsx)(e$.sG.div,{tabIndex:-1,...l,ref:f,onKeyDown:g})});function eX(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function eV(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function eY(e,{select:t=!1}={}){if(e&&e.focus){var r;let a=document.activeElement;e.focus({preventScroll:!0}),e!==a&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}eH.displayName="FocusScope";var eJ=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=eZ(e,t)).unshift(t)},remove(t){e=eZ(e,t),e[0]?.resume()}}}();function eZ(e,t){let r=[...e],a=r.indexOf(t);return -1!==a&&r.splice(a,1),r}var eQ=r(51215),e0=o.forwardRef((e,t)=>{let{container:r,...a}=e,[n,s]=o.useState(!1);eM(()=>s(!0),[]);let l=r||n&&globalThis?.document?.body;return l?eQ.createPortal((0,i.jsx)(e$.sG.div,{...a,ref:t}),l):null});e0.displayName="Portal";var e1=e=>{let{present:t,children:r}=e,a=function(e){var t,r;let[a,n]=o.useState(),s=o.useRef(null),i=o.useRef(e),l=o.useRef("none"),[d,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>r[e][t]??e,t));return o.useEffect(()=>{let e=e2(s.current);l.current="mounted"===d?e:"none"},[d]),eM(()=>{let t=s.current,r=i.current;if(r!==e){let a=l.current,n=e2(t);e?c("MOUNT"):"none"===n||t?.display==="none"?c("UNMOUNT"):r&&a!==n?c("ANIMATION_OUT"):c("UNMOUNT"),i.current=e}},[e,c]),eM(()=>{if(a){let e,t=a.ownerDocument.defaultView??window,r=r=>{let n=e2(s.current).includes(r.animationName);if(r.target===a&&n&&(c("ANIMATION_END"),!i.current)){let r=a.style.animationFillMode;a.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=r)})}},n=e=>{e.target===a&&(l.current=e2(s.current))};return a.addEventListener("animationstart",n),a.addEventListener("animationcancel",r),a.addEventListener("animationend",r),()=>{t.clearTimeout(e),a.removeEventListener("animationstart",n),a.removeEventListener("animationcancel",r),a.removeEventListener("animationend",r)}}c("ANIMATION_END")},[a,c]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:o.useCallback(e=>{s.current=e?getComputedStyle(e):null,n(e)},[])}}(t),n="function"==typeof r?r({present:a.isPresent}):o.Children.only(r),s=(0,eR.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n));return"function"==typeof r||a.isPresent?o.cloneElement(n,{ref:s}):null};function e2(e){return e?.animationName||"none"}e1.displayName="Presence";var e4=0;function e5(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var e3=function(){return(e3=Object.assign||function(e){for(var t,r=1,a=arguments.length;r<a;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)};function e6(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)0>t.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r}Object.create;Object.create;var e8=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),e9="width-before-scroll-bar";function e7(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var te="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,tt=new WeakMap;function tr(e){return e}var ta=function(e){void 0===e&&(e={});var t,r,a,n,s=(t=null,void 0===r&&(r=tr),a=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:null},useMedium:function(e){var t=r(e,n);return a.push(t),function(){a=a.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(n=!0;a.length;){var t=a;a=[],t.forEach(e)}a={push:function(t){return e(t)},filter:function(){return a}}},assignMedium:function(e){n=!0;var t=[];if(a.length){var r=a;a=[],r.forEach(e),t=a}var s=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(s)};i(),a={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),a}}}});return s.options=e3({async:!0,ssr:!1},e),s}(),tn=function(){},ts=o.forwardRef(function(e,t){var r,a,n,s,i=o.useRef(null),l=o.useState({onScrollCapture:tn,onWheelCapture:tn,onTouchMoveCapture:tn}),d=l[0],c=l[1],u=e.forwardProps,m=e.children,p=e.className,f=e.removeScrollBar,h=e.enabled,g=e.shards,x=e.sideCar,y=e.noRelative,v=e.noIsolation,b=e.inert,w=e.allowPinchZoom,j=e.as,N=e.gapMode,k=e6(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),_=(r=[i,t],a=function(e){return r.forEach(function(t){return e7(t,e)})},(n=(0,o.useState)(function(){return{value:null,callback:a,facade:{get current(){return n.value},set current(value){var e=n.value;e!==value&&(n.value=value,n.callback(value,e))}}}})[0]).callback=a,s=n.facade,te(function(){var e=tt.get(s);if(e){var t=new Set(e),a=new Set(r),n=s.current;t.forEach(function(e){a.has(e)||e7(e,null)}),a.forEach(function(e){t.has(e)||e7(e,n)})}tt.set(s,r)},[r]),s),C=e3(e3({},k),d);return o.createElement(o.Fragment,null,h&&o.createElement(x,{sideCar:ta,removeScrollBar:f,shards:g,noRelative:y,noIsolation:v,inert:b,setCallbacks:c,allowPinchZoom:!!w,lockRef:i,gapMode:N}),u?o.cloneElement(o.Children.only(m),e3(e3({},C),{ref:_})):o.createElement(void 0===j?"div":j,e3({},C,{className:p,ref:_}),m))});ts.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},ts.classNames={fullWidth:e9,zeroRight:e8};var ti=function(e){var t=e.sideCar,r=e6(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var a=t.read();if(!a)throw Error("Sidecar medium not found");return o.createElement(a,e3({},r))};ti.isSideCarExport=!0;var to=function(){var e=0,t=null;return{add:function(a){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=s||r.nc;return t&&e.setAttribute("nonce",t),e}())){var n,i;(n=t).styleSheet?n.styleSheet.cssText=a:n.appendChild(document.createTextNode(a)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},tl=function(){var e=to();return function(t,r){o.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},td=function(){var e=tl();return function(t){return e(t.styles,t.dynamic),null}},tc={left:0,top:0,right:0,gap:0},tu=function(e){return parseInt(e||"",10)||0},tm=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],a=t["padding"===e?"paddingTop":"marginTop"],n=t["padding"===e?"paddingRight":"marginRight"];return[tu(r),tu(a),tu(n)]},tp=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return tc;var t=tm(e),r=document.documentElement.clientWidth,a=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,a-r+t[2]-t[0])}},tf=td(),th="data-scroll-locked",tg=function(e,t,r,a){var n=e.left,s=e.top,i=e.right,o=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(a,";\n   padding-right: ").concat(o,"px ").concat(a,";\n  }\n  body[").concat(th,"] {\n    overflow: hidden ").concat(a,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(a,";"),"margin"===r&&"\n    padding-left: ".concat(n,"px;\n    padding-top: ").concat(s,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(o,"px ").concat(a,";\n    "),"padding"===r&&"padding-right: ".concat(o,"px ").concat(a,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(e8," {\n    right: ").concat(o,"px ").concat(a,";\n  }\n  \n  .").concat(e9," {\n    margin-right: ").concat(o,"px ").concat(a,";\n  }\n  \n  .").concat(e8," .").concat(e8," {\n    right: 0 ").concat(a,";\n  }\n  \n  .").concat(e9," .").concat(e9," {\n    margin-right: 0 ").concat(a,";\n  }\n  \n  body[").concat(th,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(o,"px;\n  }\n")},tx=function(){var e=parseInt(document.body.getAttribute(th)||"0",10);return isFinite(e)?e:0},ty=function(){o.useEffect(function(){return document.body.setAttribute(th,(tx()+1).toString()),function(){var e=tx()-1;e<=0?document.body.removeAttribute(th):document.body.setAttribute(th,e.toString())}},[])},tv=function(e){var t=e.noRelative,r=e.noImportant,a=e.gapMode,n=void 0===a?"margin":a;ty();var s=o.useMemo(function(){return tp(n)},[n]);return o.createElement(tf,{styles:tg(s,!t,n,r?"":"!important")})},tb=!1;if("undefined"!=typeof window)try{var tw=Object.defineProperty({},"passive",{get:function(){return tb=!0,!0}});window.addEventListener("test",tw,tw),window.removeEventListener("test",tw,tw)}catch(e){tb=!1}var tj=!!tb&&{passive:!1},tN=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},tk=function(e,t){var r=t.ownerDocument,a=t;do{if("undefined"!=typeof ShadowRoot&&a instanceof ShadowRoot&&(a=a.host),t_(e,a)){var n=tC(e,a);if(n[1]>n[2])return!0}a=a.parentNode}while(a&&a!==r.body);return!1},t_=function(e,t){return"v"===e?tN(t,"overflowY"):tN(t,"overflowX")},tC=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},tE=function(e,t,r,a,n){var s,i=(s=window.getComputedStyle(t).direction,"h"===e&&"rtl"===s?-1:1),o=i*a,l=r.target,d=t.contains(l),c=!1,u=o>0,m=0,p=0;do{if(!l)break;var f=tC(e,l),h=f[0],g=f[1]-f[2]-i*h;(h||g)&&t_(e,l)&&(m+=g,p+=h);var x=l.parentNode;l=x&&x.nodeType===Node.DOCUMENT_FRAGMENT_NODE?x.host:x}while(!d&&l!==document.body||d&&(t.contains(l)||t===l));return u&&(n&&1>Math.abs(m)||!n&&o>m)?c=!0:!u&&(n&&1>Math.abs(p)||!n&&-o>p)&&(c=!0),c},tS=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},tA=function(e){return[e.deltaX,e.deltaY]},tP=function(e){return e&&"current"in e?e.current:e},tR=0,tM=[];let tT=(a=function(e){var t=o.useRef([]),r=o.useRef([0,0]),a=o.useRef(),n=o.useState(tR++)[0],s=o.useState(td)[0],i=o.useRef(e);o.useEffect(function(){i.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(n));var t=(function(e,t,r){if(r||2==arguments.length)for(var a,n=0,s=t.length;n<s;n++)!a&&n in t||(a||(a=Array.prototype.slice.call(t,0,n)),a[n]=t[n]);return e.concat(a||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(tP),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(n))}),function(){document.body.classList.remove("block-interactivity-".concat(n)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(n))})}}},[e.inert,e.lockRef.current,e.shards]);var l=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var n,s=tS(e),o=r.current,l="deltaX"in e?e.deltaX:o[0]-s[0],d="deltaY"in e?e.deltaY:o[1]-s[1],c=e.target,u=Math.abs(l)>Math.abs(d)?"h":"v";if("touches"in e&&"h"===u&&"range"===c.type)return!1;var m=tk(u,c);if(!m)return!0;if(m?n=u:(n="v"===u?"h":"v",m=tk(u,c)),!m)return!1;if(!a.current&&"changedTouches"in e&&(l||d)&&(a.current=n),!n)return!0;var p=a.current||n;return tE(p,t,e,"h"===p?l:d,!0)},[]),d=o.useCallback(function(e){if(tM.length&&tM[tM.length-1]===s){var r="deltaY"in e?tA(e):tS(e),a=t.current.filter(function(t){var a;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(a=t.delta,a[0]===r[0]&&a[1]===r[1])})[0];if(a&&a.should){e.cancelable&&e.preventDefault();return}if(!a){var n=(i.current.shards||[]).map(tP).filter(Boolean).filter(function(t){return t.contains(e.target)});(n.length>0?l(e,n[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=o.useCallback(function(e,r,a,n){var s={name:e,delta:r,target:a,should:n,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(a)};t.current.push(s),setTimeout(function(){t.current=t.current.filter(function(e){return e!==s})},1)},[]),u=o.useCallback(function(e){r.current=tS(e),a.current=void 0},[]),m=o.useCallback(function(t){c(t.type,tA(t),t.target,l(t,e.lockRef.current))},[]),p=o.useCallback(function(t){c(t.type,tS(t),t.target,l(t,e.lockRef.current))},[]);o.useEffect(function(){return tM.push(s),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:p}),document.addEventListener("wheel",d,tj),document.addEventListener("touchmove",d,tj),document.addEventListener("touchstart",u,tj),function(){tM=tM.filter(function(e){return e!==s}),document.removeEventListener("wheel",d,tj),document.removeEventListener("touchmove",d,tj),document.removeEventListener("touchstart",u,tj)}},[]);var f=e.removeScrollBar,h=e.inert;return o.createElement(o.Fragment,null,h?o.createElement(s,{styles:"\n  .block-interactivity-".concat(n," {pointer-events: none;}\n  .allow-interactivity-").concat(n," {pointer-events: all;}\n")}):null,f?o.createElement(tv,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},ta.useMedium(a),ti);var tI=o.forwardRef(function(e,t){return o.createElement(ts,e3({},e,{ref:t,sideCar:tT}))});tI.classNames=ts.classNames;var tD=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},tO=new WeakMap,t$=new WeakMap,tL={},tF=0,tW=function(e){return e&&(e.host||tW(e.parentNode))},tz=function(e,t,r,a){var n=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=tW(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});tL[r]||(tL[r]=new WeakMap);var s=tL[r],i=[],o=new Set,l=new Set(n),d=function(e){!e||o.has(e)||(o.add(e),d(e.parentNode))};n.forEach(d);var c=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(o.has(e))c(e);else try{var t=e.getAttribute(a),n=null!==t&&"false"!==t,l=(tO.get(e)||0)+1,d=(s.get(e)||0)+1;tO.set(e,l),s.set(e,d),i.push(e),1===l&&n&&t$.set(e,!0),1===d&&e.setAttribute(r,"true"),n||e.setAttribute(a,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),o.clear(),tF++,function(){i.forEach(function(e){var t=tO.get(e)-1,n=s.get(e)-1;tO.set(e,t),s.set(e,n),t||(t$.has(e)||e.removeAttribute(a),t$.delete(e)),n||e.removeAttribute(r)}),--tF||(tO=new WeakMap,tO=new WeakMap,t$=new WeakMap,tL={})}},tK=function(e,t,r){void 0===r&&(r="data-aria-hidden");var a=Array.from(Array.isArray(e)?e:[e]),n=t||tD(e);return n?(a.push.apply(a,Array.from(n.querySelectorAll("[aria-live], script"))),tz(a,n,r,"aria-hidden")):function(){return null}},tq=r(8730),tB="Dialog",[tU,tG]=function(e,t=[]){let r=[],a=()=>{let t=r.map(e=>o.createContext(e));return function(r){let a=r?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...r,[e]:a}}),[r,a])}};return a.scopeName=e,[function(t,a){let n=o.createContext(a),s=r.length;r=[...r,a];let l=t=>{let{scope:r,children:a,...l}=t,d=r?.[e]?.[s]||n,c=o.useMemo(()=>l,Object.values(l));return(0,i.jsx)(d.Provider,{value:c,children:a})};return l.displayName=t+"Provider",[l,function(r,i){let l=i?.[e]?.[s]||n,d=o.useContext(l);if(d)return d;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=r.reduce((t,{useScope:r,scopeName:a})=>{let n=r(e)[`__scope${a}`];return{...t,...n}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return r.scopeName=t.scopeName,r}(a,...t)]}(tB),[tH,tX]=tU(tB),tV=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:n,onOpenChange:s,modal:l=!0}=e,d=o.useRef(null),c=o.useRef(null),[u,m]=function({prop:e,defaultProp:t,onChange:r=()=>{},caller:a}){let[n,s,i]=function({defaultProp:e,onChange:t}){let[r,a]=o.useState(e),n=o.useRef(r),s=o.useRef(t);return eO(()=>{s.current=t},[t]),o.useEffect(()=>{n.current!==r&&(s.current?.(r),n.current=r)},[r,n]),[r,a,s]}({defaultProp:t,onChange:r}),l=void 0!==e,d=l?e:n;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${a} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,a])}return[d,o.useCallback(t=>{if(l){let r="function"==typeof t?t(e):t;r!==e&&i.current?.(r)}else s(t)},[l,e,s,i])]}({prop:a,defaultProp:n??!1,onChange:s,caller:tB});return(0,i.jsx)(tH,{scope:t,triggerRef:d,contentRef:c,contentId:eD(),titleId:eD(),descriptionId:eD(),open:u,onOpenChange:m,onOpenToggle:o.useCallback(()=>m(e=>!e),[m]),modal:l,children:r})};tV.displayName=tB;var tY="DialogTrigger";o.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=tX(tY,r),s=(0,eR.s)(t,n.triggerRef);return(0,i.jsx)(e$.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":ri(n.open),...a,ref:s,onClick:eP(e.onClick,n.onOpenToggle)})}).displayName=tY;var tJ="DialogPortal",[tZ,tQ]=tU(tJ,{forceMount:void 0}),t0=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:n}=e,s=tX(tJ,t);return(0,i.jsx)(tZ,{scope:t,forceMount:r,children:o.Children.map(a,e=>(0,i.jsx)(e1,{present:r||s.open,children:(0,i.jsx)(e0,{asChild:!0,container:n,children:e})}))})};t0.displayName=tJ;var t1="DialogOverlay",t2=o.forwardRef((e,t)=>{let r=tQ(t1,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,s=tX(t1,e.__scopeDialog);return s.modal?(0,i.jsx)(e1,{present:a||s.open,children:(0,i.jsx)(t5,{...n,ref:t})}):null});t2.displayName=t1;var t4=(0,tq.TL)("DialogOverlay.RemoveScroll"),t5=o.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=tX(t1,r);return(0,i.jsx)(tI,{as:t4,allowPinchZoom:!0,shards:[n.contentRef],children:(0,i.jsx)(e$.sG.div,{"data-state":ri(n.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),t3="DialogContent",t6=o.forwardRef((e,t)=>{let r=tQ(t3,e.__scopeDialog),{forceMount:a=r.forceMount,...n}=e,s=tX(t3,e.__scopeDialog);return(0,i.jsx)(e1,{present:a||s.open,children:s.modal?(0,i.jsx)(t8,{...n,ref:t}):(0,i.jsx)(t9,{...n,ref:t})})});t6.displayName=t3;var t8=o.forwardRef((e,t)=>{let r=tX(t3,e.__scopeDialog),a=o.useRef(null),n=(0,eR.s)(t,r.contentRef,a);return o.useEffect(()=>{let e=a.current;if(e)return tK(e)},[]),(0,i.jsx)(t7,{...e,ref:n,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:eP(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:eP(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:eP(e.onFocusOutside,e=>e.preventDefault())})}),t9=o.forwardRef((e,t)=>{let r=tX(t3,e.__scopeDialog),a=o.useRef(!1),n=o.useRef(!1);return(0,i.jsx)(t7,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||r.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));let s=t.target;r.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),t7=o.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:n,onCloseAutoFocus:s,...l}=e,d=tX(t3,r),c=o.useRef(null),u=(0,eR.s)(t,c);return o.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??e5()),document.body.insertAdjacentElement("beforeend",e[1]??e5()),e4++,()=>{1===e4&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),e4--}},[]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eH,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:n,onUnmountAutoFocus:s,children:(0,i.jsx)(ez,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":ri(d.open),...l,ref:u,onDismiss:()=>d.onOpenChange(!1)})}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(rc,{titleId:d.titleId}),(0,i.jsx)(ru,{contentRef:c,descriptionId:d.descriptionId})]})]})}),re="DialogTitle",rt=o.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=tX(re,r);return(0,i.jsx)(e$.sG.h2,{id:n.titleId,...a,ref:t})});rt.displayName=re;var rr="DialogDescription",ra=o.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=tX(rr,r);return(0,i.jsx)(e$.sG.p,{id:n.descriptionId,...a,ref:t})});ra.displayName=rr;var rn="DialogClose",rs=o.forwardRef((e,t)=>{let{__scopeDialog:r,...a}=e,n=tX(rn,r);return(0,i.jsx)(e$.sG.button,{type:"button",...a,ref:t,onClick:eP(e.onClick,()=>n.onOpenChange(!1))})});function ri(e){return e?"open":"closed"}rs.displayName=rn;var ro="DialogTitleWarning",[rl,rd]=function(e,t){let r=o.createContext(t),a=e=>{let{children:t,...a}=e,n=o.useMemo(()=>a,Object.values(a));return(0,i.jsx)(r.Provider,{value:n,children:t})};return a.displayName=e+"Provider",[a,function(a){let n=o.useContext(r);if(n)return n;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}(ro,{contentName:t3,titleName:re,docsSlug:"dialog"}),rc=({titleId:e})=>{let t=rd(ro),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&document.getElementById(e)},[r,e]),null},ru=({contentRef:e,descriptionId:t})=>{let r=rd("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return o.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&document.getElementById(t)},[a,e,t]),null},rm=r(11860);let rp=o.forwardRef(({className:e,...t},r)=>(0,i.jsx)(t2,{ref:r,className:`fixed inset-0 z-50 bg-black/50 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ${e||""}`,...t}));rp.displayName=t2.displayName;let rf=o.forwardRef(({className:e,children:t,...r},a)=>(0,i.jsxs)(t0,{children:[(0,i.jsx)(rp,{}),(0,i.jsxs)(t6,{ref:a,className:`fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg ${e||""}`,...r,children:[t,(0,i.jsxs)(rs,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,i.jsx)(rm.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));rf.displayName=t6.displayName;let rh=({className:e,...t})=>(0,i.jsx)("div",{className:`flex flex-col space-y-1.5 text-center sm:text-left ${e||""}`,...t});rh.displayName="DialogHeader";let rg=({className:e,...t})=>(0,i.jsx)("div",{className:`flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 ${e||""}`,...t});rg.displayName="DialogFooter";let rx=o.forwardRef(({className:e,...t},r)=>(0,i.jsx)(rt,{ref:r,className:`text-lg font-semibold leading-none tracking-tight ${e||""}`,...t}));rx.displayName=rt.displayName;let ry=o.forwardRef(({className:e,...t},r)=>(0,i.jsx)(ra,{ref:r,className:`text-sm text-gray-600 ${e||""}`,...t}));ry.displayName=ra.displayName;let rv=(0,o.forwardRef)(({className:e="",label:t,error:r,helperText:a,icon:n,iconPosition:s="left",id:o,...l},d)=>{let c=o||`input-${Math.random().toString(36).substr(2,9)}`;return(0,i.jsxs)("div",{className:"space-y-2",children:[t&&(0,i.jsx)("label",{htmlFor:c,className:"block text-sm font-medium text-gray-300",children:t}),(0,i.jsxs)("div",{className:"relative",children:[n&&"left"===s&&(0,i.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,i.jsx)("div",{className:"h-5 w-5 text-gray-400",children:n})}),(0,i.jsx)("input",{ref:d,id:c,className:`
              w-full p-3 bg-white/5 border rounded-xl text-white placeholder-gray-400 
              focus:ring-2 focus:ring-indigo-500 focus:border-transparent 
              disabled:opacity-50 disabled:cursor-not-allowed
              transition-all duration-200
              ${r?"border-red-500 focus:ring-red-500":"border-gray-600"}
              ${n&&"left"===s?"pl-10":""}
              ${n&&"right"===s?"pr-10":""}
              ${e}
            `,...l}),n&&"right"===s&&(0,i.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,i.jsx)("div",{className:"h-5 w-5 text-gray-400",children:n})})]}),r&&(0,i.jsx)("p",{className:"text-sm text-red-400",children:r}),a&&!r&&(0,i.jsx)("p",{className:"text-sm text-gray-500",children:a})]})});rv.displayName="Input",(0,o.forwardRef)(({className:e="",label:t,error:r,helperText:a,id:n,...s},o)=>{let l=n||`textarea-${Math.random().toString(36).substr(2,9)}`;return(0,i.jsxs)("div",{className:"space-y-2",children:[t&&(0,i.jsx)("label",{htmlFor:l,className:"block text-sm font-medium text-gray-300",children:t}),(0,i.jsx)("textarea",{ref:o,id:l,className:`
            w-full p-3 bg-white/5 border rounded-xl text-white placeholder-gray-400 
            focus:ring-2 focus:ring-indigo-500 focus:border-transparent 
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-all duration-200 resize-none
            ${r?"border-red-500 focus:ring-red-500":"border-gray-600"}
            ${e}
          `,...s}),r&&(0,i.jsx)("p",{className:"text-sm text-red-400",children:r}),a&&!r&&(0,i.jsx)("p",{className:"text-sm text-gray-500",children:a})]})}).displayName="Textarea",(0,o.forwardRef)(({className:e="",label:t,error:r,helperText:a,options:n=[],children:s,id:o,...l},d)=>{let c=o||`select-${Math.random().toString(36).substr(2,9)}`;return(0,i.jsxs)("div",{className:"space-y-2",children:[t&&(0,i.jsx)("label",{htmlFor:c,className:"block text-sm font-medium text-gray-300",children:t}),(0,i.jsxs)("select",{ref:d,id:c,className:`
            w-full p-3 bg-white/5 border rounded-xl text-white 
            focus:ring-2 focus:ring-indigo-500 focus:border-transparent 
            disabled:opacity-50 disabled:cursor-not-allowed
            transition-all duration-200
            ${r?"border-red-500 focus:ring-red-500":"border-gray-600"}
            ${e}
          `,...l,children:[n.map(e=>(0,i.jsx)("option",{value:e.value,disabled:e.disabled,className:"bg-gray-800 text-white",children:e.label},e.value)),s]}),r&&(0,i.jsx)("p",{className:"text-sm text-red-400",children:r}),a&&!r&&(0,i.jsx)("p",{className:"text-sm text-gray-500",children:a})]})}).displayName="Select";var rb=r(54300);let rw=(0,es.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-blue-50 text-blue-900 border-blue-200",destructive:"bg-red-50 text-red-900 border-red-200 [&>svg]:text-red-600"}},defaultVariants:{variant:"default"}}),rj=o.forwardRef(({className:e,variant:t,...r},a)=>(0,i.jsx)("div",{ref:a,role:"alert",className:`${rw({variant:t})} ${e||""}`,...r}));rj.displayName="Alert",o.forwardRef(({className:e,...t},r)=>(0,i.jsx)("h5",{ref:r,className:`mb-1 font-medium leading-none tracking-tight ${e||""}`,...t})).displayName="AlertTitle";let rN=o.forwardRef(({className:e,...t},r)=>(0,i.jsx)("div",{ref:r,className:`text-sm [&_p]:leading-relaxed ${e||""}`,...t}));rN.displayName="AlertDescription";let rk=(0,ee.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),r_=(0,ee.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),rC=(0,ee.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);function rE({open:e,onOpenChange:t,onCreateApiKey:r,configName:a,creating:n,subscriptionTier:s}){let[l,d]=(0,o.useState)("form"),[c,u]=(0,o.useState)(null),[m,p]=(0,o.useState)(!0),[f,h]=(0,o.useState)(!1),[g,x]=(0,o.useState)({key_name:"",expires_at:""}),y=async e=>{if(e.preventDefault(),!g.key_name.trim())return void en.oR.error("Please enter a name for your API key");try{let e=await r({key_name:g.key_name.trim(),expires_at:g.expires_at||void 0});u(e),d("success")}catch(e){}},v=async()=>{if(c?.api_key)try{await navigator.clipboard.writeText(c.api_key),h(!0),en.oR.success("API key copied to clipboard"),setTimeout(()=>h(!1),2e3)}catch(e){en.oR.error("Failed to copy API key")}},b=()=>{"form"===l&&(d("form"),u(null),p(!0),x({key_name:"",expires_at:""}),t(!1))};return"success"===l&&c?(0,i.jsx)(tV,{open:e,onOpenChange:()=>{},modal:!0,children:(0,i.jsxs)(rf,{className:"max-w-lg",children:[(0,i.jsxs)(rh,{className:"text-center space-y-3",children:[(0,i.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,i.jsx)(er,{className:"h-8 w-8 text-green-600"})}),(0,i.jsx)(rx,{className:"text-2xl font-bold text-gray-900",children:"API Key Created Successfully!"}),(0,i.jsx)(ry,{className:"text-gray-600",children:"Save your API key now - this is the only time you'll see it in full."})]}),(0,i.jsxs)("div",{className:"space-y-6 py-4",children:[(0,i.jsxs)(rj,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(rk,{className:"h-4 w-4 text-red-600"}),(0,i.jsxs)(rN,{className:"text-red-800 font-medium",children:[(0,i.jsx)("strong",{children:"Important:"})," This is the only time you'll see the full API key. Make sure to copy and store it securely."]})]}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(rb.J,{className:"text-sm font-medium text-gray-700",children:"Your API Key"}),(0,i.jsx)("div",{className:"relative",children:(0,i.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,i.jsx)("code",{className:"flex-1 text-sm font-mono text-gray-900 break-all select-all",children:m?c.api_key:`${c.key_prefix}_${"*".repeat(28)}${c.api_key.slice(-4)}`}),(0,i.jsxs)("div",{className:"flex gap-1",children:[(0,i.jsx)(Q.$,{variant:"ghost",size:"sm",onClick:()=>p(!m),className:"h-8 w-8 p-0",title:m?"Hide key":"Show key",children:m?(0,i.jsx)(r_,{className:"h-4 w-4"}):(0,i.jsx)(rC,{className:"h-4 w-4"})}),(0,i.jsx)(Q.$,{variant:"ghost",size:"sm",onClick:v,className:`h-8 w-8 p-0 ${f?"text-green-600":""}`,title:"Copy to clipboard",children:f?(0,i.jsx)("span",{className:"text-xs",children:"✓"}):(0,i.jsx)(el,{className:"h-4 w-4"})})]})]})}),(0,i.jsx)(Q.$,{onClick:v,variant:"outline",className:"w-full",disabled:f,children:f?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("span",{className:"text-green-600 mr-2",children:"✓"}),"Copied!"]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(el,{className:"h-4 w-4 mr-2"}),"Copy API Key"]})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm bg-gray-50 p-4 rounded-lg",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(rb.J,{className:"text-gray-600",children:"Key Name"}),(0,i.jsx)("p",{className:"font-medium text-gray-900",children:c.key_name})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(rb.J,{className:"text-gray-600",children:"Created"}),(0,i.jsx)("p",{className:"font-medium text-gray-900",children:new Date(c.created_at).toLocaleString()})]})]})]}),(0,i.jsx)(rg,{className:"pt-6",children:(0,i.jsx)(Q.$,{onClick:()=>{d("form"),u(null),p(!0),h(!1),x({key_name:"",expires_at:""}),t(!1)},className:"w-full",children:"I've Saved My API Key"})})]})}):(0,i.jsx)(tV,{open:e,onOpenChange:b,children:(0,i.jsxs)(rf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,i.jsxs)(rh,{children:[(0,i.jsxs)(rx,{className:"flex items-center gap-2",children:[(0,i.jsx)(er,{className:"h-5 w-5"}),"Create API Key"]}),(0,i.jsxs)(ry,{children:["Create a new API key for programmatic access to ",a]})]}),(0,i.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,i.jsx)("div",{className:"space-y-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(rb.J,{htmlFor:"key_name",children:"API Key Name *"}),(0,i.jsx)(rv,{id:"key_name",value:g.key_name,onChange:e=>x(t=>({...t,key_name:e.target.value})),placeholder:"e.g., Production API Key",required:!0,className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,i.jsx)("p",{className:"text-xs text-gray-600",children:"A descriptive name to help you identify this API key"})]})}),(0,i.jsx)("div",{className:"space-y-4",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(rb.J,{htmlFor:"expires_at",children:"Expiration Date (Optional)"}),(0,i.jsx)(rv,{id:"expires_at",type:"datetime-local",value:g.expires_at,onChange:e=>x(t=>({...t,expires_at:e.target.value})),min:new Date().toISOString().slice(0,16),className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,i.jsx)("p",{className:"text-xs text-gray-600",children:"Leave empty for no expiration"})]})}),(0,i.jsxs)(rg,{children:[(0,i.jsx)(Q.$,{type:"button",variant:"outline",onClick:b,children:"Cancel"}),(0,i.jsx)(Q.$,{type:"submit",disabled:n,children:n?"Creating...":"Create API Key"})]})]})]})})}var rS=r(8449);function rA({configId:e,configName:t}){let[r,a]=(0,o.useState)([]),[n,s]=(0,o.useState)(!0),[l,d]=(0,o.useState)(!1),[c,u]=(0,o.useState)(!1),[m,p]=(0,o.useState)(null),f=(0,H.Z)(),h=async()=>{try{s(!0);let t=await fetch(`/api/user-api-keys?config_id=${e}`);if(!t.ok)throw Error("Failed to fetch API keys");let r=await t.json();a(r.api_keys||[])}catch(e){en.oR.error("Failed to load API keys")}finally{s(!1)}},g=async e=>{try{let t=await fetch("/api/user/subscription-tier"),a=t.ok?await t.json():null,n=a?.tier||"starter",s={free:3,starter:50,professional:999999,enterprise:999999},i=void 0!==e?e:r.length;p({tier:n,keyLimit:s[n]||s.free,currentCount:i})}catch(t){p({tier:"free",keyLimit:3,currentCount:void 0!==e?e:r.length})}},x=async r=>{try{d(!0);let n=await fetch("/api/user-api-keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...r,custom_api_config_id:e})});if(!n.ok){let e=await n.json();throw Error(e.error||"Failed to create API key")}let s=await n.json();en.oR.success("API key created successfully!");let i={...s,custom_api_configs:{id:e,name:t}};return a(e=>{let t=[i,...e];return g(t.length),t}),await h(),s}catch(e){throw en.oR.error(e.message||"Failed to create API key"),e}finally{d(!1)}},y=async e=>{let t=r.find(t=>t.id===e),n=t?.key_name||"this API key";f.showConfirmation({title:"Revoke API Key",message:`Are you sure you want to revoke "${n}"? This action cannot be undone and will immediately disable the key.`,confirmText:"Revoke Key",cancelText:"Cancel",type:"danger"},async()=>{try{let t=await fetch(`/api/user-api-keys/${e}`,{method:"DELETE"});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to revoke API key")}a(t=>t.map(t=>t.id===e?{...t,status:"revoked"}:t)),en.oR.success("API key revoked successfully")}catch(e){throw en.oR.error(e.message||"Failed to revoke API key"),e}})},v=!m||m.currentCount<m.keyLimit;return n?(0,i.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,i.jsx)(et,{className:"h-6 w-6 animate-spin mr-2 text-gray-400"}),(0,i.jsx)("span",{className:"text-gray-400",children:"Loading API keys..."})]})}):(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold flex items-center gap-2 text-white",children:[(0,i.jsx)(er,{className:"h-6 w-6"}),"API Keys"]}),(0,i.jsxs)("p",{className:"text-gray-400 mt-1",children:["Generate API keys for programmatic access to ",t]})]}),v?(0,i.jsxs)(Q.$,{onClick:()=>u(!0),className:"flex items-center gap-2",children:[(0,i.jsx)(ea,{className:"h-4 w-4"}),"Create API Key"]}):(0,i.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,i.jsxs)(Q.$,{disabled:!0,className:"flex items-center gap-2 opacity-50",children:[(0,i.jsx)(ea,{className:"h-4 w-4"}),"Create API Key"]}),(0,i.jsx)("p",{className:"text-xs text-orange-400 font-medium",children:m?.tier==="free"?"Upgrade to Starter plan for more API keys":"API key limit reached - upgrade for unlimited keys"})]})]}),m?.tier==="free"&&(0,i.jsx)(rS.L,{message:"Unlock intelligent routing and more API keys",variant:"compact",className:"mb-4"}),(0,i.jsx)(rS.p,{className:"mb-4"}),0===r.length?(0,i.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg text-center py-8",children:(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)(er,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2 text-white",children:"No API Keys"}),(0,i.jsx)("p",{className:"text-gray-400 mb-4",children:"Create your first API key to start using the RouKey API programmatically."}),v?(0,i.jsxs)(Q.$,{onClick:()=>u(!0),children:[(0,i.jsx)(ea,{className:"h-4 w-4 mr-2"}),"Create Your First API Key"]}):(0,i.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,i.jsxs)(Q.$,{disabled:!0,className:"opacity-50",children:[(0,i.jsx)(ea,{className:"h-4 w-4 mr-2"}),"Create Your First API Key"]}),(0,i.jsx)("p",{className:"text-xs text-orange-400 font-medium",children:m?.tier==="free"?"Upgrade to Starter plan to create API keys":"API key limit reached - upgrade for unlimited keys"})]})]})}):(0,i.jsx)("div",{className:"grid gap-4",children:r.map(e=>(0,i.jsx)(eA,{apiKey:e,onRevoke:y},e.id))}),(0,i.jsx)(rE,{open:c,onOpenChange:e=>{u(e)},onCreateApiKey:x,configName:t,creating:l,subscriptionTier:m?.tier||"starter"}),(0,i.jsx)(G.A,{isOpen:f.isOpen,onClose:f.hideConfirmation,onConfirm:f.onConfirm,title:f.title,message:f.message,confirmText:f.confirmText,cancelText:f.cancelText,type:f.type,isLoading:f.isLoading})]})}var rP=r(4847);let rR=c.MG.map(e=>({value:e.id,label:e.name}));function rM(){let e=(0,d.useParams)().configId,t=(0,H.Z)(),r=(0,Z.bu)(),a=r?.navigateOptimistically||(e=>{window.location.href=e}),{getCachedData:n,isCached:s,clearCache:l}=(0,X._)(),{createHoverPrefetch:_}=(0,J.c)(),[C,E]=(0,o.useState)(null),[S,A]=(0,o.useState)(!0),[P,R]=(0,o.useState)(!1),[M,T]=(0,o.useState)(rR[0]?.value||"openai"),[I,D]=(0,o.useState)(""),[O,$]=(0,o.useState)(""),[L,F]=(0,o.useState)(""),[W,z]=(0,o.useState)(1),[K,q]=(0,o.useState)(!1),[B,Q]=(0,o.useState)(null),[ee,et]=(0,o.useState)(null),[er,ea]=(0,o.useState)(null),[en,es]=(0,o.useState)(!1),[ei,eo]=(0,o.useState)(null),[el,ed]=(0,o.useState)([]),[ec,eu]=(0,o.useState)(!0),[em,ep]=(0,o.useState)(null),[ef,eh]=(0,o.useState)(null),[eg,ex]=(0,o.useState)(null),[ey,ev]=(0,o.useState)(null),[eb,ew]=(0,o.useState)(1),[ej,eN]=(0,o.useState)(""),[ek,e_]=(0,o.useState)(!1),[eC,eE]=(0,o.useState)([]),[eS,eA]=(0,o.useState)(!1),[eP,eR]=(0,o.useState)(null),[eM,eT]=(0,o.useState)(!1),[eI,eD]=(0,o.useState)(""),[eO,e$]=(0,o.useState)(""),[eL,eF]=(0,o.useState)(""),[eW,ez]=(0,o.useState)(!1),[eK,eq]=(0,o.useState)(null),[eB,eU]=(0,o.useState)(null),[eG,eH]=(0,o.useState)("provider-keys"),[eX,eV]=(0,o.useState)(!1),[eY,eJ]=(0,o.useState)([]),[eZ,eQ]=(0,o.useState)(!1);(0,o.useCallback)(async()=>{if(!e)return;let t=n(e);if(t&&t.configDetails){E(t.configDetails),void 0!==t.configDetails.browsing_enabled&&eV(t.configDetails.browsing_enabled),t.configDetails.browsing_models&&eJ(t.configDetails.browsing_models),A(!1);return}s(e)||R(!0),A(!0),Q(null);try{let t=await fetch("/api/custom-configs");if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to fetch configurations list")}let r=(await t.json()).find(t=>t.id===e);if(!r)throw Error("Configuration not found in the list.");E(r),void 0!==r.browsing_enabled&&eV(r.browsing_enabled),r.browsing_models&&eJ(r.browsing_models)}catch(e){Q(`Error loading model configuration: ${e.message}`),E(null)}finally{A(!1),R(!1)}},[e,n,s]),(0,o.useCallback)(async()=>{let t=n(e);if(t&&t.models){ea(t.models),es(!1);return}es(!0),eo(null),ea(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch models from database.");t.models?ea(t.models):ea([])}catch(e){eo(`Error fetching models: ${e.message}`),ea([])}finally{es(!1)}},[e,n]),(0,o.useCallback)(async()=>{let t=n(e);if(t&&t.userCustomRoles){eE(t.userCustomRoles),eA(!1);return}eA(!0),eR(null);try{let e=await fetch("/api/user/custom-roles");if(e.ok){let t=await e.json();eE(t)}else{let t;try{t=await e.json()}catch(r){t={error:await e.text().catch(()=>`HTTP error ${e.status}`)}}let r=t.error||(t.issues?JSON.stringify(t.issues):`Failed to fetch custom roles (status: ${e.status})`);if(401===e.status)eR(r);else throw Error(r);eE([])}}catch(e){eR(e.message),eE([])}finally{eA(!1)}},[]);let e0=(0,o.useCallback)(async()=>{if(!e||!eC)return;let t=n(e);if(t&&t.apiKeys&&void 0!==t.defaultChatKeyId){let e=t.apiKeys.map(async e=>{let r=await fetch(`/api/keys/${e.id}/roles`),a=[];return r.ok&&(a=(await r.json()).map(e=>{let t=(0,u.Dc)(e.role_name);if(t)return t;let r=eC.find(t=>t.role_id===e.role_name);return r?{id:r.role_id,name:r.name,description:r.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:a,is_default_general_chat_model:t.defaultChatKeyId===e.id}});ed(await Promise.all(e)),eh(t.defaultChatKeyId),eu(!1);return}eu(!0),Q(e=>e&&e.startsWith("Error loading model configuration:")?e:null),et(null);try{let t=await fetch(`/api/keys?custom_config_id=${e}`);if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to fetch API keys")}let r=await t.json(),a=await fetch(`/api/custom-configs/${e}/default-chat-key`);a.ok;let n=200===a.status?await a.json():null;eh(n?.id||null);let s=r.map(async e=>{let t=await fetch(`/api/keys/${e.id}/roles`),r=[];return t.ok&&(r=(await t.json()).map(e=>{let t=(0,u.Dc)(e.role_name);if(t)return t;let r=eC.find(t=>t.role_id===e.role_name);return r?{id:r.role_id,name:r.name,description:r.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:r,is_default_general_chat_model:n?.id===e.id}}),i=await Promise.all(s);ed(i)}catch(e){Q(t=>t?`${t}; ${e.message}`:e.message)}finally{eu(!1)}},[e,eC]),e1=(0,o.useMemo)(()=>{if(er){let e=c.MG.find(e=>e.id===M);if(!e)return[];if("openrouter"===e.id)return er.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return er.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),er.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return er.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[er,M]),e2=(0,o.useMemo)(()=>{if(er&&ey){let e=c.MG.find(e=>e.id===ey.provider);if(!e)return[];if("openrouter"===e.id)return er.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return er.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),er.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return er.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[er,ey]),e4=((0,o.useCallback)(e=>{if(!er)return[];let t=c.MG.find(t=>t.id===e);if(!t)return[];if("openrouter"===t.id)return er.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===t.id){let e=["deepseek-chat","deepseek-reasoner"];return er.filter(t=>"deepseek"===t.provider_id&&e.some(e=>t.id.includes(e))).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return er.filter(e=>e.provider_id===t.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))},[er]),async t=>{if(t.preventDefault(),!e)return void Q("Configuration ID is missing.");if(el.some(e=>e.predefined_model_id===I))return void Q("This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.");q(!0),Q(null),et(null);let r=[...el];try{let t=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({custom_api_config_id:e,provider:M,predefined_model_id:I,api_key_raw:O,label:L,temperature:W})}),r=await t.json();if(!t.ok)throw Error(r.details||r.error||"Failed to save API key");let a={id:r.id,custom_api_config_id:e,provider:M,predefined_model_id:I,label:L,temperature:W,status:"active",created_at:new Date().toISOString(),last_used_at:null,is_default_general_chat_model:!1,assigned_roles:[]};ed(e=>[...e,a]),l(e),et(`API key "${L}" saved successfully!`),T(rR[0]?.value||"openai"),$(""),F(""),z(1),e1.length>0&&D(e1[0].value)}catch(e){ed(r),Q(`Save Key Error: ${e.message}`)}finally{q(!1)}}),e5=e=>{ev(e),ew(e.temperature||1),eN(e.predefined_model_id)},e3=async()=>{if(!ey)return;if(el.some(e=>e.id!==ey.id&&e.predefined_model_id===ej))return void Q("This model is already configured in this setup. Each model can only be used once per configuration.");e_(!0),Q(null),et(null);let t=[...el];ed(e=>e.map(e=>e.id===ey.id?{...e,temperature:eb,predefined_model_id:ej}:e));try{let r=await fetch(`/api/keys?id=${ey.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({temperature:eb,predefined_model_id:ej})}),a=await r.json();if(!r.ok)throw ed(t),Error(a.details||a.error||"Failed to update API key");l(e),et(`API key "${ey.label}" updated successfully!`),ev(null)}catch(e){Q(`Update Key Error: ${e.message}`)}finally{e_(!1)}},e6=(r,a)=>{t.showConfirmation({title:"Delete API Key",message:`Are you sure you want to delete the API key "${a}"? This will permanently remove the key and unassign all its roles. This action cannot be undone.`,confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{ep(r),Q(null),et(null);let t=[...el],n=el.find(e=>e.id===r);ed(e=>e.filter(e=>e.id!==r)),n?.is_default_general_chat_model&&eh(null);try{let n=await fetch(`/api/keys/${r}`,{method:"DELETE"}),s=await n.json();if(!n.ok){if(ed(t),eh(ef),404===n.status){ed(e=>e.filter(e=>e.id!==r)),et(`API key "${a}" was already deleted.`);return}throw Error(s.details||s.error||"Failed to delete API key")}l(e),et(`API key "${a}" deleted successfully!`)}catch(e){throw Q(`Delete Key Error: ${e.message}`),e}finally{ep(null)}})},e8=async t=>{if(!e)return;Q(null),et(null);let r=[...el];ed(e=>e.map(e=>({...e,is_default_general_chat_model:e.id===t}))),eh(t);try{let a=await fetch(`/api/custom-configs/${e}/default-key-handler/${t}`,{method:"PUT"}),n=await a.json();if(!a.ok)throw ed(r.map(e=>({...e}))),eh(ef),Error(n.details||n.error||"Failed to set default chat key");l(e),et(n.message||"Default general chat key updated!")}catch(e){Q(`Set Default Error: ${e.message}`)}},e9=async(e,t,r)=>{Q(null),et(null);let a=`/api/keys/${e.id}/roles`,n=[...u.p2.map(e=>({...e,isCustom:!1})),...eC.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].find(e=>e.id===t)||{id:t,name:t,description:""},s=el.map(e=>({...e,assigned_roles:[...e.assigned_roles.map(e=>({...e}))]})),i=null;eg&&eg.id===e.id&&(i={...eg,assigned_roles:[...eg.assigned_roles.map(e=>({...e}))]}),ed(a=>a.map(a=>{if(a.id===e.id){let e=r?a.assigned_roles.filter(e=>e.id!==t):[...a.assigned_roles,n];return{...a,assigned_roles:e}}return a})),eg&&eg.id===e.id&&ex(e=>{if(!e)return null;let a=r?e.assigned_roles.filter(e=>e.id!==t):[...e.assigned_roles,n];return{...e,assigned_roles:a}});try{let o;o=r?await fetch(`${a}/${t}`,{method:"DELETE"}):await fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_name:t})});let l=await o.json();if(!o.ok){if(ed(s),i)ex(i);else if(eg&&eg.id===e.id){let t=s.find(t=>t.id===e.id);t&&ex(t)}let t=409===o.status&&l.error?l.error:l.details||l.error||(r?"Failed to unassign role":"Failed to assign role");throw Error(t)}et(l.message||`Role '${n.name}' ${r?"unassigned":"assigned"} successfully.`)}catch(e){Q(`Role Update Error: ${e.message}`)}},e7=async()=>{if(!eI.trim()||eI.trim().length>30||!/^[a-zA-Z0-9_]+$/.test(eI.trim()))return void eq("Role ID is required (max 30 chars, letters, numbers, underscores only).");if(u.p2.some(e=>e.id.toLowerCase()===eI.trim().toLowerCase())||eC.some(e=>e.role_id.toLowerCase()===eI.trim().toLowerCase()))return void eq("This Role ID is already in use (either predefined or as one of your custom roles).");if(!eO.trim())return void eq("Role Name is required.");eq(null),ez(!0);try{let t=await fetch("/api/user/custom-roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_id:eI.trim(),name:eO.trim(),description:eL.trim()})});if(!t.ok){let e;try{e=await t.json()}catch(a){let r=await t.text().catch(()=>`HTTP status ${t.status}`);e={error:"Server error, could not parse response.",details:r}}let r=e.error||"Failed to create custom role.";if(e.details)r+=` (Details: ${e.details})`;else if(e.issues){let t=Object.entries(e.issues).map(([e,t])=>`${e}: ${t.join(", ")}`).join("; ");r+=` (Issues: ${t})`}throw Error(r)}let r=await t.json();eD(""),e$(""),eF(""),l(e);let a={id:r.id,role_id:r.role_id,name:r.name,description:r.description,user_id:r.user_id,created_at:r.created_at,updated_at:r.updated_at};eE(e=>[...e,a]),et(`Custom role '${r.name}' created successfully! It is now available globally.`)}catch(e){eq(e.message)}finally{ez(!1)}},te=(r,a)=>{r&&t.showConfirmation({title:"Delete Custom Role",message:`Are you sure you want to delete the custom role "${a}"? This will unassign it from all API keys where it's currently used. This action cannot be undone.`,confirmText:"Delete Role",cancelText:"Cancel",type:"danger"},async()=>{eU(r),eR(null),eq(null),et(null);try{let t=await fetch(`/api/user/custom-roles/${r}`,{method:"DELETE"}),n=await t.json();if(!t.ok)throw Error(n.error||"Failed to delete custom role");eE(e=>e.filter(e=>e.id!==r)),l(e),et(n.message||`Global custom role "${a}" deleted successfully.`),e&&e0()}catch(e){throw eR(`Error deleting role: ${e.message}`),e}finally{eU(null)}})};return P&&!s(e)?(0,i.jsx)(V,{}):S&&!C?(0,i.jsx)(Y,{}):(0,i.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:(0,i.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsxs)("button",{onClick:()=>a("/my-models"),className:"text-orange-400 hover:text-orange-300 inline-flex items-center mb-6 transition-colors duration-200 group",children:[(0,i.jsx)(g.A,{className:"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform"}),"Back to My API Models"]}),(0,i.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-8 mb-6",children:(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[(0,i.jsx)("div",{className:"flex-1",children:C?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"flex items-center mb-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg",children:(0,i.jsx)(f.A,{className:"h-6 w-6 text-white"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-white",children:C.name}),(0,i.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Model Configuration"})]})]}),(0,i.jsxs)("div",{className:"flex items-center text-sm text-gray-300 bg-gray-800/50 px-4 py-2 rounded-lg w-fit",children:[(0,i.jsx)("span",{className:"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"}),"ID: ",C.id]})]}):B&&!S?(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-red-900/50 rounded-2xl flex items-center justify-center mr-4",children:(0,i.jsx)(m.A,{className:"h-6 w-6 text-red-400"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-red-400",children:"Configuration Error"}),(0,i.jsx)("p",{className:"text-red-300 mt-1",children:B.replace("Error loading model configuration: ","")})]})]}):(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gray-800/50 rounded-2xl flex items-center justify-center mr-4",children:(0,i.jsx)(x.A,{className:"h-6 w-6 text-gray-400 animate-pulse"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Loading Configuration..."}),(0,i.jsx)("p",{className:"text-gray-400 mt-1",children:"Please wait while we fetch your model details"})]})]})}),C&&(0,i.jsx)("div",{className:"flex flex-col sm:flex-row gap-3",children:(0,i.jsxs)("button",{onClick:()=>a(`/routing-setup/${e}?from=model-config`),className:"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group",..._(e),children:[(0,i.jsx)(f.A,{className:"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200"}),"Advanced Routing Setup"]})})]})}),ee&&(0,i.jsx)("div",{className:"bg-green-900/50 backdrop-blur-sm border border-green-800/50 rounded-lg p-4 mb-6 animate-slide-in",children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(y.A,{className:"h-5 w-5 text-green-400"}),(0,i.jsx)("p",{className:"text-green-300 font-medium",children:ee})]})}),B&&(0,i.jsx)("div",{className:"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4 mb-6 animate-slide-in",children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(m.A,{className:"h-5 w-5 text-red-400"}),(0,i.jsx)("p",{className:"text-red-300 font-medium",children:B})]})})]}),C&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-2",children:(0,i.jsxs)("div",{className:"flex space-x-1",children:[(0,i.jsx)("button",{onClick:()=>eH("provider-keys"),className:`flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${"provider-keys"===eG?"bg-orange-500 text-white shadow-md":"text-gray-400 hover:text-white hover:bg-gray-800/50"}`,children:(0,i.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,i.jsx)(v.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:"Provider API Keys"})]})}),(0,i.jsx)("button",{onClick:()=>eH("user-api-keys"),className:`flex-1 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${"user-api-keys"===eG?"bg-orange-500 text-white shadow-md":"text-gray-400 hover:text-white hover:bg-gray-800/50"}`,children:(0,i.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,i.jsx)(b.A,{className:"h-4 w-4"}),(0,i.jsx)("span",{children:"Generated API Keys"})]})}),!1]})}),"provider-keys"===eG&&(0,i.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-5 gap-8",children:[(0,i.jsx)("div",{className:"xl:col-span-2",children:(0,i.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 sticky top-8",children:[(0,i.jsxs)("div",{className:"flex items-center mb-6",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,i.jsx)(w.A,{className:"h-5 w-5 text-white"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-bold text-white",children:"Add Provider API Key"}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Configure new provider key"})]})]}),(0,i.jsxs)("form",{onSubmit:e4,className:"space-y-5",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,i.jsx)("select",{id:"provider",value:M,onChange:e=>{T(e.target.value)},className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm",children:rR.map(e=>(0,i.jsx)("option",{value:e.value,className:"bg-gray-800 text-white",children:e.label},e.value))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"apiKeyRaw",className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key"}),(0,i.jsx)("input",{id:"apiKeyRaw",type:"password",value:O,onChange:e=>$(e.target.value),className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm",placeholder:"Enter your API key"}),en&&null===er&&(0,i.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,i.jsx)(x.A,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),ei&&(0,i.jsx)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:ei})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"predefinedModelId",className:"block text-sm font-medium text-gray-300 mb-2",children:"Model Variant"}),(0,i.jsx)("select",{id:"predefinedModelId",value:I,onChange:e=>D(e.target.value),disabled:!e1.length,className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white text-sm disabled:bg-gray-800/30 disabled:text-gray-500",children:e1.length>0?e1.map(e=>(0,i.jsx)("option",{value:e.value,className:"bg-gray-800 text-white",children:e.label},e.value)):(0,i.jsx)("option",{value:"",disabled:!0,className:"bg-gray-800 text-gray-400",children:null===er&&en?"Loading models...":"Select a provider first"})})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-300 mb-2",children:"Label"}),(0,i.jsx)("input",{type:"text",id:"label",value:L,onChange:e=>F(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-700 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-gray-800/50 text-white placeholder-gray-400 text-sm",placeholder:"e.g., My OpenAI GPT-4o Key #1"})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature",(0,i.jsx)("span",{className:"text-xs text-gray-400 ml-1",children:"(0.0 - 2.0)"})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:W,onChange:e=>z(parseFloat(e.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,i.jsx)("div",{className:"flex items-center space-x-2",children:(0,i.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:W,onChange:e=>z(Math.min(2,Math.max(0,parseFloat(e.target.value)||0))),className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,i.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]})]}),(0,i.jsx)("button",{type:"submit",disabled:K||!I||""===I||!O.trim()||!L.trim(),className:"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm",children:K?(0,i.jsxs)("span",{className:"flex items-center justify-center",children:[(0,i.jsx)(x.A,{className:"h-4 w-4 mr-2 animate-pulse"}),"Saving..."]}):(0,i.jsxs)("span",{className:"flex items-center justify-center",children:[(0,i.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Add API Key"]})})]}),(0,i.jsx)("div",{className:"mt-6 p-4 bg-blue-900/50 backdrop-blur-sm border border-blue-800/50 rounded-lg",children:(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)(j.A,{className:"h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-blue-300 mb-1",children:"Key Configuration Rules"}),(0,i.jsxs)("div",{className:"text-xs text-blue-200 space-y-1",children:[(0,i.jsxs)("p",{children:["✅ ",(0,i.jsx)("strong",{children:"Same API key, different models:"})," Allowed"]}),(0,i.jsxs)("p",{children:["✅ ",(0,i.jsx)("strong",{children:"Different API keys, same model:"})," Allowed"]}),(0,i.jsxs)("p",{children:["❌ ",(0,i.jsx)("strong",{children:"Same model twice:"})," Not allowed in one configuration"]})]})]})]})})]})}),(0,i.jsx)("div",{className:"xl:col-span-3",children:(0,i.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center mb-6",children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,i.jsx)(v.A,{className:"h-5 w-5 text-white"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-bold text-white",children:"API Keys & Roles"}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Manage existing keys"})]})]}),ec&&(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)(x.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse"}),(0,i.jsx)("p",{className:"text-gray-400 text-sm",children:"Loading API keys..."})]}),!ec&&0===el.length&&(!B||B&&B.startsWith("Error loading model configuration:"))&&(0,i.jsxs)("div",{className:"text-center py-8",children:[(0,i.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,i.jsx)(v.A,{className:"h-6 w-6 text-gray-400"})}),(0,i.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-1",children:"No API Keys"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Add your first key using the form"})]}),!ec&&el.length>0&&(0,i.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:el.map((e,t)=>(0,i.jsx)("div",{className:"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 hover:border-gray-600/50 transition-all duration-200 animate-slide-in",style:{animationDelay:`${50*t}ms`},children:(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsxs)("div",{className:"flex items-center mb-2",children:[(0,i.jsx)("h3",{className:"text-sm font-semibold text-white truncate mr-2",children:e.label}),e.is_default_general_chat_model&&(0,i.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/30 text-green-300 border border-green-500/50 flex-shrink-0",children:[(0,i.jsx)(N.A,{className:"h-3 w-3 mr-1"}),"Default"]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsxs)("p",{className:"text-xs text-gray-200 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:[e.provider," (",e.predefined_model_id,")"]}),(0,i.jsxs)("p",{className:"text-xs text-orange-300 bg-orange-900/30 px-2 py-1 rounded-lg border border-orange-500/50",children:["Temp: ",e.temperature]})]}),(0,i.jsx)(rP.sU,{feature:"custom_roles",fallback:(0,i.jsx)("div",{className:"flex flex-wrap gap-1",children:(0,i.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:"Roles available on Starter plan+"})}),children:(0,i.jsx)("div",{className:"flex flex-wrap gap-1",children:e.assigned_roles.length>0?e.assigned_roles.map(e=>(0,i.jsx)("span",{className:"inline-block whitespace-nowrap rounded-full bg-orange-900/30 px-2 py-1 text-xs font-medium text-orange-300 border border-orange-500/50",children:e.name},e.id)):(0,i.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700/50 px-2 py-1 rounded-lg border border-gray-600",children:"No roles"})})})]}),!e.is_default_general_chat_model&&(0,i.jsx)("button",{onClick:()=>e8(e.id),className:"text-xs bg-gray-700/50 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white py-1 px-2 rounded-lg mt-2 transition-colors","data-tooltip-id":"global-tooltip","data-tooltip-content":"Set as default chat model",children:"Set Default"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1 ml-2 flex-shrink-0",children:[(0,i.jsx)("button",{onClick:()=>e5(e),disabled:em===e.id,className:"p-2 text-blue-400 hover:text-blue-300 hover:bg-blue-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Edit Model & Settings",children:(0,i.jsx)(k.A,{className:"h-4 w-4"})}),(0,i.jsx)(rP.sU,{feature:"custom_roles",fallback:(0,i.jsx)("button",{disabled:!0,className:"p-2 text-gray-400 cursor-not-allowed rounded-lg opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Role management requires Starter plan or higher",children:(0,i.jsx)(f.A,{className:"h-4 w-4"})}),children:(0,i.jsx)("button",{onClick:()=>ex(e),disabled:em===e.id,className:"p-2 text-orange-400 hover:text-orange-300 hover:bg-orange-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Manage Roles",children:(0,i.jsx)(f.A,{className:"h-4 w-4"})})}),(0,i.jsx)("button",{onClick:()=>e6(e.id,e.label),disabled:em===e.id,className:"p-2 text-red-400 hover:text-red-300 hover:bg-red-900/30 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"global-tooltip","data-tooltip-content":"Delete Key",children:em===e.id?(0,i.jsx)(h.A,{className:"h-4 w-4 animate-pulse"}):(0,i.jsx)(h.A,{className:"h-4 w-4"})})]})]})},e.id))}),!ec&&B&&!B.startsWith("Error loading model configuration:")&&(0,i.jsx)("div",{className:"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4",children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)(m.A,{className:"h-5 w-5 text-red-400"}),(0,i.jsxs)("p",{className:"text-red-300 font-medium text-sm",children:["Could not load API keys/roles: ",B]})]})})]})})]}),"user-api-keys"===eG&&(0,i.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:(0,i.jsx)(rA,{configId:e,configName:C.name})}),"browsing-config"===eG&&!1]}),eg&&(()=>{if(!eg)return null;let e=[...u.p2.map(e=>({...e,isCustom:!1})),...eC.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].sort((e,t)=>e.name.localeCompare(t.name));return(0,i.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,i.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg max-h-[90vh] flex flex-col",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,i.jsxs)("h2",{className:"text-xl font-semibold text-white",children:["Manage Roles for: ",(0,i.jsx)("span",{className:"text-orange-400",children:eg.label})]}),(0,i.jsx)("button",{onClick:()=>{ex(null),eT(!1),eq(null)},className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,i.jsx)(m.A,{className:"h-6 w-6"})})]}),(0,i.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[eP&&(0,i.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,i.jsxs)("p",{className:"text-red-800 text-sm",children:["Error with custom roles: ",eP]})}),(0,i.jsxs)(rP.sU,{feature:"custom_roles",customMessage:"Custom roles are available starting with the Starter plan. Create specialized roles to organize your API keys by task type and optimize routing for different use cases.",children:[(0,i.jsx)("div",{className:"flex justify-end mb-4",children:(0,i.jsxs)("button",{onClick:()=>eT(!eM),className:"btn-primary text-sm inline-flex items-center",children:[(0,i.jsx)(p.A,{className:"h-4 w-4 mr-2"}),eM?"Cancel New Role":"Create New Custom Role"]})}),eM&&(0,i.jsxs)("div",{className:"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 mb-4",children:[(0,i.jsx)("h3",{className:"text-md font-medium text-white mb-3",children:"Create New Custom Role for this Configuration"}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"newCustomRoleId",className:"block text-sm font-medium text-gray-300 mb-1",children:"Role ID (short, no spaces, max 30 chars)"}),(0,i.jsx)("input",{type:"text",id:"newCustomRoleId",value:eI,onChange:e=>eD(e.target.value.replace(/\s/g,"")),className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:30,placeholder:"e.g., my_blog_writer"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"newCustomRoleName",className:"block text-sm font-medium text-gray-300 mb-1",children:"Display Name (max 100 chars)"}),(0,i.jsx)("input",{type:"text",id:"newCustomRoleName",value:eO,onChange:e=>e$(e.target.value),className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:100,placeholder:"e.g., My Awesome Blog Writer"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"newCustomRoleDescription",className:"block text-sm font-medium text-gray-300 mb-1",children:"Description (optional, max 500 chars)"}),(0,i.jsx)("textarea",{id:"newCustomRoleDescription",value:eL,onChange:e=>eF(e.target.value),rows:2,className:"w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",maxLength:500,placeholder:"Optional: Describe what this role is for..."})]}),eK&&(0,i.jsx)("div",{className:"bg-red-900/50 border border-red-800/50 rounded-lg p-3",children:(0,i.jsx)("p",{className:"text-red-300 text-sm",children:eK})}),(0,i.jsx)("button",{onClick:e7,disabled:eW,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:eW?"Saving Role...":"Save Custom Role"})]})]})]})]}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-300 mb-3",children:"Select roles to assign:"}),(0,i.jsxs)("div",{className:"overflow-y-auto space-y-2",style:{maxHeight:"calc(90vh - 350px)"},children:[eS&&(0,i.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500"}),(0,i.jsx)("p",{className:"text-gray-400 text-sm ml-2",children:"Loading custom roles..."})]}),e.map(e=>{let t=eg.assigned_roles.some(t=>t.id===e.id);return(0,i.jsxs)("div",{className:`flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ${t?"bg-orange-500/20 border-orange-500/30 shadow-sm":"bg-gray-800/50 border-gray-700/50 hover:border-gray-600/50 hover:shadow-sm"}`,children:[(0,i.jsxs)("label",{htmlFor:`role-${e.id}`,className:"flex items-center cursor-pointer flex-grow",children:[(0,i.jsx)("input",{type:"checkbox",id:`role-${e.id}`,checked:t,onChange:()=>e9(eg,e.id,t),className:"h-4 w-4 text-orange-500 border-gray-600 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer bg-gray-700"}),(0,i.jsx)("span",{className:`ml-3 text-sm font-medium ${t?"text-orange-300":"text-white"}`,children:e.name}),e.isCustom&&(0,i.jsx)("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-500/20 text-blue-300",children:"Custom"})]}),e.isCustom&&e.databaseId&&(0,i.jsx)("button",{onClick:()=>te(e.databaseId,e.name),disabled:eB===e.databaseId,className:"p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-900/30 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2",title:"Delete this custom role",children:eB===e.databaseId?(0,i.jsx)(f.A,{className:"h-4 w-4 animate-spin"}):(0,i.jsx)(h.A,{className:"h-4 w-4"})})]},e.id)})]})]}),(0,i.jsx)("div",{className:"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg",children:(0,i.jsx)("div",{className:"flex justify-end",children:(0,i.jsx)("button",{onClick:()=>{ex(null),eT(!1),eq(null)},className:"btn-secondary",children:"Done"})})})]})})})(),ey&&(0,i.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,i.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-lg w-full max-w-lg",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Edit API Key"}),(0,i.jsx)("button",{onClick:()=>ev(null),className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,i.jsx)(m.A,{className:"h-6 w-6"})})]}),(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsxs)("div",{className:"mb-4",children:[(0,i.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:ey.label}),(0,i.jsxs)("p",{className:"text-sm text-gray-400",children:["Current: ",ey.provider," (",ey.predefined_model_id,")"]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,i.jsx)("div",{className:"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-300",children:c.MG.find(e=>e.id===ey.provider)?.name||ey.provider}),(0,i.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Provider cannot be changed"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"editModelId",className:"block text-sm font-medium text-gray-300 mb-2",children:"Model"}),(0,i.jsx)("select",{id:"editModelId",value:ej,onChange:e=>eN(e.target.value),disabled:!e2.length,className:"w-full p-2.5 bg-gray-800/50 border border-gray-700 rounded-md text-gray-200 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-800/30",children:e2.length>0?e2.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value)):(0,i.jsx)("option",{value:"",disabled:!0,children:en?"Loading models...":"No models available"})})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{htmlFor:"editTemperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature: ",eb]}),(0,i.jsx)("input",{type:"range",id:"editTemperature",min:"0",max:"2",step:"0.1",value:eb,onChange:e=>ew(parseFloat(e.target.value)),className:"slider-orange w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"}),(0,i.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,i.jsx)("span",{children:"0.0 (Focused)"}),(0,i.jsx)("span",{children:"1.0 (Balanced)"}),(0,i.jsx)("span",{children:"2.0 (Creative)"})]})]}),(0,i.jsx)("div",{className:"bg-gray-800/50 rounded-lg p-3",children:(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied."})})]})]}),(0,i.jsx)("div",{className:"p-6 border-t border-gray-800/50 bg-gray-900/50 rounded-b-lg",children:(0,i.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,i.jsx)("button",{onClick:()=>ev(null),className:"btn-secondary",disabled:ek,children:"Cancel"}),(0,i.jsx)("button",{onClick:e3,disabled:ek,className:"btn-primary",children:ek?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):"Save Changes"})]})})]})}),!C&&!S&&!B&&(0,i.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:(0,i.jsx)(j.A,{className:"h-8 w-8 text-gray-400"})}),(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Model Not Found"}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-8",children:"This API Model configuration could not be found or may have been deleted."}),(0,i.jsxs)("button",{onClick:()=>a("/my-models"),className:"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5",children:[(0,i.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Return to My API Models"]})]}),(0,i.jsx)(G.A,{isOpen:t.isOpen,onClose:t.hideConfirmation,onConfirm:t.onConfirm,title:t.title,message:t.message,confirmText:t.confirmText,cancelText:t.cancelText,type:t.type,isLoading:t.isLoading}),(0,i.jsx)(U,{id:"global-tooltip"})]})})}},51983:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=r(65239),n=r(48088),s=r(88170),i=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["my-models",{children:["[configId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,20218)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\[configId]\\page.tsx"]}]},{}]},{loading:[()=>Promise.resolve().then(r.bind(r,92529)),"C:\\RoKey App\\rokey-app\\src\\app\\my-models\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\my-models\\[configId]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/my-models/[configId]/page",pathname:"/my-models/[configId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var a=r(60687),n=r(43210),s=r(14163),i=n.forwardRef((e,t)=>(0,a.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";let o=(0,r(43985).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(i,{ref:r,className:`${o()} ${e||""}`,...t}));l.displayName=i.displayName},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57891:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(43210);let n=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},60925:(e,t,r)=>{"use strict";r.d(t,{c:()=>s});var a=r(43210);let n={};function s(){let[e,t]=(0,a.useState)({}),r=(0,a.useRef)({}),s=(0,a.useCallback)(e=>{let t=n[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),i=(0,a.useCallback)(e=>{let t=n[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete n[e],null):t.data},[]),o=(0,a.useCallback)(async(e,a="medium")=>{if(s(e))return i(e);if(n[e]?.isLoading)return null;r.current[e]&&r.current[e].abort();let o=new AbortController;r.current[e]=o,n[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===a?await new Promise(e=>setTimeout(e,200)):"medium"===a&&await new Promise(e=>setTimeout(e,50));let[r,s,i]=await Promise.allSettled([fetch(`/api/custom-configs/${e}`,{signal:o.signal}),fetch(`/api/keys?custom_config_id=${e}`,{signal:o.signal}),fetch(`/api/complexity-assignments?custom_config_id=${e}`,{signal:o.signal})]),l=null,d=[],c="none",u={},m=[];"fulfilled"===r.status&&r.value.ok&&(c=(l=await r.value.json()).routing_strategy||"none",u=l.routing_strategy_params||{}),"fulfilled"===s.status&&s.value.ok&&(d=await s.value.json()),"fulfilled"===i.status&&i.value.ok&&(m=await i.value.json());let p={configDetails:l,apiKeys:d,routingStrategy:c,routingParams:u,complexityAssignments:m};return n[e]={data:p,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),p}catch(r){if("AbortError"===r.name)return null;return delete n[e],t(t=>({...t,[e]:"error"})),null}finally{delete r.current[e]}},[s,i]),l=(0,a.useCallback)(e=>({onMouseEnter:()=>{s(e)||o(e,"high")}}),[o,s]),d=(0,a.useCallback)(e=>{delete n[e],t(t=>{let r={...t};return delete r[e],r})},[]),c=(0,a.useCallback)(()=>{Object.keys(n).forEach(e=>{delete n[e]}),t({})},[]);return{prefetchRoutingSetupData:o,getCachedData:i,isCached:s,createHoverPrefetch:l,clearCache:d,clearAllCache:c,getStatus:(0,a.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,a.useCallback)(()=>({cachedConfigs:Object.keys(n),cacheSize:Object.keys(n).length,totalCacheAge:Object.values(n).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(n).length}),[]),prefetchStatus:e}}},62525:(e,t,r)=>{"use strict";r.d(t,{Dc:()=>n,p2:()=>a});let a=[{id:"general_chat",name:"General Chat",description:"Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives."},{id:"coding_frontend",name:"Coding - Frontend",description:"Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development."},{id:"coding_backend",name:"Coding - Backend",description:"Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture."},{id:"research_synthesis",name:"Research & Synthesis",description:"Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information."},{id:"translation_localization",name:"Translation & Localization",description:"Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance."},{id:"image_generation",name:"Image Generation",description:"Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing."},{id:"data_extractor",name:"Data Extractor",description:"Extracting specific data from web pages, scraping content, and gathering information from websites."},{id:"form_filler",name:"Form Filler",description:"Filling out web forms, submitting data, and handling form-based interactions on websites."},{id:"verification_agent",name:"Verification Agent",description:"Verifying information on websites, fact-checking, and validating data accuracy."},{id:"research_assistant",name:"Research Assistant",description:"Conducting web-based research, gathering information from multiple sources, and compiling research findings."},{id:"shopping_assistant",name:"Shopping Assistant",description:"Helping with online shopping, price comparisons, product research, and e-commerce tasks."},{id:"price_comparison",name:"Price Comparison",description:"Comparing prices across different websites, finding deals, and analyzing product pricing."},{id:"fact_checker",name:"Fact Checker",description:"Verifying facts and information across multiple web sources, cross-referencing data for accuracy."},{id:"task_executor",name:"Task Executor",description:"General task execution and automation, handling various web-based tasks and workflows."}],n=e=>a.find(t=>t.id===e)},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(43210);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:s="",children:i,iconNode:c,...u},m)=>(0,a.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:o("lucide",s),...!i&&!l(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let r=(0,a.forwardRef)(({className:r,...s},l)=>(0,a.createElement)(c,{ref:l,iconNode:t,className:o(`lucide-${n(i(e))}`,`lucide-${e}`,r),...s}));return r.displayName=i(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66368:(e,t,r)=>{"use strict";r.d(t,{MG:()=>a});let a=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},69662:(e,t)=>{var r;!function(){"use strict";var a={}.hasOwnProperty;function n(){for(var e="",t=0;t<arguments.length;t++){var r=arguments[t];r&&(e=s(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return n.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var r in e)a.call(e,r)&&e[r]&&(t=s(t,r));return t}(r)))}return e}function s(e,t){return t?e?e+" "+t:e+t:e}e.exports?(n.default=n,e.exports=n):void 0===(r=(function(){return n}).apply(t,[]))||(e.exports=r)}()},70143:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(43210);let n=a.forwardRef(function({title:e,titleId:t,...r},n){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86099:(e,t,r)=>{Promise.resolve().then(r.bind(r,20218))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98599:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>s});var a=r(43210);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,a=e.map(e=>{let a=n(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():n(e[t],null)}}}}function i(...e){return a.useCallback(s(...e),e)}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,5449,2535,4999,8769,4912,4847,4629],()=>r(51983));module.exports=a})();