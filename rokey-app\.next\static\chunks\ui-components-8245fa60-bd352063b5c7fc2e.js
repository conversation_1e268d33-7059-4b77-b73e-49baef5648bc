"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7069],{41324:(e,t,a)=>{a.d(t,{A:()=>u});var s=a(95155),r=a(12115),l=a(66766),n=a(44726),o=a(82347),i=a(75922),d=a(47225),c=a(74511);let m=i.MG.map(e=>({value:e.id,label:e.name}));function u(e){let{node:t,onUpdate:a,onClose:u}=e,[x,p]=(0,r.useState)(t.data.config),[g,h]=(0,r.useState)(null),[f,b]=(0,r.useState)(!1),[y,v]=(0,r.useState)(null),[j,N]=(0,r.useState)(null),[w,k]=(0,r.useState)(!1),[C,T]=(0,r.useState)(null),[I,A]=(0,r.useState)([]),[S,_]=(0,r.useState)(!1),[M,P]=(0,r.useState)(null),D=(0,r.useCallback)(async()=>{b(!0),v(null),h(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch models from database.");t.models?h(t.models):h([])}catch(e){v(e.message),h([])}finally{b(!1)}},[]),E=(0,r.useCallback)(async()=>{_(!0),P(null);try{let e=await fetch("/api/user/custom-roles");if(!e.ok)throw Error("Failed to fetch custom roles");let t=await e.json();A(t)}catch(e){P(e.message),A([])}finally{_(!1)}},[]);(0,r.useEffect)(()=>{("provider"===t.type||"vision"===t.type||"planner"===t.type)&&D(),"roleAgent"===t.type&&E()},[t.type,D,E]),(0,r.useEffect)(()=>{if(("provider"===t.type||"vision"===t.type||"planner"===t.type||"browsing"===t.type)&&g&&g.length>0){let e=i.MG.find(e=>e.id===x.providerId);if(e&&x.providerId&&!x.modelId){let s=[];if("openrouter"===e.id?s=g.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||"")):"deepseek"===e.id?(g.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&s.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),g.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&s.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"})):s=g.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||"")),s.length>0){let e=s[0].value,r=g.find(t=>t.id===e),l=(null==r?void 0:r.output_token_limit)||(null==r?void 0:r.context_window)||4096,n=Math.min(l,Math.max(1024,Math.floor(.75*l))),o=x.parameters||{},i={...x,modelId:e,parameters:{...o,maxTokens:o.maxTokens||n}};p(i),a({config:i,isConfigured:K(t.type,i)})}}}},[g,t.type,null==x?void 0:x.providerId]),(0,r.useEffect)(()=>{"tool"===t.type&&(null==x?void 0:x.toolType)&&(async()=>{try{let e=await fetch("/api/auth/tools/status?tool=".concat(x.toolType));if(e.ok){let s=await e.json();if(N(s),s.is_connected&&!x.isAuthenticated||!s.is_connected&&x.isAuthenticated){let e={...x,connectionStatus:s.is_connected?"connected":"disconnected",isAuthenticated:s.is_connected||!1,providerUserEmail:s.provider_user_email,providerUserName:s.provider_user_name};p(e),a({config:e,isConfigured:K(t.type,e)})}}}catch(e){}})()},[t.type,null==x?void 0:x.toolType,null==x?void 0:x.isAuthenticated]);let R=(e,s)=>{let r={...x,[e]:s};p(r),a({config:r,isConfigured:K(t.type,r)})},F=(e,s)=>{let r={...x,[e]:s};"parameters"!==e&&x.parameters||(r.parameters={maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0,...x.parameters,..."parameters"===e?s:{}}),p(r),a({config:r,isConfigured:K(t.type,r)})},O=(0,r.useMemo)(()=>{if(g&&("provider"===t.type||"vision"===t.type||"planner"===t.type||"browsing"===t.type)){let s=i.MG.find(e=>e.id===x.providerId);if(!s)return[];let r=e=>"vision"===t.type?e.filter(e=>e.modality&&(e.modality.includes("multimodal")||e.modality.includes("vision")||e.modality.includes("image"))):e;if("openrouter"===s.id)return r(g).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===s.id){var e,a;let s=[],r=g.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id);r&&("provider"===t.type||"planner"===t.type||"browsing"===t.type||"vision"===t.type&&(null==(e=r.modality)?void 0:e.includes("multimodal")))&&s.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"});let l=g.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id);return l&&("provider"===t.type||"planner"===t.type||"browsing"===t.type||"vision"===t.type&&(null==(a=l.modality)?void 0:a.includes("multimodal")))&&s.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),s.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return r(g.filter(e=>e.provider_id===s.id)).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[g,x,t.type]),q=(0,r.useMemo)(()=>{if(!g||"provider"!==t.type&&"vision"!==t.type&&"planner"!==t.type&&"browsing"!==t.type||!(null==x?void 0:x.modelId))return{maxTokens:4096,minTokens:1};let e=g.find(e=>e.id===x.modelId);return e?{maxTokens:e.output_token_limit||e.context_window||4096,minTokens:1}:{maxTokens:4096,minTokens:1}},[g,x,t.type]),K=(e,t)=>{switch(e){case"provider":case"vision":case"planner":return!!(t.providerId&&t.modelId&&t.apiKey);case"roleAgent":if("new"===t.roleType)return!!(t.newRoleName&&t.customPrompt);return!!(t.roleId&&t.roleName);case"centralRouter":return!!t.routingStrategy;case"tool":return!!t.toolType;case"browsing":return!!(t.providerId&&t.modelId);case"memory":return!!t.memoryName;default:return!0}},B=()=>{var e,r,l,o;return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,s.jsxs)("select",{value:(null==x?void 0:x.providerId)||"",onChange:e=>{let s={...x,providerId:e.target.value,modelId:"",parameters:x.parameters||{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,s.jsx)("option",{value:"",children:"Select Provider"}),m.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key *"}),(0,s.jsx)("input",{type:"password",value:(null==x?void 0:x.apiKey)||"",onChange:e=>F("apiKey",e.target.value),placeholder:"Enter your API key for this provider",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required: Enter your own API key for this AI provider (BYOK)"}),f&&null===g&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,s.jsx)(n.go,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),y&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error: ",y]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Model Variant"}),(0,s.jsx)("select",{value:(null==x?void 0:x.modelId)||"",onChange:e=>{let s=e.target.value,r={...x,modelId:s};if(s&&g){let e=g.find(e=>e.id===s);if(e){let t=e.output_token_limit||e.context_window||4096,a=Math.min(t,Math.max(1024,Math.floor(.75*t))),s=(null==x?void 0:x.parameters)||{};r={...r,parameters:{...s,maxTokens:a}}}}p(r),a({config:r,isConfigured:K(t.type,r)})},disabled:!(null==x?void 0:x.providerId)||!O.length,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30",children:(null==x?void 0:x.providerId)?O.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("option",{value:"",children:"Select Model"}),O.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,s.jsx)("option",{value:"",disabled:!0,children:f?"Loading models...":"No models available"}):(0,s.jsx)("option",{value:"",disabled:!0,children:"Select a provider first"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature",(0,s.jsx)("span",{className:"text-xs text-gray-400 ml-1",children:"(0.0 - 2.0)"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:(null==x||null==(e=x.parameters)?void 0:e.temperature)||1,onChange:e=>{let t=parseFloat(e.target.value);F("parameters",{...(null==x?void 0:x.parameters)||{},temperature:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:(null==x||null==(r=x.parameters)?void 0:r.temperature)||1,onChange:e=>{let t=Math.min(2,Math.max(0,parseFloat(e.target.value)||1));F("parameters",{...(null==x?void 0:x.parameters)||{},temperature:t})},className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"maxTokens",className:"block text-sm font-medium text-gray-300 mb-2",children:["Max Tokens",(0,s.jsxs)("span",{className:"text-xs text-gray-400 ml-1",children:["(",q.minTokens," - ",q.maxTokens.toLocaleString(),")"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("input",{type:"range",id:"maxTokens",min:q.minTokens,max:q.maxTokens,step:"1",value:(null==x||null==(l=x.parameters)?void 0:l.maxTokens)||q.maxTokens,onChange:e=>{let t=parseInt(e.target.value);F("parameters",{...(null==x?void 0:x.parameters)||{},maxTokens:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Minimal"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"number",min:q.minTokens,max:q.maxTokens,step:"1",value:(null==x||null==(o=x.parameters)?void 0:o.maxTokens)||q.maxTokens,onChange:e=>{let t=Math.min(q.maxTokens,Math.max(q.minTokens,parseInt(e.target.value)||q.maxTokens));F("parameters",{...(null==x?void 0:x.parameters)||{},maxTokens:t})},className:"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"}),(0,s.jsx)("button",{type:"button",onClick:()=>{F("parameters",{...(null==x?void 0:x.parameters)||{},maxTokens:q.maxTokens})},className:"text-xs text-orange-400 hover:text-orange-300 underline",children:"Max"})]}),(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Maximum"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more."})]})]}),(null==x?void 0:x.providerId)==="openrouter"&&(0,s.jsxs)("div",{className:"p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm text-blue-300 font-medium mb-1",children:"\uD83C\uDF10 OpenRouter"}),(0,s.jsx)("div",{className:"text-xs text-blue-200",children:"Access to 300+ models from multiple providers with a single API key."})]})]})},L=()=>{var e,r,l,o;return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,s.jsxs)("select",{value:(null==x?void 0:x.providerId)||"",onChange:e=>{let s={...x,providerId:e.target.value,modelId:"",parameters:x.parameters||{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,s.jsx)("option",{value:"",children:"Select Provider"}),m.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key *"}),(0,s.jsx)("input",{type:"password",value:(null==x?void 0:x.apiKey)||"",onChange:e=>F("apiKey",e.target.value),placeholder:"Enter your API key for this provider",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required: Enter your own API key for this AI provider (BYOK)"}),f&&null===g&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,s.jsx)(n.go,{className:"w-4 h-4 mr-2"}),"Fetching models..."]}),y&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error: ",y]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Vision Model",(0,s.jsx)("span",{className:"text-xs text-purple-400 ml-1",children:"(Multimodal Only)"})]}),(0,s.jsx)("select",{value:(null==x?void 0:x.modelId)||"",onChange:e=>{let s=e.target.value,r={...x,modelId:s};if(s&&g){let e=g.find(e=>e.id===s);if(e){let t=e.output_token_limit||e.context_window||4096,a=Math.min(t,Math.max(1024,Math.floor(.75*t))),s=(null==x?void 0:x.parameters)||{};r={...r,parameters:{...s,maxTokens:a}}}}p(r),a({config:r,isConfigured:K(t.type,r)})},disabled:!(null==x?void 0:x.providerId)||!O.length,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30",children:(null==x?void 0:x.providerId)?O.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("option",{value:"",children:"Select Vision Model"}),O.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,s.jsx)("option",{value:"",disabled:!0,children:f?"Loading models...":"No vision models available"}):(0,s.jsx)("option",{value:"",disabled:!0,children:"Select a provider first"})}),0===O.length&&(null==x?void 0:x.providerId)&&!f&&(0,s.jsx)("p",{className:"mt-2 text-xs text-yellow-400 bg-yellow-900/20 p-2 rounded-lg",children:"⚠️ No multimodal models found for this provider. Vision nodes require models with image processing capabilities."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:"Temperature (0.0 - 2.0)"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:(null==x||null==(e=x.parameters)?void 0:e.temperature)||1,onChange:e=>{let t=parseFloat(e.target.value);F("parameters",{...(null==x?void 0:x.parameters)||{},temperature:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:(null==x||null==(r=x.parameters)?void 0:r.temperature)||1,onChange:e=>{let t=Math.min(2,Math.max(0,parseFloat(e.target.value)||1));F("parameters",{...(null==x?void 0:x.parameters)||{},temperature:t})},className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"maxTokens",className:"block text-sm font-medium text-gray-300 mb-2",children:["Max Tokens",(0,s.jsxs)("span",{className:"text-xs text-gray-400 ml-1",children:["(",q.minTokens," - ",q.maxTokens.toLocaleString(),")"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("input",{type:"range",id:"maxTokens",min:q.minTokens,max:q.maxTokens,step:"1",value:(null==x||null==(l=x.parameters)?void 0:l.maxTokens)||q.maxTokens,onChange:e=>{let t=parseInt(e.target.value);F("parameters",{...(null==x?void 0:x.parameters)||{},maxTokens:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Minimal"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"number",min:q.minTokens,max:q.maxTokens,step:"1",value:(null==x||null==(o=x.parameters)?void 0:o.maxTokens)||q.maxTokens,onChange:e=>{let t=Math.min(q.maxTokens,Math.max(q.minTokens,parseInt(e.target.value)||q.maxTokens));F("parameters",{...(null==x?void 0:x.parameters)||{},maxTokens:t})},className:"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"}),(0,s.jsx)("button",{type:"button",onClick:()=>{F("parameters",{...(null==x?void 0:x.parameters)||{},maxTokens:q.maxTokens})},className:"text-xs text-orange-400 hover:text-orange-300 underline",children:"Max"})]}),(0,s.jsx)("span",{className:"text-xs text-gray-400",children:"Maximum"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Controls the maximum number of tokens the model can generate for vision analysis."})]})]}),(null==x?void 0:x.providerId)==="openrouter"&&(0,s.jsxs)("div",{className:"p-3 bg-purple-900/20 border border-purple-700/30 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm text-purple-300 font-medium mb-1",children:"\uD83D\uDC41️ Vision Models"}),(0,s.jsx)("div",{className:"text-xs text-purple-200",children:"Access to multimodal models from multiple providers for image analysis and vision tasks."})]})]})},z=()=>{let e=[...d.p2.map(e=>({id:e.id,name:e.name,description:e.description,type:"predefined"})),...I.map(e=>({id:e.role_id,name:e.name,description:e.description,type:"custom"}))],r=s=>{if("create_new"===s){let e={...x,roleType:"new",roleId:"",roleName:"",newRoleName:"",newRoleDescription:"",customPrompt:""};p(e),a({config:e,isConfigured:K(t.type,e)})}else{let r=e.find(e=>e.id===s);if(r){let e={...x,roleType:r.type,roleId:r.id,roleName:r.name,customPrompt:r.description||""};p(e),a({config:e,isConfigured:K(t.type,e)})}}},l=(e,s)=>{let r={...x,[e]:s};p(r),a({config:r,isConfigured:K(t.type,r)})};return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Role"}),S?(0,s.jsx)("div",{className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400",children:"Loading roles..."}):(0,s.jsxs)("select",{value:(null==x?void 0:x.roleType)==="new"?"create_new":(null==x?void 0:x.roleId)||"",onChange:e=>r(e.target.value),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,s.jsx)("option",{value:"",children:"Select a role..."}),(0,s.jsx)("optgroup",{label:"System Roles",children:d.p2.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))}),I.length>0&&(0,s.jsx)("optgroup",{label:"Your Custom Roles",children:I.map(e=>(0,s.jsx)("option",{value:e.role_id,children:e.name},e.role_id))}),(0,s.jsx)("optgroup",{label:"Create New",children:(0,s.jsx)("option",{value:"create_new",children:"+ Create New Role"})})]}),M&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error loading roles: ",M]})]}),(null==x?void 0:x.roleType)!=="new"&&(null==x?void 0:x.roleId)&&(0,s.jsxs)("div",{className:"p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-white mb-1",children:x.roleName}),x.customPrompt&&(0,s.jsx)("div",{className:"text-xs text-gray-300",children:x.customPrompt})]}),(null==x?void 0:x.roleType)==="new"&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"New Role Name"}),(0,s.jsx)("input",{type:"text",value:x.newRoleName||"",onChange:e=>l("newRoleName",e.target.value),placeholder:"e.g., Data Analyst, Creative Writer",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Role Description"}),(0,s.jsx)("input",{type:"text",value:x.newRoleDescription||"",onChange:e=>l("newRoleDescription",e.target.value),placeholder:"Brief description of this role's purpose",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions"}),(0,s.jsx)("textarea",{value:x.customPrompt||"",onChange:e=>l("customPrompt",e.target.value),placeholder:"Enter detailed instructions for this role...",rows:4,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:(null==x?void 0:x.memoryEnabled)||!1,onChange:e=>R("memoryEnabled",e.target.checked),className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"}),(0,s.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable memory"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1 ml-6",children:"Allow this role to remember context from previous interactions"})]})]})},U=()=>(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Label"}),(0,s.jsx)("input",{type:"text",value:t.data.label,onChange:e=>a({label:e.target.value}),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description"}),(0,s.jsx)("textarea",{value:t.data.description||"",onChange:e=>a({description:e.target.value}),rows:3,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]})]}),V=()=>{var e,r;return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Routing Strategy"}),(0,s.jsxs)("select",{value:(null==x?void 0:x.routingStrategy)||"smart",onChange:e=>{let s={...x,routingStrategy:e.target.value};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:[(0,s.jsx)("option",{value:"smart",children:"Smart Routing"}),(0,s.jsx)("option",{value:"round_robin",children:"Round Robin"}),(0,s.jsx)("option",{value:"load_balanced",children:"Load Balanced"}),(0,s.jsx)("option",{value:"priority",children:"Priority Based"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"How the router selects between available AI providers"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Retries"}),(0,s.jsx)("input",{type:"number",min:"0",max:"10",value:(null==x?void 0:x.maxRetries)||3,onChange:e=>{let s={...x,maxRetries:parseInt(e.target.value)||3};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Number of retry attempts on failure"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (ms)"}),(0,s.jsx)("input",{type:"number",min:"1000",max:"300000",step:"1000",value:(null==x?void 0:x.timeout)||3e4,onChange:e=>{let s={...x,timeout:parseInt(e.target.value)||3e4};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Request timeout in milliseconds"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Enable Caching"}),(0,s.jsx)("input",{type:"checkbox",checked:null==(e=null==x?void 0:x.enableCaching)||e,onChange:e=>{let s={...x,enableCaching:e.target.checked};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Cache responses to improve performance"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Debug Mode"}),(0,s.jsx)("input",{type:"checkbox",checked:null!=(r=null==x?void 0:x.debugMode)&&r,onChange:e=>{let s={...x,debugMode:e.target.checked};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:"Enable detailed logging for debugging"})]})]})},G=()=>{var e,i,d,m;let u=[{value:"",label:"Select a tool...",icon:null,description:""},...Object.keys(c.RW).filter(e=>"supabase"!==e).map(e=>({value:e,label:c.RW[e],icon:c.s0[e],description:c.r0[e]}))],g=async e=>{if(e)try{let t=await fetch("/api/auth/tools/status?tool=".concat(e));if(t.ok){let e=await t.json();N(e)}}catch(e){}},h=async e=>{if(T(null),!e){let s={...x,toolType:e,toolConfig:{},connectionStatus:"disconnected",isAuthenticated:!1};p(s),a({config:s,isConfigured:K(t.type,s)});return}try{let s=await fetch("/api/auth/tools/status?tool=".concat(e));if(s.ok){let r=await s.json();N(r);let l={...x,toolType:e,toolConfig:{},connectionStatus:r.is_connected?"connected":"disconnected",isAuthenticated:r.is_connected||!1,providerUserEmail:r.provider_user_email,providerUserName:r.provider_user_name};p(l),a({config:l,isConfigured:K(t.type,l)})}else{let s={...x,toolType:e,toolConfig:{},connectionStatus:"disconnected",isAuthenticated:!1};p(s),a({config:s,isConfigured:K(t.type,s)})}}catch(r){let s={...x,toolType:e,toolConfig:{},connectionStatus:"disconnected",isAuthenticated:!1};p(s),a({config:s,isConfigured:K(t.type,s)})}},f=async()=>{if(null==x?void 0:x.toolType){k(!0),T(null);try{let e="google";"notion"===x.toolType&&(e="notion");let s=await fetch("/api/auth/tools/".concat(e,"/authorize"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({toolType:x.toolType,returnUrl:window.location.pathname})});if(!s.ok)throw Error("Failed to initiate OAuth flow");let{authUrl:r}=await s.json(),l=window.open(r,"oauth-popup","width=600,height=700,scrollbars=yes,resizable=yes"),n=e=>{if(e.origin===window.location.origin)if("OAUTH_SUCCESS"===e.data.type){null==l||l.close(),g(x.toolType);let e={...x,connectionStatus:"connected",isAuthenticated:!0};p(e),a({config:e,isConfigured:K(t.type,e)}),k(!1),window.removeEventListener("message",n)}else"OAUTH_ERROR"===e.data.type&&(null==l||l.close(),T(e.data.error||"Authentication failed"),k(!1),window.removeEventListener("message",n))};window.addEventListener("message",n);let o=setInterval(()=>{(null==l?void 0:l.closed)&&(clearInterval(o),window.removeEventListener("message",n),k(!1),setTimeout(()=>g(x.toolType),1e3))},1e3)}catch(e){T(e instanceof Error?e.message:"Failed to link account"),k(!1)}}},b=async()=>{if(null==x?void 0:x.toolType)try{(await fetch("/api/auth/tools/status?tool=".concat(x.toolType),{method:"DELETE"})).ok&&(N(null),g(x.toolType))}catch(e){T("Failed to disconnect tool")}};return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Tool Type"}),(0,s.jsx)(o.W,{value:(null==x?void 0:x.toolType)||"",onChange:h,children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(o.W.Button,{className:"relative w-full cursor-pointer rounded-lg bg-gray-700 border border-gray-600 py-2 pl-3 pr-10 text-left text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(null==x?void 0:x.toolType)&&(null==(e=u.find(e=>e.value===x.toolType))?void 0:e.icon)?(0,s.jsx)(l.default,{src:(null==(i=u.find(e=>e.value===x.toolType))?void 0:i.icon)||"",alt:"",width:20,height:20,className:"mr-3 h-5 w-5 flex-shrink-0 rounded-sm"}):null,(0,s.jsx)("span",{className:"block truncate",children:(null==(d=u.find(e=>e.value===((null==x?void 0:x.toolType)||"")))?void 0:d.label)||"Select a tool..."})]}),(0,s.jsx)("span",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2",children:(0,s.jsx)(n.yW,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),(0,s.jsx)(o.e,{as:r.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,s.jsx)(o.W.Options,{className:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-gray-700 border border-gray-600 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none",children:u.map(e=>(0,s.jsx)(o.W.Option,{className:e=>{let{active:t}=e;return"relative cursor-pointer select-none py-2 pl-3 pr-9 ".concat(t?"bg-[#ff6b35] text-white":"text-gray-300")},value:e.value,children:t=>{let{selected:a,active:r}=t;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center",children:[e.icon?(0,s.jsx)(l.default,{src:e.icon,alt:"",width:20,height:20,className:"mr-3 h-5 w-5 flex-shrink-0 rounded-sm"}):(0,s.jsx)("div",{className:"mr-3 h-5 w-5 flex-shrink-0"}),(0,s.jsx)("span",{className:"block truncate ".concat(a?"font-medium":"font-normal"),children:e.label})]}),a?(0,s.jsx)("span",{className:"absolute inset-y-0 right-0 flex items-center pr-4 ".concat(r?"text-white":"text-[#ff6b35]"),children:(0,s.jsx)(n.Sr,{className:"h-5 w-5","aria-hidden":"true"})}):null]})}},e.value))})})]})}),(null==x?void 0:x.toolType)&&(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:null==(m=u.find(e=>e.value===x.toolType))?void 0:m.description})]}),(null==x?void 0:x.toolType)&&j&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-700 rounded-md",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[j.icon&&j.icon.startsWith("http")?(0,s.jsx)(l.default,{src:j.icon,alt:j.display_name,width:20,height:20,className:"rounded-sm"}):(0,s.jsx)("span",{className:"text-lg",children:j.icon||"\uD83D\uDD27"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-white",children:j.display_name}),j.provider_user_email&&(0,s.jsx)("div",{className:"text-xs text-gray-400",children:j.provider_user_email})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[j.is_connected?(0,s.jsx)(n.C1,{className:"h-5 w-5 text-green-400"}):(0,s.jsx)(n.Pi,{className:"h-5 w-5 text-yellow-400"}),(0,s.jsx)("span",{className:"text-xs ".concat(j.is_connected?"text-green-400":"text-yellow-400"),children:j.is_connected?"Connected":"Not Connected"})]})]}),(0,s.jsx)("div",{className:"flex gap-2",children:j.is_connected?(0,s.jsxs)("button",{onClick:b,className:"flex-1 px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors flex items-center justify-center gap-2",children:[(0,s.jsx)(n.Pi,{className:"h-4 w-4"}),"Disconnect"]}):(0,s.jsxs)("button",{onClick:f,disabled:w,className:"flex-1 px-3 py-2 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 text-white text-sm rounded-md transition-colors flex items-center justify-center gap-2",children:[(0,s.jsx)(n.qY,{className:"h-4 w-4"}),w?"Connecting...":"Link Account"]})})]}),(null==x?void 0:x.toolType)&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (seconds)"}),(0,s.jsx)("input",{type:"number",min:"5",max:"300",value:(null==x?void 0:x.timeout)||30,onChange:e=>{let s={...x,timeout:parseInt(e.target.value)||30};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum time to wait for the tool operation to complete"})]}),C&&(0,s.jsx)("div",{className:"p-3 bg-red-900/20 border border-red-500/20 rounded-md",children:(0,s.jsx)("p",{className:"text-sm text-red-400",children:C})})]})},W=()=>{var e,r,l,n;return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,s.jsxs)("select",{value:(null==x?void 0:x.providerId)||"",onChange:e=>{let s={...x,providerId:e.target.value,modelId:"",parameters:x.parameters||{temperature:.7,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:[(0,s.jsx)("option",{value:"",children:"Select Provider"}),(0,s.jsx)("option",{value:"openai",children:"OpenAI"}),(0,s.jsx)("option",{value:"anthropic",children:"Anthropic"}),(0,s.jsx)("option",{value:"google",children:"Google"}),(0,s.jsx)("option",{value:"deepseek",children:"DeepSeek"}),(0,s.jsx)("option",{value:"xai",children:"xAI"}),(0,s.jsx)("option",{value:"openrouter",children:"OpenRouter"})]})]}),(null==x?void 0:x.providerId)&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Model"}),(0,s.jsx)("select",{value:(null==x?void 0:x.modelId)||"",onChange:e=>{let s=e.target.value,r={...x,modelId:s};if(s&&g){let e=g.find(e=>e.id===s);if(e){let t=e.output_token_limit||e.context_window||4096,a=Math.min(t,Math.max(1024,Math.floor(.75*t))),s=(null==x?void 0:x.parameters)||{};r={...r,parameters:{...s,maxTokens:a}}}}p(r),a({config:r,isConfigured:K(t.type,r)})},disabled:!(null==x?void 0:x.providerId)||!O.length,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent disabled:opacity-50 disabled:bg-gray-800/30",children:(null==x?void 0:x.providerId)?O.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("option",{value:"",children:"Select Model"}),O.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,s.jsx)("option",{value:"",disabled:!0,children:f?"Loading models...":"No models available"}):(0,s.jsx)("option",{value:"",disabled:!0,children:"Select a provider first"})}),f&&(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Loading models..."}),y&&(0,s.jsx)("p",{className:"text-xs text-red-400 mt-1",children:y})]}),(null==x?void 0:x.modelId)&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key *"}),(0,s.jsx)("input",{type:"password",value:(null==x?void 0:x.apiKey)||"",onChange:e=>{let s={...x,apiKey:e.target.value};p(s),a({config:s,isConfigured:K(t.type,s)})},placeholder:"Enter your API key for this provider",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required: Enter your own API key for this AI provider (BYOK)"})]}),(null==x?void 0:x.modelId)&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Max Tokens: ",(null==x||null==(e=x.parameters)?void 0:e.maxTokens)||"Auto"]}),(0,s.jsx)("input",{type:"range",min:q.minTokens,max:q.maxTokens,value:(null==x||null==(r=x.parameters)?void 0:r.maxTokens)||q.maxTokens,onChange:e=>{let s={...x,parameters:{...x.parameters,maxTokens:parseInt(e.target.value)}};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,s.jsx)("span",{children:q.minTokens}),(0,s.jsx)("span",{children:q.maxTokens})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature: ",(null==x||null==(l=x.parameters)?void 0:l.temperature)||.7]}),(0,s.jsx)("input",{type:"range",min:"0",max:"2",step:"0.1",value:(null==x||null==(n=x.parameters)?void 0:n.temperature)||.7,onChange:e=>{let s={...x,parameters:{...x.parameters,temperature:parseFloat(e.target.value)}};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,s.jsx)("span",{children:"0 (Focused)"}),(0,s.jsx)("span",{children:"2 (Creative)"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Subtasks"}),(0,s.jsx)("input",{type:"number",min:"1",max:"50",value:(null==x?void 0:x.maxSubtasks)||10,onChange:e=>{let s={...x,maxSubtasks:parseInt(e.target.value)||10};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum number of subtasks the planner can create"})]})]})},H=()=>{var e,r,l,o,i,d,c,u,h,b,v,j,N,w,k;return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"p-4 bg-green-900/20 border border-green-700 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("span",{className:"text-green-400",children:"\uD83C\uDF10"}),(0,s.jsx)("span",{className:"text-sm font-medium text-green-400",children:"Intelligent Browsing Agent"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-300",children:"Autonomous web browsing with dedicated AI provider configuration."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"AI Provider *"}),(0,s.jsxs)("select",{value:(null==x?void 0:x.providerId)||"",onChange:e=>{let s={...x,providerId:e.target.value,modelId:"",parameters:(null==x?void 0:x.parameters)||{temperature:.7,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"",children:"Select Provider"}),m.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]}),f&&null===g&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,s.jsx)(n.go,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),y&&(0,s.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error: ",y]})]}),(null==x?void 0:x.providerId)&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Model *"}),(0,s.jsxs)("select",{value:(null==x?void 0:x.modelId)||"",onChange:e=>{let s=e.target.value,r={...x,modelId:s};if(s&&g){let e=g.find(e=>e.id===s);if(e){let t=e.output_token_limit||e.context_window||4096,a=Math.min(t,Math.max(1024,Math.floor(.75*t))),s=(null==x?void 0:x.parameters)||{};r={...r,parameters:{...s,maxTokens:a}}}}p(r),a({config:r,isConfigured:K(t.type,r)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"",children:"Select Model"}),O.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key (Optional)"}),(0,s.jsx)("input",{type:"password",value:(null==x?void 0:x.apiKey)||"",onChange:e=>{let t={...x,apiKey:e.target.value};p(t),a({config:t,isConfigured:!!((null==x?void 0:x.providerId)&&(null==x?void 0:x.modelId))})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Leave empty to use configured keys"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Optional: Override the configured API key for this browsing node"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-300",children:"AI Parameters"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:["Temperature: ",(null==x||null==(e=x.parameters)?void 0:e.temperature)||.7]}),(0,s.jsx)("input",{type:"range",min:"0",max:"1",step:"0.1",value:(null==x||null==(r=x.parameters)?void 0:r.temperature)||.7,onChange:e=>{let s={...x,parameters:{...null==x?void 0:x.parameters,temperature:parseFloat(e.target.value)}};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Max Tokens"}),(0,s.jsx)("input",{type:"number",min:q.minTokens,max:q.maxTokens,value:(null==x||null==(l=x.parameters)?void 0:l.maxTokens)||1e3,onChange:e=>{let s={...x,parameters:{...null==x?void 0:x.parameters,maxTokens:parseInt(e.target.value)||1e3}};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Range: ",q.minTokens," - ",q.maxTokens," tokens"]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Sites to Visit"}),(0,s.jsx)("input",{type:"number",min:"1",max:"20",value:(null==x?void 0:x.maxSites)||5,onChange:e=>{let s={...x,maxSites:parseInt(e.target.value)||5};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout per Operation (seconds)"}),(0,s.jsx)("input",{type:"number",min:"10",max:"300",value:(null==x?void 0:x.timeout)||30,onChange:e=>{let s={...x,timeout:parseInt(e.target.value)||30};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-white border-b border-gray-700 pb-2",children:"Basic Settings"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Sites"}),(0,s.jsx)("input",{type:"number",min:"1",max:"20",value:(null==x?void 0:x.maxSites)||5,onChange:e=>{let s={...x,maxSites:parseInt(e.target.value)};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (seconds)"}),(0,s.jsx)("input",{type:"number",min:"10",max:"300",value:(null==x?void 0:x.timeout)||30,onChange:e=>{let s={...x,timeout:parseInt(e.target.value)};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Search Engines"}),(0,s.jsx)("div",{className:"flex gap-2",children:["google","bing"].map(e=>{var r,l;return(0,s.jsxs)("label",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null!=(l=null==x||null==(r=x.searchEngines)?void 0:r.includes(e))?l:"google"===e,onChange:s=>{let r=(null==x?void 0:x.searchEngines)||["google"],l=s.target.checked?[...r.filter(t=>t!==e),e]:r.filter(t=>t!==e),n={...x,searchEngines:l.length>0?l:["google"]};p(n),a({config:n,isConfigured:K(t.type,n)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300 capitalize",children:e})]},e)})})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-white border-b border-gray-700 pb-2",children:"Capabilities"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(c=null==x?void 0:x.enableScreenshots)||c,onChange:e=>{let s={...x,enableScreenshots:e.target.checked};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDCF8 Take Screenshots"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(u=null==x?void 0:x.enableFormFilling)||u,onChange:e=>{let s={...x,enableFormFilling:e.target.checked};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDCDD Fill Forms Automatically"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null!=(h=null==x?void 0:x.enableCaptchaSolving)&&h,onChange:e=>{let s={...x,enableCaptchaSolving:e.target.checked};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDD10 Solve CAPTCHAs"}),(0,s.jsx)("span",{className:"text-xs text-yellow-400",children:"(Beta)"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(b=null==x?void 0:x.enableJavaScript)||b,onChange:e=>{let s={...x,enableJavaScript:e.target.checked};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"⚡ Enable JavaScript"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(v=null==x?void 0:x.respectRobots)||v,onChange:e=>{let s={...x,respectRobots:e.target.checked};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83E\uDD16 Respect robots.txt"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold text-white border-b border-gray-700 pb-2",children:"Advanced Settings"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Browsing Depth"}),(0,s.jsx)("input",{type:"number",min:"1",max:"5",value:(null==x?void 0:x.maxDepth)||2,onChange:e=>{let s={...x,maxDepth:parseInt(e.target.value)};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"How many levels deep to follow links (1-5)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom User Agent"}),(0,s.jsx)("input",{type:"text",value:(null==x?void 0:x.userAgent)||"",onChange:e=>{let s={...x,userAgent:e.target.value};p(s),a({config:s,isConfigured:K(t.type,s)})},placeholder:"Leave empty for default",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Custom user agent string (optional)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Extraction Goals"}),(0,s.jsx)("textarea",{value:(null==x||null==(o=x.extractionGoals)?void 0:o.join(", "))||"",onChange:e=>{let s=e.target.value.split(",").map(e=>e.trim()).filter(e=>e),r={...x,extractionGoals:s};p(r),a({config:r,isConfigured:K(t.type,r)})},placeholder:"prices, contact info, products, links",rows:2,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Comma-separated list of what to extract (e.g., prices, contact, products)"})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(j=null==x?void 0:x.enableFormFilling)||j,onChange:e=>{let s={...x,enableFormFilling:e.target.checked};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDCDD Fill Forms Automatically"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null!=(N=null==x?void 0:x.enableCaptchaSolving)&&N,onChange:e=>{let s={...x,enableCaptchaSolving:e.target.checked};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDD10 Attempt CAPTCHA Solving"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Search Engines"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null==(w=null==x||null==(i=x.searchEngines)?void 0:i.includes("google"))||w,onChange:e=>{let s=(null==x?void 0:x.searchEngines)||["google"],r=e.target.checked?[...s.filter(e=>"google"!==e),"google"]:s.filter(e=>"google"!==e),l={...x,searchEngines:r.length>0?r:["google"]};p(l),a({config:l,isConfigured:K(t.type,l)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"Google"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:null!=(k=null==x||null==(d=x.searchEngines)?void 0:d.includes("bing"))&&k,onChange:e=>{let s=(null==x?void 0:x.searchEngines)||["google"],r=e.target.checked?[...s.filter(e=>"bing"!==e),"bing"]:s.filter(e=>"bing"!==e),l={...x,searchEngines:r.length>0?r:["google"]};p(l),a({config:l,isConfigured:K(t.type,l)})},className:"rounded"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"Bing"})]})]})]})]})},Y=()=>(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"bg-blue-900/20 border border-blue-500/30 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),(0,s.jsx)("h3",{className:"text-sm font-medium text-blue-300",children:"Plug & Play Memory"})]}),(0,s.jsx)("p",{className:"text-xs text-blue-200/80",children:"This memory node automatically acts as a brain for any connected node. It handles storing, retrieving, session data, and persistent memory intelligently without manual configuration."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Memory Name *"}),(0,s.jsx)("input",{type:"text",value:(null==x?void 0:x.memoryName)||"",onChange:e=>{let s={...x,memoryName:e.target.value};p(s),a({config:s,isConfigured:K(t.type,s)})},placeholder:"Enter a name for this memory (e.g., Browsing Memory, Router Memory)",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",required:!0}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Give this memory a descriptive name for easy identification"})]}),(null==x?void 0:x.memoryName)&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Storage Size (MB)"}),(0,s.jsx)("input",{type:"number",min:"1",max:"100",value:Math.round(((null==x?void 0:x.maxSize)||10240)/1024),onChange:e=>{let s=parseInt(e.target.value)||10,r={...x,maxSize:1024*s};p(r),a({config:r,isConfigured:K(t.type,r)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum storage size limit (default: 10MB)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:(null==x?void 0:x.encryption)!==!1,onChange:e=>{let s={...x,encryption:e.target.checked};p(s),a({config:s,isConfigured:K(t.type,s)})},className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"}),(0,s.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable encryption (recommended)"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1 ml-6",children:"Encrypt stored data for security (enabled by default)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description (Optional)"}),(0,s.jsx)("textarea",{value:(null==x?void 0:x.description)||"",onChange:e=>{let s={...x,description:e.target.value};p(s),a({config:s,isConfigured:K(t.type,s)})},placeholder:"Describe what this memory will be used for...",rows:2,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent resize-none"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Optional description of this memory's purpose"})]})]})]});return(0,s.jsxs)("div",{className:"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 bg-[#ff6b35]/20 rounded-lg",children:(0,s.jsx)(n.Vy,{className:"w-5 h-5 text-[#ff6b35]"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Configure Node"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:t.data.label})]})]}),(0,s.jsx)("button",{onClick:u,className:"text-gray-400 hover:text-white transition-colors p-1 rounded",children:(0,s.jsx)(n.fK,{className:"w-5 h-5"})})]}),(0,s.jsx)("div",{className:"space-y-6",children:(()=>{switch(t.type){case"provider":return B();case"vision":return L();case"roleAgent":return z();case"centralRouter":return V();case"tool":return G();case"planner":return W();case"browsing":return H();case"memory":return Y();default:return U()}})()}),(0,s.jsxs)("div",{className:"mt-6 p-3 rounded-lg border border-gray-700/50",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(t.data.isConfigured?"bg-green-500":"bg-yellow-500")}),(0,s.jsx)("span",{className:"text-sm font-medium text-white",children:t.data.isConfigured?"Configured":"Needs Configuration"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-400",children:t.data.isConfigured?"This node is properly configured and ready to use.":"Complete the configuration to use this node in your workflow."})]})]})}},75521:(e,t,a)=>{a.d(t,{A:()=>d});var s=a(95155),r=a(12115),l=a(76514);let n={core:{label:"Core Nodes",description:"Essential workflow components",nodes:[{type:"userRequest",label:"User Request",description:"Starting point for user input",icon:l.ny,category:"core",isAvailable:!0,defaultData:{label:"User Request",config:{},isConfigured:!0}},{type:"classifier",label:"Classifier",description:"Analyzes and categorizes requests",icon:l.YE,category:"core",isAvailable:!0,defaultData:{label:"Classifier",config:{},isConfigured:!0}},{type:"output",label:"Output",description:"Final response to user",icon:l.AQ,category:"core",isAvailable:!0,defaultData:{label:"Output",config:{},isConfigured:!0}}]},ai:{label:"AI Providers",description:"AI model integrations",nodes:[{type:"provider",label:"AI Provider",description:"Connect to AI models (OpenAI, Claude, etc.)",icon:l.hp,category:"ai",isAvailable:!0,defaultData:{label:"AI Provider",config:{providerId:"",modelId:"",apiKey:"",parameters:{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}},isConfigured:!1}},{type:"vision",label:"Vision AI",description:"Multimodal AI for image analysis and vision tasks",icon:l.bM,category:"ai",isAvailable:!0,defaultData:{label:"Vision AI",config:{providerId:"",modelId:"",apiKey:"",parameters:{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}},isConfigured:!1}},{type:"roleAgent",label:"Role Agent",description:"Role plugin for AI providers (connect to role input)",icon:l.K6,category:"ai",isAvailable:!0,defaultData:{label:"Role Agent",config:{roleId:"",roleName:"",roleType:"predefined",customPrompt:"",memoryEnabled:!1},isConfigured:!1}},{type:"centralRouter",label:"Central Router",description:"Smart routing hub for multiple AI providers and vision models",icon:l.YE,category:"ai",isAvailable:!0,defaultData:{label:"Central Router",config:{routingStrategy:"smart",fallbackProvider:"",maxRetries:3,timeout:3e4,enableCaching:!0,debugMode:!1},isConfigured:!0}},{type:"planner",label:"Planner",description:"AI model that creates browsing strategies and todo lists",icon:l.Pp,category:"ai",isAvailable:!0,defaultData:{label:"Planner",config:{providerId:"",modelId:"",apiKey:"",parameters:{temperature:.7,maxTokens:1e3},maxSubtasks:10},isConfigured:!1}}]},tools:{label:"Tools & Integrations",description:"External service integrations",nodes:[{type:"tool",label:"Tools",description:"External tool integrations (Google Drive, Zapier, etc.)",icon:l.jO,category:"tools",isAvailable:!0,defaultData:{label:"Tools",config:{toolType:"",toolConfig:{},timeout:30,connectionStatus:"disconnected",isAuthenticated:!1},isConfigured:!1}},{type:"memory",label:"Memory",description:"Store and retrieve data across workflow executions",icon:l.OL,category:"advanced",isAvailable:!0,defaultData:{label:"Memory",config:{memoryName:"",maxSize:10240,encryption:!0,description:""},isConfigured:!1}}]},browsing:{label:"Web Browsing",description:"Intelligent web browsing and automation",nodes:[{type:"browsing",label:"Browsing Agent",description:"Intelligent web browsing agent with multi-step automation",icon:l.mS,category:"advanced",isAvailable:!0,defaultData:{label:"Browsing Agent",config:{providerId:"",modelId:"",parameters:{temperature:.7,maxTokens:1e3,topP:1,frequencyPenalty:0,presencePenalty:0},maxSites:5,timeout:30,enableScreenshots:!0,enableFormFilling:!0,enableCaptchaSolving:!1,searchEngines:["google"],maxDepth:2,respectRobots:!0,enableJavaScript:!0},isConfigured:!1}}]}};function o(e){let{node:t,onAddNode:a}=e,r=t.icon;return(0,s.jsx)("div",{draggable:!0,onDragStart:e=>{e.dataTransfer.setData("application/reactflow",t.type),e.dataTransfer.effectAllowed="move"},onClick:()=>{a(t.type)},className:"p-3 rounded-lg border cursor-pointer transition-all duration-200 ".concat(t.isAvailable?"bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50":"bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed"),title:t.description,children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-lg ".concat(t.isAvailable?"bg-[#ff6b35]/20 text-[#ff6b35]":"bg-gray-700/50 text-gray-500"),children:(0,s.jsx)(r,{className:"w-4 h-4"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"font-medium text-sm ".concat(t.isAvailable?"text-white":"text-gray-500"),children:t.label}),(0,s.jsx)("div",{className:"text-xs text-gray-400 truncate",children:t.description})]})]})})}function i(e){let{category:t,data:a,isExpanded:r,onToggle:n,onAddNode:i}=e;return(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("button",{onClick:n,className:"w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[r?(0,s.jsx)(l.D3,{className:"w-4 h-4 text-gray-400"}):(0,s.jsx)(l.vK,{className:"w-4 h-4 text-gray-400"}),(0,s.jsx)("span",{className:"font-medium text-white",children:a.label})]}),(0,s.jsx)("span",{className:"text-xs text-gray-400",children:a.nodes.length})]}),r&&(0,s.jsx)("div",{className:"mt-2 space-y-2",children:a.nodes.map(e=>(0,s.jsx)(o,{node:e,onAddNode:i},e.type))})]})}function d(e){let{onAddNode:t}=e,[a,l]=(0,r.useState)(new Set(["core","ai"])),o=e=>{let t=new Set(a);t.has(e)?t.delete(e):t.add(e),l(t)},d=e=>{t(e,{x:400,y:200})};return(0,s.jsxs)("div",{className:"w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-white mb-2",children:"Node Palette"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:"Drag nodes to the canvas or click to add at center"})]}),(0,s.jsx)("div",{className:"space-y-1",children:Object.entries(n).filter(e=>{let[t]=e;return"browsing"!==t}).map(e=>{let[t,r]=e;return(0,s.jsx)(i,{category:t,data:r,isExpanded:a.has(t),onToggle:()=>o(t),onAddNode:d},t)})}),(0,s.jsxs)("div",{className:"mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm text-blue-300 font-medium mb-1",children:"\uD83D\uDCA1 Pro Tip"}),(0,s.jsx)("div",{className:"text-xs text-blue-200",children:"Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node."})]})]})}}}]);