(()=>{var e={};e.id=8700,e.ids=[1489,8700],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{H:()=>u,Q:()=>o,createSupabaseServerClientOnRequest:()=>n});var s=r(34386),a=r(39398),i=r(44999);async function n(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function o(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}function u(){return(0,a.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32801:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var s=r(56534);class a{static{this.KEY_PREFIX="rk_live_"}static{this.RANDOM_PART_LENGTH=8}static{this.SECRET_PART_LENGTH=32}static async generateApiKey(){let e=Array.from(crypto.getRandomValues(new Uint8Array(this.RANDOM_PART_LENGTH/2)),e=>e.toString(16).padStart(2,"0")).join(""),t=this.generateRandomString(this.SECRET_PART_LENGTH),r=`${this.KEY_PREFIX}${e}`,s=`${r}_${t}`,a=await this.hashApiKey(s);return{fullKey:s,prefix:r,secretPart:t,hash:a}}static generateRandomString(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="";for(let s=0;s<e;s++){let e=crypto.getRandomValues(new Uint8Array(1))[0]%t.length;r+=t[e]}return r}static async hashApiKey(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t)),e=>e.toString(16).padStart(2,"0")).join("")}static isValidFormat(e){return RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`).test(e)}static extractPrefix(e){let t=e.split("_");return t.length>=3?`${t[0]}_${t[1]}_${t[2]}`:""}static async encryptSuffix(e){let t=e.slice(-4);return await (0,s.w)(t)}static async decryptSuffix(e){try{return await (0,s.Y)(e)}catch(e){return"xxxx"}}static async createMaskedKey(e,t){let r=await this.decryptSuffix(t),s=this.SECRET_PART_LENGTH-4;return`${e}_${"*".repeat(s)}${r}`}static validateSubscriptionLimits(e,t){let r={free:3,starter:50,professional:999999,enterprise:999999},s=r[e]||r.free;return t>=s?{allowed:!1,limit:s,message:`You have reached the maximum number of user-generated API keys (${s}) for your ${e} plan.`}:{allowed:!0,limit:s}}}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,t,r)=>{"use strict";r.d(t,{Y:()=>p,w:()=>u});let s="AES-GCM",a=process.env.ROKEY_ENCRYPTION_KEY;if(!a||64!==a.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");function i(e){let t=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2)t[r/2]=parseInt(e.substr(r,2),16);return t}function n(e){return Array.from(e,e=>e.toString(16).padStart(2,"0")).join("")}let o=i(a);async function u(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=crypto.getRandomValues(new Uint8Array(12)),r=await crypto.subtle.importKey("raw",o,{name:s},!1,["encrypt"]),a=new TextEncoder().encode(e),i=new Uint8Array(await crypto.subtle.encrypt({name:s,iv:t},r,a)),u=i.slice(0,-16),p=i.slice(-16);return`${n(t)}:${n(p)}:${n(u)}`}async function p(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let r=i(t[0]),a=i(t[1]),n=i(t[2]);if(12!==r.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==a.length)throw Error("Invalid authTag length. Expected 16 bytes.");let u=await crypto.subtle.importKey("raw",o,{name:s},!1,["decrypt"]),p=new Uint8Array(n.length+a.length);p.set(n),p.set(a,n.length);let c=await crypto.subtle.decrypt({name:s,iv:r},u,p);return new TextDecoder().decode(c)}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73877:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>f,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{DELETE:()=>_,GET:()=>l,PATCH:()=>y});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(2507),p=r(32801),c=r(45697);let d=c.z.object({key_name:c.z.string().min(1).max(100).optional(),status:c.z.enum(["active","inactive"]).optional(),expires_at:c.z.string().datetime().nullable().optional()});async function l(e,{params:t}){let r=(0,u.Q)(e),{keyId:s}=await t;try{let{data:{user:e},error:t}=await r.auth.getUser();if(t||!e)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{data:a,error:i}=await r.from("user_generated_api_keys").select(`
        id,
        key_name,
        key_prefix,
        encrypted_key_suffix,
        permissions,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        last_used_ip,
        status,
        expires_at,
        created_at,
        updated_at,
        custom_api_configs!inner(
          id,
          name
        )
      `).eq("id",s).eq("user_id",e.id).single();if(i||!a)return o.NextResponse.json({error:"API key not found"},{status:404});let n={...a,masked_key:await p.F.createMaskedKey(a.key_prefix,a.encrypted_key_suffix),encrypted_key_suffix:void 0};return o.NextResponse.json({api_key:n})}catch(e){return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e,{params:t}){let r=(0,u.Q)(e),{keyId:s}=await t;try{let{data:{user:t},error:a}=await r.auth.getUser();if(a||!t)return o.NextResponse.json({error:"Unauthorized"},{status:401});let i=await e.json(),n=d.parse(i),{data:u,error:c}=await r.from("user_generated_api_keys").select("id, user_id, custom_api_config_id").eq("id",s).eq("user_id",t.id).single();if(c||!u)return o.NextResponse.json({error:"API key not found or access denied"},{status:404});if(n.key_name){let{data:e}=await r.from("user_generated_api_keys").select("id").eq("custom_api_config_id",u.custom_api_config_id).eq("key_name",n.key_name).eq("status","active").neq("id",s).single();if(e)return o.NextResponse.json({error:"An API key with this name already exists for this configuration"},{status:409})}let{data:l,error:y}=await r.from("user_generated_api_keys").update({...n,updated_at:new Date().toISOString()}).eq("id",s).eq("user_id",t.id).select(`
        id,
        key_name,
        key_prefix,
        encrypted_key_suffix,
        permissions,
        allowed_ips,
        allowed_domains,
        total_requests,
        last_used_at,
        status,
        expires_at,
        created_at,
        updated_at
      `).single();if(y)return o.NextResponse.json({error:"Failed to update API key"},{status:500});let _={...l,masked_key:await p.F.createMaskedKey(l.key_prefix,l.encrypted_key_suffix),encrypted_key_suffix:void 0};return o.NextResponse.json({api_key:_})}catch(e){if(e instanceof c.z.ZodError)return o.NextResponse.json({error:"Invalid request data",details:e.errors},{status:400});return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function _(e,{params:t}){let r=(0,u.Q)(e),{keyId:s}=await t;try{let{data:{user:e},error:t}=await r.auth.getUser();if(t||!e)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{data:a,error:i}=await r.from("user_generated_api_keys").select("id, user_id, key_name").eq("id",s).eq("user_id",e.id).single();if(i||!a)return o.NextResponse.json({error:"API key not found or access denied"},{status:404});let{error:n}=await r.from("user_generated_api_keys").update({status:"revoked",updated_at:new Date().toISOString()}).eq("id",s).eq("user_id",e.id);if(n)return o.NextResponse.json({error:"Failed to revoke API key"},{status:500});return o.NextResponse.json({message:"API key revoked successfully"},{status:200})}catch(e){return o.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/user-api-keys/[keyId]/route",pathname:"/api/user-api-keys/[keyId]",filename:"route",bundlePath:"app/api/user-api-keys/[keyId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\user-api-keys\\[keyId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:x,serverHooks:m}=f;function g(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:x})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410,5697],()=>r(73877));module.exports=s})();