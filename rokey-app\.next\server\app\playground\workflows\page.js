(()=>{var e={};e.id=9675,e.ids=[9675],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24245:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(43210);let o=s.forwardRef(function({title:e,titleId:t,...r},o){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"}))})},25165:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),o=r(48088),n=r(88170),a=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["playground",{children:["workflows",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74671)),"C:\\RoKey App\\rokey-app\\src\\app\\playground\\workflows\\page.tsx"]}]},{}]},{loading:[()=>Promise.resolve().then(r.bind(r,45807)),"C:\\RoKey App\\rokey-app\\src\\app\\playground\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\playground\\workflows\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/playground/workflows/page",pathname:"/playground/workflows",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32424:(e,t,r)=>{Promise.resolve().then(r.bind(r,74671))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},45807:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(37413),o=r(47417);function n(){return(0,s.jsx)("div",{className:"h-screen flex bg-[#040716]",children:(0,s.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,s.jsxs)("div",{className:"bg-[#040716]/95 backdrop-blur-sm border-b border-gray-800/50 p-4 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"animate-pulse bg-gray-700 h-8 w-32 rounded"}),(0,s.jsx)(o.ConfigSelectorSkeleton,{})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"animate-pulse bg-gray-700 h-8 w-20 rounded"}),(0,s.jsx)("div",{className:"animate-pulse bg-gray-700 h-8 w-8 rounded-full"})]})]}),(0,s.jsxs)("div",{className:"flex-1 flex",children:[(0,s.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,s.jsx)("div",{className:"h-full flex justify-center",children:(0,s.jsx)("div",{className:"w-full max-w-4xl px-6",children:(0,s.jsx)(o.MessageSkeleton,{})})})}),(0,s.jsxs)("div",{className:"w-80 bg-gray-900/50 border-l border-gray-800/50 flex flex-col",children:[(0,s.jsxs)("div",{className:"p-4 border-b border-gray-800/50",children:[(0,s.jsx)("div",{className:"animate-pulse bg-gray-700 h-6 w-24 rounded mb-2"}),(0,s.jsx)("div",{className:"animate-pulse bg-gray-700 h-8 w-full rounded"})]}),(0,s.jsx)("div",{className:"flex-1 p-4",children:(0,s.jsx)("div",{className:"space-y-3",children:Array.from({length:5}).map((e,t)=>(0,s.jsx)("div",{className:"p-3 rounded-xl border border-gray-700/50",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-2",children:[(0,s.jsx)("div",{className:"bg-gray-700 h-4 w-3/4 rounded"}),(0,s.jsx)("div",{className:"bg-gray-700 h-3 w-1/2 rounded"}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("div",{className:"bg-gray-700 h-3 w-16 rounded"}),(0,s.jsx)("div",{className:"bg-gray-700 h-3 w-12 rounded"})]})]})},t))})})]})]}),(0,s.jsx)("div",{className:"bg-white/95 backdrop-blur-sm border-t border-gray-200 p-4",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsx)("div",{className:"animate-pulse bg-gray-200 h-12 w-full rounded-xl"})})})]})})}},47417:(e,t,r)=>{"use strict";r.d(t,{AnalyticsSkeleton:()=>l,ConfigSelectorSkeleton:()=>n,MessageSkeleton:()=>o,MyModelsSkeleton:()=>a,RoutingSetupSkeleton:()=>i});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call LoadingSkeleton() from the server but LoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LoadingSkeleton"),(0,s.registerClientReference)(function(){throw Error("Attempted to call ChatHistorySkeleton() from the server but ChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ChatHistorySkeleton"),(0,s.registerClientReference)(function(){throw Error("Attempted to call EnhancedChatHistorySkeleton() from the server but EnhancedChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","EnhancedChatHistorySkeleton");let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call MessageSkeleton() from the server but MessageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MessageSkeleton"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call ConfigSelectorSkeleton() from the server but ConfigSelectorSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ConfigSelectorSkeleton");(0,s.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","DashboardSkeleton");let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call MyModelsSkeleton() from the server but MyModelsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MyModelsSkeleton"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call RoutingSetupSkeleton() from the server but RoutingSetupSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","RoutingSetupSkeleton");(0,s.registerClientReference)(function(){throw Error("Attempted to call TrainingSkeleton() from the server but TrainingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","TrainingSkeleton");let l=(0,s.registerClientReference)(function(){throw Error("Attempted to call AnalyticsSkeleton() from the server but AnalyticsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","AnalyticsSkeleton");(0,s.registerClientReference)(function(){throw Error("Attempted to call PlaygroundSkeleton() from the server but PlaygroundSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","PlaygroundSkeleton"),(0,s.registerClientReference)(function(){throw Error("Attempted to call LogsSkeleton() from the server but LogsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LogsSkeleton"),(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LoadingSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","default")},50515:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(43210);let o=s.forwardRef(function({title:e,titleId:t,...r},o){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56477:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687),o=r(43210),n=r(50515),a=r(24245),i=r(58089),l=r(81521),d=r(66524);function c(){let[e,t]=(0,o.useState)([]),[r,c]=(0,o.useState)(null),[p,m]=(0,o.useState)(""),[u,f]=(0,o.useState)(!1),[h,x]=(0,o.useState)(null),[g,b]=(0,o.useState)([]),[y,v]=(0,o.useState)(!0),k=async()=>{if(r&&p.trim()){f(!0),x(null),b([]);try{let e=await fetch(`/api/workflows?id=${r.id}`);if(!e.ok)throw Error("Failed to get workflow details");let t=await e.json(),s=t.api_key_info?.key_prefix+"_"+t.api_key_info?.encrypted_suffix,o=await fetch("/api/workflows/execute",{method:"POST",headers:{"Content-Type":"application/json","X-API-Key":s},body:JSON.stringify({input:p.trim(),options:{enableLogs:!0,enableProgress:!0}})});if(!o.ok){let e=await o.json();throw Error(e.details||"Execution failed")}let n=await o.json();x({execution_id:n.execution_id,status:"completed",result:n.result,timestamp:n.executed_at}),await w(n.execution_id)}catch(e){x({execution_id:"error",status:"failed",error:e instanceof Error?e.message:"Unknown error",timestamp:new Date().toISOString()})}finally{f(!1)}}},w=async e=>{try{b([{id:"1",nodeId:"user-request-1",nodeType:"userRequest",level:"info",message:"Processing user input",timestamp:new Date().toISOString()},{id:"2",nodeId:"browsing-1",nodeType:"browsing",level:"info",message:"Starting intelligent browsing",timestamp:new Date().toISOString()},{id:"3",nodeId:"browsing-1",nodeType:"browsing",level:"success",message:"Browsing completed successfully",timestamp:new Date().toISOString()}])}catch(e){}};return y?(0,s.jsx)("div",{className:"min-h-screen bg-[#040716] flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-white",children:"Loading workflows..."})}):(0,s.jsx)("div",{className:"min-h-screen bg-[#040716] text-white",children:(0,s.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Workflow Playground"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Test your workflows with real inputs and see live results"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsxs)("div",{className:"bg-gray-900/50 rounded-lg border border-gray-700/50 p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Select Workflow"}),0===e.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{className:"text-gray-400 mb-4",children:"No workflows found"}),(0,s.jsx)("a",{href:"/manual-build",className:"text-[#ff6b35] hover:underline",children:"Create your first workflow"})]}):(0,s.jsx)("div",{className:"space-y-3",children:e.map(e=>(0,s.jsxs)("div",{onClick:()=>c(e),className:`p-4 rounded-lg border cursor-pointer transition-all ${r?.id===e.id?"border-[#ff6b35] bg-[#ff6b35]/10":"border-gray-700 hover:border-gray-600"}`,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("h3",{className:"font-medium",children:e.name}),(0,s.jsx)("div",{className:`w-2 h-2 rounded-full ${e.is_active?"bg-green-500":"bg-gray-500"}`})]}),e.description&&(0,s.jsx)("p",{className:"text-sm text-gray-400 mb-2",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[(0,s.jsxs)("span",{children:[e.node_count," nodes"]}),(0,s.jsxs)("span",{children:["v",e.version]})]})]},e.id))})]})}),(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)("div",{className:"bg-gray-900/50 rounded-lg border border-gray-700/50 p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Execution"}),r?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:['Input for "',r.name,'"']}),(0,s.jsx)("textarea",{value:p,onChange:e=>m(e.target.value),placeholder:"Enter your request or input for the workflow...",rows:4,className:"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"})]}),(0,s.jsx)("button",{onClick:k,disabled:u||!p.trim(),className:"w-full bg-[#ff6b35] hover:bg-[#e55a2b] disabled:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2",children:u?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.A,{className:"w-5 h-5 animate-spin"}),"Executing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.A,{className:"w-5 h-5"}),"Execute Workflow"]})}),h&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:`p-4 rounded-lg border ${"completed"===h.status?"border-green-500 bg-green-900/20":"failed"===h.status?"border-red-500 bg-red-900/20":"border-yellow-500 bg-yellow-900/20"}`,children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:["completed"===h.status&&(0,s.jsx)(i.A,{className:"w-5 h-5 text-green-500"}),"failed"===h.status&&(0,s.jsx)(l.A,{className:"w-5 h-5 text-red-500"}),"running"===h.status&&(0,s.jsx)(n.A,{className:"w-5 h-5 text-yellow-500 animate-spin"}),(0,s.jsx)("span",{className:"font-medium capitalize",children:h.status})]}),h.error&&(0,s.jsx)("p",{className:"text-red-300 text-sm",children:h.error}),h.result&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Result:"}),(0,s.jsx)("pre",{className:"text-sm bg-gray-800 p-3 rounded overflow-auto max-h-64",children:JSON.stringify(h.result,null,2)})]})]}),g.length>0&&(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,s.jsxs)("h4",{className:"font-medium mb-3 flex items-center gap-2",children:[(0,s.jsx)(d.A,{className:"w-4 h-4"}),"Execution Logs"]}),(0,s.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:g.map(e=>(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("span",{className:`inline-block w-2 h-2 rounded-full mr-2 ${"success"===e.level?"bg-green-500":"error"===e.level?"bg-red-500":"warning"===e.level?"bg-yellow-500":"bg-blue-500"}`}),(0,s.jsxs)("span",{className:"text-gray-400",children:["[",e.nodeType,"]"]}),(0,s.jsx)("span",{className:"ml-2",children:e.message})]},e.id))})]})]})]}):(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-gray-400",children:"Select a workflow to start testing"})})]})})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66524:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(43210);let o=s.forwardRef(function({title:e,titleId:t,...r},o){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},72132:(e,t,r)=>{Promise.resolve().then(r.bind(r,35291))},74075:e=>{"use strict";e.exports=require("zlib")},74671:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\playground\\\\workflows\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\playground\\workflows\\page.tsx","default")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},90692:(e,t,r)=>{Promise.resolve().then(r.bind(r,47417))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95976:(e,t,r)=>{Promise.resolve().then(r.bind(r,56477))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5449,4912],()=>r(25165));module.exports=s})();