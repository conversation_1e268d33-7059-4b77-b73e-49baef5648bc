(()=>{var e={};e.id=1524,e.ids=[1524],e.modules={321:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(37413);function a(){return(0,s.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,s.jsx)("div",{className:"border-b border-gray-800/50",children:(0,s.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-8 w-32 rounded"}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-6 w-16 rounded"}),(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-6 w-16 rounded"})]})]}),(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-10 w-32 rounded"})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-6 w-80 rounded mb-2"}),(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-4 w-96 rounded"})]})]})}),(0,s.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 mb-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-6 w-32 rounded mb-2"}),(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-4 w-48 rounded"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Array.from({length:4}).map((e,t)=>(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-4 w-20 rounded"}),(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-10 w-full rounded"})]},t))}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-10 w-24 rounded"}),(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-10 w-20 rounded"})]})]}),(0,s.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full text-sm",children:[(0,s.jsx)("thead",{className:"bg-gray-800/50 border-b border-gray-700",children:(0,s.jsx)("tr",{children:Array.from({length:11}).map((e,t)=>(0,s.jsx)("th",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-4 w-20 rounded"})},t))})}),(0,s.jsx)("tbody",{className:"divide-y divide-gray-700 bg-gray-900/30",children:Array.from({length:8}).map((e,t)=>(0,s.jsx)("tr",{className:"hover:bg-gray-800/30",children:Array.from({length:11}).map((e,t)=>(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-4 w-16 rounded"})},t))},t))})]})})}),(0,s.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-4 border border-gray-800/50 mt-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-4 w-32 rounded"}),(0,s.jsx)("div",{className:"flex space-x-2",children:Array.from({length:3}).map((e,t)=>(0,s.jsx)("div",{className:"animate-pulse bg-gray-800 h-8 w-16 rounded"},t))})]})})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6312:(e,t,r)=>{Promise.resolve().then(r.bind(r,13289))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13289:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>S});var s=r(60687),a=r(43210),l=r(70149);let i=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3"}))}),n=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 7.5 7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5"}))});var o=r(73559),d=r(50515);let c=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))}),u=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75"}))}),m=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Zm3.75 11.625a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});var p=r(49579),x=r(61245),g=r(66524),h=r(81836);let y=({label:e,value:t})=>(0,s.jsxs)("div",{className:"py-2 sm:grid sm:grid-cols-3 sm:gap-4",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-300",children:e}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-white sm:mt-0 sm:col-span-2 break-words",children:null!=t&&""!==t?t:"N/A"})]}),b=({title:e,data:t})=>{let r;if(null==t)r="N/A";else if("string"==typeof t)r=t;else try{r=JSON.stringify(t,null,2)}catch(e){r="Invalid JSON data"}return(0,s.jsxs)("div",{className:"py-2",children:[(0,s.jsx)("dt",{className:"text-sm font-medium text-gray-300 mb-1",children:e}),(0,s.jsx)("dd",{className:"mt-1 text-sm text-gray-300 bg-gray-800/50 p-3 rounded-lg border border-gray-700",children:(0,s.jsx)("pre",{className:"whitespace-pre-wrap break-all",children:r})})]})};function f({log:e,onClose:t,apiConfigNameMap:r}){var a;if(!e)return null;let l=e.custom_api_config_id?r[e.custom_api_config_id]||"Unknown Model":"N/A";return(0,s.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out",onClick:t,children:(0,s.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm rounded-lg border border-gray-800/50 max-w-2xl w-full max-h-[90vh] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,s.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["Log Details (ID: ",e.id.substring(0,8),"...)"]}),(0,s.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,s.jsx)(h.A,{className:"h-6 w-6"})})]}),(0,s.jsxs)("div",{className:"p-6 space-y-4 overflow-y-auto",children:[(0,s.jsxs)("dl",{className:"divide-y divide-gray-700",children:[(0,s.jsx)(y,{label:"Timestamp",value:new Date(e.request_timestamp).toLocaleString()}),(0,s.jsx)(y,{label:"API Model Used",value:l}),(0,s.jsx)(y,{label:"Role Requested",value:e.role_requested}),(0,s.jsx)(y,{label:"Role Used",value:e.role_used}),(0,s.jsx)(y,{label:"Status",value:null===(a=e.status_code)?(0,s.jsx)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-gray-600 text-gray-100",children:"N/A"}):a>=200&&a<300?(0,s.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-green-600 text-green-100",children:["Success (",a,")"]}):a>=400?(0,s.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-red-600 text-red-100",children:["Error (",a,")"]}):(0,s.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-yellow-500 text-yellow-100",children:["Other (",a,")"]})}),(0,s.jsx)(y,{label:"LLM Provider",value:e.llm_provider_name}),(0,s.jsx)(y,{label:"LLM Model Name",value:(()=>{if(e.role_used?.includes("RouKey_Multi Roles_")&&e.response_payload_summary?.models_used){let t=e.response_payload_summary.models_used;return t.length<=3?t.join(", "):`${t.slice(0,3).join(", ")}...`}return e.llm_model_name})()}),(0,s.jsx)(y,{label:"LLM Latency",value:null!==e.llm_provider_latency_ms?`${e.llm_provider_latency_ms} ms`:"N/A"}),(0,s.jsx)(y,{label:"RoKey Latency",value:null!==e.processing_duration_ms?`${e.processing_duration_ms} ms`:"N/A"}),(0,s.jsx)(y,{label:"Input Tokens",value:null!==e.input_tokens?e.input_tokens:"N/A"}),(0,s.jsx)(y,{label:"Output Tokens",value:null!==e.output_tokens?e.output_tokens:"N/A"}),(0,s.jsx)(y,{label:"Cost",value:null!==e.cost?`$${e.cost.toFixed(6)}`:"N/A"}),(0,s.jsx)(y,{label:"Multimodal Request",value:e.is_multimodal?"Yes":"No"}),(0,s.jsx)(y,{label:"IP Address",value:e.ip_address}),e.user_id&&(0,s.jsx)(y,{label:"User ID",value:e.user_id}),e.error_message&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y,{label:"Error Message",value:e.error_message}),(0,s.jsx)(y,{label:"Error Source",value:e.error_source})]}),e.llm_provider_status_code&&(0,s.jsx)(y,{label:"LLM Provider Status",value:e.llm_provider_status_code})]}),e.request_payload_summary&&(0,s.jsx)(b,{title:"Request Payload Summary",data:e.request_payload_summary}),e.response_payload_summary&&(0,s.jsx)(b,{title:"Response Payload Summary",data:e.response_payload_summary}),e.error_details_zod&&(0,s.jsx)(b,{title:"Zod Validation Error Details",data:e.error_details_zod})]}),(0,s.jsx)("div",{className:"p-6 border-t border-gray-800/50 text-right",children:(0,s.jsx)("button",{onClick:t,className:"px-4 py-2 bg-gray-800/50 text-gray-300 border border-gray-700 rounded-lg hover:border-gray-600 transition-all duration-200",children:"Close"})})]})})}let v=e=>![/default_key/i,/attempt_\d+/i,/status_\d+/i,/failed/i,/success/i,/complexity_rr/i,/fallback_position/i,/^[a-f0-9-]{8,}/i,/_then_/i,/classification_/i,/no_prompt/i,/error/i].some(t=>t.test(e))&&/^[a-z_]+$/i.test(e)&&e.length>2&&e.length<50,j=e=>{if(!e)return{text:"N/A",type:"fallback"};if(v(e))return{text:k(e),type:"role",details:`Role-based routing: ${k(e)}`};if(e.includes("default_key")&&e.includes("success")){let t=e.match(/attempt_(\d+)/),r=t?parseInt(t[1]):1;return{text:1===r?"Default Key":`Default Key (Attempt ${r})`,type:"success",details:r>1?`Required ${r} attempts to succeed`:void 0}}if(e.includes("default_key")&&e.includes("failed")){let t=e.match(/attempt_(\d+)/),r=e.match(/status_(\w+)/),s=t?parseInt(t[1]):1,a=r?r[1]:"unknown";return{text:`Failed (Attempt ${s})`,type:"error",details:`Failed with status: ${a}`}}if(e.includes("default_all")&&e.includes("attempts_failed")){let t=e.match(/default_all_(\d+)_attempts/),r=t?parseInt(t[1]):0;return{text:`All Keys Failed (${r} attempts)`,type:"error",details:`Tried ${r} different API keys, all failed`}}if(e.includes("complexity_rr_clsf_")||e.includes("complexity_level_")){let t=e.match(/complexity_rr_clsf_(\d+)_used_lvl_(\d+)/);if(t){let e=t[1],r=t[2];return e===r?{text:`Complexity Level ${r}`,type:"success",details:`Classified and routed to complexity level ${r}`}:{text:`Complexity ${e}→${r}`,type:"success",details:`Classified as level ${e}, routed to available level ${r}`}}let r=e.match(/complexity_level_(\d+)/);if(r){let e=r[1];return{text:`Complexity Level ${e}`,type:"success",details:"Routed based on prompt complexity analysis"}}}if(e.includes("fallback_position_")){let t=e.match(/fallback_position_(\d+)/),r=t?parseInt(t[1]):0;return{text:`Fallback Key #${r+1}`,type:"success",details:`Used fallback key at position ${r+1}`}}if(e.includes("intelligent_role_")){let t=e.match(/intelligent_role_(.+)$/),r=t?t[1]:"unknown";return{text:`Smart: ${k(r)}`,type:"role",details:`AI detected role: ${k(r)}`}}return w(e)},w=e=>{let t=e.match(/complexity[_\s]*(\d+)/i);if(t){let e=t[1];return{text:`Complexity Level ${e}`,type:"success",details:`Extracted complexity level ${e} from routing pattern`}}let r=_(e);if(r)return{text:k(r),type:"role",details:`Extracted role: ${k(r)}`};let s=e.match(/fallback[_\s]*(\d+)/i);if(s){let e=parseInt(s[1]);return{text:`Fallback Key #${e+1}`,type:"success",details:`Extracted fallback position ${e+1}`}}let a=e.match(/attempt[_\s]*(\d+)/i);if(a){let t=parseInt(a[1]),r=e.toLowerCase().includes("success"),s=e.toLowerCase().includes("fail");if(r)return{text:1===t?"Default Key":`Default Key (Attempt ${t})`,type:"success",details:`Extracted success on attempt ${t}`};if(s)return{text:`Failed (Attempt ${t})`,type:"error",details:`Extracted failure on attempt ${t}`}}return{text:N(e),type:"fallback",details:`Raw routing pattern: ${e}`}},_=e=>{let t=e.match(/classified_as_([a-z_]+)_/i);if(t&&v(t[1]))return t[1];let r=e.match(/role_([a-z_]+)_/i);if(r&&v(r[1]))return r[1];let s=e.match(/fb_role_([a-z_]+)/i);return s&&v(s[1])?s[1]:null},N=e=>{let t=e.replace(/^default_key_[a-f0-9-]+_/i,"").replace(/_attempt_\d+$/i,"").replace(/_status_\w+$/i,"").replace(/_key_selected$/i,"").replace(/_then_.*$/i,"").replace(/^complexity_rr_/i,"").replace(/_no_.*$/i,"");return t&&t.length>2&&t.length<30&&v(t)?k(t):e.replace(/_/g," ").replace(/([a-z])([A-Z])/g,"$1 $2").split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" ").substring(0,30)+(e.length>30?"...":"")},k=e=>e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),A=e=>{switch(e){case"role":return"inline-block px-2 py-1 rounded text-xs font-medium bg-blue-500/15 text-blue-300 border border-blue-500/25";case"success":return"inline-block px-2 py-1 rounded text-xs font-medium bg-green-500/15 text-green-300 border border-green-500/25";case"error":return"inline-block px-2 py-1 rounded text-xs font-medium bg-red-500/15 text-red-300 border border-red-500/25";default:return"inline-block px-2 py-1 rounded text-xs font-medium bg-orange-500/15 text-orange-300 border border-orange-500/25"}},C=e=>e?({openai:"OpenAI",anthropic:"Anthropic",google:"Google",openrouter:"OpenRouter",deepseek:"DeepSeek",xai:"xAI",langgraph:"RouKey Orchestration",langgraph_orchestration:"RouKey Orchestration",hybrid_orchestration:"RouKey Orchestration",roukey:"RouKey Orchestration"})[e.toLowerCase()]||e:"N/A",L=e=>e?e.replace(/^(gpt-|claude-|gemini-|meta-llama\/|deepseek-|grok-)/,"").replace(/-/g," ").replace(/\b\w/g,e=>e.toUpperCase()):"N/A";var $=r(11016);let E=[{label:"Timestamp",field:"request_timestamp",defaultSortOrder:"desc"},{label:"API Model",field:"custom_api_config_id"},{label:"Role Used",field:"role_used"},{label:"Provider",field:"llm_provider_name"},{label:"LLM Model",field:"llm_model_name"},{label:"Status",field:"status_code"},{label:"Latency (LLM)",field:"llm_provider_latency_ms"},{label:"Latency (RoKey)",field:"processing_duration_ms"},{label:"Input Tokens",field:"input_tokens"},{label:"Output Tokens",field:"output_tokens"}];function S(){let{user:e}=(0,$.R)(),[t,r]=(0,a.useState)([]),[h,y]=(0,a.useState)(null),[b,v]=(0,a.useState)(!0),[w,_]=(0,a.useState)(!0),[N,k]=(0,a.useState)(null),[S,M]=(0,a.useState)([]),P={startDate:"",endDate:"",customApiConfigId:"all",status:"all"},[R,q]=(0,a.useState)(P),[O,D]=(0,a.useState)(!1),[I,K]=(0,a.useState)({}),[Z,z]=(0,a.useState)(!1),[B,F]=(0,a.useState)(null),[H,V]=(0,a.useState)({sortBy:"request_timestamp",sortOrder:"desc"}),U=(0,a.useCallback)(async(e=1,t,s)=>{v(!0),k(null);try{let a={page:e.toString(),pageSize:"10",sortBy:s.sortBy,sortOrder:s.sortOrder};t.startDate&&(a.startDate=new Date(t.startDate).toISOString()),t.endDate&&(a.endDate=new Date(t.endDate).toISOString()),"all"!==t.customApiConfigId&&(a.customApiConfigId=t.customApiConfigId),"all"!==t.status&&(a.status=t.status);let l=await fetch(`/api/logs?${new URLSearchParams(a).toString()}`);if(!l.ok){let e=await l.json();throw Error(e.error||e.details||"Failed to fetch logs")}let i=await l.json();r(i.logs||[]),y(i.pagination||null)}catch(e){k(e.message),r([]),y(null)}finally{v(!1)}},[]),W=e=>{q(t=>({...t,[e.target.name]:e.target.value}))},T=e=>{e>0&&(!h||e<=h.totalPages)&&U(e,R,H)},G=e=>{let t=H.sortBy===e&&"asc"===H.sortOrder?"desc":"asc",r={sortBy:e,sortOrder:t};V(r),U(1,R,r)},J=e=>{F(e),z(!0)},X=e=>null===e?"bg-gray-600 text-gray-100":e>=200&&e<300?"bg-green-600 text-green-100":e>=400?"bg-red-600 text-red-100":"bg-yellow-500 text-yellow-100",Y=({column:e})=>{let t=H.sortBy===e.field;return(0,s.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:(0,s.jsxs)("button",{onClick:()=>G(e.field),className:"flex items-center space-x-2 hover:text-white transition-colors duration-200 group",children:[(0,s.jsx)("span",{children:e.label}),t?"asc"===H.sortOrder?(0,s.jsx)(l.A,{className:"h-4 w-4 text-cyan-400"}):(0,s.jsx)(i,{className:"h-4 w-4 text-cyan-400"}):(0,s.jsx)(n,{className:"h-4 w-4 text-gray-500 group-hover:text-gray-300"})]})})};return(0,s.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,s.jsx)("div",{className:"border-b border-gray-800/50",children:(0,s.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"flex items-center space-x-8",children:(0,s.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Request Logs"})}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)("button",{onClick:()=>D(!O),className:`px-4 py-2 rounded-lg border transition-all duration-200 flex items-center space-x-2 ${O?"bg-cyan-500 text-white border-cyan-500":"bg-gray-900/50 text-gray-300 border-gray-700 hover:border-gray-600"}`,children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:O?"Hide Filters":"Show Filters"})]})})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("h2",{className:"text-lg mb-2",children:(0,s.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400",children:"Monitor and analyze your API request history"})}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Track performance, debug issues, and optimize your routing configurations"})]})]})}),(0,s.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[N&&(0,s.jsx)("div",{className:"bg-red-900/20 border border-red-800 rounded-lg p-6 mb-8",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,s.jsx)("p",{className:"text-red-400",children:N})]})}),O&&(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 mb-8 animate-scale-in",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Filter Logs"}),(0,s.jsx)("p",{className:"text-gray-400 mt-1",children:"Narrow down your search results"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Real-time updates"})]})]})}),(0,s.jsxs)("form",{onSubmit:e=>{e?.preventDefault(),U(1,R,H)},className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[(0,s.jsx)(c,{className:"h-4 w-4 inline mr-1"}),"Start Date"]}),(0,s.jsx)("input",{type:"date",name:"startDate",value:R.startDate,onChange:W,className:"w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[(0,s.jsx)(c,{className:"h-4 w-4 inline mr-1"}),"End Date"]}),(0,s.jsx)("input",{type:"date",name:"endDate",value:R.endDate,onChange:W,className:"w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[(0,s.jsx)(u,{className:"h-4 w-4 inline mr-1"}),"Configuration"]}),(0,s.jsxs)("select",{name:"customApiConfigId",value:R.customApiConfigId,onChange:W,disabled:w,className:"w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"all",children:"All Configurations"}),S.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[(0,s.jsx)(m,{className:"h-4 w-4 inline mr-1"}),"Status"]}),(0,s.jsxs)("select",{name:"status",value:R.status,onChange:W,className:"w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"all",children:"All Statuses"}),(0,s.jsx)("option",{value:"success",children:"Success (2xx)"}),(0,s.jsx)("option",{value:"error",children:"Error (4xx/5xx)"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>{q(P),U(1,P,H)},className:"px-4 py-2 bg-gray-800/50 text-gray-300 border border-gray-700 rounded-lg hover:border-gray-600 transition-all duration-200 flex items-center space-x-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Clear Filters"})]}),(0,s.jsxs)("button",{type:"submit",className:"px-4 py-2 bg-cyan-500 text-white rounded-lg hover:bg-cyan-600 transition-all duration-200 flex items-center space-x-2",children:[(0,s.jsx)(m,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Apply Filters"})]})]})]})]}),b&&(0,s.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 border border-gray-800/50",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-800 rounded w-1/4"}),(0,s.jsx)("div",{className:"space-y-3",children:[...Array(8)].map((e,t)=>(0,s.jsx)("div",{className:"grid grid-cols-11 gap-4",children:[...Array(11)].map((e,t)=>(0,s.jsx)("div",{className:"h-4 bg-gray-800 rounded"},t))},t))})]})}),!b&&!t.length&&!N&&(0,s.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-12 border border-gray-800/50 text-center",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gray-800/50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-gray-700",children:(0,s.jsx)(x.A,{className:"h-8 w-8 text-gray-400"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Logs Found"}),(0,s.jsx)("p",{className:"text-gray-400 mb-6",children:"No request logs match your criteria. Once you make requests to the unified API, they will appear here."})]})}),!b&&t.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full text-sm",children:[(0,s.jsx)("thead",{className:"bg-gray-800/50 border-b border-gray-700",children:(0,s.jsxs)("tr",{children:[E.map(e=>(0,s.jsx)(Y,{column:e},e.field)),(0,s.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Multimodal"}),(0,s.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,s.jsx)("tbody",{className:"divide-y divide-gray-700 bg-gray-900/30",children:t.map((e,t)=>(0,s.jsxs)("tr",{className:"hover:bg-gray-800/30 transition-colors duration-200 animate-slide-in",style:{animationDelay:`${50*t}ms`},children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-white",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 text-gray-400"}),(0,s.jsx)("span",{children:new Date(e.request_timestamp).toLocaleString()})]})}),(0,s.jsx)("td",{className:"px-6 py-4 text-white",children:(0,s.jsx)("div",{className:"font-medium",children:e.custom_api_config_id?I[e.custom_api_config_id]||e.custom_api_config_id.substring(0,8)+"...":"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 text-white",children:(0,s.jsx)("div",{className:"flex items-center",children:(()=>{let t=j(e.role_used);return(0,s.jsx)("span",{className:A(t.type),children:t.text})})()})}),(0,s.jsx)("td",{className:"px-6 py-4 text-white",children:(0,s.jsx)("span",{className:"font-medium",children:C(e.llm_provider_name)})}),(0,s.jsx)("td",{className:"px-6 py-4 text-white truncate max-w-xs",title:L(e.llm_model_name),children:(0,s.jsx)("span",{className:"font-medium",children:L(e.llm_model_name)})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("span",{className:`px-3 py-1 rounded-full text-xs font-medium ${X(e.status_code)}`,children:e.status_code||"N/A"})}),(0,s.jsx)("td",{className:"px-6 py-4 text-right text-white",children:null!==e.llm_provider_latency_ms?`${e.llm_provider_latency_ms} ms`:"-"}),(0,s.jsx)("td",{className:"px-6 py-4 text-right text-white",children:null!==e.processing_duration_ms?`${e.processing_duration_ms} ms`:"-"}),(0,s.jsx)("td",{className:"px-6 py-4 text-right text-white",children:null!==e.input_tokens?e.input_tokens.toLocaleString():"-"}),(0,s.jsx)("td",{className:"px-6 py-4 text-right text-white",children:null!==e.output_tokens?e.output_tokens.toLocaleString():"-"}),(0,s.jsx)("td",{className:"px-6 py-4 text-center",children:e.is_multimodal?(0,s.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full inline-block"}):(0,s.jsx)("span",{className:"w-2 h-2 bg-gray-500 rounded-full inline-block"})}),(0,s.jsx)("td",{className:"px-6 py-4",children:(0,s.jsx)("button",{onClick:()=>J(e),className:"p-2 text-gray-400 hover:text-cyan-400 hover:bg-gray-800/50 rounded-lg transition-all duration-200",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})})})]},e.id))})]})})}),h&&h.totalPages>1&&(0,s.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-4 border border-gray-800/50 mt-6",children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-400",children:["Showing ",(0,s.jsx)("span",{className:"font-medium text-white",children:(h.currentPage-1)*h.pageSize+1}),t.length>0?` - ${Math.min(h.currentPage*h.pageSize,h.totalCount)}`:""," ","of ",(0,s.jsx)("span",{className:"font-medium text-white",children:h.totalCount})," logs"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>T(h.currentPage-1),disabled:h.currentPage<=1||b,className:"px-3 py-1 text-sm bg-gray-800/50 text-gray-300 border border-gray-700 rounded hover:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:"Previous"}),(0,s.jsxs)("span",{className:"px-3 py-1 text-sm text-gray-400",children:["Page ",h.currentPage," of ",h.totalPages]}),(0,s.jsx)("button",{onClick:()=>T(h.currentPage+1),disabled:h.currentPage>=h.totalPages||b,className:"px-3 py-1 text-sm bg-gray-800/50 text-gray-300 border border-gray-700 rounded hover:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:"Next"})]})]})})]}),Z&&B&&(0,s.jsx)(f,{log:B,onClose:()=>{z(!1),F(null)},apiConfigNameMap:I})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40903:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),l=r(88170),i=r.n(l),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["logs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81076)),"C:\\RoKey App\\rokey-app\\src\\app\\logs\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,321)),"C:\\RoKey App\\rokey-app\\src\\app\\logs\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\logs\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/logs/page",pathname:"/logs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},42760:(e,t,r)=>{Promise.resolve().then(r.bind(r,81076))},49579:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))})},50515:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66524:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},70149:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))})},73559:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))})},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81076:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\logs\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\logs\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5449,4912],()=>r(40903));module.exports=s})();