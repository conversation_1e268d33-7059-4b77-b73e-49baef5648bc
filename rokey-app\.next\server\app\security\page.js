(()=>{var e={};e.id=1369,e.ids=[1369],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7855:(e,s,t)=>{Promise.resolve().then(t.bind(t,33411))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26310:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i,metadata:()=>r});let r={title:"Security & Trust - RouKey",description:"Learn how RouKey protects your data, API keys, and AI requests with enterprise-grade security measures.",keywords:["security","data protection","encryption","API security","RouKey","enterprise security"],openGraph:{title:"Security & Trust - RouKey",description:"Learn how RouKey protects your data, API keys, and AI requests with enterprise-grade security measures.",type:"website",url:"https://roukey.online/security",images:[{url:"https://roukey.online/og-security.jpg",width:1200,height:630,alt:"RouKey Security & Trust"}]},twitter:{card:"summary_large_image",title:"Security & Trust - RouKey",description:"Enterprise-grade security for your AI routing platform.",images:["https://roukey.online/og-security.jpg"]},alternates:{canonical:"https://roukey.online/security"}};function i({children:e}){return e}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33411:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(60687),i=t(52535),l=t(57093),n=t(17457);function a(){return(0,r.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,r.jsx)(l.A,{}),(0,r.jsxs)("main",{className:"pt-20",children:[(0,r.jsx)("section",{className:"py-20 bg-gradient-to-br from-gray-50 to-white",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12",children:(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,r.jsx)("h1",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-8",children:"Security & Trust"}),(0,r.jsx)("p",{className:"text-2xl text-gray-600 mb-10 max-w-4xl",children:"Learn how RouKey protects your data, API keys, and AI requests with enterprise-grade security."}),(0,r.jsx)("div",{className:"bg-green-50 border-l-4 border-green-500 p-8 rounded-r-lg max-w-4xl",children:(0,r.jsxs)("p",{className:"text-green-800 text-lg",children:[(0,r.jsx)("strong",{children:"Security First:"})," Your data security and privacy are our top priorities. We implement industry-leading security measures to protect your information."]})})]})})}),(0,r.jsx)("section",{className:"py-20",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12",children:(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"prose prose-xl max-w-none",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-12",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-12 text-gray-800",children:[(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDD12 Data Protection"}),(0,r.jsxs)("div",{className:"bg-blue-50 border-l-4 border-blue-500 p-6 rounded-r-lg mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-2",children:"Zero Content Storage"}),(0,r.jsx)("p",{className:"text-blue-800",children:"RouKey does not store, log, or access the content of your AI requests and responses. Your conversations and data remain completely private."})]}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Encryption Standards"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"In Transit:"})," All data is encrypted using TLS 1.3 with perfect forward secrecy"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"At Rest:"})," Sensitive data is encrypted using AES-256 encryption"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"API Keys:"})," Stored using industry-standard encryption with unique salt values"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Database:"})," All database connections use encrypted channels"]})]}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3 mt-6",children:"Data Minimization"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,r.jsx)("li",{children:"We only collect data necessary for service operation"}),(0,r.jsx)("li",{children:"Personal information is limited to account essentials"}),(0,r.jsx)("li",{children:"Usage analytics are aggregated and anonymized"}),(0,r.jsx)("li",{children:"Automatic data purging for inactive accounts"})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDEE1️ Infrastructure Security"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Cloud Security"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Vercel Platform"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,r.jsx)("li",{children:"SOC 2 Type II certified"}),(0,r.jsx)("li",{children:"Global edge network"}),(0,r.jsx)("li",{children:"DDoS protection"}),(0,r.jsx)("li",{children:"Automatic security updates"})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Supabase Database"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,r.jsx)("li",{children:"PostgreSQL with RLS policies"}),(0,r.jsx)("li",{children:"Encrypted backups"}),(0,r.jsx)("li",{children:"Network isolation"}),(0,r.jsx)("li",{children:"Regular security patches"})]})]})]}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Network Security"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,r.jsx)("li",{children:"Web Application Firewall (WAF) protection"}),(0,r.jsx)("li",{children:"Rate limiting and abuse prevention"}),(0,r.jsx)("li",{children:"IP allowlisting for enterprise customers"}),(0,r.jsx)("li",{children:"Continuous monitoring for threats"}),(0,r.jsx)("li",{children:"Automated incident response"})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDD11 API Key Security"}),(0,r.jsxs)("div",{className:"bg-yellow-50 border-l-4 border-yellow-500 p-6 rounded-r-lg mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-yellow-900 mb-2",children:"⚠️ Your Keys, Your Control"}),(0,r.jsx)("p",{className:"text-yellow-800",children:"You maintain full control over your AI provider API keys. RouKey encrypts and securely stores them, but you can revoke or rotate them at any time."})]}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Key Management"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Encryption:"})," All API keys are encrypted with unique encryption keys"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Access Control:"})," Keys are only accessible to your account"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Audit Logging:"})," All key usage is logged for security monitoring"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Rotation Support:"})," Easy key rotation without service interruption"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Secure Deletion:"})," Keys are securely wiped when deleted"]})]}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3 mt-6",children:"Best Practices"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,r.jsx)("li",{children:"Use API keys with minimal required permissions"}),(0,r.jsx)("li",{children:"Regularly rotate your API keys"}),(0,r.jsx)("li",{children:"Monitor usage patterns for anomalies"}),(0,r.jsx)("li",{children:"Never share your RouKey account credentials"}),(0,r.jsx)("li",{children:"Enable two-factor authentication when available"})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDD10 Authentication & Access Control"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"User Authentication"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,r.jsx)("li",{children:"Secure email-based authentication"}),(0,r.jsx)("li",{children:"JWT tokens with short expiration times"}),(0,r.jsx)("li",{children:"Session management with automatic timeout"}),(0,r.jsx)("li",{children:"Password strength requirements"}),(0,r.jsx)("li",{children:"Account lockout protection against brute force attacks"})]}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3 mt-6",children:"API Security"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,r.jsx)("li",{children:"X-API-Key header authentication"}),(0,r.jsx)("li",{children:"Request signing and validation"}),(0,r.jsx)("li",{children:"Rate limiting per API key"}),(0,r.jsx)("li",{children:"IP-based access restrictions (Enterprise)"}),(0,r.jsx)("li",{children:"Real-time abuse detection"})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDCCA Security Monitoring"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Continuous Monitoring"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,r.jsx)("li",{children:"24/7 security monitoring and alerting"}),(0,r.jsx)("li",{children:"Automated threat detection and response"}),(0,r.jsx)("li",{children:"Regular vulnerability assessments"}),(0,r.jsx)("li",{children:"Penetration testing by third-party security firms"}),(0,r.jsx)("li",{children:"Security incident response procedures"})]}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3 mt-6",children:"Audit Logging"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,r.jsx)("li",{children:"Comprehensive audit trails for all actions"}),(0,r.jsx)("li",{children:"Immutable log storage"}),(0,r.jsx)("li",{children:"Real-time anomaly detection"}),(0,r.jsx)("li",{children:"Compliance reporting capabilities"}),(0,r.jsx)("li",{children:"Log retention according to industry standards"})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83C\uDFC6 Compliance & Certifications"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{className:"bg-green-50 p-6 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-green-900 mb-3",children:"Privacy Compliance"}),(0,r.jsxs)("ul",{className:"text-green-800 space-y-2",children:[(0,r.jsx)("li",{children:"✅ GDPR compliant"}),(0,r.jsx)("li",{children:"✅ CCPA compliant"}),(0,r.jsx)("li",{children:"✅ Privacy by design"}),(0,r.jsx)("li",{children:"✅ Data minimization"})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-3",children:"Security Standards"}),(0,r.jsxs)("ul",{className:"text-blue-800 space-y-2",children:[(0,r.jsx)("li",{children:"\uD83D\uDD12 SOC 2 Type II (via providers)"}),(0,r.jsx)("li",{children:"\uD83D\uDD12 ISO 27001 aligned"}),(0,r.jsx)("li",{children:"\uD83D\uDD12 OWASP Top 10 protection"}),(0,r.jsx)("li",{children:"\uD83D\uDD12 Industry best practices"})]})]})]}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Enterprise Features"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,r.jsx)("li",{children:"Single Sign-On (SSO) integration"}),(0,r.jsx)("li",{children:"Advanced audit logging and reporting"}),(0,r.jsx)("li",{children:"Custom data retention policies"}),(0,r.jsx)("li",{children:"Dedicated security support"}),(0,r.jsx)("li",{children:"On-premise deployment options (coming soon)"})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDEA8 Incident Response"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Response Procedures"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,r.jsx)("li",{children:"Immediate containment and assessment"}),(0,r.jsx)("li",{children:"Rapid notification to affected users"}),(0,r.jsx)("li",{children:"Transparent communication about incidents"}),(0,r.jsx)("li",{children:"Post-incident analysis and improvements"}),(0,r.jsx)("li",{children:"Coordination with law enforcement if required"})]}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3 mt-6",children:"Communication Channels"}),(0,r.jsxs)("div",{className:"bg-red-50 border-l-4 border-red-500 p-6 rounded-r-lg",children:[(0,r.jsx)("p",{className:"text-red-800 mb-4",children:"In case of a security incident, we will notify affected users within 72 hours through:"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-1 text-red-800",children:[(0,r.jsx)("li",{children:"Email notifications to registered addresses"}),(0,r.jsx)("li",{children:"In-app security alerts"}),(0,r.jsx)("li",{children:"Public status page updates"}),(0,r.jsx)("li",{children:"Direct communication for enterprise customers"})]})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDD0D Security Transparency"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Regular Updates"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-2",children:[(0,r.jsx)("li",{children:"Quarterly security reports"}),(0,r.jsx)("li",{children:"Annual third-party security audits"}),(0,r.jsx)("li",{children:"Public disclosure of resolved vulnerabilities"}),(0,r.jsx)("li",{children:"Security roadmap and improvements"})]}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3 mt-6",children:"Bug Bounty Program"}),(0,r.jsxs)("div",{className:"bg-purple-50 border-l-4 border-purple-500 p-6 rounded-r-lg",children:[(0,r.jsx)("p",{className:"text-purple-800 mb-4",children:"We welcome security researchers to help us maintain the highest security standards. Our responsible disclosure program includes:"}),(0,r.jsxs)("ul",{className:"list-disc pl-6 space-y-1 text-purple-800",children:[(0,r.jsx)("li",{children:"Clear reporting guidelines"}),(0,r.jsx)("li",{children:"Rapid response to valid reports"}),(0,r.jsx)("li",{children:"Recognition for security researchers"}),(0,r.jsx)("li",{children:"Coordinated disclosure process"})]})]})]}),(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"\uD83D\uDCDE Security Contact"}),(0,r.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,r.jsx)("p",{className:"mb-4",children:"For security-related inquiries, vulnerability reports, or urgent security matters:"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Security Email:"})," ",(0,r.jsx)("a",{href:"mailto:<EMAIL>",className:"text-[#ff6b35] hover:underline",children:"<EMAIL>"})]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"General Contact:"})," ",(0,r.jsx)("a",{href:"mailto:<EMAIL>",className:"text-[#ff6b35] hover:underline",children:"<EMAIL>"})]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Emergency:"})," ",(0,r.jsx)("a",{href:"mailto:<EMAIL>",className:"text-[#ff6b35] hover:underline",children:"<EMAIL>"})]})]}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-yellow-50 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-800",children:[(0,r.jsx)("strong",{children:"PGP Key:"})," For sensitive security communications, please use our PGP key available at ",(0,r.jsx)("a",{href:"/pgp-key",className:"text-[#ff6b35] hover:underline",children:"roukey.online/pgp-key"})]})}),(0,r.jsx)("p",{className:"mt-4 text-sm text-gray-600",children:"Security reports will be acknowledged within 24 hours and addressed according to severity."})]})]})]}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"sticky top-24 space-y-8",children:[(0,r.jsxs)("div",{className:"bg-green-50 p-6 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-green-900 mb-3",children:"\uD83D\uDD12 Security Highlights"}),(0,r.jsxs)("ul",{className:"space-y-2 text-green-800 text-sm",children:[(0,r.jsx)("li",{children:"• Zero content storage"}),(0,r.jsx)("li",{children:"• AES-256 encryption"}),(0,r.jsx)("li",{children:"• TLS 1.3 in transit"}),(0,r.jsx)("li",{children:"• SOC 2 Type II"}),(0,r.jsx)("li",{children:"• 24/7 monitoring"})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-3",children:"\uD83D\uDEA8 Report Security Issues"}),(0,r.jsx)("p",{className:"text-blue-800 text-sm mb-3",children:"Found a security vulnerability?"}),(0,r.jsx)("a",{href:"mailto:<EMAIL>",className:"text-[#ff6b35] hover:text-[#e55a2b] font-medium",children:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-6 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-purple-900 mb-3",children:"\uD83C\uDFC5 Certifications"}),(0,r.jsxs)("ul",{className:"space-y-2 text-purple-800 text-sm",children:[(0,r.jsx)("li",{children:"✅ GDPR Compliant"}),(0,r.jsx)("li",{children:"✅ CCPA Compliant"}),(0,r.jsx)("li",{children:"✅ ISO 27001 Aligned"}),(0,r.jsx)("li",{children:"✅ OWASP Protected"})]})]})]})})]})})})})]}),(0,r.jsx)(n.A,{})]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44509:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\security\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\security\\page.tsx","default")},54711:(e,s,t)=>{Promise.resolve().then(t.bind(t,44509))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74597:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>o});var r=t(65239),i=t(48088),l=t(88170),n=t.n(l),a=t(30893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);t.d(s,c);let o={children:["",{children:["security",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,44509)),"C:\\RoKey App\\rokey-app\\src\\app\\security\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,26310)),"C:\\RoKey App\\rokey-app\\src\\app\\security\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\RoKey App\\rokey-app\\src\\app\\security\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/security/page",pathname:"/security",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,5449,2535,4912,7093,7457],()=>t(74597));module.exports=r})();