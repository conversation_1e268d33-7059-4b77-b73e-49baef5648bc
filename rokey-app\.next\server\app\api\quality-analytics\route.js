(()=>{var e={};e.id=7954,e.ids=[1489,7954],e.modules={2507:(e,t,s)=>{"use strict";s.d(t,{H:()=>u,Q:()=>n,createSupabaseServerClientOnRequest:()=>i});var r=s(34386),o=s(39398),a=s(44999);async function i(){let e=await (0,a.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,s,r){try{e.set({name:t,value:s,...r})}catch(e){}},remove(t,s){try{e.set({name:t,value:"",...s})}catch(e){}}}})}function n(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,s){},remove(e,t){}}})}function u(){return(0,o.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},90905:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>d,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{GET:()=>c});var o=s(96559),a=s(48088),i=s(37719),n=s(32190),u=s(2507);async function c(e){try{let t=(0,u.Q)(e),{data:{user:s},error:r}=await t.auth.getUser();if(r||!s)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:o}=new URL(e.url),a=o.get("config_id"),i=parseInt(o.get("days")||"30");if(!a)return n.NextResponse.json({error:"config_id parameter required"},{status:400});let c=new Date;c.setDate(c.getDate()-i);let{data:l,error:p}=await t.from("routing_quality_metrics").select("*").eq("user_id",s.id).eq("custom_api_config_id",a).gte("created_at",c.toISOString()).order("created_at",{ascending:!1});if(p)return n.NextResponse.json({error:"Failed to fetch analytics"},{status:500});let{data:g}=await t.from("cost_optimization_profiles").select("*").eq("user_id",s.id).eq("custom_api_config_id",a).single(),{data:d}=await t.from("ab_test_assignments").select("*").eq("user_id",s.id).eq("custom_api_config_id",a).single(),h=function(e,t,s){if(0===e.length)return{overview:{averageQuality:0,totalCost:0,costSavings:0,learningPhase:t?.learning_phase_completed===!1},modelPerformance:[],routingStrategies:[],abTestResults:null,recommendations:[]};let r=e.reduce((e,t)=>e+(t.quality_score||0),0)/e.length,o=e.reduce((e,t)=>e+(t.cost_usd||0),0),a=new Map;e.forEach(e=>{let t=`${e.model_used}-${e.provider}`;a.has(t)||a.set(t,{model:e.model_used,provider:e.provider,requests:0,totalQuality:0,totalCost:0,userRatings:[]});let s=a.get(t);s.requests+=1,s.totalQuality+=e.quality_score||0,s.totalCost+=e.cost_usd||0,e.user_rating&&s.userRatings.push(e.user_rating)});let i=Array.from(a.values()).map(e=>({model:e.model,provider:e.provider,requests:e.requests,averageQuality:e.totalQuality/e.requests,totalCost:e.totalCost,costPerRequest:e.totalCost/e.requests,averageUserRating:e.userRatings.length>0?e.userRatings.reduce((e,t)=>e+t,0)/e.userRatings.length:null,costEfficiency:e.totalCost>0?e.totalQuality/e.requests/(e.totalCost/e.requests):0})).sort((e,t)=>t.costEfficiency-e.costEfficiency),n=new Map;e.forEach(e=>{let t=e.routing_strategy||"unknown";n.has(t)||n.set(t,{strategy:t,requests:0,totalQuality:0,totalCost:0});let s=n.get(t);s.requests+=1,s.totalQuality+=e.quality_score||0,s.totalCost+=e.cost_usd||0});let u=Array.from(n.values()).map(e=>({strategy:e.strategy,requests:e.requests,averageQuality:e.totalQuality/e.requests,totalCost:e.totalCost,costPerRequest:e.totalCost/e.requests})),c=null;s&&s.test_requests>0&&s.control_requests>0&&(c={testRequests:s.test_requests,controlRequests:s.control_requests,testAverageQuality:s.test_avg_quality,controlAverageQuality:s.control_avg_quality,testAverageCost:s.test_avg_cost,controlAverageCost:s.control_avg_cost,qualityImprovement:s.test_avg_quality&&s.control_avg_quality?(s.test_avg_quality-s.control_avg_quality)/s.control_avg_quality*100:null,costChange:s.test_avg_cost&&s.control_avg_cost?(s.test_avg_cost-s.control_avg_cost)/s.control_avg_cost*100:null});let l=function(e,t,s,r){let o=[];if(r?.learning_phase_completed===!1&&o.push(`You're in the learning phase (${r.learning_phase_requests}/50 requests). Continue using the system to unlock smart cost optimization.`),e.length>1){let t=e[0],s=e[e.length-1];t.costEfficiency>2*s.costEfficiency&&o.push(`Consider using ${t.model} more often - it has ${Math.round((t.costEfficiency/s.costEfficiency-1)*100)}% better cost efficiency than ${s.model}.`)}s&&(s.qualityImprovement>10&&o.push(`A/B testing shows ${Math.round(s.qualityImprovement)}% quality improvement. Consider adjusting your routing strategy.`),s.costChange<-20&&o.push(`A/B testing found models that are ${Math.round(Math.abs(s.costChange))}% cheaper with similar quality.`));let a=t.find(e=>"cost_optimized"===e.strategy),i=t.find(e=>"none"===e.strategy||"unknown"===e.strategy);return a&&i&&a.costPerRequest<.8*i.costPerRequest&&o.push(`Smart cost-optimized routing saves ${Math.round((1-a.costPerRequest/i.costPerRequest)*100)}% on costs compared to default routing.`),0===o.length&&o.push("Your routing is performing well! Continue monitoring for optimization opportunities."),o}(i,u,c,t),p=i.reduce((e,t)=>t.costPerRequest>e.costPerRequest?t:e,i[0]||{costPerRequest:0}),g=p.costPerRequest>0?p.costPerRequest*e.length-o:0;return{overview:{averageQuality:Math.round(100*r)/100,totalCost:Math.round(1e5*o)/1e5,costSavings:Math.round(1e5*g)/1e5,learningPhase:t?.learning_phase_completed===!1,learningProgress:t?`${t.learning_phase_requests}/50`:"0/50"},modelPerformance:i,routingStrategies:u,abTestResults:c,recommendations:l}}(l||[],g,d);return n.NextResponse.json({success:!0,period:`${i} days`,totalRequests:l?.length||0,analytics:h})}catch(e){return n.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/quality-analytics/route",pathname:"/api/quality-analytics",filename:"route",bundlePath:"app/api/quality-analytics/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\quality-analytics\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:d}=l;function h(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,9398,3410],()=>s(90905));module.exports=r})();