{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "kbfm2kbYBmAf58yWqmV6k", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "07a056f94bacab682f6b9842a7916c54", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "13ba5f756de5a738ba6ccdca26a62fb9dd39b9796506ad60240509ee0c1635e2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0f52f648ccf63de59413aef4ac141f26f3c1b1c672a119b8241aa0b3eb6d10c0"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "kbfm2kbYBmAf58yWqmV6k", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "07a056f94bacab682f6b9842a7916c54", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "13ba5f756de5a738ba6ccdca26a62fb9dd39b9796506ad60240509ee0c1635e2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0f52f648ccf63de59413aef4ac141f26f3c1b1c672a119b8241aa0b3eb6d10c0"}}}, "sortedMiddleware": ["/"]}