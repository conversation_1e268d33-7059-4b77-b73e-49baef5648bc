{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "L7TnvjhzQgH6EXsRpK0gE", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "aff5a697c483e34042dee6fe53899c0f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ea9873293c21d00ff5090f85d48dc1cc7ac240fe9e35dfc481883d753288605f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "adcffdd03d43e1c94654fc3aff841dbf31a37c6c1bdd1c0c8ab9875a96b82d46"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "L7TnvjhzQgH6EXsRpK0gE", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "aff5a697c483e34042dee6fe53899c0f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ea9873293c21d00ff5090f85d48dc1cc7ac240fe9e35dfc481883d753288605f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "adcffdd03d43e1c94654fc3aff841dbf31a37c6c1bdd1c0c8ab9875a96b82d46"}}}, "sortedMiddleware": ["/"]}