{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "PjEVGqwwbbFAUv2Upobl7", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "79013ae0bf799733460a798b2ee7cf99", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4dd90d16383b60d986dee519ecd8365043bf38b02857f0e4256f8477041dc6b5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7c6de31b70ca127708e37f3b1037fcbd61437927635b3fa49c3760f3af3385c2"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "PjEVGqwwbbFAUv2Upobl7", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "79013ae0bf799733460a798b2ee7cf99", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4dd90d16383b60d986dee519ecd8365043bf38b02857f0e4256f8477041dc6b5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7c6de31b70ca127708e37f3b1037fcbd61437927635b3fa49c3760f3af3385c2"}}}, "sortedMiddleware": ["/"]}