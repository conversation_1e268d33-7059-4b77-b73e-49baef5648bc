import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { sendWelcomeEmail } from '@/lib/email/welcomeEmail';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientFromRequest();
    
    // Verify the request is from an authenticated user or internal system
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.error('❌ WELCOME-EMAIL: Auth error:', authError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { userEmail, userName, userTier } = body;

    if (!userEmail || !userName) {
      return NextResponse.json(
        { error: 'Missing required fields: userEmail, userName' },
        { status: 400 }
      );
    }

    // Send welcome email
    const success = await sendWelcomeEmail({
      userEmail,
      userName: userName || 'New User',
      userTier: userTier || 'free'
    });

    if (success) {
      console.log('✅ WELCOME-EMAIL: Successfully sent to:', userEmail);
      return NextResponse.json({ success: true, message: 'Welcome email sent' });
    } else {
      console.error('❌ WELCOME-EMAIL: Failed to send to:', userEmail);
      return NextResponse.json(
        { error: 'Failed to send welcome email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('❌ WELCOME-EMAIL: Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
