(()=>{var e={};e.id=2778,e.ids=[2778],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14689:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22461:(e,t,r)=>{Promise.resolve().then(r.bind(r,47677))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},47677:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(60687),a=r(43210),n=r(85814),i=r.n(n),o=r(30474),l=r(94257),d=r(66524),c=r(14689),u=r(79481),m=r(16189),p=r(34374);function x(){let[e,t]=(0,a.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:""}),[r,n]=(0,a.useState)(!1),[x,h]=(0,a.useState)(!1),[g,f]=(0,a.useState)(!1),[b,y]=(0,a.useState)(""),[w,v]=(0,a.useState)(!1),j=(0,m.useRouter)(),N=(0,m.useSearchParams)(),k=(0,u.createSupabaseBrowserClient)(),{success:P,error:C}=(0,p.dj)(),A=N.get("plan")||"free",S=e=>{t(t=>({...t,[e.target.name]:e.target.value}))},q=async t=>{if(t.preventDefault(),f(!0),y(""),e.password!==e.confirmPassword){y("Passwords do not match"),f(!1);return}if(e.password.length<8){y("Password must be at least 8 characters long"),f(!1);return}if(!w){y("Please agree to the Terms of Service and Privacy Policy"),f(!1);return}try{let t={exists:!1};try{let r=await fetch("/api/auth/check-pending-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email})});if(r.ok){let e=await r.text();try{t=JSON.parse(e)}catch(e){y("Server error: Invalid response format. Please try again."),f(!1);return}}else{await r.text(),y(`Server error (${r.status}): Please try again.`),f(!1);return}}catch(e){y("Network error: Please check your connection and try again."),f(!1);return}if(t.exists){if("hasPendingPayment"in t&&t.hasPendingPayment){P("Welcome back! Redirecting to complete your checkout...");let r=`/checkout?plan=${t.plan}&user_id=${t.userId}&email=${encodeURIComponent(e.email)}`;j.push(r);return}else if("hasActiveSubscription"in t&&t.hasActiveSubscription){let{data:t,error:r}=await k.auth.signInWithPassword({email:e.email,password:e.password});if(r){y("An account with this email already exists. Please sign in instead or use a different email."),f(!1);return}if(t.user){P("Welcome back! You have been signed in to your existing account."),j.push("/dashboard");return}}}if("free"===A){let t=await fetch("/api/auth/free-signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email,password:e.password,fullName:`${e.firstName} ${e.lastName}`})}),r=await t.json();if(!t.ok){y(r.error||"Failed to create account"),f(!1);return}let{data:s,error:a}=await k.auth.signInWithPassword({email:e.email,password:e.password});if(a){y("Account created but failed to sign in. Please try signing in manually."),f(!1);return}P("Account created successfully! Welcome to RouKey."),j.push("/dashboard")}else{let t=await fetch("/api/auth/paid-signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email,password:e.password,firstName:e.firstName,lastName:e.lastName,plan:A})}),r=await t.json();if(!t.ok){y(r.error||"Failed to create account"),f(!1);return}P("Account created! Redirecting to complete your payment...");let s=`/checkout?plan=${A}&user_id=${r.user.id}&email=${encodeURIComponent(e.email)}`;j.push(s)}}catch(e){y("An unexpected error occurred. Please try again.")}finally{f(!1)}},R=[{text:"At least 8 characters",met:e.password.length>=8},{text:"Contains uppercase letter",met:/[A-Z]/.test(e.password)},{text:"Contains lowercase letter",met:/[a-z]/.test(e.password)},{text:"Contains number",met:/\d/.test(e.password)}];return(0,s.jsxs)("div",{className:"min-h-screen flex bg-gradient-to-br from-[#1C051C] to-[#040716]",children:[(0,s.jsxs)("div",{className:"hidden lg:flex lg:w-1/2 items-center justify-center p-12 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.15]",style:{backgroundImage:"linear-gradient(rgba(255, 255, 255, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.3) 1px, transparent 1px)",backgroundSize:"40px 40px",maskImage:"radial-gradient(ellipse at center, black 30%, transparent 80%)",WebkitMaskImage:"radial-gradient(ellipse at center, black 30%, transparent 80%)"}}),(0,s.jsxs)("div",{className:"max-w-lg relative z-10",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)(i(),{href:"/",className:"inline-flex items-center space-x-3",children:[(0,s.jsx)(o.default,{src:"/RouKey_Logo_GLOW.png",alt:"RouKey",width:48,height:48,className:"w-12 h-12"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-white",children:"RouKey"})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-white leading-tight",children:"Welcome to RouKey!"}),(0,s.jsx)("p",{className:"text-xl text-white/80 leading-relaxed",children:"Create your account and start building with our powerful routing platform."}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsxs)("p",{className:"text-white/60 text-sm",children:["Already have an account?"," ",(0,s.jsx)(i(),{href:"/auth/signin",className:"text-white font-medium hover:text-white/80 transition-colors",children:"Sign in here"})]})})]})]})]}),(0,s.jsx)("div",{className:"flex-1 flex items-center justify-center p-6 bg-white",children:(0,s.jsxs)("div",{className:"w-full max-w-md space-y-4",children:[(0,s.jsx)("div",{className:"lg:hidden text-center",children:(0,s.jsxs)(i(),{href:"/",className:"inline-flex items-center space-x-2",children:[(0,s.jsx)(o.default,{src:"/RouKey_Logo_NOGLOW.png",alt:"RouKey",width:32,height:32,className:"w-8 h-8"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"RouKey"})]})}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Sign up"}),(0,s.jsxs)("p",{className:"mt-1 text-gray-600 text-sm",children:["Create your account for the"," ",(0,s.jsx)("span",{className:"text-pink-600 font-semibold capitalize",children:A})," ","plan"]})]}),A&&"free"!==A&&(0,s.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h3",{className:"text-sm font-semibold text-blue-900 capitalize",children:[A," Plan Selected"]}),(0,s.jsxs)("p",{className:"text-blue-700 text-xs mt-1",children:["starter"===A&&"$24/month - Perfect for small teams","professional"===A&&"$60/month - Advanced features included","enterprise"===A&&"$170/month - Full enterprise solution"]})]})}),b&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,s.jsx)("p",{className:"text-red-600 text-sm",children:b})}),(0,s.jsxs)("form",{onSubmit:q,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-2",children:"First name"}),(0,s.jsx)("input",{id:"firstName",name:"firstName",type:"text",autoComplete:"given-name",required:!0,value:e.firstName,onChange:S,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"First name"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Last name"}),(0,s.jsx)("input",{id:"lastName",name:"lastName",type:"text",autoComplete:"family-name",required:!0,value:e.lastName,onChange:S,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Last name"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:S,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Enter your email"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"password",name:"password",type:r?"text":"password",autoComplete:"new-password",required:!0,value:e.password,onChange:S,className:"w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Create a password"}),(0,s.jsx)("button",{type:"button",onClick:()=>n(!r),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:r?(0,s.jsx)(l.A,{className:"h-5 w-5"}):(0,s.jsx)(d.A,{className:"h-5 w-5"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:x?"text":"password",autoComplete:"new-password",required:!0,value:e.confirmPassword,onChange:S,className:"w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors",placeholder:"Confirm your password"}),(0,s.jsx)("button",{type:"button",onClick:()=>h(!x),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:x?(0,s.jsx)(l.A,{className:"h-5 w-5"}):(0,s.jsx)(d.A,{className:"h-5 w-5"})})]})]}),e.password&&(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Password requirements:"}),(0,s.jsx)("div",{className:"space-y-2",children:R.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.A,{className:`h-4 w-4 ${e.met?"text-green-500":"text-gray-300"}`}),(0,s.jsx)("span",{className:`text-sm ${e.met?"text-green-700":"text-gray-500"}`,children:e.text})]},t))})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("input",{id:"terms",type:"checkbox",checked:w,onChange:e=>v(e.target.checked),className:"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsxs)("label",{htmlFor:"terms",className:"text-sm text-gray-600",children:["I agree to the"," ",(0,s.jsx)(i(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",(0,s.jsx)(i(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})]}),(0,s.jsx)("button",{type:"submit",disabled:g,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:g?"Creating account...":"Create account"})]}),(0,s.jsx)("div",{className:"text-center lg:hidden",children:(0,s.jsxs)("p",{className:"text-gray-600 text-sm",children:["Already have an account?"," ",(0,s.jsx)(i(),{href:"/auth/signin",className:"text-blue-600 font-medium hover:text-blue-500",children:"Sign in"})]})})]})})]})}function h(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,s.jsx)(x,{})})}},52317:(e,t,r)=>{Promise.resolve().then(r.bind(r,94796))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63319:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94796)),"C:\\RoKey App\\rokey-app\\src\\app\\auth\\signup\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\auth\\signup\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},66524:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94257:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})},94735:e=>{"use strict";e.exports=require("events")},94796:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\auth\\signup\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5449,4912],()=>r(63319));module.exports=s})();