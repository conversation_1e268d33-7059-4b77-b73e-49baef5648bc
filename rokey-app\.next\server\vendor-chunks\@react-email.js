"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-email";
exports.ids = ["vendor-chunks/@react-email"];
exports.modules = {

/***/ "(rsc)/../node_modules/@react-email/render/dist/node/index.mjs":
/*!***************************************************************!*\
  !*** ../node_modules/@react-email/render/dist/node/index.mjs ***!
  \***************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   plainTextSelectors: () => (/* binding */ plainTextSelectors),\n/* harmony export */   pretty: () => (/* binding */ pretty),\n/* harmony export */   render: () => (/* binding */ render),\n/* harmony export */   renderAsync: () => (/* binding */ renderAsync)\n/* harmony export */ });\n/* harmony import */ var html_to_text__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! html-to-text */ \"(rsc)/../node_modules/html-to-text/lib/html-to-text.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var prettier_plugins_html__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prettier/plugins/html */ \"prettier/plugins/html\");\n/* harmony import */ var prettier_standalone__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prettier/standalone */ \"prettier/standalone\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([prettier_plugins_html__WEBPACK_IMPORTED_MODULE_2__, prettier_standalone__WEBPACK_IMPORTED_MODULE_3__]);\n([prettier_plugins_html__WEBPACK_IMPORTED_MODULE_2__, prettier_standalone__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// src/node/render.tsx\n\n\n\n// src/shared/plain-text-selectors.ts\nvar plainTextSelectors = [\n  { selector: \"img\", format: \"skip\" },\n  { selector: \"[data-skip-in-text=true]\", format: \"skip\" },\n  {\n    selector: \"a\",\n    options: { linkBrackets: false }\n  }\n];\n\n// src/shared/utils/pretty.ts\n\n\nfunction recursivelyMapDoc(doc, callback) {\n  if (Array.isArray(doc)) {\n    return doc.map((innerDoc) => recursivelyMapDoc(innerDoc, callback));\n  }\n  if (typeof doc === \"object\") {\n    if (doc.type === \"group\") {\n      return __spreadProps(__spreadValues({}, doc), {\n        contents: recursivelyMapDoc(doc.contents, callback),\n        expandedStates: recursivelyMapDoc(\n          doc.expandedStates,\n          callback\n        )\n      });\n    }\n    if (\"contents\" in doc) {\n      return __spreadProps(__spreadValues({}, doc), {\n        contents: recursivelyMapDoc(doc.contents, callback)\n      });\n    }\n    if (\"parts\" in doc) {\n      return __spreadProps(__spreadValues({}, doc), {\n        parts: recursivelyMapDoc(doc.parts, callback)\n      });\n    }\n    if (doc.type === \"if-break\") {\n      return __spreadProps(__spreadValues({}, doc), {\n        breakContents: recursivelyMapDoc(doc.breakContents, callback),\n        flatContents: recursivelyMapDoc(doc.flatContents, callback)\n      });\n    }\n  }\n  return callback(doc);\n}\nvar modifiedHtml = __spreadValues({}, prettier_plugins_html__WEBPACK_IMPORTED_MODULE_2__);\nif (modifiedHtml.printers) {\n  const previousPrint = modifiedHtml.printers.html.print;\n  modifiedHtml.printers.html.print = (path, options, print, args) => {\n    const node = path.getNode();\n    const rawPrintingResult = previousPrint(path, options, print, args);\n    if (node.type === \"ieConditionalComment\") {\n      const printingResult = recursivelyMapDoc(rawPrintingResult, (doc) => {\n        if (typeof doc === \"object\" && doc.type === \"line\") {\n          return doc.soft ? \"\" : \" \";\n        }\n        return doc;\n      });\n      return printingResult;\n    }\n    return rawPrintingResult;\n  };\n}\nvar defaults = {\n  endOfLine: \"lf\",\n  tabWidth: 2,\n  plugins: [modifiedHtml],\n  bracketSameLine: true,\n  parser: \"html\"\n};\nvar pretty = (str, options = {}) => {\n  return (0,prettier_standalone__WEBPACK_IMPORTED_MODULE_3__.format)(str.replaceAll(\"\\0\", \"\"), __spreadValues(__spreadValues({}, defaults), options));\n};\n\n// src/node/read-stream.ts\n\nvar decoder = new TextDecoder(\"utf-8\");\nvar readStream = (stream) => __async(void 0, null, function* () {\n  let result = \"\";\n  if (\"pipeTo\" in stream) {\n    const writableStream = new WritableStream({\n      write(chunk) {\n        result += decoder.decode(chunk);\n      }\n    });\n    yield stream.pipeTo(writableStream);\n  } else {\n    const writable = new node_stream__WEBPACK_IMPORTED_MODULE_4__.Writable({\n      write(chunk, _encoding, callback) {\n        result += decoder.decode(chunk);\n        callback();\n      }\n    });\n    stream.pipe(writable);\n    yield new Promise((resolve, reject) => {\n      writable.on(\"error\", reject);\n      writable.on(\"close\", () => {\n        resolve();\n      });\n    });\n  }\n  return result;\n});\n\n// src/node/render.tsx\n\nvar render = (node, options) => __async(void 0, null, function* () {\n  const suspendedElement = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, { children: node });\n  const reactDOMServer = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(2)]).then(__webpack_require__.t.bind(__webpack_require__, /*! react-dom/server */ \"(rsc)/./node_modules/next/dist/compiled/react-dom/server.js\", 19)).then(\n    // This is beacuse react-dom/server is CJS\n    (m) => m.default\n  );\n  let html2;\n  if (Object.hasOwn(reactDOMServer, \"renderToReadableStream\")) {\n    html2 = yield readStream(\n      yield reactDOMServer.renderToReadableStream(suspendedElement)\n    );\n  } else {\n    yield new Promise((resolve, reject) => {\n      const stream = reactDOMServer.renderToPipeableStream(suspendedElement, {\n        onAllReady() {\n          return __async(this, null, function* () {\n            html2 = yield readStream(stream);\n            resolve();\n          });\n        },\n        onError(error) {\n          reject(error);\n        }\n      });\n    });\n  }\n  if (options == null ? void 0 : options.plainText) {\n    return (0,html_to_text__WEBPACK_IMPORTED_MODULE_0__.convert)(html2, __spreadValues({\n      selectors: plainTextSelectors\n    }, options.htmlToTextOptions));\n  }\n  const doctype = '<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">';\n  const document = `${doctype}${html2.replace(/<!DOCTYPE.*?>/, \"\")}`;\n  if (options == null ? void 0 : options.pretty) {\n    return pretty(document);\n  }\n  return document;\n});\n\n// src/node/index.ts\nvar renderAsync = (element, options) => {\n  return render(element, options);\n};\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/@react-email/render/dist/node/index.mjs\n");

/***/ })

};
;