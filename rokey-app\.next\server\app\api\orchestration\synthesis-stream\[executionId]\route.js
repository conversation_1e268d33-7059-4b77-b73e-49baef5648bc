"use strict";(()=>{var e={};e.id=6487,e.ids=[6487,9704],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5649:(e,t,s)=>{s.d(t,{y:()=>r});var o=s(68811);class r{constructor(e,t){this.classificationApiKey=e,this.executionId=t}async validateStepOutput(e,t,s,r,i){let n=`As an AI orchestration moderator, evaluate this step output:

Original Request: "${r}"
Role: ${t}
Expected Outcome: ${i}
Actual Output: "${s}"

Evaluate:
1. Does the output fulfill the role's responsibility?
2. Is the quality sufficient for the next step?
3. Are there any issues or gaps?
4. Can we proceed to the next step?

Respond in JSON format:
{
  "isValid": true/false,
  "quality": 0.85,
  "issues": ["list of any issues"],
  "suggestions": ["list of improvements"],
  "canProceed": true/false,
  "reasoning": "detailed explanation"
}`;try{let s=await this.callModerator(n),r=JSON.parse(s);return await (0,o.Zi)(this.executionId,"moderator_commentary",{commentary:`Validating ${t} output: ${r.reasoning}`,validation:r},e,t),r}catch(e){return{isValid:s.length>50,quality:.7,issues:[],suggestions:[],canProceed:!0}}}async resolveConflicts(e,t){let s=`As an AI orchestration moderator, resolve conflicts between multiple AI outputs:

Original Request: "${t}"

Conflicting Outputs:
${e.map((e,t)=>`${t+1}. ${e.roleId} (confidence: ${e.confidence}): "${e.output}"`).join("\n")}

Determine the best approach:
1. Choose the best output
2. Combine elements from multiple outputs
3. Request modifications
4. Escalate for human review

Respond in JSON format:
{
  "action": "proceed|retry|escalate|modify|synthesize",
  "reasoning": "detailed explanation",
  "modifications": "specific changes needed (if action is modify)",
  "confidence": 0.85,
  "nextSteps": ["list of next actions"]
}`;try{let e=await this.callModerator(s),t=JSON.parse(e);return await (0,o.Zi)(this.executionId,"moderator_commentary",{commentary:`Conflict resolution: ${t.reasoning}`,decision:t}),t}catch(s){let t=e.reduce((e,t)=>t.confidence>e.confidence?t:e);return{action:"proceed",reasoning:`Selected ${t.roleId} output with highest confidence (${t.confidence})`,confidence:.6,nextSteps:["continue_with_selected_output"]}}}async synthesizeOutputs(e,t){await (0,o.Zi)(this.executionId,"synthesis_started",{commentary:"\uD83E\uDDE9 Beginning synthesis of all specialist outputs...",totalSteps:e.length});let s=`As an AI orchestration moderator, synthesize multiple specialist outputs into one cohesive response:

Original Request: "${t}"

Specialist Outputs:
${e.map(e=>`${e.stepNumber}. ${e.roleId} (quality: ${e.quality}): "${e.output}"`).join("\n\n")}

Create a comprehensive, well-structured response that:
1. Integrates all valuable insights
2. Maintains logical flow
3. Resolves any contradictions
4. Provides a complete answer to the original request

Respond in JSON format:
{
  "combinedOutput": "the synthesized response",
  "methodology": "how you combined the outputs",
  "qualityScore": 0.92,
  "conflictsResolved": ["list of conflicts resolved"],
  "improvements": ["how the synthesis improved upon individual outputs"]
}`;try{await (0,o.Zi)(this.executionId,"synthesis_progress",{commentary:"\uD83D\uDD04 Analyzing specialist contributions...",progress:.3});let e=await this.callModerator(s);await (0,o.Zi)(this.executionId,"synthesis_progress",{commentary:"\uD83C\uDFA8 Weaving outputs together...",progress:.7});let t=JSON.parse(e);return await (0,o.Zi)(this.executionId,"synthesis_progress",{commentary:"✨ Finalizing synthesized response...",progress:1}),t}catch(t){return{combinedOutput:e.map(e=>`**${e.roleId} Contribution:**
${e.output}`).join("\n\n"),methodology:"Simple concatenation due to synthesis error",qualityScore:.6,conflictsResolved:[],improvements:[]}}}generateLiveCommentary(e,t){let s={orchestration_started:["\uD83C\uDFAC Alright team, we've got an interesting challenge ahead!","\uD83D\uDE80 Let's break this down and see who's best suited for each part.","\uD83C\uDFAF Time to coordinate our AI specialists for optimal results."],task_decomposed:["\uD83D\uDCCB Task analysis complete! I've identified the perfect team composition.","\uD83C\uDFAA Perfect breakdown! Each specialist will handle their area of expertise.","⚡ Task decomposition successful! Ready to assign specialists."],step_assigned:[`📋 Assigning ${t.roleId} specialist to handle this part.`,`🎪 Our ${t.roleId} expert is stepping up to the plate!`,`⚡ Perfect match - ${t.roleId} is exactly what we need here.`],step_started:[`🎬 ${t.roleId} is diving deep into this challenge...`,`⚡ Watch ${t.roleId} work their specialized magic!`,`🎯 ${t.roleId} is laser-focused on delivering excellence.`],step_progress:[`🔥 ${t.roleId} is making great progress...`,`⚙️ The gears are turning smoothly in ${t.roleId}'s domain!`,`🌟 ${t.roleId} is crafting something special...`],step_streaming:[`📡 ${t.roleId} is streaming their thoughts in real-time...`,`⚡ Live updates from ${t.roleId} - watch the magic happen!`,`🌊 ${t.roleId} is flowing with brilliant insights...`],step_completed:[`✅ Excellent work from ${t.roleId}! Moving to the next phase.`,`🎉 ${t.roleId} delivered exactly what we needed. Handoff time!`,`💫 Beautiful execution by ${t.roleId}. The team is flowing perfectly.`],step_failed:[`⚠️ ${t.roleId} hit a snag, but we're adapting quickly!`,`🔄 Minor setback with ${t.roleId} - implementing recovery strategy.`,`🛠️ ${t.roleId} needs a different approach. Adjusting tactics...`],synthesis_started:["\uD83E\uDDE9 Time for the grand finale! I'm combining all these brilliant contributions...","\uD83C\uDFAD Watch as I orchestrate these individual masterpieces into one cohesive symphony!","\uD83C\uDF1F The magic happens now - weaving together all our specialists' expertise!"],synthesis_progress:["\uD83D\uDD04 Synthesis in progress - combining specialist outputs...","\uD83C\uDFA8 Weaving together the brilliant contributions...","⚙️ Processing and harmonizing all the expert insights..."],synthesis_streaming:["\uD83D\uDCE1 Streaming the synthesis process live...","\uD83C\uDF0A Watch the final result take shape in real-time...","⚡ Live synthesis - see how all pieces come together..."],synthesis_complete:["\uD83C\uDF8A Synthesis complete! All specialist outputs have been perfectly combined.","✨ The final masterpiece is ready! What an incredible team effort.","\uD83C\uDFC6 Synthesis successful! The AI team has delivered excellence."],orchestration_completed:["\uD83C\uDF89 What a performance! Our AI team delivered something truly remarkable.","✨ Mission accomplished! Each specialist played their part perfectly.","\uD83C\uDFC6 Outstanding collaboration - this is what AI teamwork looks like!"],orchestration_failed:["⚠️ The orchestration encountered issues, but we're learning from this.","\uD83D\uDD04 Orchestration failed - analyzing what went wrong for next time.","\uD83D\uDEE0️ Technical difficulties occurred - implementing improvements."],moderator_commentary:["\uD83C\uDF99️ Moderator providing guidance and coordination...","\uD83D\uDCCB Quality check and process optimization in progress...","\uD83C\uDFAF Ensuring optimal team coordination and output quality..."],specialist_message:[`💬 ${t.roleId} is sharing insights with the team...`,`🗣️ ${t.roleId} has something important to communicate...`,`📢 ${t.roleId} is contributing to the team discussion...`],moderator_assignment:[`🎯 Moderator assigning ${t.roleId} to the next task...`,`📋 Task delegation: ${t.roleId} is now taking the lead...`,`⚡ ${t.roleId} has been selected for this specialized work...`],specialist_acknowledgment:[`✅ ${t.roleId} acknowledges the assignment and is ready to proceed.`,`👍 ${t.roleId} confirms understanding and begins work.`,`🎯 ${t.roleId} is locked and loaded for this task.`],handoff_message:[`🤝 ${t.roleId} is handing off to the next specialist...`,`📤 ${t.roleId} has completed their part - passing the baton...`,`✨ ${t.roleId} finished beautifully - next specialist incoming...`],clarification_request:[`❓ ${t.roleId} needs clarification to deliver the best results...`,`🤔 ${t.roleId} is asking for more details to optimize their output...`,`💭 ${t.roleId} wants to ensure they understand the requirements perfectly...`],clarification_response:[`💡 Clarification provided - ${t.roleId} now has what they need!`,`✅ Question answered - ${t.roleId} can proceed with confidence.`,`🎯 All clear! ${t.roleId} has the details needed for success.`]}[e]||["\uD83E\uDD16 Processing..."];return s[Math.floor(Math.random()*s.length)]}async callModerator(e){let t=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.classificationApiKey}`},body:JSON.stringify({model:"gemini-2.0-flash-lite",messages:[{role:"system",content:"You are an expert AI orchestration moderator. You coordinate multiple AI specialists, validate their work, resolve conflicts, and synthesize their outputs into cohesive results. Always respond in the requested JSON format."},{role:"user",content:e}],temperature:.2,max_tokens:2e3,response_format:{type:"json_object"}})});if(!t.ok)throw Error(`Moderator API error: ${t.status}`);let s=await t.json(),o=s.choices?.[0]?.message?.content;if(!o)throw Error("Empty moderator response");return o}async analyzeParallelizationOpportunities(e){let t=`Analyze these orchestration steps for parallelization opportunities:

Steps:
${e.map(e=>`${e.stepNumber}. ${e.roleId}: "${e.prompt}" (depends on: ${e.dependencies.join(", ")||"none"})`).join("\n")}

Determine:
1. Which steps can run in parallel
2. Optimal grouping strategy
3. Expected performance improvement

Respond in JSON format:
{
  "parallelGroups": [[1], [2, 3], [4]],
  "reasoning": "explanation of grouping strategy",
  "estimatedSpeedup": 1.8
}`;try{let e=await this.callModerator(t);return JSON.parse(e)}catch(t){return{parallelGroups:e.map(e=>[e.stepNumber]),reasoning:"Sequential execution due to analysis error",estimatedSpeedup:1}}}}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{e.exports=require("tls")},40305:(e,t,s)=>{let o;s.r(t),s.d(t,{patchFetch:()=>T,routeModule:()=>y,serverHooks:()=>x,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>_});var r={};s.r(r),s.d(r,{GET:()=>f});var i=s(96559),n=s(48088),a=s(37719),l=s(32190),c=s(2507),u=s(68811),d=s(5649),p=s(55511),h=s.n(p);async function m(e,t,s,o){let r=await (0,c.createSupabaseServerClientOnRequest)(),{error:i}=await r.from("synthesis_storage").upsert({synthesis_id:e,conversation_id:t,complete_synthesis:s.join(""),chunks:s,total_chunks:s.length,created_at:new Date().toISOString(),last_access_time:new Date().toISOString()});if(i)throw Error(`Failed to store dynamic chunk: ${i.message}`)}try{o=s(99704).createFirstTokenTrackingStream}catch(e){o=e=>e}async function g(e,t={},s=2,o=300){let r=t.timeout||6e4;try{let s=new AbortController,o=setTimeout(()=>s.abort(),r),i={...t,signal:s.signal},n=await fetch(e,i);return clearTimeout(o),n}catch(r){if(s>0)return await new Promise(e=>setTimeout(e,o)),g(e,t,s-1,2*o);throw Error(`Fetch failed after multiple attempts: ${r.message}`)}}async function f(e,{params:t}){let{executionId:s}=await t;if(!s)return l.NextResponse.json({error:"Execution ID is required"},{status:400});let r=await (0,c.createSupabaseServerClientOnRequest)();try{let e,t,{data:i,error:n}=await r.from("orchestration_executions").select("*").eq("id",s).single();if(n)return l.NextResponse.json({error:`Orchestration execution not found: ${n.message}`},{status:404});if(!i)return l.NextResponse.json({error:"Orchestration execution not found"},{status:404});let{data:a,error:c}=await r.from("orchestration_steps").select("step_number, role_id, response, prompt").eq("execution_id",s).eq("status","completed").order("step_number",{ascending:!0});if(c)return l.NextResponse.json({error:`Error fetching steps: ${c.message}`},{status:500});if(!a||0===a.length)return l.NextResponse.json({error:"No completed steps found for synthesis"},{status:400});let p=a[0]?.prompt?.split('"')[1]||"user request",f=`You are the final moderator synthesizing the work of multiple AI specialists who collaborated on this request: "${p}"

Here are the outputs from each specialist:

${a.map(e=>`**${e.role_id.toUpperCase()} (Step ${e.step_number}):**
${e.response}

`).join("\n")}

Your task is to:
1. Combine these outputs into a single, cohesive, and complete response
2. Ensure the final result fully addresses the original user request
3. Present it in a clear, well-structured format
4. Include any necessary explanations or instructions for the user

Provide the final, polished response that the user will receive:`,y=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!y)return l.NextResponse.json({error:"Classification API key not found"},{status:500});let w=new d.y(y,s);try{if(!(e=await g("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${y}`,"User-Agent":"RoKey/1.0 (Synthesis)",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify({model:"gemini-2.0-flash-001",messages:[{role:"user",content:f}],stream:!0,temperature:.3,max_tokens:8e3})})).ok){let t=await e.text().catch(()=>"Could not read error response");return l.NextResponse.json({error:`Synthesis API call failed: ${e.status}, ${t}`},{status:e.status})}}catch(e){return l.NextResponse.json({error:`Synthesis API call exception: ${e}`},{status:500})}let _=e.body.getReader(),x=new TextEncoder,T=new TextDecoder,I="",S="",v=0,O="",k=[],$=`synthesis_${s}`,b=`synthesis_${$}_${Date.now()}`,E={MAX_CHARS:2e4,MIN_CHUNK_SIZE:1e3,SEMANTIC_BREAK_BONUS:500},N=async()=>{if(O.length<E.MIN_CHUNK_SIZE)return;let e=O;for(let t of["\n\n","\n",". ","! ","? "]){let s=O.lastIndexOf(t);if(s>E.MIN_CHUNK_SIZE){e=O.substring(0,s+t.length),O=O.substring(s+t.length);break}}e===O&&(O=""),k.push(e);try{await m(b,$,k,v),v++}catch(e){}},A=new ReadableStream({async start(e){try{for(;;){let{done:t,value:o}=await _.read();if(t){if(O.length>0){k.push(O);try{await m(b,$,k,v)}catch(e){}}try{let{data:t}=await r.from("orchestration_executions").select("status").eq("id",s).single();await r.from("orchestration_executions").update({status:"completed",completed_at:new Date().toISOString(),final_response:I}).eq("id",s);let{data:o}=await r.from("orchestration_executions").select("created_at, status").eq("id",s).single(),i=o?.created_at?Date.now()-new Date(o.created_at).getTime():0,{data:n}=await r.from("orchestration_steps").select("step_number").eq("execution_id",s),a=n?.length||0;(0,u.tl)(s,{id:h().randomUUID(),execution_id:s,type:"synthesis_complete",timestamp:new Date().toISOString(),data:{message:(0,u.re)("synthesis_complete"),result:I,totalSteps:a,totalDuration:i}}),await new Promise(e=>setTimeout(e,1e3)),(0,u.tl)(s,{id:h().randomUUID(),execution_id:s,type:"orchestration_completed",timestamp:new Date().toISOString(),data:{commentary:w.generateLiveCommentary("orchestration_completed",{totalSteps:a}),finalResult:I,totalSteps:a,totalDuration:i}}),e.enqueue(x.encode("event: done\ndata: {}\n\n")),e.enqueue(x.encode("data: [DONE]\n\n"))}catch(e){}e.close();break}for(let e of T.decode(o,{stream:!0}).split("\n"))if(e.startsWith("data: ")&&!e.includes("[DONE]"))try{let t=e.substring(6),o=JSON.parse(t);if(o.choices?.[0]?.delta?.content){let e=o.choices[0].delta.content;if(I+=e,S+=e,O+=e,(0,u.tl)(s,{id:h().randomUUID(),execution_id:s,type:"synthesis_streaming",timestamp:new Date().toISOString(),data:{commentary:"\uD83C\uDFA8 Streaming synthesis response...",partialResult:S,progress:Math.min(.9,S.length/1e3),chunkIndex:v,chunkProgress:O.length}}),O.length>=E.MAX_CHARS-E.SEMANTIC_BREAK_BONUS)try{await N()}catch(e){}}}catch(e){}e.enqueue(o)}}catch(t){e.error(t)}}});try{t=o(A,"Gemini","gemini-2.0-flash-001")}catch(e){t=A}let R=new Headers({"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","X-RoKey-Stream-Type":"synthesis","X-RoKey-Execution-ID":s});return setTimeout(async()=>{try{let{data:e}=await r.from("orchestration_executions").select("status").eq("id",s).single();e?.status!=="completed"&&(await r.from("orchestration_executions").update({status:"completed",completed_at:new Date().toISOString(),final_response:"Synthesis timed out. Please check the individual specialist outputs."}).eq("id",s),(0,u.tl)(s,{id:h().randomUUID(),execution_id:s,type:"orchestration_completed",timestamp:new Date().toISOString(),data:{commentary:"⚠️ Synthesis timed out, but specialist outputs are available.",finalResult:"Synthesis timed out. Please check the individual specialist outputs.",totalSteps:0,totalDuration:3e4}}))}catch(e){}},3e4),new Response(t,{headers:R})}catch(e){return l.NextResponse.json({error:"Internal server error"},{status:500})}}let y=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/orchestration/synthesis-stream/[executionId]/route",pathname:"/api/orchestration/synthesis-stream/[executionId]",filename:"route",bundlePath:"app/api/orchestration/synthesis-stream/[executionId]/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\orchestration\\synthesis-stream\\[executionId]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:w,workUnitAsyncStorage:_,serverHooks:x}=y;function T(){return(0,a.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:_})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},99704:(e,t,s)=>{function o(e,t,s,o){let r=e.getReader(),i=new TextDecoder;return new TextEncoder,new ReadableStream({async start(e){let s=!1,l="";Date.now();try{for(;;){let{done:c,value:u}=await r.read();if(c){if(o&&!s){let e=a(l,t);o(e)}e.close();break}let d=i.decode(u,{stream:!0});if(!s&&d.includes("delta"))try{for(let e of d.split("\n"))if(e.startsWith("data: ")&&!e.includes("[DONE]")){let t=e.substring(6);try{let e=JSON.parse(t);if(e.choices?.[0]?.delta?.content){Date.now(),s=!0,l+=e.choices[0].delta.content;break}}catch(e){}}}catch(e){s||(Date.now(),s=!0)}if(o&&d.includes("usage"))try{let e=n(d,t);e&&o(e)}catch(e){}if(d.includes("delta"))try{for(let e of d.split("\n"))if(e.startsWith("data: ")&&!e.includes("[DONE]")){let t=e.substring(6);try{let e=JSON.parse(t);e.choices?.[0]?.delta?.content&&(l+=e.choices[0].delta.content)}catch(e){}}}catch(e){}e.enqueue(u)}}catch(t){t instanceof Error&&(t.message.includes("aborted")||t.message.includes("ECONNRESET"))?e.close():e.error(t)}}})}function r(e,t,s){void 0!==s.timeToFirstToken&&(s.timeToFirstToken<500||s.timeToFirstToken<1e3||s.timeToFirstToken),s.totalStreamTime,s.totalTokens,s.averageTokenLatency}function i(e,t){return{provider:e||"unknown",model:t||"unknown"}}function n(e,t){try{for(let s of e.split("\n"))if(s.startsWith("data: ")&&!s.includes("[DONE]")){let e=s.substring(6);try{let s=JSON.parse(e);if(s.usage){let e,o=s.usage,r=0,i=0,n=0;if("openrouter"===t.toLowerCase()?(r=o.prompt_tokens||0,i=o.completion_tokens||0,n=o.total_tokens||r+i,e=o.cost?1e-6*o.cost:void 0):(r=o.prompt_tokens||o.input_tokens||0,i=o.completion_tokens||o.output_tokens||0,n=o.total_tokens||r+i),n>0)return{promptTokens:r,completionTokens:i,totalTokens:n,cost:e}}}catch(e){}}}catch(e){}return null}function a(e,t,s){let o=.25;switch(t.toLowerCase()){case"openrouter":case"openai":case"xai":o=.25;break;case"google":o=.22;break;case"anthropic":o=.26}let r=Math.ceil(e.length*o),i=s?Math.ceil(s.length*o):Math.ceil(.3*r);return{promptTokens:i,completionTokens:r,totalTokens:i+r}}function l(e){return Math.ceil(e.length/4)}s.r(t),s.d(t,{PERFORMANCE_THRESHOLDS:()=>c,createFirstTokenTrackingStream:()=>o,estimateTokenCount:()=>l,estimateUsageFromContent:()=>a,evaluatePerformance:()=>u,extractUsageFromStreamChunk:()=>n,getProviderModelFromContext:()=>i,logStreamingPerformance:()=>r});let c={EXCELLENT_FIRST_TOKEN:500,GOOD_FIRST_TOKEN:1e3,SLOW_FIRST_TOKEN:2e3,EXCELLENT_TOTAL:3e3,GOOD_TOTAL:5e3,SLOW_TOTAL:1e4,TARGET_TOKEN_LATENCY:50};function u(e){let t=e.timeToFirstToken?e.timeToFirstToken<c.EXCELLENT_FIRST_TOKEN?"excellent":e.timeToFirstToken<c.GOOD_FIRST_TOKEN?"good":e.timeToFirstToken<c.SLOW_FIRST_TOKEN?"slow":"very_slow":"very_slow",s=e.totalStreamTime?e.totalStreamTime<c.EXCELLENT_TOTAL?"excellent":e.totalStreamTime<c.GOOD_TOTAL?"good":e.totalStreamTime<c.SLOW_TOTAL?"slow":"very_slow":"very_slow",o=e.averageTokenLatency?e.averageTokenLatency<c.TARGET_TOKEN_LATENCY?"excellent":e.averageTokenLatency<2*c.TARGET_TOKEN_LATENCY?"good":e.averageTokenLatency<4*c.TARGET_TOKEN_LATENCY?"slow":"very_slow":"very_slow",r=["excellent","good","slow","very_slow"],i=[t,s,o].reduce((e,t)=>r.indexOf(t)>r.indexOf(e)?t:e,"excellent");return{firstTokenGrade:t,totalTimeGrade:s,tokenLatencyGrade:o,overallGrade:i}}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),o=t.X(0,[4447,580,9398,3410,367],()=>s(40305));module.exports=o})();