(()=>{var e={};e.id=6715,e.ids=[6715],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7521:(e,s,r)=>{Promise.resolve().then(r.bind(r,97981))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14281:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(65239),i=r(48088),o=r(88170),n=r.n(o),l=r(30893),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);r.d(s,a);let d={children:["",{children:["test-full-browsing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,24143)),"C:\\RoKey App\\rokey-app\\src\\app\\test-full-browsing\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\test-full-browsing\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-full-browsing/page",pathname:"/test-full-browsing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24143:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\test-full-browsing\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\test-full-browsing\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71073:(e,s,r)=>{Promise.resolve().then(r.bind(r,24143))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97981:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(60687),i=r(43210);function o(){let[e,s]=(0,i.useState)(null),[r,o]=(0,i.useState)(!1),[n,l]=(0,i.useState)(null),a=async()=>{o(!0),l(null),s(null);try{let e={id:`test_workflow_${Date.now()}`,nodes:[{id:"user-request-1",type:"userRequest",position:{x:100,y:100},data:{label:"User Request",config:{},isConfigured:!0}},{id:"memory-1",type:"memory",position:{x:100,y:200},data:{label:"Memory Brain",config:{memoryName:"browsing_memory",maxSize:10240,encryption:!0},isConfigured:!0}},{id:"planner-1",type:"planner",position:{x:300,y:200},data:{label:"AI Planner",config:{modelId:"gemini-pro",providerId:"google",maxSubtasks:5},isConfigured:!0}},{id:"browsing-1",type:"browsing",position:{x:500,y:200},data:{label:"Intelligent Browsing",config:{maxSites:5,timeout:30,enableScreenshots:!0,enableFormFilling:!0,searchEngines:["google"],maxDepth:2},isConfigured:!0}},{id:"provider-1",type:"provider",position:{x:700,y:200},data:{label:"AI Provider",config:{providerId:"google",modelId:"gemini-pro",apiKey:"test-key"},isConfigured:!0}}],edges:[{id:"e1",source:"user-request-1",target:"browsing-1",type:"smoothstep"},{id:"e2",source:"memory-1",target:"browsing-1",type:"smoothstep"},{id:"e3",source:"planner-1",target:"browsing-1",sourceHandle:"output",targetHandle:"planner",type:"smoothstep"},{id:"e4",source:"browsing-1",target:"provider-1",type:"smoothstep"}]},r="Find the latest iPhone 15 Pro prices from multiple retailers and compare features",t=await fetch("/api/manual-build/execute-workflow",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({workflowId:e.id,nodes:e.nodes,edges:e.edges,userInput:r})});if(!t.ok){let e=await t.json();throw Error(e.details||"Workflow execution failed")}let i=await t.json();s({workflow:e,execution:i,userInput:r,timestamp:new Date().toISOString()})}catch(e){l(e instanceof Error?e.message:"Unknown error")}finally{o(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-[#040716] text-white p-8",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"\uD83C\uDF10 Full Browsing System Test"}),(0,t.jsxs)("div",{className:"mb-8 p-6 bg-gray-800/50 rounded-lg border border-gray-700",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Scenario"}),(0,t.jsx)("p",{className:"text-gray-300 mb-4",children:"This test creates a complete browsing workflow with all nodes connected:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside text-gray-300 space-y-2",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"User Request Node:"})," Provides the browsing task"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Memory Node:"})," Tracks progress and stores context"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Planner Node:"})," Creates intelligent browsing plans"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Browsing Node:"})," Executes multi-step browsing with smart completion"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"AI Provider Node:"})," Processes and synthesizes results"]})]})]}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)("button",{onClick:a,disabled:r,className:"bg-[#ff6b35] hover:bg-[#e55a2b] disabled:bg-gray-600 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200 text-lg",children:r?"\uD83D\uDD04 Running Complex Browsing Test...":"\uD83D\uDE80 Test Full Browsing System"})}),n&&(0,t.jsxs)("div",{className:"mb-6 p-4 bg-red-900/50 border border-red-500 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-red-400 mb-2",children:"❌ Test Failed"}),(0,t.jsx)("p",{className:"text-red-300",children:n})]}),e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-green-900/30 border border-green-500 rounded-lg p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-green-400 mb-4",children:"✅ Test Completed Successfully"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-white mb-2",children:"Workflow Configuration"}),(0,t.jsxs)("div",{className:"text-sm text-gray-300 bg-gray-800 p-3 rounded",children:[(0,t.jsxs)("div",{children:["Nodes: ",e.workflow.nodes.length]}),(0,t.jsxs)("div",{children:["Edges: ",e.workflow.edges.length]}),(0,t.jsxs)("div",{children:["Task: ",e.userInput]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-white mb-2",children:"Execution Status"}),(0,t.jsxs)("div",{className:"text-sm text-gray-300 bg-gray-800 p-3 rounded",children:[(0,t.jsxs)("div",{children:["Status: ",e.execution.success?"✅ Success":"❌ Failed"]}),(0,t.jsxs)("div",{children:["Executed At: ",new Date(e.execution.executedAt).toLocaleString()]}),(0,t.jsxs)("div",{children:["Workflow ID: ",e.execution.workflowId]})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-800 p-6 rounded-lg",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDCCA Detailed Results"}),(0,t.jsx)("pre",{className:"text-sm overflow-auto max-h-96 bg-gray-900 p-4 rounded",children:JSON.stringify(e.execution.result,null,2)})]}),(0,t.jsxs)("div",{className:"bg-blue-900/30 border border-blue-500 rounded-lg p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-blue-400 mb-4",children:"\uD83E\uDDE0 How It Works"}),(0,t.jsxs)("div",{className:"text-sm text-gray-300 space-y-2",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"1. User Request:"})," Provides the browsing task to the system"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"2. Memory Connection:"})," Browsing node connects to memory for persistent tracking"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"3. AI Planning:"})," Planner creates detailed multi-step browsing strategy"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"4. Intelligent Browsing:"})," Executes searches, analyzes results, selects best sites"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"5. Smart Completion:"})," Detects when sufficient information is gathered"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"6. AI Processing:"})," Provider node synthesizes and presents final results"]})]})]})]})]})})}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,5449,4912],()=>r(14281));module.exports=t})();