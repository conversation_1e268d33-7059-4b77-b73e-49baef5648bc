(()=>{var e={};e.id=2934,e.ids=[2934],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8372:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>c});var o=t(96559),n=t(48088),a=t(37719),i=t(32190),u=t(750);async function c(e){try{let r,t=await e.json(),{action:s,url:o,query:n,searchEngine:a="google",extractionType:c="content",customSelector:p,timeout:d}=t;if(!s)return i.NextResponse.json({error:"Action is required"},{status:400});let x=u.A.getInstance();switch(s){case"navigate":if(!o)return i.NextResponse.json({error:"URL is required for navigate action"},{status:400});r=await x.navigateAndExtract(o,p);break;case"search":if(!n)return i.NextResponse.json({error:"Query is required for search action"},{status:400});r=await x.searchAndExtractUnblocked(n,a);break;case"screenshot":if(!o)return i.NextResponse.json({error:"URL is required for screenshot action"},{status:400});r=await x.takeScreenshot(o);break;case"custom":if(!o)return i.NextResponse.json({error:"URL is required for custom action"},{status:400});let w=t.code;if(!w)return i.NextResponse.json({error:"Code is required for custom action"},{status:400});r=await x.executeFunction(w,{url:o,...t.context},{timeout:d});break;default:return i.NextResponse.json({error:`Unknown action: ${s}`},{status:400})}return i.NextResponse.json({success:!0,data:r.data,action:s,timestamp:new Date().toISOString()})}catch(e){return i.NextResponse.json({error:"Web browsing failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(){try{let e=u.A.getInstance().getStats();return i.NextResponse.json({status:"operational",service:"web-browsing",stats:e,timestamp:new Date().toISOString()})}catch(e){return i.NextResponse.json({status:"error",service:"web-browsing",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/tools/web-browsing/route",pathname:"/api/tools/web-browsing",filename:"route",bundlePath:"app/api/tools/web-browsing/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\tools\\web-browsing\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:w,serverHooks:g}=d;function l(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:w})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,750],()=>t(8372));module.exports=s})();