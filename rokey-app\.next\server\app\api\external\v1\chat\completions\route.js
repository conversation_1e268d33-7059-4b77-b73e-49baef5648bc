(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{2:(e,t,r)=>{"use strict";function n(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}r.d(t,{Q:()=>n})},7:(e,t,r)=>{"use strict";r.r(t),r.d(t,{DynamicServerError:()=>i,isDynamicServerError:()=>s});let n="DYNAMIC_SERVER_USAGE";class i extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=n}}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===n}},50:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});class n{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}},96:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=n(r(4306));class s extends i.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let r=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:n}={}){let i="";"plain"===n?i="pl":"phrase"===n?i="ph":"websearch"===n&&(i="w");let s=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${i}fts${s}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){let n=r?`${r}.or`:"or";return this.url.searchParams.append(n,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}t.default=s},252:(e,t,r)=>{"use strict";r.d(t,{W:()=>s});class n extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest="HANGING_PROMISE_REJECTION"}}let i=new WeakMap;function s(e,t){if(e.aborted)return Promise.reject(new n(t));{let r=new Promise((r,s)=>{let a=s.bind(null,new n(t)),o=i.get(e);if(o)o.push(a);else{let t=[a];i.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(a),r}}function a(){}},362:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=n(r(3738)),s=n(r(4487));class a{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=i.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,n;let i=null,a=null,o=null,l=e.status,u=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(a="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let n=null==(t=this.headers.Prefer)?void 0:t.match(/count=(exact|planned|estimated)/),s=null==(r=e.headers.get("content-range"))?void 0:r.split("/");n&&s&&s.length>1&&(o=parseInt(s[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(i={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,l=406,u="Not Acceptable"):a=1===a.length?a[0]:null)}else{let t=await e.text();try{i=JSON.parse(t),Array.isArray(i)&&404===e.status&&(a=[],i=null,l=200,u="OK")}catch(r){404===e.status&&""===t?(l=204,u="No Content"):i={message:t}}if(i&&this.isMaybeSingle&&(null==(n=null==i?void 0:i.details)?void 0:n.includes("0 rows"))&&(i=null,l=200,u="OK"),i&&this.shouldThrowOnError)throw new s.default(i)}return{error:i,data:a,count:o,status:l,statusText:u}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,n;return{error:{message:`${null!=(t=null==e?void 0:e.name)?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!=(r=null==e?void 0:e.stack)?r:""}`,hint:"",code:`${null!=(n=null==e?void 0:e.code)?n:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=a},364:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let i=n(r(1750));t.PostgrestClient=i.default;let s=n(r(9574));t.PostgrestQueryBuilder=s.default;let a=n(r(96));t.PostgrestFilterBuilder=a.default;let o=n(r(4306));t.PostgrestTransformBuilder=o.default;let l=n(r(362));t.PostgrestBuilder=l.default;let u=n(r(4487));t.PostgrestError=u.default,t.default={PostgrestClient:i.default,PostgrestQueryBuilder:s.default,PostgrestFilterBuilder:a.default,PostgrestTransformBuilder:o.default,PostgrestBuilder:l.default,PostgrestError:u.default}},424:(e,t,r)=>{"use strict";r.d(t,{E0:()=>a,FP:()=>n.e,XN:()=>i,fm:()=>s});var n=r(2223);function i(e){let t=n.e.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}function s(e){return"prerender"===e.type||"prerender-ppr"===e.type?e.prerenderResumeDataCache:null}function a(e){return"prerender-legacy"!==e.type&&"cache"!==e.type&&"unstable-cache"!==e.type?"request"===e.type?e.renderResumeDataCache:e.prerenderResumeDataCache:null}r(7612)},556:(e,t,r)=>{"use strict";r.d(t,{Y:()=>i,P:()=>s});var n=r(6243);function i(e){return(0,n.A)(e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},808:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},897:(e,t,r)=>{"use strict";let n;r.d(t,{EK:()=>_,v8:()=>c});var i=r(5455),s=r(2);let{context:a,propagation:o,trace:l,SpanStatusCode:u,SpanKind:c,ROOT_CONTEXT:d}=n=r(5293);class h extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let f=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof h})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:u.ERROR,message:null==t?void 0:t.message})),e.end()},p=new Map,g=n.createContextKey("next.rootSpanId"),m=0,y=()=>m++,v={set(e,t,r){e.push({key:t,value:r})}};class b{getTracerInstance(){return l.getTracer("next.js","0.0.1")}getContext(){return a}getTracePropagationData(){let e=a.active(),t=[];return o.inject(e,t,v),t}getActiveScopeSpan(){return l.getSpan(null==a?void 0:a.active())}withPropagatedContext(e,t,r){let n=a.active();if(l.getSpanContext(n))return t();let i=o.extract(n,e,r);return a.with(i,t)}trace(...e){var t;let[r,n,o]=e,{fn:u,options:c}="function"==typeof n?{fn:n,options:{}}:{fn:o,options:{...n}},h=c.spanName??r;if(!i.KK.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||c.hideSpan)return u();let m=this.getSpanContext((null==c?void 0:c.parentSpan)??this.getActiveScopeSpan()),v=!1;m?(null==(t=l.getSpanContext(m))?void 0:t.isRemote)&&(v=!0):(m=(null==a?void 0:a.active())??d,v=!0);let b=y();return c.attributes={"next.span_name":h,"next.span_type":r,...c.attributes},a.with(m.setValue(g,b),()=>this.getTracerInstance().startActiveSpan(h,c,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{p.delete(b),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&i.EI.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};v&&p.set(b,new Map(Object.entries(c.attributes??{})));try{if(u.length>1)return u(e,t=>f(e,t));let t=u(e);if((0,s.Q)(t))return t.then(t=>(e.end(),t)).catch(t=>{throw f(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw f(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,s]=3===e.length?e:[e[0],{},e[1]];return i.KK.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof s&&(e=e.apply(this,arguments));let i=arguments.length-1,o=arguments[i];if("function"!=typeof o)return t.trace(r,e,()=>s.apply(this,arguments));{let n=t.getContext().bind(a.active(),o);return t.trace(r,e,(e,t)=>(arguments[i]=function(e){return null==t||t(e),n.apply(this,arguments)},s.apply(this,arguments)))}}:s}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?l.setSpan(a.active(),e):void 0}getRootSpanAttributes(){let e=a.active().getValue(g);return p.get(e)}setRootSpanAttribute(e,t){let r=a.active().getValue(g),n=p.get(r);n&&n.set(e,t)}}let _=(()=>{let e=new b;return()=>e})()},1092:(e,t,r)=>{"use strict";e.exports=r(4186)},1251:e=>{(()=>{"use strict";var t={328:e=>{e.exports=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,n),a=!1}finally{a&&delete r[e]}return s.exports}n.ab="//",e.exports=n(328)})()},1438:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return s}});let n=new(r(5521)).AsyncLocalStorage;function i(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let n=t.url(e);return{url:n,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function s(e,t,r){let s=i(e,t);return s?n.run(s,r):r()}function a(e,t){let r=n.getStore();return r||(e&&t?i(e,t):void 0)}},1496:(e,t,r)=>{"use strict";r.d(t,{yD:()=>n,Bs:()=>i});var n=function(e){return e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.PAGES="PAGES",e.FETCH="FETCH",e.REDIRECT="REDIRECT",e.IMAGE="IMAGE",e}({}),i=function(e){return e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.PAGES="PAGES",e.FETCH="FETCH",e.IMAGE="IMAGE",e}({});r(897),r(5455),new Uint8Array([60,104,116,109,108]),new Uint8Array([60,98,111,100,121]),new Uint8Array([60,47,104,101,97,100,62]),new Uint8Array([60,47,98,111,100,121,62]),new Uint8Array([60,47,104,116,109,108,62]),new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62]),r(5356).Buffer;let s=new TextEncoder;r(8123),r(5356).Buffer,r(4144)},1750:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=n(r(9574)),s=n(r(96)),a=r(4705);class o{constructor(e,{headers:t={},schema:r,fetch:n}={}){this.url=e,this.headers=Object.assign(Object.assign({},a.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=n}from(e){let t=new URL(`${this.url}/${e}`);return new i.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new o(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:n=!1,count:i}={}){let a,o,l=new URL(`${this.url}/rpc/${e}`);r||n?(a=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(a="POST",o=t);let u=Object.assign({},this.headers);return i&&(u.Prefer=`count=${i}`),new s.default({method:a,url:l,headers:u,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}t.default=o},1818:(e,t,r)=>{!function(){var t={452:function(e){"use strict";e.exports=r(4102)}},n={};function i(e){var r=n[e];if(void 0!==r)return r.exports;var s=n[e]={exports:{}},a=!0;try{t[e](s,s.exports,i),a=!1}finally{a&&delete n[e]}return s.exports}i.ab="//";var s={};!function(){var e,t=(e=i(452))&&"object"==typeof e&&"default"in e?e.default:e,r=/https?|ftp|gopher|file/;function n(e){"string"==typeof e&&(e=y(e));var n,i,s,a,o,l,u,c,d,h=(i=(n=e).auth,s=n.hostname,a=n.protocol||"",o=n.pathname||"",l=n.hash||"",u=n.query||"",c=!1,i=i?encodeURIComponent(i).replace(/%3A/i,":")+"@":"",n.host?c=i+n.host:s&&(c=i+(~s.indexOf(":")?"["+s+"]":s),n.port&&(c+=":"+n.port)),u&&"object"==typeof u&&(u=t.encode(u)),d=n.search||u&&"?"+u||"",a&&":"!==a.substr(-1)&&(a+=":"),n.slashes||(!a||r.test(a))&&!1!==c?(c="//"+(c||""),o&&"/"!==o[0]&&(o="/"+o)):c||(c=""),l&&"#"!==l[0]&&(l="#"+l),d&&"?"!==d[0]&&(d="?"+d),{protocol:a,host:c,pathname:o=o.replace(/[?#]/g,encodeURIComponent),search:d=d.replace("#","%23"),hash:l});return""+h.protocol+h.host+h.pathname+h.search+h.hash}var a="http://",o=a+"w.w",l=/^([a-z0-9.+-]*:\/\/\/)([a-z0-9.+-]:\/*)?/i,u=/https?|ftp|gopher|file/;function c(e,t){var r="string"==typeof e?y(e):e;e="object"==typeof e?n(e):e;var i=y(t),s="";r.protocol&&!r.slashes&&(s=r.protocol,e=e.replace(r.protocol,""),s+="/"===t[0]||"/"===e[0]?"/":""),s&&i.protocol&&(s="",i.slashes||(s=i.protocol,t=t.replace(i.protocol,"")));var c=e.match(l);c&&!i.protocol&&(e=e.substr((s=c[1]+(c[2]||"")).length),/^\/\/[^/]/.test(t)&&(s=s.slice(0,-1)));var d=new URL(e,o+"/"),h=new URL(t,d).toString().replace(o,""),f=i.protocol||r.protocol;return f+=r.slashes||i.slashes?"//":"",!s&&f?h=h.replace(a,f):s&&(h=h.replace(a,"")),u.test(h)||~t.indexOf(".")||"/"===e.slice(-1)||"/"===t.slice(-1)||"/"!==h.slice(-1)||(h=h.slice(0,-1)),s&&(h=s+("/"===h[0]?h.substr(1):h)),h}function d(){}d.prototype.parse=y,d.prototype.format=n,d.prototype.resolve=c,d.prototype.resolveObject=c;var h=/^https?|ftp|gopher|file/,f=/^(.*?)([#?].*)/,p=/^([a-z0-9.+-]*:)(\/{0,3})(.*)/i,g=/^([a-z0-9.+-]*:)?\/\/\/*/i,m=/^([a-z0-9.+-]*:)(\/{0,2})\[(.*)\]$/i;function y(e,r,i){if(void 0===r&&(r=!1),void 0===i&&(i=!1),e&&"object"==typeof e&&e instanceof d)return e;var s=(e=e.trim()).match(f);e=s?s[1].replace(/\\/g,"/")+s[2]:e.replace(/\\/g,"/"),m.test(e)&&"/"!==e.slice(-1)&&(e+="/");var a=!/(^javascript)/.test(e)&&e.match(p),l=g.test(e),u="";a&&(h.test(a[1])||(u=a[1].toLowerCase(),e=""+a[2]+a[3]),a[2]||(l=!1,h.test(a[1])?(u=a[1],e=""+a[3]):e="//"+a[3]),3!==a[2].length&&1!==a[2].length||(u=a[1],e="/"+a[3]));var c,y=(s?s[1]:e).match(/^https?:\/\/[^/]+(:[0-9]+)(?=\/|$)/),v=y&&y[1],b=new d,_="",w="";try{c=new URL(e)}catch(t){_=t,u||i||!/^\/\//.test(e)||/^\/\/.+[@.]/.test(e)||(w="/",e=e.substr(1));try{c=new URL(e,o)}catch(e){return b.protocol=u,b.href=u,b}}b.slashes=l&&!w,b.host="w.w"===c.host?"":c.host,b.hostname="w.w"===c.hostname?"":c.hostname.replace(/(\[|\])/g,""),b.protocol=_?u||null:c.protocol,b.search=c.search.replace(/\\/g,"%5C"),b.hash=c.hash.replace(/\\/g,"%5C");var E=e.split("#");!b.search&&~E[0].indexOf("?")&&(b.search="?"),b.hash||""!==E[1]||(b.hash="#"),b.query=r?t.decode(c.search.substr(1)):b.search.substr(1),b.pathname=w+(a?c.pathname.replace(/['^|`]/g,function(e){return"%"+e.charCodeAt().toString(16).toUpperCase()}).replace(/((?:%[0-9A-F]{2})+)/g,function(e,t){try{return decodeURIComponent(t).split("").map(function(e){var t=e.charCodeAt();return t>256||/^[a-z0-9]$/i.test(e)?e:"%"+t.toString(16).toUpperCase()}).join("")}catch(e){return t}}):c.pathname),"about:"===b.protocol&&"blank"===b.pathname&&(b.protocol="",b.pathname=""),_&&"/"!==e[0]&&(b.pathname=b.pathname.substr(1)),u&&!h.test(u)&&"/"!==e.slice(-1)&&"/"===b.pathname&&(b.pathname=""),b.path=b.pathname+b.search,b.auth=[c.username,c.password].map(decodeURIComponent).filter(Boolean).join(":"),b.port=c.port,v&&!b.host.endsWith(v)&&(b.host+=v,b.port=v.slice(1)),b.href=w?""+b.pathname+b.search+b.hash:n(b);var x=/^(file)/.test(b.href)?["host","hostname"]:[];return Object.keys(b).forEach(function(e){~x.indexOf(e)||(b[e]=b[e]||null)}),b}s.parse=y,s.format=n,s.resolve=c,s.resolveObject=function(e,t){return y(c(e,t))},s.Url=d}(),e.exports=s}()},2058:(e,t,r)=>{"use strict";r.d(t,{xl:()=>a});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let s="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return s?new s:new i}},2223:(e,t,r)=>{"use strict";r.d(t,{e:()=>n});let n=(0,r(2058).xl)()},2295:(e,t,r)=>{"use strict";e.exports=r(7855)},2409:(e,t,r)=>{var n;(()=>{var i={226:function(i,s){!function(a,o){"use strict";var l="function",u="undefined",c="object",d="string",h="major",f="model",p="name",g="type",m="vendor",y="version",v="architecture",b="console",_="mobile",w="tablet",E="smarttv",x="wearable",S="embedded",k="Amazon",R="Apple",C="ASUS",O="BlackBerry",T="Browser",P="Chrome",A="Firefox",j="Google",I="Huawei",N="Microsoft",$="Motorola",D="Opera",M="Samsung",L="Sharp",U="Sony",q="Xiaomi",B="Zebra",F="Facebook",z="Chromium OS",H="Mac OS",V=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},G=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},K=function(e,t){return typeof e===d&&-1!==W(t).indexOf(W(e))},W=function(e){return e.toLowerCase()},X=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===u?e:e.substring(0,350)},Z=function(e,t){for(var r,n,i,s,a,u,d=0;d<t.length&&!a;){var h=t[d],f=t[d+1];for(r=n=0;r<h.length&&!a&&h[r];)if(a=h[r++].exec(e))for(i=0;i<f.length;i++)u=a[++n],typeof(s=f[i])===c&&s.length>0?2===s.length?typeof s[1]==l?this[s[0]]=s[1].call(this,u):this[s[0]]=s[1]:3===s.length?typeof s[1]!==l||s[1].exec&&s[1].test?this[s[0]]=u?u.replace(s[1],s[2]):void 0:this[s[0]]=u?s[1].call(this,u,s[2]):void 0:4===s.length&&(this[s[0]]=u?s[3].call(this,u.replace(s[1],s[2])):o):this[s]=u||o;d+=2}},J=function(e,t){for(var r in t)if(typeof t[r]===c&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(K(t[r][n],e))return"?"===r?o:r}else if(K(t[r],e))return"?"===r?o:r;return e},Y={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[y,[p,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[y,[p,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[p,y],[/opios[\/ ]+([\w\.]+)/i],[y,[p,D+" Mini"]],[/\bopr\/([\w\.]+)/i],[y,[p,D]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[p,y],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[y,[p,"UC"+T]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[y,[p,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[y,[p,"WeChat"]],[/konqueror\/([\w\.]+)/i],[y,[p,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[y,[p,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[y,[p,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[p,/(.+)/,"$1 Secure "+T],y],[/\bfocus\/([\w\.]+)/i],[y,[p,A+" Focus"]],[/\bopt\/([\w\.]+)/i],[y,[p,D+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[y,[p,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[y,[p,"Dolphin"]],[/coast\/([\w\.]+)/i],[y,[p,D+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[y,[p,"MIUI "+T]],[/fxios\/([-\w\.]+)/i],[y,[p,A]],[/\bqihu|(qi?ho?o?|360)browser/i],[[p,"360 "+T]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[p,/(.+)/,"$1 "+T],y],[/(comodo_dragon)\/([\w\.]+)/i],[[p,/_/g," "],y],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[p,y],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[p],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[p,F],y],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[p,y],[/\bgsa\/([\w\.]+) .*safari\//i],[y,[p,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[y,[p,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[y,[p,P+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[p,P+" WebView"],y],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[y,[p,"Android "+T]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[p,y],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[y,[p,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[y,p],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[p,[y,J,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[p,y],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[p,"Netscape"],y],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[y,[p,A+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[p,y],[/(cobalt)\/([\w\.]+)/i],[p,[y,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[v,"amd64"]],[/(ia32(?=;))/i],[[v,W]],[/((?:i[346]|x)86)[;\)]/i],[[v,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[v,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[v,"armhf"]],[/windows (ce|mobile); ppc;/i],[[v,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[v,/ower/,"",W]],[/(sun4\w)[;\)]/i],[[v,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[v,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[f,[m,M],[g,w]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[f,[m,M],[g,_]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[f,[m,R],[g,_]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[f,[m,R],[g,w]],[/(macintosh);/i],[f,[m,R]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[f,[m,L],[g,_]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[f,[m,I],[g,w]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[f,[m,I],[g,_]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[f,/_/g," "],[m,q],[g,_]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[f,/_/g," "],[m,q],[g,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[f,[m,"OPPO"],[g,_]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[f,[m,"Vivo"],[g,_]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[f,[m,"Realme"],[g,_]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[f,[m,$],[g,_]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[f,[m,$],[g,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[f,[m,"LG"],[g,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[f,[m,"LG"],[g,_]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[f,[m,"Lenovo"],[g,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[f,/_/g," "],[m,"Nokia"],[g,_]],[/(pixel c)\b/i],[f,[m,j],[g,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[f,[m,j],[g,_]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[f,[m,U],[g,_]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[f,"Xperia Tablet"],[m,U],[g,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[f,[m,"OnePlus"],[g,_]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[f,[m,k],[g,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[f,/(.+)/g,"Fire Phone $1"],[m,k],[g,_]],[/(playbook);[-\w\),; ]+(rim)/i],[f,m,[g,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[f,[m,O],[g,_]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[f,[m,C],[g,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[f,[m,C],[g,_]],[/(nexus 9)/i],[f,[m,"HTC"],[g,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[f,/_/g," "],[g,_]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[f,[m,"Acer"],[g,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[f,[m,"Meizu"],[g,_]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,f,[g,_]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,f,[g,w]],[/(surface duo)/i],[f,[m,N],[g,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[f,[m,"Fairphone"],[g,_]],[/(u304aa)/i],[f,[m,"AT&T"],[g,_]],[/\bsie-(\w*)/i],[f,[m,"Siemens"],[g,_]],[/\b(rct\w+) b/i],[f,[m,"RCA"],[g,w]],[/\b(venue[\d ]{2,7}) b/i],[f,[m,"Dell"],[g,w]],[/\b(q(?:mv|ta)\w+) b/i],[f,[m,"Verizon"],[g,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[f,[m,"Barnes & Noble"],[g,w]],[/\b(tm\d{3}\w+) b/i],[f,[m,"NuVision"],[g,w]],[/\b(k88) b/i],[f,[m,"ZTE"],[g,w]],[/\b(nx\d{3}j) b/i],[f,[m,"ZTE"],[g,_]],[/\b(gen\d{3}) b.+49h/i],[f,[m,"Swiss"],[g,_]],[/\b(zur\d{3}) b/i],[f,[m,"Swiss"],[g,w]],[/\b((zeki)?tb.*\b) b/i],[f,[m,"Zeki"],[g,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],f,[g,w]],[/\b(ns-?\w{0,9}) b/i],[f,[m,"Insignia"],[g,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[f,[m,"NextBook"],[g,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],f,[g,_]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],f,[g,_]],[/\b(ph-1) /i],[f,[m,"Essential"],[g,_]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[f,[m,"Envizen"],[g,w]],[/\b(trio[-\w\. ]+) b/i],[f,[m,"MachSpeed"],[g,w]],[/\btu_(1491) b/i],[f,[m,"Rotor"],[g,w]],[/(shield[\w ]+) b/i],[f,[m,"Nvidia"],[g,w]],[/(sprint) (\w+)/i],[m,f,[g,_]],[/(kin\.[onetw]{3})/i],[[f,/\./g," "],[m,N],[g,_]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[f,[m,B],[g,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[f,[m,B],[g,_]],[/smart-tv.+(samsung)/i],[m,[g,E]],[/hbbtv.+maple;(\d+)/i],[[f,/^/,"SmartTV"],[m,M],[g,E]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,"LG"],[g,E]],[/(apple) ?tv/i],[m,[f,R+" TV"],[g,E]],[/crkey/i],[[f,P+"cast"],[m,j],[g,E]],[/droid.+aft(\w)( bui|\))/i],[f,[m,k],[g,E]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[f,[m,L],[g,E]],[/(bravia[\w ]+)( bui|\))/i],[f,[m,U],[g,E]],[/(mitv-\w{5}) bui/i],[f,[m,q],[g,E]],[/Hbbtv.*(technisat) (.*);/i],[m,f,[g,E]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,X],[f,X],[g,E]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,E]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,f,[g,b]],[/droid.+; (shield) bui/i],[f,[m,"Nvidia"],[g,b]],[/(playstation [345portablevi]+)/i],[f,[m,U],[g,b]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[f,[m,N],[g,b]],[/((pebble))app/i],[m,f,[g,x]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[f,[m,R],[g,x]],[/droid.+; (glass) \d/i],[f,[m,j],[g,x]],[/droid.+; (wt63?0{2,3})\)/i],[f,[m,B],[g,x]],[/(quest( 2| pro)?)/i],[f,[m,F],[g,x]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[g,S]],[/(aeobc)\b/i],[f,[m,k],[g,S]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[f,[g,_]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[f,[g,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,_]],[/(android[-\w\. ]{0,9});.+buil/i],[f,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[y,[p,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[y,[p,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[p,y],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[y,p]],os:[[/microsoft (windows) (vista|xp)/i],[p,y],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[p,[y,J,Y]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[p,"Windows"],[y,J,Y]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[y,/_/g,"."],[p,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[p,H],[y,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[y,p],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[p,y],[/\(bb(10);/i],[y,[p,O]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[y,[p,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[y,[p,A+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[y,[p,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[y,[p,"watchOS"]],[/crkey\/([\d\.]+)/i],[y,[p,P+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[p,z],y],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[p,y],[/(sunos) ?([\w\.\d]*)/i],[[p,"Solaris"],y],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[p,y]]},ee=function(e,t){if(typeof e===c&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==u&&a.navigator?a.navigator:o,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:o,s=t?V(Q,t):Q,b=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[p]=o,t[y]=o,Z.call(t,n,s.browser),t[h]=typeof(e=t[y])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:o,b&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[p]="Brave"),t},this.getCPU=function(){var e={};return e[v]=o,Z.call(e,n,s.cpu),e},this.getDevice=function(){var e={};return e[m]=o,e[f]=o,e[g]=o,Z.call(e,n,s.device),b&&!e[g]&&i&&i.mobile&&(e[g]=_),b&&"Macintosh"==e[f]&&r&&typeof r.standalone!==u&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[f]="iPad",e[g]=w),e},this.getEngine=function(){var e={};return e[p]=o,e[y]=o,Z.call(e,n,s.engine),e},this.getOS=function(){var e={};return e[p]=o,e[y]=o,Z.call(e,n,s.os),b&&!e[p]&&i&&"Unknown"!=i.platform&&(e[p]=i.platform.replace(/chrome os/i,z).replace(/macos/i,H)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?X(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=G([p,y,h]),ee.CPU=G([v]),ee.DEVICE=G([f,m,g,b,_,E,w,x,S]),ee.ENGINE=ee.OS=G([p,y]),typeof s!==u?(i.exports&&(s=i.exports=ee),s.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof a!==u&&(a.UAParser=ee);var et=typeof a!==u&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},s={};function a(e){var t=s[e];if(void 0!==t)return t.exports;var r=s[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,a),n=!1}finally{n&&delete s[e]}return r.exports}a.ab="//",e.exports=a(226)})()},2709:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function s(e,t,n,s,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var o=new i(n,s||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function o(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),o.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,s=n.length,a=Array(s);i<s;i++)a[i]=n[i].fn;return a},o.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},o.prototype.emit=function(e,t,n,i,s,a){var o=r?r+e:e;if(!this._events[o])return!1;var l,u,c=this._events[o],d=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),d){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,n),!0;case 4:return c.fn.call(c.context,t,n,i),!0;case 5:return c.fn.call(c.context,t,n,i,s),!0;case 6:return c.fn.call(c.context,t,n,i,s,a),!0}for(u=1,l=Array(d-1);u<d;u++)l[u-1]=arguments[u];c.fn.apply(c.context,l)}else{var h,f=c.length;for(u=0;u<f;u++)switch(c[u].once&&this.removeListener(e,c[u].fn,void 0,!0),d){case 1:c[u].fn.call(c[u].context);break;case 2:c[u].fn.call(c[u].context,t);break;case 3:c[u].fn.call(c[u].context,t,n);break;case 4:c[u].fn.call(c[u].context,t,n,i);break;default:if(!l)for(h=1,l=Array(d-1);h<d;h++)l[h-1]=arguments[h];c[u].fn.apply(c[u].context,l)}}return!0},o.prototype.on=function(e,t,r){return s(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return s(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,n,i){var s=r?r+e:e;if(!this._events[s])return this;if(!t)return a(this,s),this;var o=this._events[s];if(o.fn)o.fn!==t||i&&!o.once||n&&o.context!==n||a(this,s);else{for(var l=0,u=[],c=o.length;l<c;l++)(o[l].fn!==t||i&&!o[l].once||n&&o[l].context!==n)&&u.push(o[l]);u.length?this._events[s]=1===u.length?u[0]:u:a(this,s)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,i=e.length;for(;i>0;){let s=i/2|0,a=n+s;0>=r(e[a],t)?(n=++a,i-=s+1):i=s}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let i=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},816:(e,t,r)=>{let n=r(213);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let s=(e,t,r)=>new Promise((s,a)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void s(e);let o=setTimeout(()=>{if("function"==typeof r){try{s(r())}catch(e){a(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,o=r instanceof Error?r:new i(n);"function"==typeof e.cancel&&e.cancel(),a(o)},t);n(e.then(s,a),()=>{clearTimeout(o)})});e.exports=s,e.exports.default=s,e.exports.TimeoutError=i}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,n),a=!1}finally{a&&delete r[e]}return s.exports}n.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),s=()=>{},a=new t.TimeoutError;class o extends e{constructor(e){var t,n,i,a;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=s,this._resolveIdle=s,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(n=null==(t=e.intervalCap)?void 0:t.toString())?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(a=null==(i=e.interval)?void 0:i.toString())?a:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=s,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=s,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,i)=>{let s=async()=>{this._pendingCount++,this._intervalCount++;try{let s=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&i(a)});n(await s)}catch(e){i(e)}this._next()};this._queue.enqueue(s,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}i.default=o})(),e.exports=i})()},3144:(e,t,r)=>{"use strict";e.exports=r(8730)},3543:(e,t,r)=>{"use strict";r.d(t,{AA:()=>n,AR:()=>_,EP:()=>h,RM:()=>c,VC:()=>f,c1:()=>g,gW:()=>v,h:()=>i,kz:()=>s,mH:()=>l,o7:()=>m,pu:()=>o,qF:()=>b,qq:()=>y,r4:()=>a,tz:()=>u,vS:()=>p,x3:()=>d});let n="nxtP",i="nxtI",s="x-prerender-revalidate",a="x-prerender-revalidate-if-generated",o=".prefetch.rsc",l=".segments",u=".segment.rsc",c=".rsc",d=".json",h=".meta",f="x-next-cache-tags",p="x-next-revalidated-tags",g="x-next-revalidate-tag-token",m=128,y=256,v="_N_T_",b=31536e3,_=0xfffffffe,w={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};({...w,GROUP:{builtinReact:[w.reactServerComponents,w.actionBrowser],serverOnly:[w.reactServerComponents,w.actionBrowser,w.instrument,w.middleware],neutralTarget:[w.apiNode,w.apiEdge],clientOnly:[w.serverSideRendering,w.appPagesBrowser],bundled:[w.reactServerComponents,w.actionBrowser,w.serverSideRendering,w.appPagesBrowser,w.shared,w.instrument,w.middleware],appPages:[w.reactServerComponents,w.serverSideRendering,w.appPagesBrowser,w.actionBrowser]}})},3689:(e,t,r)=>{"use strict";r.d(t,{ke:()=>i,lY:()=>s});let n=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function i(e,t){return n.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}let s=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},3738:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Headers:()=>a,Request:()=>o,Response:()=>l,default:()=>s,fetch:()=>i});var n=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();let i=n.fetch,s=n.fetch.bind(n),a=n.Headers,o=n.Request,l=n.Response},3936:(e,t,r)=>{"use strict";r.d(t,{Cu:()=>a,RD:()=>s,p$:()=>i,qU:()=>o,wN:()=>l});var n=r(3543);function i(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function s(e){var t,r,n,i,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=i,a.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}function a(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...s(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function o(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}function l(e){for(let t of[n.AA,n.h])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}},4102:e=>{!function(){"use strict";var t={815:function(e){e.exports=function(e,r,n,i){r=r||"&",n=n||"=";var s={};if("string"!=typeof e||0===e.length)return s;var a=/\+/g;e=e.split(r);var o=1e3;i&&"number"==typeof i.maxKeys&&(o=i.maxKeys);var l=e.length;o>0&&l>o&&(l=o);for(var u=0;u<l;++u){var c,d,h,f,p=e[u].replace(a,"%20"),g=p.indexOf(n);(g>=0?(c=p.substr(0,g),d=p.substr(g+1)):(c=p,d=""),h=decodeURIComponent(c),f=decodeURIComponent(d),Object.prototype.hasOwnProperty.call(s,h))?t(s[h])?s[h].push(f):s[h]=[s[h],f]:s[h]=f}return s};var t=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},577:function(e){var t=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,s,a,o){return(s=s||"&",a=a||"=",null===e&&(e=void 0),"object"==typeof e)?n(i(e),function(i){var o=encodeURIComponent(t(i))+a;return r(e[i])?n(e[i],function(e){return o+encodeURIComponent(t(e))}).join(s):o+encodeURIComponent(t(e[i]))}).join(s):o?encodeURIComponent(t(o))+a+encodeURIComponent(t(e)):""};var r=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function n(e,t){if(e.map)return e.map(t);for(var r=[],n=0;n<e.length;n++)r.push(t(e[n],n));return r}var i=Object.keys||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,n),a=!1}finally{a&&delete r[e]}return s.exports}n.ab="//";var i={};i.decode=i.parse=n(815),i.encode=i.stringify=n(577),e.exports=i}()},4144:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var n=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},4181:(e,t,r)=>{"use strict";r.d(t,{m:()=>i});var n=r(7935);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.R)(e);return r===t||r.startsWith(t+"/")}},4186:(e,t)=>{"use strict";var r={H:null,A:null};function n(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=Array.isArray,s=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator,g=Object.prototype.hasOwnProperty,m=Object.assign;function y(e,t,r,n,i,a){return{$$typeof:s,type:e,key:t,ref:void 0!==(r=a.ref)?r:null,props:a}}function v(e){return"object"==typeof e&&null!==e&&e.$$typeof===s}var b=/\/+/g;function _(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function w(){}function E(e,t,r){if(null==e)return e;var o=[],l=0;return!function e(t,r,o,l,u){var c,d,h,g=typeof t;("undefined"===g||"boolean"===g)&&(t=null);var m=!1;if(null===t)m=!0;else switch(g){case"bigint":case"string":case"number":m=!0;break;case"object":switch(t.$$typeof){case s:case a:m=!0;break;case f:return e((m=t._init)(t._payload),r,o,l,u)}}if(m)return u=u(t),m=""===l?"."+_(t,0):l,i(u)?(o="",null!=m&&(o=m.replace(b,"$&/")+"/"),e(u,r,o,"",function(e){return e})):null!=u&&(v(u)&&(c=u,d=o+(null==u.key||t&&t.key===u.key?"":(""+u.key).replace(b,"$&/")+"/")+m,u=y(c.type,d,void 0,void 0,void 0,c.props)),r.push(u)),1;m=0;var E=""===l?".":l+":";if(i(t))for(var x=0;x<t.length;x++)g=E+_(l=t[x],x),m+=e(l,r,o,g,u);else if("function"==typeof(x=null===(h=t)||"object"!=typeof h?null:"function"==typeof(h=p&&h[p]||h["@@iterator"])?h:null))for(t=x.call(t),x=0;!(l=t.next()).done;)g=E+_(l=l.value,x++),m+=e(l,r,o,g,u);else if("object"===g){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(w,w):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,o,l,u);throw Error(n(31,"[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r))}return m}(e,o,"","",function(e){return t.call(r,e,l++)}),o}function x(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}function S(){return new WeakMap}function k(){return{s:0,v:void 0,o:null,p:null}}t.Children={map:E,forEach:function(e,t,r){E(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return E(e,function(){t++}),t},toArray:function(e){return E(e,function(e){return e})||[]},only:function(e){if(!v(e))throw Error(n(143));return e}},t.Fragment=o,t.Profiler=u,t.StrictMode=l,t.Suspense=d,t.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,t.cache=function(e){return function(){var t=r.A;if(!t)return e.apply(null,arguments);var n=t.getCacheForType(S);void 0===(t=n.get(e))&&(t=k(),n.set(e,t)),n=0;for(var i=arguments.length;n<i;n++){var s=arguments[n];if("function"==typeof s||"object"==typeof s&&null!==s){var a=t.o;null===a&&(t.o=a=new WeakMap),void 0===(t=a.get(s))&&(t=k(),a.set(s,t))}else null===(a=t.p)&&(t.p=a=new Map),void 0===(t=a.get(s))&&(t=k(),a.set(s,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var o=e.apply(null,arguments);return(n=t).s=1,n.v=o}catch(e){throw(o=t).s=2,o.v=e,e}}},t.captureOwnerStack=function(){return null},t.cloneElement=function(e,t,r){if(null==e)throw Error(n(267,e));var i=m({},e.props),s=e.key,a=void 0;if(null!=t)for(o in void 0!==t.ref&&(a=void 0),void 0!==t.key&&(s=""+t.key),t)g.call(t,o)&&"key"!==o&&"__self"!==o&&"__source"!==o&&("ref"!==o||void 0!==t.ref)&&(i[o]=t[o]);var o=arguments.length-2;if(1===o)i.children=r;else if(1<o){for(var l=Array(o),u=0;u<o;u++)l[u]=arguments[u+2];i.children=l}return y(e.type,s,void 0,void 0,a,i)},t.createElement=function(e,t,r){var n,i={},s=null;if(null!=t)for(n in void 0!==t.key&&(s=""+t.key),t)g.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(i[n]=t[n]);var a=arguments.length-2;if(1===a)i.children=r;else if(1<a){for(var o=Array(a),l=0;l<a;l++)o[l]=arguments[l+2];i.children=o}if(e&&e.defaultProps)for(n in a=e.defaultProps)void 0===i[n]&&(i[n]=a[n]);return y(e,s,void 0,void 0,null,i)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=v,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:x}},t.memo=function(e,t){return{$$typeof:h,type:e,compare:void 0===t?null:t}},t.use=function(e){return r.H.use(e)},t.useCallback=function(e,t){return r.H.useCallback(e,t)},t.useDebugValue=function(){},t.useId=function(){return r.H.useId()},t.useMemo=function(e,t){return r.H.useMemo(e,t)},t.version="19.2.0-canary-3fbfb9ba-20250409"},4195:(e,t,r)=>{"use strict";r.d(t,{X$:()=>n,kf:()=>i});let n=e=>{setTimeout(e,0)};function i(){return new Promise(e=>setTimeout(e,0))}},4261:(e,t,r)=>{"use strict";r.d(t,{AppRouteRouteModule:()=>eg});var n,i={};r.r(i),r.d(i,{AppRouterContext:()=>F,GlobalLayoutRouterContext:()=>H,LayoutRouterContext:()=>z,MissingSlotContext:()=>G,TemplateContext:()=>V});var s={};r.r(s),r.d(s,{appRouterContext:()=>i});class a{constructor({userland:e,definition:t}){this.userland=e,this.definition=t}}var o=r(5325),l=r(5481);let u=["GET","HEAD","OPTIONS","POST","PUT","DELETE","PATCH"];var c=r(7903),d=r(5421),h=r(897),f=r(5455);let{env:p,stdout:g}=(null==(n=globalThis)?void 0:n.process)??{},m=p&&!p.NO_COLOR&&(p.FORCE_COLOR||(null==g?void 0:g.isTTY)&&!p.CI&&"dumb"!==p.TERM),y=(e,t,r,n)=>{let i=e.substring(0,n)+r,s=e.substring(n+t.length),a=s.indexOf(t);return~a?i+y(s,t,r,a):i+s},v=(e,t,r=e)=>m?n=>{let i=""+n,s=i.indexOf(t,e.length);return~s?e+y(i,t,r,s)+t:e+i+t}:String,b=v("\x1b[1m","\x1b[22m","\x1b[22m\x1b[1m");v("\x1b[2m","\x1b[22m","\x1b[22m\x1b[2m"),v("\x1b[3m","\x1b[23m"),v("\x1b[4m","\x1b[24m"),v("\x1b[7m","\x1b[27m"),v("\x1b[8m","\x1b[28m"),v("\x1b[9m","\x1b[29m"),v("\x1b[30m","\x1b[39m");let _=v("\x1b[31m","\x1b[39m"),w=v("\x1b[32m","\x1b[39m"),E=v("\x1b[33m","\x1b[39m");v("\x1b[34m","\x1b[39m");let x=v("\x1b[35m","\x1b[39m");v("\x1b[38;2;173;127;168m","\x1b[39m"),v("\x1b[36m","\x1b[39m");let S=v("\x1b[37m","\x1b[39m");v("\x1b[90m","\x1b[39m"),v("\x1b[40m","\x1b[49m"),v("\x1b[41m","\x1b[49m"),v("\x1b[42m","\x1b[49m"),v("\x1b[43m","\x1b[49m"),v("\x1b[44m","\x1b[49m"),v("\x1b[45m","\x1b[49m"),v("\x1b[46m","\x1b[49m"),v("\x1b[47m","\x1b[49m");var k=r(50);let R={wait:S(b("○")),error:_(b("⨯")),warn:E(b("⚠")),ready:"▲",info:S(b(" ")),event:w(b("✓")),trace:x(b("\xbb"))},C={log:"log",warn:"warn",error:"error"};new k.q(1e4,e=>e.length);let O=["HEAD","OPTIONS"];function T(){return new Response(null,{status:405})}var P=r(6237),A=r(6464);r(1251),r(8123);var j=r(7);let I=new Set(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401}));function N(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return"NEXT_HTTP_ERROR_FALLBACK"===t&&I.has(Number(r))}var $=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});function D(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,n]=t,i=t.slice(2,-2).join(";"),s=Number(t.at(-2));return"NEXT_REDIRECT"===r&&("replace"===n||"push"===n)&&"string"==typeof i&&!isNaN(s)&&s in $}function M(e,t){let r;if(!function(e){if("object"==typeof e&&null!==e&&"digest"in e&&"BAILOUT_TO_CLIENT_SIDE_RENDERING"===e.digest||D(e)||N(e)||(0,j.isDynamicServerError)(e))return e.digest}(e)){if("object"==typeof e&&null!==e&&"string"==typeof e.message){if(r=e.message,"string"==typeof e.stack){let n=e.stack,i=n.indexOf("\n");if(i>-1){let e=Object.defineProperty(Error(`Route ${t} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled.
          
Original Error: ${r}`),"__NEXT_ERROR_CODE",{value:"E362",enumerable:!1,configurable:!0});e.stack="Error: "+e.message+n.slice(i),console.error(e);return}}}else"string"==typeof e&&(r=e);if(r)return void console.error(`Route ${t} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled. No stack was provided.
          
Original Message: ${r}`);console.error(`Route ${t} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled. The thrown value is logged just following this message`),console.error(e)}}var L=r(4515),U=r(424),q=r(6225),B=r(4337);let F=(0,B.YR)(function(){throw Error("Attempted to call AppRouterContext() from the server but AppRouterContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\node_modules\\next\\dist\\esm\\shared\\lib\\app-router-context.shared-runtime.js","AppRouterContext"),z=(0,B.YR)(function(){throw Error("Attempted to call LayoutRouterContext() from the server but LayoutRouterContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\node_modules\\next\\dist\\esm\\shared\\lib\\app-router-context.shared-runtime.js","LayoutRouterContext"),H=(0,B.YR)(function(){throw Error("Attempted to call GlobalLayoutRouterContext() from the server but GlobalLayoutRouterContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\node_modules\\next\\dist\\esm\\shared\\lib\\app-router-context.shared-runtime.js","GlobalLayoutRouterContext"),V=(0,B.YR)(function(){throw Error("Attempted to call TemplateContext() from the server but TemplateContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\node_modules\\next\\dist\\esm\\shared\\lib\\app-router-context.shared-runtime.js","TemplateContext"),G=(0,B.YR)(function(){throw Error("Attempted to call MissingSlotContext() from the server but MissingSlotContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\node_modules\\next\\dist\\esm\\shared\\lib\\app-router-context.shared-runtime.js","MissingSlotContext");var K=r(6129),W=r(5375),X=r(4319),Z=r(6937),J=r(5835);class Y{constructor(){this.count=0,this.earlyListeners=[],this.listeners=[],this.tickPending=!1,this.taskPending=!1}noMorePendingCaches(){this.tickPending||(this.tickPending=!0,process.nextTick(()=>{if(this.tickPending=!1,0===this.count){for(let e=0;e<this.earlyListeners.length;e++)this.earlyListeners[e]();this.earlyListeners.length=0}})),this.taskPending||(this.taskPending=!0,setTimeout(()=>{if(this.taskPending=!1,0===this.count){for(let e=0;e<this.listeners.length;e++)this.listeners[e]();this.listeners.length=0}},0))}inputReady(){return new Promise(e=>{this.earlyListeners.push(e),0===this.count&&this.noMorePendingCaches()})}cacheReady(){return new Promise(e=>{this.listeners.push(e),0===this.count&&this.noMorePendingCaches()})}beginRead(){this.count++}endRead(){this.count--,0===this.count&&this.noMorePendingCaches()}}var Q=r(4195),ee=r(7753),et=r(3689),er=r(252),en=r(1092);let ei={current:null},es="function"==typeof en.cache?en.cache:e=>e,ea=console.warn;function eo(e){return function(...t){ea(e(...t))}}es(e=>{try{ea(ei.current)}finally{ei.current=null}});let el=new WeakMap;function eu(e){let t=el.get(e);if(t)return t;let r=Promise.resolve(e);return el.set(e,r),Object.keys(e).forEach(t=>{et.lY.has(t)||(r[t]=e[t])}),r}let ec=eo(eh),ed=eo(function(e,t,r){let n=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${n}used ${t}. \`params\` should be awaited before using its properties. The following properties were not available through enumeration because they conflict with builtin property names: ${function(e){switch(e.length){case 0:throw Object.defineProperty(new ee.z("Expected describeListOfPropertyNames to be called with a non-empty list of strings."),"__NEXT_ERROR_CODE",{value:"E531",enumerable:!1,configurable:!0});case 1:return`\`${e[0]}\``;case 2:return`\`${e[0]}\` and \`${e[1]}\``;default:{let t="";for(let r=0;r<e.length-1;r++)t+=`\`${e[r]}\`, `;return t+`, and \`${e[e.length-1]}\``}}}(r)}. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E482",enumerable:!1,configurable:!0})});function eh(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`params\` should be awaited before using its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E307",enumerable:!1,configurable:!0})}r(6225).s;var ef=r(3543),ep=r(7472);class eg extends a{static #e=this.sharedModules=s;constructor({userland:e,definition:t,resolvedPagePath:r,nextConfigOutput:n}){if(super({userland:e,definition:t}),this.workUnitAsyncStorage=U.FP,this.workAsyncStorage=L.J,this.serverHooks=j,this.actionAsyncStorage=q.s,this.resolvedPagePath=r,this.nextConfigOutput=n,this.methods=function(e){let t=u.reduce((t,r)=>({...t,[r]:e[r]??T}),{}),r=new Set(u.filter(t=>e[t]));for(let n of O.filter(e=>!r.has(e))){if("HEAD"===n){e.GET&&(t.HEAD=e.GET,r.add("HEAD"));continue}if("OPTIONS"===n){let e=["OPTIONS",...r];!r.has("HEAD")&&r.has("GET")&&e.push("HEAD");let n={Allow:e.sort().join(", ")};t.OPTIONS=()=>new Response(null,{status:204,headers:n}),r.add("OPTIONS");continue}throw Object.defineProperty(Error(`Invariant: should handle all automatic implementable methods, got method: ${n}`),"__NEXT_ERROR_CODE",{value:"E211",enumerable:!1,configurable:!0})}return t}(e),this.hasNonStaticMethods=function(e){return!!e.POST||!!e.PUT||!!e.DELETE||!!e.PATCH||!!e.OPTIONS}(e),this.dynamic=this.userland.dynamic,"export"===this.nextConfigOutput)if("force-dynamic"===this.dynamic)throw Object.defineProperty(Error(`export const dynamic = "force-dynamic" on page "${t.pathname}" cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`),"__NEXT_ERROR_CODE",{value:"E278",enumerable:!1,configurable:!0});else if(!function(e){return"force-static"===e.dynamic||"error"===e.dynamic||!1===e.revalidate||void 0!==e.revalidate&&e.revalidate>0||"function"==typeof e.generateStaticParams}(this.userland)&&this.userland.GET)throw Object.defineProperty(Error(`export const dynamic = "force-static"/export const revalidate not configured on route "${t.pathname}" with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`),"__NEXT_ERROR_CODE",{value:"E301",enumerable:!1,configurable:!0});else this.dynamic="error"}resolve(e){return u.includes(e)?this.methods[e]:()=>new Response(null,{status:400})}async do(e,t,r,n,i,s,a){var o,l;let u,c=r.isStaticGeneration,h=!!(null==(o=a.renderOpts.experimental)?void 0:o.dynamicIO);(0,d.V5)({workAsyncStorage:this.workAsyncStorage,workUnitAsyncStorage:this.workUnitAsyncStorage});let f={params:a.params?function(e,t){let r=U.FP.getStore();if(r)switch(r.type){case"prerender":case"prerender-ppr":case"prerender-legacy":var n,i=e,s=t,a=r;let o=s.fallbackRouteParams;if(o){let e=!1;for(let t in i)if(o.has(t)){e=!0;break}if(e)return"prerender"===a.type?function(e,t,r){let n=el.get(e);if(n)return n;let i=(0,er.W)(r.renderSignal,"`params`");return el.set(e,i),Object.keys(e).forEach(e=>{et.lY.has(e)||Object.defineProperty(i,e,{get(){let n=(0,et.ke)("params",e),i=eh(t,n);(0,Z.t3)(t,n,i,r)},set(t){Object.defineProperty(i,e,{value:t,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0})}),i}(i,s.route,a):function(e,t,r,n){let i=el.get(e);if(i)return i;let s={...e},a=Promise.resolve(s);return el.set(e,a),Object.keys(e).forEach(i=>{et.lY.has(i)||(t.has(i)?(Object.defineProperty(s,i,{get(){let e=(0,et.ke)("params",i);"prerender-ppr"===n.type?(0,Z.Ui)(r.route,e,n.dynamicTracking):(0,Z.xI)(e,r,n)},enumerable:!0}),Object.defineProperty(a,i,{get(){let e=(0,et.ke)("params",i);"prerender-ppr"===n.type?(0,Z.Ui)(r.route,e,n.dynamicTracking):(0,Z.xI)(e,r,n)},set(e){Object.defineProperty(a,i,{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0})):a[i]=e[i])}),a}(i,o,s,a)}return eu(i)}return n=0,eu(e)}(function(e){let t={};for(let[r,n]of Object.entries(e))void 0!==n&&(t[r]=n);return t}(a.params),r):void 0},p=()=>{a.renderOpts.pendingWaitUntil=(0,ep.C)(r).finally(()=>{process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.log("pending revalidates promise finished for:",n.url)})},g=null;try{if(c){let t=this.userland.revalidate,n=!1===t||void 0===t?ef.AR:t;if(h){let t,a=new AbortController,o=!1,l=new Y,c=(0,Z.uO)(void 0),d=g={type:"prerender",phase:"action",rootParams:{},implicitTags:i,renderSignal:a.signal,controller:a,cacheSignal:l,dynamicTracking:c,revalidate:n,expire:ef.AR,stale:ef.AR,tags:[...i.tags],prerenderResumeDataCache:null,hmrRefreshHash:void 0};try{t=this.workUnitAsyncStorage.run(d,e,s,f)}catch(e){a.signal.aborted?o=!0:(process.env.NEXT_DEBUG_BUILD||process.env.__NEXT_VERBOSE_LOGGING)&&M(e,r.route)}if("object"==typeof t&&null!==t&&"function"==typeof t.then&&t.then(()=>{},e=>{a.signal.aborted?o=!0:process.env.NEXT_DEBUG_BUILD&&M(e,r.route)}),await l.cacheReady(),o){let e=(0,Z.gz)(c);if(e)throw Object.defineProperty(new j.DynamicServerError(`Route ${r.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw console.error("Expected Next.js to keep track of reason for opting out of static rendering but one was not found. This is a bug in Next.js"),Object.defineProperty(new j.DynamicServerError(`Route ${r.route} couldn't be rendered statically because it used a dynamic API. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E577",enumerable:!1,configurable:!0})}let h=new AbortController;c=(0,Z.uO)(void 0);let p=g={type:"prerender",phase:"action",rootParams:{},implicitTags:i,renderSignal:h.signal,controller:h,cacheSignal:null,dynamicTracking:c,revalidate:n,expire:ef.AR,stale:ef.AR,tags:[...i.tags],prerenderResumeDataCache:null,hmrRefreshHash:void 0},m=!1;if(u=await new Promise((t,n)=>{(0,Q.X$)(async()=>{try{let i=await this.workUnitAsyncStorage.run(p,e,s,f);if(m)return;if(!(i instanceof Response))return void t(i);m=!0;let a=!1;i.arrayBuffer().then(e=>{a||(a=!0,t(new Response(e,{headers:i.headers,status:i.status,statusText:i.statusText})))},n),(0,Q.X$)(()=>{a||(a=!0,h.abort(),n(eO(r.route)))})}catch(e){n(e)}}),(0,Q.X$)(()=>{m||(m=!0,h.abort(),n(eO(r.route)))})}),h.signal.aborted)throw eO(r.route);h.abort()}else g={type:"prerender-legacy",phase:"action",rootParams:{},implicitTags:i,revalidate:n,expire:ef.AR,stale:ef.AR,tags:[...i.tags]},u=await U.FP.run(g,e,s,f)}else u=await U.FP.run(n,e,s,f)}catch(e){if(D(e)){let r=D(e)?e.digest.split(";").slice(2,-2).join(";"):null;if(!r)throw Object.defineProperty(Error("Invariant: Unexpected redirect url format"),"__NEXT_ERROR_CODE",{value:"E399",enumerable:!1,configurable:!0});let i=new Headers({Location:r});return"request"===n.type&&(0,P.IN)(i,n.mutableCookies),p(),new Response(null,{status:t.isAction?$.SeeOther:function(e){if(!D(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}(e),headers:i})}if(N(e))return new Response(null,{status:Number(e.digest.split(";")[1])});throw e}if(!(u instanceof Response))throw Object.defineProperty(Error(`No response is returned from route handler '${this.resolvedPagePath}'. Ensure you return a \`Response\` or a \`NextResponse\` in all branches of your handler.`),"__NEXT_ERROR_CODE",{value:"E325",enumerable:!1,configurable:!0});a.renderOpts.fetchMetrics=r.fetchMetrics,p(),g&&(a.renderOpts.collectedTags=null==(l=g.tags)?void 0:l.join(","),a.renderOpts.collectedRevalidate=g.revalidate,a.renderOpts.collectedExpire=g.expire,a.renderOpts.collectedStale=g.stale);let m=new Headers(u.headers);return"request"===n.type&&(0,P.IN)(m,n.mutableCookies)?new Response(u.body,{status:u.status,statusText:u.statusText,headers:m}):u}async handle(e,t){let r=this.resolve(e.method),n={fallbackRouteParams:null,page:this.definition.page,renderOpts:t.renderOpts,buildId:t.sharedContext.buildId,previouslyRevalidatedTags:[]};n.renderOpts.fetchCache=this.userland.fetchCache;let i={isAppRoute:!0,isAction:function(e){let t,r;e.headers instanceof Headers?(t=e.headers.get(K.ts.toLowerCase())??null,r=e.headers.get("content-type")):(t=e.headers[K.ts.toLowerCase()]??null,r=e.headers["content-type"]??null);let n="POST"===e.method&&"application/x-www-form-urlencoded"===r,i=!!("POST"===e.method&&(null==r?void 0:r.startsWith("multipart/form-data"))),s=void 0!==t&&"string"==typeof t&&"POST"===e.method;return{actionId:t,isURLEncodedAction:n,isMultipartAction:i,isFetchAction:s,isPossibleServerAction:!!(s||n||i)}}(e).isPossibleServerAction},s=await (0,c.l)(this.definition.page,e.nextUrl,null),a=(0,o.q9)(e,e.nextUrl,s,void 0,t.prerenderManifest.preview),u=(0,l.X)(n),d=await this.actionAsyncStorage.run(i,()=>this.workUnitAsyncStorage.run(a,()=>this.workAsyncStorage.run(u,async()=>{if(this.hasNonStaticMethods&&u.isStaticGeneration){let e=Object.defineProperty(new j.DynamicServerError("Route is configured with methods that cannot be statically generated."),"__NEXT_ERROR_CODE",{value:"E582",enumerable:!1,configurable:!0});throw u.dynamicUsageDescription=e.message,u.dynamicUsageStack=e.stack,e}let n=e;switch(this.dynamic){case"force-dynamic":u.forceDynamic=!0;break;case"force-static":u.forceStatic=!0,n=new Proxy(e,eS);break;case"error":u.dynamicShouldError=!0,u.isStaticGeneration&&(n=new Proxy(e,eR));break;default:n=function(e,t){let r={get(e,n,i){switch(n){case"search":case"searchParams":case"url":case"href":case"toJSON":case"toString":case"origin":return eT(t,U.FP.getStore(),`nextUrl.${n}`),J.l.get(e,n,i);case"clone":return e[ev]||(e[ev]=()=>new Proxy(e.clone(),r));default:return J.l.get(e,n,i)}}},n={get(e,i){switch(i){case"nextUrl":return e[em]||(e[em]=new Proxy(e.nextUrl,r));case"headers":case"cookies":case"url":case"body":case"blob":case"json":case"text":case"arrayBuffer":case"formData":return eT(t,U.FP.getStore(),`request.${i}`),J.l.get(e,i,e);case"clone":return e[ey]||(e[ey]=()=>new Proxy(e.clone(),n));default:return J.l.get(e,i,e)}}};return new Proxy(e,n)}(e,u)}let o=function(e){let t="/app/";e.includes(t)||(t="\\app\\");let[,...r]=e.split(t);return(t[0]+r.join(t)).split(".").slice(0,-1).join(".")}(this.resolvedPagePath),l=(0,h.EK)();return l.setRootSpanAttribute("next.route",o),l.trace(f.jM.runHandler,{spanName:`executing api route (app) ${o}`,attributes:{"next.route":o}},async()=>this.do(r,i,u,a,s,n,t))})));if(!(d instanceof Response))return new Response(null,{status:500});if(d.headers.has("x-middleware-rewrite"))throw Object.defineProperty(Error("NextResponse.rewrite() was used in a app route handler, this is not currently supported. Please remove the invocation to continue."),"__NEXT_ERROR_CODE",{value:"E374",enumerable:!1,configurable:!0});if("1"===d.headers.get("x-middleware-next"))throw Object.defineProperty(Error("NextResponse.next() was used in a app route handler, this is not supported. See here for more info: https://nextjs.org/docs/messages/next-response-next-in-app-route-handler"),"__NEXT_ERROR_CODE",{value:"E385",enumerable:!1,configurable:!0});return d}}let em=Symbol("nextUrl"),ey=Symbol("clone"),ev=Symbol("clone"),eb=Symbol("searchParams"),e_=Symbol("href"),ew=Symbol("toString"),eE=Symbol("headers"),ex=Symbol("cookies"),eS={get(e,t,r){switch(t){case"headers":return e[eE]||(e[eE]=A.o.seal(new Headers({})));case"cookies":return e[ex]||(e[ex]=P.Ck.seal(new W.RequestCookies(new Headers({}))));case"nextUrl":return e[em]||(e[em]=new Proxy(e.nextUrl,ek));case"url":return r.nextUrl.href;case"geo":case"ip":return;case"clone":return e[ey]||(e[ey]=()=>new Proxy(e.clone(),eS));default:return J.l.get(e,t,r)}}},ek={get(e,t,r){switch(t){case"search":return"";case"searchParams":return e[eb]||(e[eb]=new URLSearchParams);case"href":return e[e_]||(e[e_]=function(e){let t=new URL(e);return t.host="localhost:3000",t.search="",t.protocol="http",t}(e.href).href);case"toJSON":case"toString":return e[ew]||(e[ew]=()=>r.href);case"url":return;case"clone":return e[ev]||(e[ev]=()=>new Proxy(e.clone(),ek));default:return J.l.get(e,t,r)}}},eR={get(e,t,r){switch(t){case"nextUrl":return e[em]||(e[em]=new Proxy(e.nextUrl,eC));case"headers":case"cookies":case"url":case"body":case"blob":case"json":case"text":case"arrayBuffer":case"formData":throw Object.defineProperty(new X.f(`Route ${e.nextUrl.pathname} with \`dynamic = "error"\` couldn't be rendered statically because it used \`request.${t}\`.`),"__NEXT_ERROR_CODE",{value:"E611",enumerable:!1,configurable:!0});case"clone":return e[ey]||(e[ey]=()=>new Proxy(e.clone(),eR));default:return J.l.get(e,t,r)}}},eC={get(e,t,r){switch(t){case"search":case"searchParams":case"url":case"href":case"toJSON":case"toString":case"origin":throw Object.defineProperty(new X.f(`Route ${e.pathname} with \`dynamic = "error"\` couldn't be rendered statically because it used \`nextUrl.${t}\`.`),"__NEXT_ERROR_CODE",{value:"E575",enumerable:!1,configurable:!0});case"clone":return e[ev]||(e[ev]=()=>new Proxy(e.clone(),eC));default:return J.l.get(e,t,r)}}};function eO(e){return Object.defineProperty(new j.DynamicServerError(`Route ${e} couldn't be rendered statically because it used IO that was not cached. See more info here: https://nextjs.org/docs/messages/dynamic-io`),"__NEXT_ERROR_CODE",{value:"E609",enumerable:!1,configurable:!0})}function eT(e,t,r){if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "${r}" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "${r}" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E178",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "${r}" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "${r}" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E133",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new X.f(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${r}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(t){if("prerender"===t.type){let n=Object.defineProperty(Error(`Route ${e.route} used ${r} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-request`),"__NEXT_ERROR_CODE",{value:"E261",enumerable:!1,configurable:!0});(0,Z.t3)(e.route,r,n,t)}else if("prerender-ppr"===t.type)(0,Z.Ui)(e.route,r,t.dynamicTracking);else if("prerender-legacy"===t.type){t.revalidate=0;let n=Object.defineProperty(new j.DynamicServerError(`Route ${e.route} couldn't be rendered statically because it used \`${r}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw e.dynamicUsageDescription=r,e.dynamicUsageStack=n.stack,n}}}},4306:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=n(r(362));class s extends i.default{select(e){let t=!1,r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:n,referencedTable:i=n}={}){let s=i?`${i}.order`:"order",a=this.url.searchParams.get(s);return this.url.searchParams.set(s,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){let n=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(n,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:n=r}={}){let i=void 0===n?"offset":`${n}.offset`,s=void 0===n?"limit":`${n}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(s,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:n=!1,wal:i=!1,format:s="text"}={}){var a;let o=[e?"analyze":null,t?"verbose":null,r?"settings":null,n?"buffers":null,i?"wal":null].filter(Boolean).join("|"),l=null!=(a=this.headers.Accept)?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${s}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!=(e=this.headers.Prefer)?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=s},4318:(e,t,r)=>{"use strict";r.d(t,{Ud:()=>n.stringifyCookie,VO:()=>n.ResponseCookies,tm:()=>n.RequestCookies});var n=r(5375)},4319:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});class n extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}},4337:(e,t,r)=>{"use strict";var n;(n=r(7404)).renderToReadableStream,n.decodeReply,n.decodeReplyFromAsyncIterable,n.decodeAction,n.decodeFormState,n.registerServerReference,t.YR=n.registerClientReference,n.createClientModuleProxy,n.createTemporaryReferenceSet},4487:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=r},4515:(e,t,r)=>{"use strict";r.d(t,{J:()=>n.I});var n=r(5912)},4525:(e,t,r)=>{"use strict";let n,i,s;r.r(t),r.d(t,{ComponentMod:()=>iq,default:()=>iB});var a,o,l,u,c,d,h,f,p,g,m,y,v,b,_,w,E={};r.r(E),r.d(E,{OPTIONS:()=>ij,POST:()=>iA,runtime:()=>iO});var x={};r.r(x),r.d(x,{patchFetch:()=>iM,routeModule:()=>iI,serverHooks:()=>iD,workAsyncStorage:()=>iN,workUnitAsyncStorage:()=>i$});var S=r(4181),k=r(4515),R=r(7753),C=r(556);let O=Symbol.for("next.server.action-manifests");async function T(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}let P=null;async function A(){if("phase-production-build"===process.env.NEXT_PHASE)return;P||(P=T());let e=await P;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}let j=null;function I(){return j||(j=A()),j}function N(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(N(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(N(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,n,i){if("function"==typeof i[0])return i[0](t);throw Object.defineProperty(Error(N(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),I();var $=r(7779),D=r(3936);let M=Symbol("response"),L=Symbol("passThrough"),U=Symbol("waitUntil");class q{constructor(e,t){this[L]=!1,this[U]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[M]||(this[M]=Promise.resolve(e))}passThroughOnException(){this[L]=!0}waitUntil(e){if("external"===this[U].kind)return(0,this[U].function)(e);this[U].promises.push(e)}}class B extends q{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new $.CB({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new $.CB({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}var F=r(6804),z=r(4318),H=r(9691),V=r(5835);let G=Symbol("internal response"),K=new Set([301,302,303,307,308]);function W(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class X extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new z.VO(r),{get(e,n,i){switch(n){case"delete":case"set":return(...i)=>{let s=Reflect.apply(e[n],e,i),a=new Headers(r);return s instanceof z.VO&&r.set("x-middleware-set-cookie",s.getAll().map(e=>(0,z.Ud)(e)).join(",")),W(t,a),s};default:return V.l.get(e,n,i)}}});this[G]={cookies:n,url:t.url?new H.X(t.url,{headers:(0,D.Cu)(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[G].cookies}static json(e,t){let r=Response.json(e,t);return new X(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!K.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",(0,D.qU)(e)),new X(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,D.qU)(e)),W(t,r),new X(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),W(e,t),new X(null,{...e,headers:t})}}function Z(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),i=n.origin===r.origin;return{url:i?n.toString().slice(r.origin.length):n.toString(),isRelative:i}}var J=r(6129);J._A;var Y=r(5325),Q=r(424),ee=r(5481),et=r(897),er=r(5455);class en{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function ei(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}r(7205);let es=Symbol.for("@next/request-context");var ea=r(7903);class eo extends F.J{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new $.CB({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new $.CB({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new $.CB({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let el={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},eu=(e,t)=>(0,et.EK)().withPropagatedContext(e.headers,t,el),ec=!1;async function ed(e){var t;let n,i;if(!ec&&(ec=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(8062);e(),eu=t(eu)}await I();let s=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=(0,C.P)(e.request.url);let a=new H.X(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e),r=(0,D.wN)(e);if(r){for(let e of(a.searchParams.delete(r),t))a.searchParams.append(r,e);a.searchParams.delete(e)}}let o=a.buildId;a.buildId="";let l=(0,D.p$)(e.request.headers),u=l.has("x-nextjs-data"),c="1"===l.get(J.hY);u&&"/index"===a.pathname&&(a.pathname="/");let d=new Map;if(!s)for(let e of J.KD){let t=e.toLowerCase(),r=l.get(t);null!==r&&(d.set(t,r),l.delete(t))}let h=new eo({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(J._A),t?r.toString():r})(a).toString(),init:{body:e.request.body,headers:l,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});u&&Object.defineProperty(h,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:ei()})}));let f=e.request.waitUntil??(null==(t=function(){let e=globalThis[es];return null==e?void 0:e.get()}())?void 0:t.waitUntil),p=new B({request:h,page:e.page,context:f?{waitUntil:f}:void 0});if((n=await eu(h,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=p.waitUntil.bind(p),r=new en;return(0,et.EK)().trace(er.rd.execute,{spanName:`middleware ${h.method} ${h.nextUrl.pathname}`,attributes:{"http.target":h.nextUrl.pathname,"http.method":h.method}},async()=>{try{var n,s,a,l;let u=ei(),c=await (0,ea.l)("/",h.nextUrl,null),d=(0,Y.q9)(h,h.nextUrl,c,e=>{i=e},u),f=(0,ee.X)({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(s=e.request.nextConfig)||null==(n=s.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(l=e.request.nextConfig)||null==(a=l.experimental)?void 0:a.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:h.headers.has(J._V),buildId:o??"",previouslyRevalidatedTags:[]});return await k.J.run(f,()=>Q.FP.run(d,e.handler,h,p))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(h,p)}))&&!(n instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});n&&i&&n.headers.set("set-cookie",i);let g=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&g&&(c||!s)){let t=new H.X(g,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});s||t.host!==h.nextUrl.host||(t.buildId=o||t.buildId,n.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:i}=Z(t.toString(),a.toString());!s&&u&&n.headers.set("x-nextjs-rewrite",r),c&&i&&(a.pathname!==t.pathname&&n.headers.set(J.j9,t.pathname),a.search!==t.search&&n.headers.set(J.Wc,t.search.slice(1)))}let m=null==n?void 0:n.headers.get("Location");if(n&&m&&!s){let t=new H.X(m,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});n=new Response(n.body,n),t.host===a.host&&(t.buildId=o||t.buildId,n.headers.set("Location",t.toString())),u&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",Z(t.toString(),a.toString()).url))}let y=n||X.next(),v=y.headers.get("x-middleware-override-headers"),b=[];if(v){for(let[e,t]of d)y.headers.set(`x-middleware-request-${e}`,t),b.push(e);b.length>0&&y.headers.set("x-middleware-override-headers",v+","+b.join(","))}return{response:y,waitUntil:("internal"===p[U].kind?Promise.all(p[U].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:h.fetchMetrics}}var eh=r(1496),ef=r(50),ep=r(2295),eg=r.n(ep),em=r(3543),ey=r(6640);class ev{constructor(e){this.fs=e,this.tasks=[]}findOrCreateTask(e){for(let t of this.tasks)if(t[0]===e)return t;let t=this.fs.mkdir(e);t.catch(()=>{});let r=[e,t,[]];return this.tasks.push(r),r}append(e,t){let r=this.findOrCreateTask(eg().dirname(e)),n=r[1].then(()=>this.fs.writeFile(e,t));n.catch(()=>{}),r[2].push(n)}wait(){return Promise.all(this.tasks.flatMap(e=>e[2]))}}class eb{constructor(e){this.fs=e.fs,this.flushToDisk=e.flushToDisk,this.serverDistDir=e.serverDistDir,this.revalidatedTags=e.revalidatedTags,this.debug=!!process.env.NEXT_PRIVATE_DEBUG_CACHE,e.maxMemoryCacheSize?n||(this.debug&&console.log("using memory store for fetch cache"),n=new ef.q(e.maxMemoryCacheSize,function({value:e}){var t;if(!e)return 25;if(e.kind===eh.yD.REDIRECT)return JSON.stringify(e.props).length;if(e.kind===eh.yD.IMAGE)throw Object.defineProperty(Error("invariant image should not be incremental-cache"),"__NEXT_ERROR_CODE",{value:"E501",enumerable:!1,configurable:!0});if(e.kind===eh.yD.FETCH)return JSON.stringify(e.data||"").length;if(e.kind===eh.yD.APP_ROUTE)return e.body.length;return e.html.length+((null==(t=JSON.stringify(e.kind===eh.yD.APP_PAGE?e.rscData:e.pageData))?void 0:t.length)||0)})):this.debug&&console.log("not using memory store for fetch cache")}resetRequestCache(){}async revalidateTag(...e){let[t]=e;if(t="string"==typeof t?[t]:t,this.debug&&console.log("revalidateTag",t),0!==t.length)for(let e of t)ey.n.has(e)||ey.n.set(e,Date.now())}async get(...e){var t,r,i,s;let[a,o]=e,{kind:l}=o,u=null==n?void 0:n.get(a);if(this.debug&&(l===eh.Bs.FETCH?console.log("get",a,o.tags,l,!!u):console.log("get",a,l,!!u)),(null==u||null==(t=u.value)?void 0:t.kind)===eh.yD.APP_PAGE||(null==u||null==(r=u.value)?void 0:r.kind)===eh.yD.PAGES){let e,t=null==(s=u.value.headers)?void 0:s[em.VC];if("string"==typeof t&&(e=t.split(",")),(null==e?void 0:e.length)&&(0,ey.Q)(e,(null==u?void 0:u.lastModified)||Date.now()))return null}else(null==u||null==(i=u.value)?void 0:i.kind)===eh.yD.FETCH&&(o.kind===eh.Bs.FETCH?[...o.tags||[],...o.softTags||[]]:[]).some(e=>!!this.revalidatedTags.includes(e)||(0,ey.Q)([e],(null==u?void 0:u.lastModified)||Date.now()))&&(u=void 0);return u??null}async set(e,t,r){if(null==n||n.set(e,{value:t,lastModified:Date.now()}),this.debug&&console.log("set",e),!this.flushToDisk||!t)return;let i=new ev(this.fs);if(t.kind===eh.yD.APP_ROUTE){let r=this.getFilePath(`${e}.body`,eh.Bs.APP_ROUTE);i.append(r,t.body);let n={headers:t.headers,status:t.status,postponed:void 0,segmentPaths:void 0};i.append(r.replace(/\.body$/,em.EP),JSON.stringify(n,null,2))}else if(t.kind===eh.yD.PAGES||t.kind===eh.yD.APP_PAGE){let n=t.kind===eh.yD.APP_PAGE,s=this.getFilePath(`${e}.html`,n?eh.Bs.APP_PAGE:eh.Bs.PAGES);if(i.append(s,t.html),r.fetchCache||r.isFallback||i.append(this.getFilePath(`${e}${n?r.isRoutePPREnabled?em.pu:em.RM:em.x3}`,n?eh.Bs.APP_PAGE:eh.Bs.PAGES),n?t.rscData:JSON.stringify(t.pageData)),(null==t?void 0:t.kind)===eh.yD.APP_PAGE){let e;if(t.segmentData){e=[];let r=s.replace(/\.html$/,em.mH);for(let[n,s]of t.segmentData){e.push(n);let t=r+n+em.tz;i.append(t,s)}}let r={headers:t.headers,status:t.status,postponed:t.postponed,segmentPaths:e};i.append(s.replace(/\.html$/,em.EP),JSON.stringify(r))}}else if(t.kind===eh.yD.FETCH){let n=this.getFilePath(e,eh.Bs.FETCH);i.append(n,JSON.stringify({...t,tags:r.fetchCache?r.tags:[]}))}await i.wait()}getFilePath(e,t){switch(t){case eh.Bs.FETCH:return eg().join(this.serverDistDir,"..","cache","fetch-cache",e);case eh.Bs.PAGES:return eg().join(this.serverDistDir,"pages",e);case eh.Bs.IMAGE:case eh.Bs.APP_PAGE:case eh.Bs.APP_ROUTE:return eg().join(this.serverDistDir,"app",e);default:throw Object.defineProperty(Error(`Unexpected file path kind: ${t}`),"__NEXT_ERROR_CODE",{value:"E479",enumerable:!1,configurable:!0})}}}var e_=r(6243);let ew=["(..)(..)","(.)","(..)","(...)"];function eE(e){return void 0!==e.split("/").find(e=>ew.find(t=>e.startsWith(t)))}let ex=/\/[^/]*\[[^/]+\][^/]*(?=\/|$)/,eS=/\/\[[^/]+\](?=\/|$)/;function ek(e,t){return(void 0===t&&(t=!0),eE(e)&&(e=function(e){let t,r,n;for(let i of e.split("/"))if(r=ew.find(e=>i.startsWith(e))){[t,n]=e.split(r,2);break}if(!t||!r||!n)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,C.Y)(t),r){case"(.)":n="/"===t?"/"+n:t+"/"+n;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});n=t.split("/").slice(0,-1).concat(n).join("/");break;case"(...)":n="/"+n;break;case"(..)(..)":let i=t.split("/");if(i.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});n=i.slice(0,-2).concat(n).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:n}}(e).interceptedRoute),t)?eS.test(e):ex.test(e)}"undefined"!=typeof performance&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class eR extends Error{}function eC(e){return e.replace(/(?:\/index)?\/?$/,"")||"/"}class eO{static #e=this.cacheControls=new Map;constructor(e){this.prerenderManifest=e}get(e){let t=eO.cacheControls.get(e);if(t)return t;let r=this.prerenderManifest.routes[e];if(r){let{initialRevalidateSeconds:e,initialExpireSeconds:t}=r;if(void 0!==e)return{revalidate:e,expire:t}}let n=this.prerenderManifest.dynamicRoutes[e];if(n){let{fallbackRevalidate:e,fallbackExpire:t}=n;if(void 0!==e)return{revalidate:e,expire:t}}}set(e,t){eO.cacheControls.set(e,t)}clear(){eO.cacheControls.clear()}}var eT=r(1818),eP=r(5951),eA=r(8622);let ej=/[|\\{}()[\]^$+*?.-]/,eI=/[|\\{}()[\]^$+*?.-]/g;function eN(e){return ej.test(e)?e.replace(eI,"\\$&"):e}var e$=r(9055);let eD=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function eM(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function eL(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:s,groups:a}=function(e,t,r){let n={},i=1,s=[];for(let a of(0,e$.U)(e).slice(1).split("/")){let e=ew.find(e=>a.startsWith(e)),o=a.match(eD);if(e&&o&&o[2]){let{key:t,optional:r,repeat:a}=eM(o[2]);n[t]={pos:i++,repeat:a,optional:r},s.push("/"+eN(e)+"([^/]+?)")}else if(o&&o[2]){let{key:e,repeat:t,optional:a}=eM(o[2]);n[e]={pos:i++,repeat:t,optional:a},r&&o[1]&&s.push("/"+eN(o[1]));let l=t?a?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&o[1]&&(l=l.substring(1)),s.push(l)}else s.push("/"+eN(a));t&&o&&o[3]&&s.push(eN(o[3]))}return{parameterizedRoute:s.join(""),groups:n}}(e,r,n),o=s;return i||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:a}}function eU(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:s,keyPrefix:a,backreferenceDuplicateKeys:o}=e,{key:l,optional:u,repeat:c}=eM(i),d=l.replace(/\W/g,"");a&&(d=""+a+d);let h=!1;(0===d.length||d.length>30)&&(h=!0),isNaN(parseInt(d.slice(0,1)))||(h=!0),h&&(d=n());let f=d in s;a?s[d]=""+a+l:s[d]=l;let p=r?eN(r):"";return t=f&&o?"\\k<"+d+">":c?"(?<"+d+">.+?)":"(?<"+d+">[^/]+?)",u?"(?:/"+p+t+")?":"/"+p+t}function eq(e){let{re:t,groups:r}=e;return e=>{let n=t.exec(e);if(!n)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new eR("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},s={};for(let[e,t]of Object.entries(r)){let r=n[t.pos];void 0!==r&&(t.repeat?s[e]=r.split("/").map(e=>i(e)):s[e]=i(r))}return s}}function eB(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function eF(e){return e.replace(/__ESC_COLON_/gi,":")}function ez(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,eA.compile)("/"+e,{validate:!1})(t).slice(1)}class eH{constructor({fs:e,dev:t,flushToDisk:r,minimalMode:n,serverDistDir:i,requestHeaders:s,requestProtocol:a,maxMemoryCacheSize:o,getPrerenderManifest:l,fetchCacheKeyPrefix:u,CurCacheHandler:c,allowedRevalidateHeaderKeys:d}){var h,f,p,g;this.locks=new Map;let m=!!process.env.NEXT_PRIVATE_DEBUG_CACHE;this.hasCustomCacheHandler=!!c;let y=Symbol.for("@next/cache-handlers"),v=globalThis;if(c)m&&console.log("using custom cache handler",c.name);else{let t=v[y];(null==t?void 0:t.FetchCache)?c=t.FetchCache:e&&i&&(m&&console.log("using filesystem cache handler"),c=eb)}process.env.__NEXT_TEST_MAX_ISR_CACHE&&(o=parseInt(process.env.__NEXT_TEST_MAX_ISR_CACHE,10)),this.dev=t,this.disableForTestmode="true"===process.env.NEXT_PRIVATE_TEST_PROXY,this.minimalMode=n,this.requestHeaders=s,this.requestProtocol=a,this.allowedRevalidateHeaderKeys=d,this.prerenderManifest=l(),this.cacheControls=new eO(this.prerenderManifest),this.fetchCacheKeyPrefix=u;let b=[];s[em.kz]===(null==(f=this.prerenderManifest)||null==(h=f.preview)?void 0:h.previewModeId)&&(this.isOnDemandRevalidate=!0),n&&(b=function(e,t){return"string"==typeof e[em.vS]&&e[em.c1]===t?e[em.vS].split(","):[]}(s,null==(g=this.prerenderManifest)||null==(p=g.preview)?void 0:p.previewModeId)),c&&(this.cacheHandler=new c({dev:t,fs:e,flushToDisk:r,serverDistDir:i,revalidatedTags:b,maxMemoryCacheSize:o,_requestHeaders:s,fetchCacheKeyPrefix:u}))}calculateRevalidate(e,t,r,n){if(r)return Math.floor(performance.timeOrigin+performance.now()-1e3);let i=this.cacheControls.get(eC(e)),s=i?i.revalidate:!n&&1;return"number"==typeof s?1e3*s+t:s}_getPathname(e,t){var r;return t?e:(r=e,/^\/index(\/|$)/.test(r)&&!ek(r)?"/index"+r:"/"===r?"/index":(0,e_.A)(r))}resetRequestCache(){var e,t;null==(t=this.cacheHandler)||null==(e=t.resetRequestCache)||e.call(t)}async lock(e){let t=()=>Promise.resolve(),r=this.locks.get(e);r&&await r;let n=new Promise(r=>{t=async()=>{r(),this.locks.delete(e)}});return this.locks.set(e,n),t}async revalidateTag(e){var t;return null==(t=this.cacheHandler)?void 0:t.revalidateTag(e)}async generateCacheKey(e,t={}){let r=[],n=new TextEncoder,i=new TextDecoder;if(t.body)if("function"==typeof t.body.getReader){let e=t.body,s=[];try{await e.pipeTo(new WritableStream({write(e){"string"==typeof e?(s.push(n.encode(e)),r.push(e)):(s.push(e),r.push(i.decode(e,{stream:!0})))}})),r.push(i.decode());let a=s.reduce((e,t)=>e+t.length,0),o=new Uint8Array(a),l=0;for(let e of s)o.set(e,l),l+=e.length;t._ogBody=o}catch(e){console.error("Problem reading body",e)}}else if("function"==typeof t.body.keys){let e=t.body;for(let n of(t._ogBody=t.body,new Set([...e.keys()]))){let t=e.getAll(n);r.push(`${n}=${(await Promise.all(t.map(async e=>"string"==typeof e?e:await e.text()))).join(",")}`)}}else if("function"==typeof t.body.arrayBuffer){let e=t.body,n=await e.arrayBuffer();r.push(await e.text()),t._ogBody=new Blob([n],{type:e.type})}else"string"==typeof t.body&&(r.push(t.body),t._ogBody=t.body);let s="function"==typeof(t.headers||{}).keys?Object.fromEntries(t.headers):Object.assign({},t.headers);"traceparent"in s&&delete s.traceparent,"tracestate"in s&&delete s.tracestate;let a=JSON.stringify(["v3",this.fetchCacheKeyPrefix||"",e,t.method,s,t.mode,t.redirect,t.credentials,t.referrer,t.referrerPolicy,t.integrity,t.cache,r]);{var o;let e=n.encode(a);return o=await crypto.subtle.digest("SHA-256",e),Array.prototype.map.call(new Uint8Array(o),e=>e.toString(16).padStart(2,"0")).join("")}}async get(e,t){var r,n,i,s;let a,o;if(t.kind===eh.Bs.FETCH){let t=Q.FP.getStore(),r=t?(0,Q.E0)(t):null;if(r){let t=r.fetch.get(e);if((null==t?void 0:t.kind)===eh.yD.FETCH)return{isStale:!1,value:t}}}if(this.disableForTestmode||this.dev&&(t.kind!==eh.Bs.FETCH||"no-cache"===this.requestHeaders["cache-control"]))return null;e=this._getPathname(e,t.kind===eh.Bs.FETCH);let l=await (null==(r=this.cacheHandler)?void 0:r.get(e,t));if(t.kind===eh.Bs.FETCH){if(!l)return null;if((null==(i=l.value)?void 0:i.kind)!==eh.yD.FETCH)throw Object.defineProperty(new R.z(`Expected cached value for cache key ${JSON.stringify(e)} to be a "FETCH" kind, got ${JSON.stringify(null==(s=l.value)?void 0:s.kind)} instead.`),"__NEXT_ERROR_CODE",{value:"E653",enumerable:!1,configurable:!0});let r=k.J.getStore();if([...t.tags||[],...t.softTags||[]].some(e=>{var t,n;return(null==(t=this.revalidatedTags)?void 0:t.includes(e))||(null==r||null==(n=r.pendingRevalidatedTags)?void 0:n.includes(e))}))return null;let n=t.revalidate||l.value.revalidate,a=(performance.timeOrigin+performance.now()-(l.lastModified||0))/1e3,o=l.value.data;return{isStale:a>n,value:{kind:eh.yD.FETCH,data:o,revalidate:n}}}if((null==l||null==(n=l.value)?void 0:n.kind)===eh.yD.FETCH)throw Object.defineProperty(new R.z(`Expected cached value for cache key ${JSON.stringify(e)} not to be a ${JSON.stringify(t.kind)} kind, got "FETCH" instead.`),"__NEXT_ERROR_CODE",{value:"E652",enumerable:!1,configurable:!0});let u=null,{isFallback:c}=t,d=this.cacheControls.get(eC(e));return(null==l?void 0:l.lastModified)===-1?(a=-1,o=-1*em.qF):a=!!(!1!==(o=this.calculateRevalidate(e,(null==l?void 0:l.lastModified)||performance.timeOrigin+performance.now(),this.dev??!1,t.isFallback))&&o<performance.timeOrigin+performance.now())||void 0,l&&(u={isStale:a,cacheControl:d,revalidateAfter:o,value:l.value,isFallback:c}),!l&&this.prerenderManifest.notFoundRoutes.includes(e)&&(u={isStale:a,value:null,cacheControl:d,revalidateAfter:o,isFallback:c},this.set(e,u.value,{...t,cacheControl:d})),u}async set(e,t,r){if((null==t?void 0:t.kind)===eh.yD.FETCH){let r=Q.FP.getStore(),n=r?(0,Q.fm)(r):null;n&&n.fetch.set(e,t)}if(this.disableForTestmode||this.dev&&!r.fetchCache)return;e=this._getPathname(e,r.fetchCache);let n=JSON.stringify(t).length;if(r.fetchCache&&!this.hasCustomCacheHandler&&n>2097152){if(this.dev)throw Object.defineProperty(Error(`Failed to set Next.js data cache, items over 2MB can not be cached (${n} bytes)`),"__NEXT_ERROR_CODE",{value:"E86",enumerable:!1,configurable:!0});return}try{var i;!r.fetchCache&&r.cacheControl&&this.cacheControls.set(eC(e),r.cacheControl),await (null==(i=this.cacheHandler)?void 0:i.set(e,t,r))}catch(t){console.warn("Failed to update prerender cache for",e,t)}}}class eV{constructor(e){this.definition=e,ek(e.pathname)&&(this.dynamic=eq(eL(e.pathname)))}get identity(){return this.definition.pathname}get isDynamic(){return void 0!==this.dynamic}match(e){let t=this.test(e);return t?{definition:this.definition,params:t.params}:null}test(e){if(this.dynamic){let t=this.dynamic(e);return t?{params:t}:null}return e===this.definition.pathname?{}:null}}let eG=Symbol.for("__next_internal_waitUntil__"),eK=globalThis[eG]||(globalThis[eG]={waitUntilCounter:0,waitUntilResolve:void 0,waitUntilPromise:null});class eW{constructor(e,t){this.routeModule=e,this.nextConfig=t,this.matcher=new eV(e.definition)}static wrap(e,t){let r=new eW(e,t.nextConfig);return e=>ed({...e,IncrementalCache:eH,handler:r.handler.bind(r)})}async handler(e,t){let{params:n}=(function({page:e,i18n:t,basePath:n,rewrites:i,pageIsDynamic:s,trailingSlash:a,caseSensitive:o}){let l,u,c;return s&&(c=(u=eq(l=function(e,t){var r,n,i;let s=function(e,t,r,n,i){let s,a=(s=0,()=>{let e="",t=++s;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),o={},l=[];for(let s of(0,e$.U)(e).slice(1).split("/")){let e=ew.some(e=>s.startsWith(e)),u=s.match(eD);if(e&&u&&u[2])l.push(eU({getSafeRouteKey:a,interceptionMarker:u[1],segment:u[2],routeKeys:o,keyPrefix:t?em.h:void 0,backreferenceDuplicateKeys:i}));else if(u&&u[2]){n&&u[1]&&l.push("/"+eN(u[1]));let e=eU({getSafeRouteKey:a,segment:u[2],routeKeys:o,keyPrefix:t?em.AA:void 0,backreferenceDuplicateKeys:i});n&&u[1]&&(e=e.substring(1)),l.push(e)}else l.push("/"+eN(s));r&&u&&u[3]&&l.push(eN(u[3]))}return{namedParameterizedRoute:l.join(""),routeKeys:o}}(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),a=s.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...eL(e,t),namedRegex:"^"+a+"$",routeKeys:s.routeKeys}}(e,{prefixRouteKeys:!1})))(e)),{handleRewrites:function(l,c){let d={},h=c.pathname,f=i=>{let f=function(e,t){let r=[],n=(0,eA.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),i=(0,eA.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(n.source),n.flags):n,r);return(e,n)=>{if("string"!=typeof e)return!1;let s=i(e);if(!s)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete s.params[e.name];return{...n,...s.params}}}(i.source+(a?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!o});if(!c.pathname)return!1;let p=f(c.pathname);if((i.has||i.missing)&&p){let e=function(e,t,n,i){void 0===n&&(n=[]),void 0===i&&(i=[]);let s={},a=n=>{let i,a=n.key;switch(n.type){case"header":a=a.toLowerCase(),i=e.headers[a];break;case"cookie":if("cookies"in e)i=e.cookies[n.key];else{var o;i=(o=e.headers,function(){let{cookie:e}=o;if(!e)return{};let{parse:t}=r(4819);return t(Array.isArray(e)?e.join("; "):e)})()[n.key]}break;case"query":i=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};i=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!n.value&&i)return s[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(a)]=i,!0;if(i){let e=RegExp("^"+n.value+"$"),t=Array.isArray(i)?i.slice(-1)[0].match(e):i.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{s[e]=t.groups[e]}):"host"===n.type&&t[0]&&(s.host=t[0])),!0}return!1};return!(!n.every(e=>a(e))||i.some(e=>a(e)))&&s}(l,c.query,i.has,i.missing);e?Object.assign(p,e):p=!1}if(p){let{parsedDestination:r,destQuery:a}=function(e){let t,r,n=Object.assign({},e.query),i=function(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+eN(r),"g"),"__ESC_COLON_"+r));let r=function(e){if(e.startsWith("/"))return function(e,t,r){void 0===r&&(r=!0);let n=new URL("http://n"),i=e.startsWith(".")?new URL("http://n"):n,{pathname:s,searchParams:a,search:o,hash:l,href:u,origin:c}=new URL(e,i);if(c!==n.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:s,query:r?eB(a):void 0,search:o,hash:l,href:u.slice(c.length)}}(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:eB(t.searchParams),search:t.search}}(t),n=r.pathname;n&&(n=eF(n));let i=r.href;i&&(i=eF(i));let s=r.hostname;s&&(s=eF(s));let a=r.hash;return a&&(a=eF(a)),{...r,pathname:n,hostname:s,href:i,hash:a}}(e),{hostname:s,query:a}=i,o=i.pathname;i.hash&&(o=""+o+i.hash);let l=[],u=[];for(let e of((0,eA.pathToRegexp)(o,u),u))l.push(e.name);if(s){let e=[];for(let t of((0,eA.pathToRegexp)(s,e),e))l.push(t.name)}let c=(0,eA.compile)(o,{validate:!1});for(let[r,n]of(s&&(t=(0,eA.compile)(s,{validate:!1})),Object.entries(a)))Array.isArray(n)?a[r]=n.map(t=>ez(eF(t),e.params)):"string"==typeof n&&(a[r]=ez(eF(n),e.params));let d=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!d.some(e=>l.includes(e)))for(let t of d)t in a||(a[t]=e.params[t]);if(eE(o))for(let t of o.split("/")){let r=ew.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,s]=(r=c(e.params)).split("#",2);t&&(i.hostname=t(e.params)),i.pathname=n,i.hash=(s?"#":"")+(s||""),delete i.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return i.query={...n,...i.query},{newUrl:r,destQuery:a,parsedDestination:i}}({appendParamsToQuery:!0,destination:i.destination,params:p,query:c.query});if(r.protocol)return!0;if(Object.assign(d,a,p),Object.assign(c.query,r.query),delete r.query,Object.assign(c,r),!(h=c.pathname))return!1;if(n&&(h=h.replace(RegExp(`^${n}`),"")||"/"),t){let e=(0,eP.d)(h,t.locales);h=e.pathname,c.query.nextInternalLocale=e.detectedLocale||p.nextInternalLocale}if(h===e)return!0;if(s&&u){let e=u(h);if(e)return c.query={...c.query,...e},!0}}return!1};for(let e of i.beforeFiles||[])f(e);if(h!==e){let t=!1;for(let e of i.afterFiles||[])if(t=f(e))break;if(!t&&!(()=>{let t=(0,e$.U)(h||"");return t===(0,e$.U)(e)||(null==u?void 0:u(t))})()){for(let e of i.fallback||[])if(t=f(e))break}}return d},defaultRouteRegex:l,dynamicRouteMatcher:u,defaultRouteMatches:c,getParamsFromRouteMatches:function(e){if(!l)return null;let{groups:t,routeKeys:r}=l,n=eq({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,D.wN)(e);r&&(n[r]=t,delete n[e])}let i={};for(let e of Object.keys(r)){let s=r[e];if(!s)continue;let a=t[s],o=n[e];if(!a.optional&&!o)return null;i[a.pos]=o}return i}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>{if(!l||!c)return{params:{},hasValidParams:!1};var r=l,n=c;let i={};for(let s of Object.keys(r.groups)){let a=e[s];"string"==typeof a?a=(0,C.P)(a):Array.isArray(a)&&(a=a.map(C.P));let o=n[s],l=r.groups[s].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(a)?a.some(t=>t.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(o))||void 0===a&&!(l&&t))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${s}]]`))&&(a=void 0,delete e[s]),a&&"string"==typeof a&&r.groups[s].repeat&&(a=a.split("/")),a&&(i[s]=a)}return{params:i,hasValidParams:!0}},normalizeVercelUrl:(e,t)=>(function(e,t,r){let n=(0,eT.parse)(e.url,!0);for(let e of(delete n.search,Object.keys(n.query))){let i=e!==em.AA&&e.startsWith(em.AA),s=e!==em.h&&e.startsWith(em.h);(i||s||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete n.query[e]}e.url=(0,eT.format)(n)})(e,t,l),interpolateDynamicPath:(e,t)=>(function(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let i,{optional:s,repeat:a}=r.groups[n],o=`[${a?"...":""}${n}]`;s&&(o=`[${o}]`);let l=t[n];i=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,i)}return e})(e,t,l)}})({pageIsDynamic:this.matcher.isDynamic,page:this.matcher.definition.pathname,basePath:e.nextUrl.basePath,rewrites:{},caseSensitive:!1}).normalizeDynamicRouteParams(eB(e.nextUrl.searchParams),!1),i=t.waitUntil.bind(t),s=new en,a={params:n,prerenderManifest:{version:4,routes:{},dynamicRoutes:{},preview:ei(),notFoundRoutes:[]},renderOpts:{supportsDynamicResponse:!0,waitUntil:i,onClose:s.onClose.bind(s),onAfterTaskError:void 0,experimental:{dynamicIO:!1,authInterrupts:!1},cacheLifeProfiles:this.nextConfig.experimental.cacheLife},sharedContext:{buildId:""}},o=await this.routeModule.handle(e,a),l=[eK.waitUntilPromise];return a.renderOpts.pendingWaitUntil&&l.push(a.renderOpts.pendingWaitUntil),t.waitUntil(Promise.all(l)),o.body?o=new Response(function(e,t){let r=new TransformStream,n=()=>t();return e.pipeTo(r.writable).then(n,n),r.readable}(o.body,()=>s.dispatchClose()),{status:o.status,statusText:o.statusText,headers:o.headers}):setTimeout(()=>s.dispatchClose(),0),o}}var eX=r(6567),eZ=r(4144),eJ=r(5421);r(2409),"undefined"==typeof URLPattern||URLPattern,r(6937),r(4319),r(252),r(6534),r(3689),new WeakMap;let eY=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,3738)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)};class eQ extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class e0 extends eQ{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class e1 extends eQ{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class e2 extends eQ{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(a||(a={}));class e4{constructor(e,{headers:t={},customFetch:r,region:n=a.Any}={}){this.url=e,this.headers=t,this.region=n,this.fetch=eY(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r,n,i,s,a;return n=this,i=void 0,s=void 0,a=function*(){try{let n,i,{headers:s,method:a,body:o}=t,l={},{region:u}=t;u||(u=this.region),u&&"any"!==u&&(l["x-region"]=u),o&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&("undefined"!=typeof Blob&&o instanceof Blob||o instanceof ArrayBuffer?(l["Content-Type"]="application/octet-stream",n=o):"string"==typeof o?(l["Content-Type"]="text/plain",n=o):"undefined"!=typeof FormData&&o instanceof FormData?n=o:(l["Content-Type"]="application/json",n=JSON.stringify(o)));let c=yield this.fetch(`${this.url}/${e}`,{method:a||"POST",headers:Object.assign(Object.assign(Object.assign({},l),this.headers),s),body:n}).catch(e=>{throw new e0(e)}),d=c.headers.get("x-relay-error");if(d&&"true"===d)throw new e1(c);if(!c.ok)throw new e2(c);let h=(null!=(r=c.headers.get("Content-Type"))?r:"text/plain").split(";")[0].trim();return{data:"application/json"===h?yield c.json():"application/octet-stream"===h?yield c.blob():"text/event-stream"===h?c:"multipart/form-data"===h?yield c.formData():yield c.text(),error:null}}catch(e){return{data:null,error:e}}},new(s||(s=Promise))(function(e,t){function r(e){try{l(a.next(e))}catch(e){t(e)}}function o(e){try{l(a.throw(e))}catch(e){t(e)}}function l(t){var n;t.done?e(t.value):((n=t.value)instanceof s?n:new s(function(e){e(n)})).then(r,o)}l((a=a.apply(n,i||[])).next())})}}let{PostgrestClient:e3,PostgrestQueryBuilder:e9,PostgrestFilterBuilder:e5,PostgrestTransformBuilder:e6,PostgrestBuilder:e8,PostgrestError:e7}=r(364),te="undefined"==typeof window?r(4818):window.WebSocket,tt={"X-Client-Info":"realtime-js/2.11.10"};!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(o||(o={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(l||(l={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(u||(u={})),(c||(c={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(d||(d={}));class tr{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){let n=t.getUint8(1),i=t.getUint8(2),s=this.HEADER_LENGTH+2,a=r.decode(e.slice(s,s+n));s+=n;let o=r.decode(e.slice(s,s+i));return s+=i,{ref:null,topic:a,event:o,payload:JSON.parse(r.decode(e.slice(s,e.byteLength)))}}}class tn{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(h||(h={}));let ti=(e,t,r={})=>{var n;let i=null!=(n=r.skipTypes)?n:[];return Object.keys(t).reduce((r,n)=>(r[n]=ts(n,e,t,i),r),{})},ts=(e,t,r,n)=>{let i=t.find(t=>t.name===e),s=null==i?void 0:i.type,a=r[e];return s&&!n.includes(s)?ta(s,a):to(a)},ta=(e,t)=>{if("_"===e.charAt(0))return td(t,e.slice(1,e.length));switch(e){case h.bool:return tl(t);case h.float4:case h.float8:case h.int2:case h.int4:case h.int8:case h.numeric:case h.oid:return tu(t);case h.json:case h.jsonb:return tc(t);case h.timestamp:return th(t);case h.abstime:case h.date:case h.daterange:case h.int4range:case h.int8range:case h.money:case h.reltime:case h.text:case h.time:case h.timestamptz:case h.timetz:case h.tsrange:case h.tstzrange:default:return to(t)}},to=e=>e,tl=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},tu=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},tc=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},td=(e,t)=>{if("string"!=typeof e)return e;let r=e.length-1,n=e[r];if("{"===e[0]&&"}"===n){let n,i=e.slice(1,r);try{n=JSON.parse("["+i+"]")}catch(e){n=i?i.split(","):[]}return n.map(e=>ta(t,e))}return e},th=e=>"string"==typeof e?e.replace(" ","T"):e,tf=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class tp{constructor(e,t,r={},n=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=n,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null==(r=this.receivedResp)?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(f||(f={}));class tg{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{let{onJoin:t,onLeave:r,onSync:n}=this.caller;this.joinRef=this.channel._joinRef(),this.state=tg.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=tg.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],n()}),this.channel._on(r.diff,{},e=>{let{onJoin:t,onLeave:r,onSync:n}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=tg.syncDiff(this.state,e,t,r),n())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,n){let i=this.cloneDeep(e),s=this.transformState(t),a={},o={};return this.map(i,(e,t)=>{s[e]||(o[e]=t)}),this.map(s,(e,t)=>{let r=i[e];if(r){let n=t.map(e=>e.presence_ref),i=r.map(e=>e.presence_ref),s=t.filter(e=>0>i.indexOf(e.presence_ref)),l=r.filter(e=>0>n.indexOf(e.presence_ref));s.length>0&&(a[e]=s),l.length>0&&(o[e]=l)}else a[e]=t}),this.syncDiff(i,{joins:a,leaves:o},r,n)}static syncDiff(e,t,r,n){let{joins:i,leaves:s}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),n||(n=()=>{}),this.map(i,(t,n)=>{var i;let s=null!=(i=e[t])?i:[];if(e[t]=this.cloneDeep(n),s.length>0){let r=e[t].map(e=>e.presence_ref),n=s.filter(e=>0>r.indexOf(e.presence_ref));e[t].unshift(...n)}r(t,s,n)}),this.map(s,(t,r)=>{let i=e[t];if(!i)return;let s=r.map(e=>e.presence_ref);i=i.filter(e=>0>s.indexOf(e.presence_ref)),e[t]=i,n(t,i,r),0===i.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,r)=>{let n=e[r];return"metas"in n?t[r]=n.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[r]=n,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(p||(p={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(g||(g={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(m||(m={}));class tm{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=l.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new tp(this,u.join,this.params,this.timeout),this.rejoinTimer=new tn(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=l.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=l.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=l.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=l.errored,this.rejoinTimer.scheduleTimeout())}),this._on(u.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new tg(this),this.broadcastEndpointURL=tf(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,n;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:i,presence:s,private:a}}=this.params;this._onError(t=>null==e?void 0:e(m.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(m.CLOSED));let o={},u={broadcast:i,presence:s,postgres_changes:null!=(n=null==(r=this.bindings.postgres_changes)?void 0:r.map(e=>e.filter))?n:[],private:a};this.socket.accessTokenValue&&(o.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:u},o)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0===t){null==e||e(m.SUBSCRIBED);return}{let n=this.bindings.postgres_changes,i=null!=(r=null==n?void 0:n.length)?r:0,s=[];for(let r=0;r<i;r++){let i=n[r],{filter:{event:a,schema:o,table:u,filter:c}}=i,d=t&&t[r];if(d&&d.event===a&&d.schema===o&&d.table===u&&d.filter===c)s.push(Object.assign(Object.assign({},i),{id:d.id}));else{this.unsubscribe(),this.state=l.errored,null==e||e(m.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=s,e&&e(m.SUBSCRIBED);return}}).receive("error",t=>{this.state=l.errored,null==e||e(m.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(m.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,n;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var n,i,s;let a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null==(s=null==(i=null==(n=this.params)?void 0:n.config)?void 0:i.broadcast)?void 0:s.ack)||r("ok"),a.receive("ok",()=>r("ok")),a.receive("error",()=>r("error")),a.receive("timeout",()=>r("timed out"))});{let{event:i,payload:s}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:s,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!=(r=t.timeout)?r:this.timeout);return await (null==(n=e.body)?void 0:n.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=l.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(u.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise(r=>{let n=new tp(this,u.leave,{},e);n.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),n.send(),this._canPush()||n.trigger("ok",{})})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,r){let n=new AbortController,i=setTimeout(()=>n.abort(),r),s=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:n.signal}));return clearTimeout(i),s}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let n=new tp(this,e,t,r);return this._canPush()?n.send():(n.startTimeout(),this.pushBuffer.push(n)),n}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var n,i;let s=e.toLocaleLowerCase(),{close:a,error:o,leave:l,join:c}=u;if(r&&[a,o,l,c].indexOf(s)>=0&&r!==this._joinRef())return;let d=this._onMessage(s,t,r);if(t&&!d)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(s)?null==(n=this.bindings.postgres_changes)||n.filter(e=>{var t,r,n;return(null==(t=e.filter)?void 0:t.event)==="*"||(null==(n=null==(r=e.filter)?void 0:r.event)?void 0:n.toLocaleLowerCase())===s}).map(e=>e.callback(d,r)):null==(i=this.bindings[s])||i.filter(e=>{var r,n,i,a,o,l;if(!["broadcast","presence","postgres_changes"].includes(s))return e.type.toLocaleLowerCase()===s;if("id"in e){let s=e.id,a=null==(r=e.filter)?void 0:r.event;return s&&(null==(n=t.ids)?void 0:n.includes(s))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null==(i=t.data)?void 0:i.type.toLocaleLowerCase()))}{let r=null==(o=null==(a=null==e?void 0:e.filter)?void 0:a.event)?void 0:o.toLocaleLowerCase();return"*"===r||r===(null==(l=null==t?void 0:t.event)?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof d&&"ids"in d){let e=d.data,{schema:t,table:r,commit_timestamp:n,type:i,errors:s}=e;d=Object.assign(Object.assign({},{schema:t,table:r,commit_timestamp:n,eventType:i,new:{},old:{},errors:s}),this._getPayloadRecords(e))}e.callback(d,r)})}_isClosed(){return this.state===l.closed}_isJoined(){return this.state===l.joined}_isJoining(){return this.state===l.joining}_isLeaving(){return this.state===l.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){let n=e.toLocaleLowerCase(),i={type:n,filter:t,callback:r};return this.bindings[n]?this.bindings[n].push(i):this.bindings[n]=[i],this}_off(e,t){let r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var n;return!((null==(n=e.type)?void 0:n.toLocaleLowerCase())===r&&tm.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(u.close,{},e)}_onError(e){this._on(u.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=l.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=ti(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=ti(e.columns,e.old_record)),t}}let ty=()=>{},tv=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class tb{constructor(e,t){var n;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=tt,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=ty,this.ref=0,this.logger=ty,this.conn=null,this.sendBuffer=[],this.serializer=new tr,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,3738)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${c.websocket}`,this.httpEndpoint=tf(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let i=null==(n=null==t?void 0:t.params)?void 0:n.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new tn(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=te),this.transport){"undefined"!=typeof window&&this.transport===window.WebSocket?this.conn=new this.transport(this.endpointURL()):this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection();return}this.conn=new t_(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return this.channels=this.channels.filter(t=>t._joinRef!==e._joinRef),0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case o.connecting:return d.Connecting;case o.open:return d.Open;case o.closing:return d.Closing;default:return d.Closed}}isConnected(){return this.connectionState()===d.Open}channel(e,t={config:{}}){let r=`realtime:${e}`,n=this.getChannels().find(e=>e.topic===r);if(n)return n;{let r=new tm(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){let{topic:t,event:r,payload:n,ref:i}=e,s=()=>{this.encode(e,e=>{var t;null==(t=this.conn)||t.send(e)})};this.log("push",`${t} ${r} (${i})`,n),this.isConnected()?s():this.sendBuffer.push(s)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t,version:this.headers&&this.headers["X-Client-Info"]}),e.joinedOnce&&e._isJoined()&&e._push(u.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected())return void this.heartbeatCallback("disconnected");if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null==(e=this.conn)||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:n,ref:i}=e;"phoenix"===t&&"phx_reply"===r&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),i&&i===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${n.status||""} ${t} ${r} ${i&&"("+i+")"||""}`,n),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,n,i)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(u.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?",n=new URLSearchParams(t);return`${e}${r}${n}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([tv],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class t_{constructor(e,t,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=o.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=r.close}}class tw extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function tE(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class tx extends tw{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class tS extends tw{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let tk=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,3738)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},tR=()=>(function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function a(e){try{l(n.next(e))}catch(e){s(e)}}function o(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((n=n.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(r.bind(r,3738))).Response:Response}),tC=e=>{if(Array.isArray(e))return e.map(e=>tC(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,r])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=tC(r)}),t};var tO=function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function a(e){try{l(n.next(e))}catch(e){s(e)}}function o(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((n=n.apply(e,t||[])).next())})};let tT=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),tP=(e,t,r)=>tO(void 0,void 0,void 0,function*(){e instanceof(yield tR())&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new tx(tT(r),e.status||500))}).catch(e=>{t(new tS(tT(e),e))}):t(new tS(tT(e),e))}),tA=(e,t,r,n)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),n&&(i.body=JSON.stringify(n)),Object.assign(Object.assign({},i),r))};function tj(e,t,r,n,i,s){return tO(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(r,tA(t,n,i,s)).then(e=>{if(!e.ok)throw e;return(null==n?void 0:n.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>tP(e,o,n))})})}function tI(e,t,r,n){return tO(this,void 0,void 0,function*(){return tj(e,"GET",t,r,n)})}function tN(e,t,r,n,i){return tO(this,void 0,void 0,function*(){return tj(e,"POST",t,n,i,r)})}function t$(e,t,r,n,i){return tO(this,void 0,void 0,function*(){return tj(e,"DELETE",t,n,i,r)})}var tD=r(5356).Buffer,tM=function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function a(e){try{l(n.next(e))}catch(e){s(e)}}function o(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((n=n.apply(e,t||[])).next())})};let tL={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},tU={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class tq{constructor(e,t={},r,n){this.url=e,this.headers=t,this.bucketId=r,this.fetch=tk(n)}uploadOrUpdate(e,t,r,n){return tM(this,void 0,void 0,function*(){try{let i,s=Object.assign(Object.assign({},tU),n),a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(s.upsert)}),o=s.metadata;"undefined"!=typeof Blob&&r instanceof Blob?((i=new FormData).append("cacheControl",s.cacheControl),o&&i.append("metadata",this.encodeMetadata(o)),i.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((i=r).append("cacheControl",s.cacheControl),o&&i.append("metadata",this.encodeMetadata(o))):(i=r,a["cache-control"]=`max-age=${s.cacheControl}`,a["content-type"]=s.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==n?void 0:n.headers)&&(a=Object.assign(Object.assign({},a),n.headers));let l=this._removeEmptyFolders(t),u=this._getFinalPath(l),c=yield this.fetch(`${this.url}/object/${u}`,Object.assign({method:e,body:i,headers:a},(null==s?void 0:s.duplex)?{duplex:s.duplex}:{})),d=yield c.json();if(c.ok)return{data:{path:l,id:d.Id,fullPath:d.Key},error:null};return{data:null,error:d}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}upload(e,t,r){return tM(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,n){return tM(this,void 0,void 0,function*(){let i=this._removeEmptyFolders(e),s=this._getFinalPath(i),a=new URL(this.url+`/object/upload/sign/${s}`);a.searchParams.set("token",t);try{let e,t=Object.assign({upsert:tU.upsert},n),s=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r).append("cacheControl",t.cacheControl):(e=r,s["cache-control"]=`max-age=${t.cacheControl}`,s["content-type"]=t.contentType);let o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:s}),l=yield o.json();if(o.ok)return{data:{path:i,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return tM(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),n=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(n["x-upsert"]="true");let i=yield tN(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:n}),s=new URL(this.url+i.url),a=s.searchParams.get("token");if(!a)throw new tw("No token returned by API");return{data:{signedUrl:s.toString(),path:e,token:a},error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}update(e,t,r){return tM(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return tM(this,void 0,void 0,function*(){try{return{data:yield tN(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}copy(e,t,r){return tM(this,void 0,void 0,function*(){try{return{data:{path:(yield tN(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,r){return tM(this,void 0,void 0,function*(){try{let n=this._getFinalPath(e),i=yield tN(this.fetch,`${this.url}/object/sign/${n}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers}),s=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:i={signedUrl:encodeURI(`${this.url}${i.signedURL}${s}`)},error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,r){return tM(this,void 0,void 0,function*(){try{let n=yield tN(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:n.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${i}`):null})),error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}download(e,t){return tM(this,void 0,void 0,function*(){let r=void 0!==(null==t?void 0:t.transform),n=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),i=n?`?${n}`:"";try{let t=this._getFinalPath(e),n=yield tI(this.fetch,`${this.url}/${r?"render/image/authenticated":"object"}/${t}${i}`,{headers:this.headers,noResolveJson:!0});return{data:yield n.blob(),error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}info(e){return tM(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield tI(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:tC(e),error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}exists(e){return tM(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,r,n){return tO(this,void 0,void 0,function*(){return tj(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(tE(e)&&e instanceof tS){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let r=this._getFinalPath(e),n=[],i=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==i&&n.push(i);let s=void 0!==(null==t?void 0:t.transform),a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&n.push(a);let o=n.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${s?"render/image":"object"}/public/${r}${o}`)}}}remove(e){return tM(this,void 0,void 0,function*(){try{return{data:yield t$(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}list(e,t,r){return tM(this,void 0,void 0,function*(){try{let n=Object.assign(Object.assign(Object.assign({},tL),t),{prefix:e||""});return{data:yield tN(this.fetch,`${this.url}/object/list/${this.bucketId}`,n,{headers:this.headers},r),error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==tD?tD.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let tB={"X-Client-Info":"storage-js/2.7.1"};var tF=function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function a(e){try{l(n.next(e))}catch(e){s(e)}}function o(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((n=n.apply(e,t||[])).next())})};class tz{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},tB),t),this.fetch=tk(r)}listBuckets(){return tF(this,void 0,void 0,function*(){try{return{data:yield tI(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}getBucket(e){return tF(this,void 0,void 0,function*(){try{return{data:yield tI(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return tF(this,void 0,void 0,function*(){try{return{data:yield tN(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return tF(this,void 0,void 0,function*(){try{return{data:yield function(e,t,r,n,i){return tO(this,void 0,void 0,function*(){return tj(e,"PUT",t,n,void 0,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}emptyBucket(e){return tF(this,void 0,void 0,function*(){try{return{data:yield tN(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}deleteBucket(e){return tF(this,void 0,void 0,function*(){try{return{data:yield t$(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(tE(e))return{data:null,error:e};throw e}})}}class tH extends tz{constructor(e,t={},r){super(e,t,r)}from(e){return new tq(this.url,this.headers,e,this.fetch)}}let tV="";tV="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let tG={headers:{"X-Client-Info":`supabase-js-${tV}/2.50.0`}},tK={schema:"public"},tW={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},tX={};var tZ=r(3738);let tJ=e=>{let t;return t=e||("undefined"==typeof fetch?tZ.default:fetch),(...e)=>t(...e)},tY=()=>"undefined"==typeof Headers?tZ.Headers:Headers,tQ=(e,t,r)=>{let n=tJ(r),i=tY();return(r,s)=>(function(e,t,r,n){return new(r||(r=Promise))(function(i,s){function a(e){try{l(n.next(e))}catch(e){s(e)}}function o(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((n=n.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var a;let o=null!=(a=yield t())?a:e,l=new i(null==s?void 0:s.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${o}`),n(r,Object.assign(Object.assign({},s),{headers:l}))})},t0="2.70.0",t1={"X-Client-Info":`gotrue-js/${t0}`},t2="X-Supabase-Api-Version",t4={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},t3=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class t9 extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function t5(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class t6 extends t9{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class t8 extends t9{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class t7 extends t9{constructor(e,t,r,n){super(e,r,n),this.name=t,this.status=r}}class re extends t7{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class rt extends t7{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class rr extends t7{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class rn extends t7{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class ri extends t7{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class rs extends t7{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function ra(e){return t5(e)&&"AuthRetryableFetchError"===e.name}class ro extends t7{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class rl extends t7{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let ru="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),rc=" 	\n\r=".split(""),rd=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<rc.length;t+=1)e[rc[t].charCodeAt(0)]=-2;for(let t=0;t<ru.length;t+=1)e[ru[t].charCodeAt(0)]=t;return e})();function rh(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;)r(ru[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6;else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;)r(ru[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6}function rf(e,t,r){let n=rd[e];if(n>-1)for(t.queue=t.queue<<6|n,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===n)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function rp(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},n={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},s=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127)return r(e);for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,n,r)};for(let t=0;t<e.length;t+=1)rf(e.charCodeAt(t),i,s);return t.join("")}let rg=()=>"undefined"!=typeof window&&"undefined"!=typeof document,rm={tested:!1,writable:!1},ry=()=>{if(!rg())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(rm.tested)return rm.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),rm.tested=!0,rm.writable=!0}catch(e){rm.tested=!0,rm.writable=!1}return rm.writable},rv=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,3738)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},rb=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,r_=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},rw=async(e,t)=>{let r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(e){return r}},rE=async(e,t)=>{await e.removeItem(t)};class rx{constructor(){this.promise=new rx.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function rS(e){let t=e.split(".");if(3!==t.length)throw new rl("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!t3.test(t[e]))throw new rl("JWT not in base64url format");return{header:JSON.parse(rp(t[0])),payload:JSON.parse(rp(t[1])),signature:function(e){let t=[],r={queue:0,queuedBits:0},n=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)rf(e.charCodeAt(t),r,n);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function rk(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function rR(e){return("0"+e.toString(16)).substr(-2)}async function rC(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function rO(e){return"undefined"==typeof crypto||void 0===crypto.subtle||"undefined"==typeof TextEncoder?(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e):btoa(await rC(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function rT(e,t,r=!1){let n=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,r="";for(let n=0;n<56;n++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,rR).join("")}(),i=n;r&&(i+="/PASSWORD_RECOVERY"),await r_(e,`${t}-code-verifier`,i);let s=await rO(n),a=n===s?"plain":"s256";return[s,a]}rx.promiseConstructor=Promise;let rP=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i,rA=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function rj(e){if(!rA.test(e))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var rI=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r};let rN=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),r$=[502,503,504];async function rD(e){var t;let r,n;if(!rb(e))throw new rs(rN(e),0);if(r$.includes(e.status))throw new rs(rN(e),e.status);try{r=await e.json()}catch(e){throw new t8(rN(e),e)}let i=function(e){let t=e.headers.get(t2);if(!t||!t.match(rP))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(i&&i.getTime()>=t4["2024-01-01"].timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?n=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(n=r.error_code),n){if("weak_password"===n)throw new ro(rN(r),e.status,(null==(t=r.weak_password)?void 0:t.reasons)||[]);else if("session_not_found"===n)throw new re}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new ro(rN(r),e.status,r.weak_password.reasons);throw new t6(rN(r),e.status||500,n)}let rM=(e,t,r,n)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),i.body=JSON.stringify(n),Object.assign(Object.assign({},i),r))};async function rL(e,t,r,n){var i;let s=Object.assign({},null==n?void 0:n.headers);s[t2]||(s[t2]=t4["2024-01-01"].name),(null==n?void 0:n.jwt)&&(s.Authorization=`Bearer ${n.jwt}`);let a=null!=(i=null==n?void 0:n.query)?i:{};(null==n?void 0:n.redirectTo)&&(a.redirect_to=n.redirectTo);let o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await rU(e,t,r+o,{headers:s,noResolveJson:null==n?void 0:n.noResolveJson},{},null==n?void 0:n.body);return(null==n?void 0:n.xform)?null==n?void 0:n.xform(l):{data:Object.assign({},l),error:null}}async function rU(e,t,r,n,i,s){let a,o=rM(t,n,i,s);try{a=await e(r,Object.assign({},o))}catch(e){throw console.error(e),new rs(rN(e),0)}if(a.ok||await rD(a),null==n?void 0:n.noResolveJson)return a;try{return await a.json()}catch(e){await rD(e)}}function rq(e){var t,r,n;let i=null;(n=e).access_token&&n.refresh_token&&n.expires_in&&(i=Object.assign({},e),e.expires_at||(i.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:i,user:null!=(t=e.user)?t:e},error:null}}function rB(e){let t=rq(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function rF(e){var t;return{data:{user:null!=(t=e.user)?t:e},error:null}}function rz(e){return{data:e,error:null}}function rH(e){let{action_link:t,email_otp:r,hashed_token:n,redirect_to:i,verification_type:s}=e;return{data:{properties:{action_link:t,email_otp:r,hashed_token:n,redirect_to:i,verification_type:s},user:Object.assign({},rI(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function rV(e){return e}let rG=["global","local","others"];var rK=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r};class rW{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=rv(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=rG[0]){if(0>rG.indexOf(t))throw Error(`@supabase/auth-js: Parameter scope must be one of ${rG.join(", ")}`);try{return await rL(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(t5(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await rL(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:rF})}catch(e){if(t5(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,r=rK(e,["options"]),n=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(n.new_email=null==r?void 0:r.newEmail,delete n.newEmail),await rL(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:n,headers:this.headers,xform:rH,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(t5(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await rL(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:rF})}catch(e){if(t5(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,r,n,i,s,a,o;try{let l={nextPage:null,lastPage:0,total:0},u=await rL(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!=(r=null==(t=null==e?void 0:e.page)?void 0:t.toString())?r:"",per_page:null!=(i=null==(n=null==e?void 0:e.perPage)?void 0:n.toString())?i:""},xform:rV});if(u.error)throw u.error;let c=await u.json(),d=null!=(s=u.headers.get("x-total-count"))?s:0,h=null!=(o=null==(a=u.headers.get("link"))?void 0:a.split(","))?o:[];return h.length>0&&(h.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(d)),{data:Object.assign(Object.assign({},c),l),error:null}}catch(e){if(t5(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){rj(e);try{return await rL(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:rF})}catch(e){if(t5(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){rj(e);try{return await rL(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:rF})}catch(e){if(t5(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){rj(e);try{return await rL(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:rF})}catch(e){if(t5(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){rj(e.userId);try{let{data:t,error:r}=await rL(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(e){if(t5(e))return{data:null,error:e};throw e}}async _deleteFactor(e){rj(e.userId),rj(e.id);try{return{data:await rL(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(t5(e))return{data:null,error:e};throw e}}}let rX={getItem:e=>ry()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{ry()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{ry()&&globalThis.localStorage.removeItem(e)}};function rZ(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}let rJ={debug:!!(globalThis&&ry()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class rY extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class rQ extends rY{}async function r0(e,t,r){rJ.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let n=new globalThis.AbortController;return t>0&&setTimeout(()=>{n.abort(),rJ.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:n.signal},async n=>{if(n){rJ.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,n.name);try{return await r()}finally{rJ.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,n.name)}}if(0===t)throw rJ.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new rQ(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(rJ.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}))}if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}let r1={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:t1,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function r2(e,t,r){return await r()}class r4{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=r4.nextInstanceID,r4.nextInstanceID+=1,this.instanceID>0&&rg()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let n=Object.assign(Object.assign({},r1),e);if(this.logDebugMessages=!!n.debug,"function"==typeof n.debug&&(this.logger=n.debug),this.persistSession=n.persistSession,this.storageKey=n.storageKey,this.autoRefreshToken=n.autoRefreshToken,this.admin=new rW({url:n.url,headers:n.headers,fetch:n.fetch}),this.url=n.url,this.headers=n.headers,this.fetch=rv(n.fetch),this.lock=n.lock||r2,this.detectSessionInUrl=n.detectSessionInUrl,this.flowType=n.flowType,this.hasCustomAuthorizationHeader=n.hasCustomAuthorizationHeader,n.lock?this.lock=n.lock:rg()&&(null==(t=null==globalThis?void 0:globalThis.navigator)?void 0:t.locks)?this.lock=r0:this.lock=r2,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?n.storage?this.storage=n.storage:ry()?this.storage=rX:(this.memoryStorage={},this.storage=rZ(this.memoryStorage)):(this.memoryStorage={},this.storage=rZ(this.memoryStorage)),rg()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null==(r=this.broadcastChannel)||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${t0}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(e){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href),r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),rg()&&this.detectSessionInUrl&&"none"!==r){let{data:n,error:i}=await this._getSessionFromURL(t,r);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),t5(i)&&"AuthImplicitGrantRedirectError"===i.name){let t=null==(e=i.details)?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:i}}return await this._removeSession(),{error:i}}let{session:s,redirectType:a}=n;return this._debug("#_initialize()","detected session in URL",s,"redirect type",a),await this._saveSession(s),setTimeout(async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",s):await this._notifyAllSubscribers("SIGNED_IN",s)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(t5(e))return{error:e};return{error:new t8("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,n;try{let{data:i,error:s}=await rL(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!=(r=null==(t=null==e?void 0:e.options)?void 0:t.data)?r:{},gotrue_meta_security:{captcha_token:null==(n=null==e?void 0:e.options)?void 0:n.captchaToken}},xform:rq});if(s||!i)return{data:{user:null,session:null},error:s};let a=i.session,o=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:o,session:a},error:null}}catch(e){if(t5(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,r,n;try{let i;if("email"in e){let{email:r,password:n,options:s}=e,a=null,o=null;"pkce"===this.flowType&&([a,o]=await rT(this.storage,this.storageKey)),i=await rL(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==s?void 0:s.emailRedirectTo,body:{email:r,password:n,data:null!=(t=null==s?void 0:s.data)?t:{},gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},code_challenge:a,code_challenge_method:o},xform:rq})}else if("phone"in e){let{phone:t,password:s,options:a}=e;i=await rL(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:s,data:null!=(r=null==a?void 0:a.data)?r:{},channel:null!=(n=null==a?void 0:a.channel)?n:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:rq})}else throw new rr("You must provide either an email or phone number and a password");let{data:s,error:a}=i;if(a||!s)return{data:{user:null,session:null},error:a};let o=s.session,l=s.user;return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(t5(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:r,password:n,options:i}=e;t=await rL(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:n,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:rB})}else if("phone"in e){let{phone:r,password:n,options:i}=e;t=await rL(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:n,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:rB})}else throw new rr("You must provide either an email or phone number and a password");let{data:r,error:n}=t;if(n)return{data:{user:null,session:null},error:n};if(!r||!r.session||!r.user)return{data:{user:null,session:null},error:new rt};return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:n}}catch(e){if(t5(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,r,n,i;return await this._handleProviderSignIn(e.provider,{redirectTo:null==(t=e.options)?void 0:t.redirectTo,scopes:null==(r=e.options)?void 0:r.scopes,queryParams:null==(n=e.options)?void 0:n.queryParams,skipBrowserRedirect:null==(i=e.options)?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){let{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,n,i,s,a,o,l,u,c,d,h;let f,p;if("message"in e)f=e.message,p=e.signature;else{let d,{chain:h,wallet:g,statement:m,options:y}=e;if(rg())if("object"==typeof g)d=g;else{let e=window;if("solana"in e&&"object"==typeof e.solana&&("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))d=e.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if("object"!=typeof g||!(null==y?void 0:y.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");d=g}let v=new URL(null!=(t=null==y?void 0:y.url)?t:window.location.href);if("signIn"in d&&d.signIn){let e,t=await d.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==y?void 0:y.signInWithSolana),{version:"1",domain:v.host,uri:v.href}),m?{statement:m}:null));if(Array.isArray(t)&&t[0]&&"object"==typeof t[0])e=t[0];else if(t&&"object"==typeof t&&"signedMessage"in t&&"signature"in t)e=t;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in e&&"signature"in e&&("string"==typeof e.signedMessage||e.signedMessage instanceof Uint8Array)&&e.signature instanceof Uint8Array)f="string"==typeof e.signedMessage?e.signedMessage:new TextDecoder().decode(e.signedMessage),p=e.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in d)||"function"!=typeof d.signMessage||!("publicKey"in d)||"object"!=typeof d||!d.publicKey||!("toBase58"in d.publicKey)||"function"!=typeof d.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${v.host} wants you to sign in with your Solana account:`,d.publicKey.toBase58(),...m?["",m,""]:[""],"Version: 1",`URI: ${v.href}`,`Issued At: ${null!=(n=null==(r=null==y?void 0:y.signInWithSolana)?void 0:r.issuedAt)?n:new Date().toISOString()}`,...(null==(i=null==y?void 0:y.signInWithSolana)?void 0:i.notBefore)?[`Not Before: ${y.signInWithSolana.notBefore}`]:[],...(null==(s=null==y?void 0:y.signInWithSolana)?void 0:s.expirationTime)?[`Expiration Time: ${y.signInWithSolana.expirationTime}`]:[],...(null==(a=null==y?void 0:y.signInWithSolana)?void 0:a.chainId)?[`Chain ID: ${y.signInWithSolana.chainId}`]:[],...(null==(o=null==y?void 0:y.signInWithSolana)?void 0:o.nonce)?[`Nonce: ${y.signInWithSolana.nonce}`]:[],...(null==(l=null==y?void 0:y.signInWithSolana)?void 0:l.requestId)?[`Request ID: ${y.signInWithSolana.requestId}`]:[],...(null==(c=null==(u=null==y?void 0:y.signInWithSolana)?void 0:u.resources)?void 0:c.length)?["Resources",...y.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");let e=await d.signMessage(new TextEncoder().encode(f),"utf8");if(!e||!(e instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{let{data:t,error:r}=await rL(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:function(e){let t=[],r={queue:0,queuedBits:0},n=e=>{t.push(e)};return e.forEach(e=>rh(e,r,n)),rh(null,r,n),t.join("")}(p)},(null==(d=e.options)?void 0:d.captchaToken)?{gotrue_meta_security:{captcha_token:null==(h=e.options)?void 0:h.captchaToken}}:null),xform:rq});if(r)throw r;if(!t||!t.session||!t.user)return{data:{user:null,session:null},error:new rt};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:r}}catch(e){if(t5(e))return{data:{user:null,session:null},error:e};throw e}}async _exchangeCodeForSession(e){let t=await rw(this.storage,`${this.storageKey}-code-verifier`),[r,n]=(null!=t?t:"").split("/");try{let{data:t,error:i}=await rL(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:rq});if(await rE(this.storage,`${this.storageKey}-code-verifier`),i)throw i;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new rt};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=n?n:null}),error:i}}catch(e){if(t5(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:r,token:n,access_token:i,nonce:s}=e,{data:a,error:o}=await rL(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:n,access_token:i,nonce:s,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:rq});if(o)return{data:{user:null,session:null},error:o};if(!a||!a.session||!a.user)return{data:{user:null,session:null},error:new rt};return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:o}}catch(e){if(t5(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,r,n,i,s;try{if("email"in e){let{email:n,options:i}=e,s=null,a=null;"pkce"===this.flowType&&([s,a]=await rT(this.storage,this.storageKey));let{error:o}=await rL(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:n,data:null!=(t=null==i?void 0:i.data)?t:{},create_user:null==(r=null==i?void 0:i.shouldCreateUser)||r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:s,code_challenge_method:a},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){let{phone:t,options:r}=e,{data:a,error:o}=await rL(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!=(n=null==r?void 0:r.data)?n:{},create_user:null==(i=null==r?void 0:r.shouldCreateUser)||i,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!=(s=null==r?void 0:r.channel)?s:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new rr("You must provide either an email or phone number.")}catch(e){if(t5(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,r;try{let n,i;"options"in e&&(n=null==(t=e.options)?void 0:t.redirectTo,i=null==(r=e.options)?void 0:r.captchaToken);let{data:s,error:a}=await rL(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:n,xform:rq});if(a)throw a;if(!s)throw Error("An error occurred on token verification.");let o=s.session,l=s.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(t5(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,r,n;try{let i=null,s=null;return"pkce"===this.flowType&&([i,s]=await rT(this.storage,this.storageKey)),await rL(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!=(r=null==(t=e.options)?void 0:t.redirectTo)?r:void 0}),(null==(n=null==e?void 0:e.options)?void 0:n.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:s}),headers:this.headers,xform:rz})}catch(e){if(t5(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new re;let{error:n}=await rL(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:n}})}catch(e){if(t5(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:r,type:n,options:i}=e,{error:s}=await rL(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:n,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:s}}if("phone"in e){let{phone:r,type:n,options:i}=e,{data:s,error:a}=await rL(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:n,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:null==s?void 0:s.message_id},error:a}}throw new rr("You must provide either an email or phone number and a type")}catch(e){if(t5(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await rw(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let r=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,n)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,n))})}return{data:{session:e},error:null}}let{session:n,error:i}=await this._callRefreshToken(e.refresh_token);if(i)return{data:{session:null},error:i};return{data:{session:n},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await rL(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:rF});return await this._useSession(async e=>{var t,r,n;let{data:i,error:s}=e;if(s)throw s;return(null==(t=i.session)?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await rL(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!=(n=null==(r=i.session)?void 0:r.access_token)?n:void 0,xform:rF}):{data:{user:null},error:new re}})}catch(e){if(t5(e))return t5(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await rE(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{let{data:n,error:i}=r;if(i)throw i;if(!n.session)throw new re;let s=n.session,a=null,o=null;"pkce"===this.flowType&&null!=e.email&&([a,o]=await rT(this.storage,this.storageKey));let{data:l,error:u}=await rL(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:o}),jwt:s.access_token,xform:rF});if(u)throw u;return s.user=l.user,await this._saveSession(s),await this._notifyAllSubscribers("USER_UPDATED",s),{data:{user:s.user},error:null}})}catch(e){if(t5(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new re;let t=Date.now()/1e3,r=t,n=!0,i=null,{payload:s}=rS(e.access_token);if(s.exp&&(n=(r=s.exp)<=t),n){let{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};i=t}else{let{data:n,error:s}=await this._getUser(e.access_token);if(s)throw s;i={access_token:e.access_token,refresh_token:e.refresh_token,user:n.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(e){if(t5(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){let{data:n,error:i}=t;if(i)throw i;e=null!=(r=n.session)?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new re;let{session:n,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:n?{data:{user:n.user,session:n},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(t5(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!rg())throw new rn("No browser detected.");if(e.error||e.error_description||e.error_code)throw new rn(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new ri("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new rn("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new ri("No code detected.");let{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;let n=new URL(window.location.href);return n.searchParams.delete("code"),window.history.replaceState(window.history.state,"",n.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:r,provider_refresh_token:n,access_token:i,refresh_token:s,expires_in:a,expires_at:o,token_type:l}=e;if(!i||!a||!s||!l)throw new rn("No session defined in URL");let u=Math.round(Date.now()/1e3),c=parseInt(a),d=u+c;o&&(d=parseInt(o));let h=d-u;1e3*h<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${h}s, should have been closer to ${c}s`);let f=d-c;u-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,d,u):u-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,d,u);let{data:p,error:g}=await this._getUser(i);if(g)throw g;let m={provider_token:r,provider_refresh_token:n,access_token:i,expires_in:c,expires_at:d,refresh_token:s,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:m,redirectType:e.type},error:null}}catch(e){if(t5(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await rw(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;let{data:n,error:i}=t;if(i)return{error:i};let s=null==(r=n.session)?void 0:r.access_token;if(s){let{error:t}=await this.admin.signOut(s,e);if(t&&!(t5(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await rE(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,n;try{let{data:{session:n},error:i}=t;if(i)throw i;await (null==(r=this.stateChangeEmitters.get(e))?void 0:r.callback("INITIAL_SESSION",n)),this._debug("INITIAL_SESSION","callback id",e,"session",n)}catch(t){await (null==(n=this.stateChangeEmitters.get(e))?void 0:n.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let r=null,n=null;"pkce"===this.flowType&&([r,n]=await rT(this.storage,this.storageKey,!0));try{return await rL(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:n,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(t5(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!=(e=t.user.identities)?e:[]},error:null}}catch(e){if(t5(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:r,error:n}=await this._useSession(async t=>{var r,n,i,s,a;let{data:o,error:l}=t;if(l)throw l;let u=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null==(r=e.options)?void 0:r.redirectTo,scopes:null==(n=e.options)?void 0:n.scopes,queryParams:null==(i=e.options)?void 0:i.queryParams,skipBrowserRedirect:!0});return await rL(this.fetch,"GET",u,{headers:this.headers,jwt:null!=(a=null==(s=o.session)?void 0:s.access_token)?a:void 0})});if(n)throw n;return!rg()||(null==(t=e.options)?void 0:t.skipBrowserRedirect)||window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(t){if(t5(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,n;let{data:i,error:s}=t;if(s)throw s;return await rL(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!=(n=null==(r=i.session)?void 0:r.access_token)?n:void 0})})}catch(e){if(t5(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var r,n;let i=Date.now();return await (r=async r=>(r>0&&await rk(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await rL(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:rq})),n=(e,t)=>{let r=200*Math.pow(2,e);return t&&ra(t)&&Date.now()+r-i<3e4},new Promise((e,t)=>{(async()=>{for(let i=0;i<1/0;i++)try{let t=await r(i);if(!n(i,null,t))return void e(t)}catch(e){if(!n(i,e))return void t(e)}})()}))}catch(e){if(this._debug(t,"error",e),t5(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),rg()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let r=await rw(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r)){this._debug(t,"session is not valid"),null!==r&&await this._removeSession();return}let n=(null!=(e=r.expires_at)?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${n?"":" not"} expired with margin of 90000s`),n){if(this.autoRefreshToken&&r.refresh_token){let{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),ra(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new re;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let n=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(n,"begin");try{this.refreshingDeferred=new rx;let{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new re;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let n={session:t.session,error:null};return this.refreshingDeferred.resolve(n),n}catch(e){if(this._debug(n,"error",e),t5(e)){let r={session:null,error:e};return ra(e)||await this._removeSession(),null==(t=this.refreshingDeferred)||t.resolve(r),r}throw null==(r=this.refreshingDeferred)||r.reject(e),e}finally{this.refreshingDeferred=null,this._debug(n,"end")}}async _notifyAllSubscribers(e,t,r=!0){let n=`#_notifyAllSubscribers(${e})`;this._debug(n,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});let n=[],i=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(e){n.push(e)}});if(await Promise.all(i),n.length>0){for(let e=0;e<n.length;e+=1)console.error(n[e]);throw n[0]}}finally{this._debug(n,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await r_(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await rE(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&rg()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");let n=Math.floor((1e3*r.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${n} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),n<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof rY)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!rg()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState)return void this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){let n=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&n.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&n.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){let[e,t]=await rT(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});n.push(r.toString())}if(null==r?void 0:r.queryParams){let e=new URLSearchParams(r.queryParams);n.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&n.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${n.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;let{data:n,error:i}=t;return i?{data:null,error:i}:await rL(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null==(r=null==n?void 0:n.session)?void 0:r.access_token})})}catch(e){if(t5(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var r,n;let{data:i,error:s}=t;if(s)return{data:null,error:s};let a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await rL(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null==(r=null==i?void 0:i.session)?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null==(n=null==o?void 0:o.totp)?void 0:n.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})})}catch(e){if(t5(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:n,error:i}=t;if(i)return{data:null,error:i};let{data:s,error:a}=await rL(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null==(r=null==n?void 0:n.session)?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+s.expires_in},s)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",s),{data:s,error:a})})}catch(e){if(t5(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:n,error:i}=t;return i?{data:null,error:i}:await rL(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null==(r=null==n?void 0:n.session)?void 0:r.access_token})})}catch(e){if(t5(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let r=(null==e?void 0:e.factors)||[],n=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),i=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:n,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;let{data:{session:n},error:i}=e;if(i)return{data:null,error:i};if(!n)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:s}=rS(n.access_token),a=null;s.aal&&(a=s.aal);let o=a;return(null!=(r=null==(t=n.user.factors)?void 0:t.filter(e=>"verified"===e.status))?r:[]).length>0&&(o="aal2"),{data:{currentLevel:a,nextLevel:o,currentAuthenticationMethods:s.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r||(r=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return r;let{data:n,error:i}=await rL(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!n.keys||0===n.keys.length)throw new rl("JWKS is empty");if(this.jwks=n,this.jwks_cached_at=Date.now(),!(r=n.keys.find(t=>t.kid===e)))throw new rl("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let n=e;if(!n){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};n=e.session.access_token}let{header:i,payload:s,signature:a,raw:{header:o,payload:l}}=rS(n);var r=s.exp;if(!r)throw Error("Missing exp claim");if(r<=Math.floor(Date.now()/1e3))throw Error("JWT has expired");if(!i.kid||"HS256"===i.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(n);if(e)throw e;return{data:{claims:s,header:i,signature:a},error:null}}let u=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(i.alg),c=await this.fetchJwk(i.kid,t),d=await crypto.subtle.importKey("jwk",c,u,!0,["verify"]);if(!await crypto.subtle.verify(u,d,a,function(e){let t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let n=e.charCodeAt(r);if(n>55295&&n<=56319){let t=(n-55296)*1024&65535;n=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(n,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${o}.${l}`)))throw new rl("Invalid JWT signature");return{data:{claims:s,header:i,signature:a},error:null}}catch(e){if(t5(e))return{data:null,error:e};throw e}}}r4.nextInstanceID=0;let r3=r4;class r9 extends r3{constructor(e){super(e)}}class r5{constructor(e,t,r){var n,i,s;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let a=new URL(function(e){return e.endsWith("/")?e:e+"/"}(e));this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);let o=`sb-${a.hostname.split(".")[0]}-auth-token`,l=function(e,t){var r,n;let{db:i,auth:s,realtime:a,global:o}=e,{db:l,auth:u,realtime:c,global:d}=t,h={db:Object.assign(Object.assign({},l),i),auth:Object.assign(Object.assign({},u),s),realtime:Object.assign(Object.assign({},c),a),global:Object.assign(Object.assign(Object.assign({},d),o),{headers:Object.assign(Object.assign({},null!=(r=null==d?void 0:d.headers)?r:{}),null!=(n=null==o?void 0:o.headers)?n:{})}),accessToken:()=>{var e,t,r,n;return e=this,t=void 0,n=function*(){return""},new(r=void 0,r=Promise)(function(i,s){function a(e){try{l(n.next(e))}catch(e){s(e)}}function o(e){try{l(n.throw(e))}catch(e){s(e)}}function l(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((n=n.apply(e,t||[])).next())})}};return e.accessToken?h.accessToken=e.accessToken:delete h.accessToken,h}(null!=r?r:{},{db:tK,realtime:tX,auth:Object.assign(Object.assign({},tW),{storageKey:o}),global:tG});this.storageKey=null!=(n=l.auth.storageKey)?n:"",this.headers=null!=(i=l.global.headers)?i:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!=(s=l.auth)?s:{},this.headers,l.global.fetch),this.fetch=tQ(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new e3(new URL("rest/v1",a).href,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new e4(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new tH(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,r,n,i,s;return r=this,n=void 0,i=void 0,s=function*(){if(this.accessToken)return yield this.accessToken();let{data:r}=yield this.auth.getSession();return null!=(t=null==(e=r.session)?void 0:e.access_token)?t:null},new(i||(i=Promise))(function(e,t){function a(e){try{l(s.next(e))}catch(e){t(e)}}function o(e){try{l(s.throw(e))}catch(e){t(e)}}function l(t){var r;t.done?e(t.value):((r=t.value)instanceof i?r:new i(function(e){e(r)})).then(a,o)}l((s=s.apply(r,n||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:n,storageKey:i,flowType:s,lock:a,debug:o},l,u){let c={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new r9({url:this.authUrl.href,headers:Object.assign(Object.assign({},c),l),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:n,flowType:s,lock:a,debug:o,fetch:u,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new tb(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let r6=(e,t,r)=>new r5(e,t,r),r8="AES-GCM",r7=process.env.ROKEY_ENCRYPTION_KEY;if(!r7||64!==r7.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");function ne(e){let t=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2)t[r/2]=parseInt(e.substr(r,2),16);return t}function nt(e){return Array.from(e,e=>e.toString(16).padStart(2,"0")).join("")}let nr=ne(r7);async function nn(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=crypto.getRandomValues(new Uint8Array(12)),r=await crypto.subtle.importKey("raw",nr,{name:r8},!1,["encrypt"]),n=new TextEncoder().encode(e),i=new Uint8Array(await crypto.subtle.encrypt({name:r8,iv:t},r,n)),s=i.slice(0,-16),a=i.slice(-16);return`${nt(t)}:${nt(a)}:${nt(s)}`}async function ni(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let r=ne(t[0]),n=ne(t[1]),i=ne(t[2]);if(12!==r.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==n.length)throw Error("Invalid authTag length. Expected 16 bytes.");let s=await crypto.subtle.importKey("raw",nr,{name:r8},!1,["decrypt"]),a=new Uint8Array(i.length+n.length);a.set(i),a.set(n,i.length);let o=await crypto.subtle.decrypt({name:r8,iv:r},s,a);return new TextDecoder().decode(o)}class ns{static{this.KEY_PREFIX="rk_live_"}static{this.RANDOM_PART_LENGTH=8}static{this.SECRET_PART_LENGTH=32}static async generateApiKey(){let e=Array.from(crypto.getRandomValues(new Uint8Array(this.RANDOM_PART_LENGTH/2)),e=>e.toString(16).padStart(2,"0")).join(""),t=this.generateRandomString(this.SECRET_PART_LENGTH),r=`${this.KEY_PREFIX}${e}`,n=`${r}_${t}`,i=await this.hashApiKey(n);return{fullKey:n,prefix:r,secretPart:t,hash:i}}static generateRandomString(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r="";for(let n=0;n<e;n++){let e=crypto.getRandomValues(new Uint8Array(1))[0]%t.length;r+=t[e]}return r}static async hashApiKey(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t)),e=>e.toString(16).padStart(2,"0")).join("")}static isValidFormat(e){return RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`).test(e)}static extractPrefix(e){let t=e.split("_");return t.length>=3?`${t[0]}_${t[1]}_${t[2]}`:""}static async encryptSuffix(e){let t=e.slice(-4);return await nn(t)}static async decryptSuffix(e){try{return await ni(e)}catch(e){return"xxxx"}}static async createMaskedKey(e,t){let r=await this.decryptSuffix(t),n=this.SECRET_PART_LENGTH-4;return`${e}_${"*".repeat(n)}${r}`}static validateSubscriptionLimits(e,t){let r={free:3,starter:50,professional:999999,enterprise:999999},n=r[e]||r.free;return t>=n?{allowed:!1,limit:n,message:`You have reached the maximum number of user-generated API keys (${n}) for your ${e} plan.`}:{allowed:!0,limit:n}}}class na{constructor(){this.supabase=r6("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY)}async validateApiKey(e,t){try{if(!ns.isValidFormat(e))return{isValid:!1,error:"Invalid API key format"};let r=await ns.hashApiKey(e),{data:n,error:i}=await this.supabase.from("user_generated_api_keys").select(`
          *,
          custom_api_configs!inner(
            id,
            name,
            user_id,
            routing_strategy,
            routing_strategy_params
          )
        `).eq("key_hash",r).eq("status","active").single();if(i||!n)return{isValid:!1,error:"Invalid or inactive API key"};if(n.expires_at&&new Date(n.expires_at)<new Date)return await this.supabase.from("user_generated_api_keys").update({status:"expired"}).eq("id",n.id),{isValid:!1,error:"API key has expired"};if(n.allowed_ips&&n.allowed_ips.length>0&&t&&!this.checkIpAllowed(t,n.allowed_ips))return{isValid:!1,error:"IP address not allowed for this API key"};return await this.updateLastUsed(n.id,t),{isValid:!0,apiKey:n}}catch(e){return{isValid:!1,error:"Internal server error during validation"}}}checkIpAllowed(e,t){return t.includes(e)||t.includes("*")}async updateLastUsed(e,t){let{data:r}=await this.supabase.from("user_generated_api_keys").select("total_requests").eq("id",e).single(),n={last_used_at:new Date().toISOString(),total_requests:(r?.total_requests||0)+1};t&&(n.last_used_ip=t),await this.supabase.from("user_generated_api_keys").update(n).eq("id",e)}async logApiUsage(e,t,r,n){await this.supabase.from("user_api_key_usage_logs").insert({user_generated_api_key_id:e,user_id:t,custom_api_config_id:r,endpoint:n.endpoint,http_method:n.method,status_code:n.statusCode,ip_address:n.ipAddress,user_agent:n.userAgent,referer:n.referer,model_used:n.modelUsed,provider_used:n.providerUsed,tokens_prompt:n.tokensPrompt,tokens_completion:n.tokensCompletion,cost_usd:n.costUsd,response_time_ms:n.responseTimeMs,error_message:n.errorMessage,error_type:n.errorType})}}class no{constructor(){this.validator=new na}extractApiKey(e){let t=e.headers.get("authorization");if(t){let e=t.match(/^Bearer\s+(.+)$/i);if(e)return e[1]}let r=e.headers.get("x-api-key");return r||null}getClientIp(e){let t=e.headers.get("x-forwarded-for");if(t)return t.split(",")[0].trim();let r=e.headers.get("x-real-ip");if(r)return r;let n=e.headers.get("cf-connecting-ip");return n||"127.0.0.1"}async authenticateRequest(e){try{let t=this.extractApiKey(e);if(!t)return{success:!1,error:'API key is required. Provide it in Authorization header as "Bearer YOUR_API_KEY" or in x-api-key header.',statusCode:401};let r=this.getClientIp(e),n=await this.validator.validateApiKey(t,r);if(!n.isValid){let e=401;return n.error?.includes("expired")?e=401:n.error?.includes("IP address not allowed")&&(e=403),{success:!1,error:n.error||"Invalid API key",statusCode:e}}return{success:!0,userApiKey:n.apiKey,userConfig:n.apiKey.custom_api_configs,ipAddress:r}}catch(e){return{success:!1,error:"Internal authentication error",statusCode:500}}}async logApiUsage(e,t,r,n){try{let i=new URL(t.url);await this.validator.logApiUsage(e.id,e.user_id,e.custom_api_config_id,{endpoint:i.pathname,method:t.method,statusCode:r.statusCode,ipAddress:n,userAgent:t.headers.get("user-agent")||void 0,referer:t.headers.get("referer")||void 0,modelUsed:r.modelUsed,providerUsed:r.providerUsed,tokensPrompt:r.tokensPrompt,tokensCompletion:r.tokensCompletion,costUsd:r.costUsd,responseTimeMs:r.responseTimeMs,errorMessage:r.errorMessage,errorType:r.errorType})}catch(e){}}hasPermission(e,t){let r=e.permissions;switch(t){case"chat":return!0===r.chat;case"streaming":return!0===r.streaming;case"all_models":return!0===r.all_models;default:return!1}}isOriginAllowed(e,t){if(!t)return!0;let r=e.allowed_domains;return!r||0===r.length||r.some(e=>{if("*"===e)return!0;if(e.startsWith("*.")){let r=e.slice(2);return t.endsWith(r)}return t===e||t===`https://${e}`||t===`http://${e}`})}createErrorResponse(e,t){return new Response(JSON.stringify({error:{message:e,type:this.getErrorType(t),code:t}}),{status:t,headers:{"Content-Type":"application/json"}})}getErrorType(e){switch(e){case 401:return"authentication_error";case 403:return"permission_denied";case 500:return"internal_error";default:return"api_error"}}}!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let r of e)t[r]=r;return t},e.getValidEnumValues=t=>{let r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),n={};for(let e of r)n[e]=t[e];return e.objectValues(n)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(let r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(y||(y={})),(v||(v={})).mergeShapes=(e,t)=>({...e,...t});let nl=y.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),nu=e=>{switch(typeof e){case"undefined":return nl.undefined;case"string":return nl.string;case"number":return Number.isNaN(e)?nl.nan:nl.number;case"boolean":return nl.boolean;case"function":return nl.function;case"bigint":return nl.bigint;case"symbol":return nl.symbol;case"object":if(Array.isArray(e))return nl.array;if(null===e)return nl.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return nl.promise;if("undefined"!=typeof Map&&e instanceof Map)return nl.map;if("undefined"!=typeof Set&&e instanceof Set)return nl.set;if("undefined"!=typeof Date&&e instanceof Date)return nl.date;return nl.object;default:return nl.unknown}},nc=y.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class nd extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},r={_errors:[]},n=e=>{for(let i of e.issues)if("invalid_union"===i.code)i.unionErrors.map(n);else if("invalid_return_type"===i.code)n(i.returnTypeError);else if("invalid_arguments"===i.code)n(i.argumentsError);else if(0===i.path.length)r._errors.push(t(i));else{let e=r,n=0;for(;n<i.path.length;){let r=i.path[n];n===i.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(i))):e[r]=e[r]||{_errors:[]},e=e[r],n++}}};return n(this),r}static assert(e){if(!(e instanceof nd))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,y.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},r=[];for(let n of this.issues)n.path.length>0?(t[n.path[0]]=t[n.path[0]]||[],t[n.path[0]].push(e(n))):r.push(e(n));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}nd.create=e=>new nd(e);let nh=(e,t)=>{let r;switch(e.code){case nc.invalid_type:r=e.received===nl.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case nc.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,y.jsonStringifyReplacer)}`;break;case nc.unrecognized_keys:r=`Unrecognized key(s) in object: ${y.joinValues(e.keys,", ")}`;break;case nc.invalid_union:r="Invalid input";break;case nc.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${y.joinValues(e.options)}`;break;case nc.invalid_enum_value:r=`Invalid enum value. Expected ${y.joinValues(e.options)}, received '${e.received}'`;break;case nc.invalid_arguments:r="Invalid function arguments";break;case nc.invalid_return_type:r="Invalid function return type";break;case nc.invalid_date:r="Invalid date";break;case nc.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:y.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case nc.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case nc.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case nc.custom:r="Invalid input";break;case nc.invalid_intersection_types:r="Intersection results could not be merged";break;case nc.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case nc.not_finite:r="Number must be finite";break;default:r=t.defaultError,y.assertNever(e)}return{message:r}},nf=e=>{let{data:t,path:r,errorMaps:n,issueData:i}=e,s=[...r,...i.path||[]],a={...i,path:s};if(void 0!==i.message)return{...i,path:s,message:i.message};let o="";for(let e of n.filter(e=>!!e).slice().reverse())o=e(a,{data:t,defaultError:o}).message;return{...i,path:s,message:o}};function np(e,t){let r=nf({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,nh,nh==nh?void 0:nh].filter(e=>!!e)});e.common.issues.push(r)}class ng{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let r=[];for(let n of t){if("aborted"===n.status)return nm;"dirty"===n.status&&e.dirty(),r.push(n.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){let r=[];for(let e of t){let t=await e.key,n=await e.value;r.push({key:t,value:n})}return ng.mergeObjectSync(e,r)}static mergeObjectSync(e,t){let r={};for(let n of t){let{key:t,value:i}=n;if("aborted"===t.status||"aborted"===i.status)return nm;"dirty"===t.status&&e.dirty(),"dirty"===i.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==i.value||n.alwaysSet)&&(r[t.value]=i.value)}return{status:e.value,value:r}}}let nm=Object.freeze({status:"aborted"}),ny=e=>({status:"dirty",value:e}),nv=e=>({status:"valid",value:e}),nb=e=>"aborted"===e.status,n_=e=>"dirty"===e.status,nw=e=>"valid"===e.status,nE=e=>"undefined"!=typeof Promise&&e instanceof Promise;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(b||(b={}));class nx{constructor(e,t,r,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=n}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let nS=(e,t)=>{if(nw(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new nd(e.common.issues);return this._error=t,this._error}}};function nk(e){if(!e)return{};let{errorMap:t,invalid_type_error:r,required_error:n,description:i}=e;if(t&&(r||n))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:i}:{errorMap:(t,i)=>{let{message:s}=e;return"invalid_enum_value"===t.code?{message:s??i.defaultError}:void 0===i.data?{message:s??n??i.defaultError}:"invalid_type"!==t.code?{message:i.defaultError}:{message:s??r??i.defaultError}},description:i}}class nR{get description(){return this._def.description}_getType(e){return nu(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:nu(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new ng,ctx:{common:e.parent.common,data:e.data,parsedType:nu(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(nE(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){let r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:nu(e)},n=this._parseSync({data:e,path:r.path,parent:r});return nS(r,n)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:nu(e)};if(!this["~standard"].async)try{let r=this._parseSync({data:e,path:[],parent:t});return nw(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>nw(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){let r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:nu(e)},n=this._parse({data:e,path:r.path,parent:r});return nS(r,await (nE(n)?n:Promise.resolve(n)))}refine(e,t){let r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,n)=>{let i=e(t),s=()=>n.addIssue({code:nc.custom,...r(t)});return"undefined"!=typeof Promise&&i instanceof Promise?i.then(e=>!!e||(s(),!1)):!!i||(s(),!1)})}refinement(e,t){return this._refinement((r,n)=>!!e(r)||(n.addIssue("function"==typeof t?t(r,n):t),!1))}_refinement(e){return new ic({schema:this,typeName:_.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return id.create(this,this._def)}nullable(){return ih.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return n2.create(this)}promise(){return iu.create(this,this._def)}or(e){return n3.create([this,e],this._def)}and(e){return n6.create(this,e,this._def)}transform(e){return new ic({...nk(this._def),schema:this,typeName:_.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new ip({...nk(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:_.ZodDefault})}brand(){return new iy({typeName:_.ZodBranded,type:this,...nk(this._def)})}catch(e){return new ig({...nk(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:_.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return iv.create(this,e)}readonly(){return ib.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let nC=/^c[^\s-]{8,}$/i,nO=/^[0-9a-z]+$/,nT=/^[0-9A-HJKMNP-TV-Z]{26}$/i,nP=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,nA=/^[a-z0-9_-]{21}$/i,nj=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,nI=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,nN=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,n$=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,nD=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,nM=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,nL=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,nU=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,nq=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nB="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",nF=RegExp(`^${nB}$`);function nz(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}class nH extends nR{_parse(e){var t,r,n,i;let a;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==nl.string){let t=this._getOrReturnCtx(e);return np(t,{code:nc.invalid_type,expected:nl.string,received:t.parsedType}),nm}let o=new ng;for(let l of this._def.checks)if("min"===l.kind)e.data.length<l.value&&(np(a=this._getOrReturnCtx(e,a),{code:nc.too_small,minimum:l.value,type:"string",inclusive:!0,exact:!1,message:l.message}),o.dirty());else if("max"===l.kind)e.data.length>l.value&&(np(a=this._getOrReturnCtx(e,a),{code:nc.too_big,maximum:l.value,type:"string",inclusive:!0,exact:!1,message:l.message}),o.dirty());else if("length"===l.kind){let t=e.data.length>l.value,r=e.data.length<l.value;(t||r)&&(a=this._getOrReturnCtx(e,a),t?np(a,{code:nc.too_big,maximum:l.value,type:"string",inclusive:!0,exact:!0,message:l.message}):r&&np(a,{code:nc.too_small,minimum:l.value,type:"string",inclusive:!0,exact:!0,message:l.message}),o.dirty())}else if("email"===l.kind)nN.test(e.data)||(np(a=this._getOrReturnCtx(e,a),{validation:"email",code:nc.invalid_string,message:l.message}),o.dirty());else if("emoji"===l.kind)s||(s=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),s.test(e.data)||(np(a=this._getOrReturnCtx(e,a),{validation:"emoji",code:nc.invalid_string,message:l.message}),o.dirty());else if("uuid"===l.kind)nP.test(e.data)||(np(a=this._getOrReturnCtx(e,a),{validation:"uuid",code:nc.invalid_string,message:l.message}),o.dirty());else if("nanoid"===l.kind)nA.test(e.data)||(np(a=this._getOrReturnCtx(e,a),{validation:"nanoid",code:nc.invalid_string,message:l.message}),o.dirty());else if("cuid"===l.kind)nC.test(e.data)||(np(a=this._getOrReturnCtx(e,a),{validation:"cuid",code:nc.invalid_string,message:l.message}),o.dirty());else if("cuid2"===l.kind)nO.test(e.data)||(np(a=this._getOrReturnCtx(e,a),{validation:"cuid2",code:nc.invalid_string,message:l.message}),o.dirty());else if("ulid"===l.kind)nT.test(e.data)||(np(a=this._getOrReturnCtx(e,a),{validation:"ulid",code:nc.invalid_string,message:l.message}),o.dirty());else if("url"===l.kind)try{new URL(e.data)}catch{np(a=this._getOrReturnCtx(e,a),{validation:"url",code:nc.invalid_string,message:l.message}),o.dirty()}else"regex"===l.kind?(l.regex.lastIndex=0,l.regex.test(e.data)||(np(a=this._getOrReturnCtx(e,a),{validation:"regex",code:nc.invalid_string,message:l.message}),o.dirty())):"trim"===l.kind?e.data=e.data.trim():"includes"===l.kind?e.data.includes(l.value,l.position)||(np(a=this._getOrReturnCtx(e,a),{code:nc.invalid_string,validation:{includes:l.value,position:l.position},message:l.message}),o.dirty()):"toLowerCase"===l.kind?e.data=e.data.toLowerCase():"toUpperCase"===l.kind?e.data=e.data.toUpperCase():"startsWith"===l.kind?e.data.startsWith(l.value)||(np(a=this._getOrReturnCtx(e,a),{code:nc.invalid_string,validation:{startsWith:l.value},message:l.message}),o.dirty()):"endsWith"===l.kind?e.data.endsWith(l.value)||(np(a=this._getOrReturnCtx(e,a),{code:nc.invalid_string,validation:{endsWith:l.value},message:l.message}),o.dirty()):"datetime"===l.kind?(function(e){let t=`${nB}T${nz(e)}`,r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,RegExp(`^${t}$`)})(l).test(e.data)||(np(a=this._getOrReturnCtx(e,a),{code:nc.invalid_string,validation:"datetime",message:l.message}),o.dirty()):"date"===l.kind?nF.test(e.data)||(np(a=this._getOrReturnCtx(e,a),{code:nc.invalid_string,validation:"date",message:l.message}),o.dirty()):"time"===l.kind?RegExp(`^${nz(l)}$`).test(e.data)||(np(a=this._getOrReturnCtx(e,a),{code:nc.invalid_string,validation:"time",message:l.message}),o.dirty()):"duration"===l.kind?nI.test(e.data)||(np(a=this._getOrReturnCtx(e,a),{validation:"duration",code:nc.invalid_string,message:l.message}),o.dirty()):"ip"===l.kind?(t=e.data,!(("v4"===(r=l.version)||!r)&&n$.test(t)||("v6"===r||!r)&&nM.test(t))&&1&&(np(a=this._getOrReturnCtx(e,a),{validation:"ip",code:nc.invalid_string,message:l.message}),o.dirty())):"jwt"===l.kind?!function(e,t){if(!nj.test(e))return!1;try{let[r]=e.split("."),n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),i=JSON.parse(atob(n));if("object"!=typeof i||null===i||"typ"in i&&i?.typ!=="JWT"||!i.alg||t&&i.alg!==t)return!1;return!0}catch{return!1}}(e.data,l.alg)&&(np(a=this._getOrReturnCtx(e,a),{validation:"jwt",code:nc.invalid_string,message:l.message}),o.dirty()):"cidr"===l.kind?(n=e.data,!(("v4"===(i=l.version)||!i)&&nD.test(n)||("v6"===i||!i)&&nL.test(n))&&1&&(np(a=this._getOrReturnCtx(e,a),{validation:"cidr",code:nc.invalid_string,message:l.message}),o.dirty())):"base64"===l.kind?nU.test(e.data)||(np(a=this._getOrReturnCtx(e,a),{validation:"base64",code:nc.invalid_string,message:l.message}),o.dirty()):"base64url"===l.kind?nq.test(e.data)||(np(a=this._getOrReturnCtx(e,a),{validation:"base64url",code:nc.invalid_string,message:l.message}),o.dirty()):y.assertNever(l);return{status:o.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),{validation:t,code:nc.invalid_string,...b.errToObj(r)})}_addCheck(e){return new nH({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...b.errToObj(e)})}url(e){return this._addCheck({kind:"url",...b.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...b.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...b.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...b.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...b.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...b.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...b.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...b.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...b.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...b.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...b.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...b.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...b.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...b.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...b.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...b.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...b.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...b.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...b.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...b.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...b.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...b.errToObj(t)})}nonempty(e){return this.min(1,b.errToObj(e))}trim(){return new nH({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new nH({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new nH({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}nH.create=e=>new nH({checks:[],typeName:_.ZodString,coerce:e?.coerce??!1,...nk(e)});class nV extends nR{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==nl.number){let t=this._getOrReturnCtx(e);return np(t,{code:nc.invalid_type,expected:nl.number,received:t.parsedType}),nm}let r=new ng;for(let n of this._def.checks)"int"===n.kind?y.isInteger(e.data)||(np(t=this._getOrReturnCtx(e,t),{code:nc.invalid_type,expected:"integer",received:"float",message:n.message}),r.dirty()):"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(np(t=this._getOrReturnCtx(e,t),{code:nc.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(np(t=this._getOrReturnCtx(e,t),{code:nc.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):"multipleOf"===n.kind?0!==function(e,t){let r=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,i=r>n?r:n;return Number.parseInt(e.toFixed(i).replace(".",""))%Number.parseInt(t.toFixed(i).replace(".",""))/10**i}(e.data,n.value)&&(np(t=this._getOrReturnCtx(e,t),{code:nc.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):"finite"===n.kind?Number.isFinite(e.data)||(np(t=this._getOrReturnCtx(e,t),{code:nc.not_finite,message:n.message}),r.dirty()):y.assertNever(n);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,b.toString(t))}gt(e,t){return this.setLimit("min",e,!1,b.toString(t))}lte(e,t){return this.setLimit("max",e,!0,b.toString(t))}lt(e,t){return this.setLimit("max",e,!1,b.toString(t))}setLimit(e,t,r,n){return new nV({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:b.toString(n)}]})}_addCheck(e){return new nV({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:b.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:b.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:b.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:b.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:b.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:b.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:b.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:b.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:b.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&y.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let r of this._def.checks)if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;else"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value);return Number.isFinite(t)&&Number.isFinite(e)}}nV.create=e=>new nV({checks:[],typeName:_.ZodNumber,coerce:e?.coerce||!1,...nk(e)});class nG extends nR{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==nl.bigint)return this._getInvalidInput(e);let r=new ng;for(let n of this._def.checks)"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(np(t=this._getOrReturnCtx(e,t),{code:nc.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(np(t=this._getOrReturnCtx(e,t),{code:nc.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):"multipleOf"===n.kind?e.data%n.value!==BigInt(0)&&(np(t=this._getOrReturnCtx(e,t),{code:nc.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):y.assertNever(n);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return np(t,{code:nc.invalid_type,expected:nl.bigint,received:t.parsedType}),nm}gte(e,t){return this.setLimit("min",e,!0,b.toString(t))}gt(e,t){return this.setLimit("min",e,!1,b.toString(t))}lte(e,t){return this.setLimit("max",e,!0,b.toString(t))}lt(e,t){return this.setLimit("max",e,!1,b.toString(t))}setLimit(e,t,r,n){return new nG({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:b.toString(n)}]})}_addCheck(e){return new nG({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:b.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:b.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:b.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:b.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:b.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}nG.create=e=>new nG({checks:[],typeName:_.ZodBigInt,coerce:e?.coerce??!1,...nk(e)});class nK extends nR{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==nl.boolean){let t=this._getOrReturnCtx(e);return np(t,{code:nc.invalid_type,expected:nl.boolean,received:t.parsedType}),nm}return nv(e.data)}}nK.create=e=>new nK({typeName:_.ZodBoolean,coerce:e?.coerce||!1,...nk(e)});class nW extends nR{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==nl.date){let t=this._getOrReturnCtx(e);return np(t,{code:nc.invalid_type,expected:nl.date,received:t.parsedType}),nm}if(Number.isNaN(e.data.getTime()))return np(this._getOrReturnCtx(e),{code:nc.invalid_date}),nm;let r=new ng;for(let n of this._def.checks)"min"===n.kind?e.data.getTime()<n.value&&(np(t=this._getOrReturnCtx(e,t),{code:nc.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),r.dirty()):"max"===n.kind?e.data.getTime()>n.value&&(np(t=this._getOrReturnCtx(e,t),{code:nc.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),r.dirty()):y.assertNever(n);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new nW({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:b.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:b.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}nW.create=e=>new nW({checks:[],coerce:e?.coerce||!1,typeName:_.ZodDate,...nk(e)});class nX extends nR{_parse(e){if(this._getType(e)!==nl.symbol){let t=this._getOrReturnCtx(e);return np(t,{code:nc.invalid_type,expected:nl.symbol,received:t.parsedType}),nm}return nv(e.data)}}nX.create=e=>new nX({typeName:_.ZodSymbol,...nk(e)});class nZ extends nR{_parse(e){if(this._getType(e)!==nl.undefined){let t=this._getOrReturnCtx(e);return np(t,{code:nc.invalid_type,expected:nl.undefined,received:t.parsedType}),nm}return nv(e.data)}}nZ.create=e=>new nZ({typeName:_.ZodUndefined,...nk(e)});class nJ extends nR{_parse(e){if(this._getType(e)!==nl.null){let t=this._getOrReturnCtx(e);return np(t,{code:nc.invalid_type,expected:nl.null,received:t.parsedType}),nm}return nv(e.data)}}nJ.create=e=>new nJ({typeName:_.ZodNull,...nk(e)});class nY extends nR{constructor(){super(...arguments),this._any=!0}_parse(e){return nv(e.data)}}nY.create=e=>new nY({typeName:_.ZodAny,...nk(e)});class nQ extends nR{constructor(){super(...arguments),this._unknown=!0}_parse(e){return nv(e.data)}}nQ.create=e=>new nQ({typeName:_.ZodUnknown,...nk(e)});class n0 extends nR{_parse(e){let t=this._getOrReturnCtx(e);return np(t,{code:nc.invalid_type,expected:nl.never,received:t.parsedType}),nm}}n0.create=e=>new n0({typeName:_.ZodNever,...nk(e)});class n1 extends nR{_parse(e){if(this._getType(e)!==nl.undefined){let t=this._getOrReturnCtx(e);return np(t,{code:nc.invalid_type,expected:nl.void,received:t.parsedType}),nm}return nv(e.data)}}n1.create=e=>new n1({typeName:_.ZodVoid,...nk(e)});class n2 extends nR{_parse(e){let{ctx:t,status:r}=this._processInputParams(e),n=this._def;if(t.parsedType!==nl.array)return np(t,{code:nc.invalid_type,expected:nl.array,received:t.parsedType}),nm;if(null!==n.exactLength){let e=t.data.length>n.exactLength.value,i=t.data.length<n.exactLength.value;(e||i)&&(np(t,{code:e?nc.too_big:nc.too_small,minimum:i?n.exactLength.value:void 0,maximum:e?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),r.dirty())}if(null!==n.minLength&&t.data.length<n.minLength.value&&(np(t,{code:nc.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),r.dirty()),null!==n.maxLength&&t.data.length>n.maxLength.value&&(np(t,{code:nc.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>n.type._parseAsync(new nx(t,e,t.path,r)))).then(e=>ng.mergeArray(r,e));let i=[...t.data].map((e,r)=>n.type._parseSync(new nx(t,e,t.path,r)));return ng.mergeArray(r,i)}get element(){return this._def.type}min(e,t){return new n2({...this._def,minLength:{value:e,message:b.toString(t)}})}max(e,t){return new n2({...this._def,maxLength:{value:e,message:b.toString(t)}})}length(e,t){return new n2({...this._def,exactLength:{value:e,message:b.toString(t)}})}nonempty(e){return this.min(1,e)}}n2.create=(e,t)=>new n2({type:e,minLength:null,maxLength:null,exactLength:null,typeName:_.ZodArray,...nk(t)});class n4 extends nR{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=y.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==nl.object){let t=this._getOrReturnCtx(e);return np(t,{code:nc.invalid_type,expected:nl.object,received:t.parsedType}),nm}let{status:t,ctx:r}=this._processInputParams(e),{shape:n,keys:i}=this._getCached(),s=[];if(!(this._def.catchall instanceof n0&&"strip"===this._def.unknownKeys))for(let e in r.data)i.includes(e)||s.push(e);let a=[];for(let e of i){let t=n[e],i=r.data[e];a.push({key:{status:"valid",value:e},value:t._parse(new nx(r,i,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof n0){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of s)a.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)s.length>0&&(np(r,{code:nc.unrecognized_keys,keys:s}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of s){let n=r.data[t];a.push({key:{status:"valid",value:t},value:e._parse(new nx(r,n,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of a){let r=await t.key,n=await t.value;e.push({key:r,value:n,alwaysSet:t.alwaysSet})}return e}).then(e=>ng.mergeObjectSync(t,e)):ng.mergeObjectSync(t,a)}get shape(){return this._def.shape()}strict(e){return b.errToObj,new n4({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{let n=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:b.errToObj(e).message??n}:{message:n}}}:{}})}strip(){return new n4({...this._def,unknownKeys:"strip"})}passthrough(){return new n4({...this._def,unknownKeys:"passthrough"})}extend(e){return new n4({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new n4({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:_.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new n4({...this._def,catchall:e})}pick(e){let t={};for(let r of y.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new n4({...this._def,shape:()=>t})}omit(e){let t={};for(let r of y.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new n4({...this._def,shape:()=>t})}deepPartial(){return function e(t){if(t instanceof n4){let r={};for(let n in t.shape){let i=t.shape[n];r[n]=id.create(e(i))}return new n4({...t._def,shape:()=>r})}if(t instanceof n2)return new n2({...t._def,type:e(t.element)});if(t instanceof id)return id.create(e(t.unwrap()));if(t instanceof ih)return ih.create(e(t.unwrap()));if(t instanceof n8)return n8.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let r of y.objectKeys(this.shape)){let n=this.shape[r];e&&!e[r]?t[r]=n:t[r]=n.optional()}return new n4({...this._def,shape:()=>t})}required(e){let t={};for(let r of y.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof id;)e=e._def.innerType;t[r]=e}return new n4({...this._def,shape:()=>t})}keyof(){return ia(y.objectKeys(this.shape))}}n4.create=(e,t)=>new n4({shape:()=>e,unknownKeys:"strip",catchall:n0.create(),typeName:_.ZodObject,...nk(t)}),n4.strictCreate=(e,t)=>new n4({shape:()=>e,unknownKeys:"strict",catchall:n0.create(),typeName:_.ZodObject,...nk(t)}),n4.lazycreate=(e,t)=>new n4({shape:e,unknownKeys:"strip",catchall:n0.create(),typeName:_.ZodObject,...nk(t)});class n3 extends nR{_parse(e){let{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{let r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;let r=e.map(e=>new nd(e.ctx.common.issues));return np(t,{code:nc.invalid_union,unionErrors:r}),nm});{let e,n=[];for(let i of r){let r={...t,common:{...t.common,issues:[]},parent:null},s=i._parseSync({data:t.data,path:t.path,parent:r});if("valid"===s.status)return s;"dirty"!==s.status||e||(e={result:s,ctx:r}),r.common.issues.length&&n.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let i=n.map(e=>new nd(e));return np(t,{code:nc.invalid_union,unionErrors:i}),nm}}get options(){return this._def.options}}n3.create=(e,t)=>new n3({options:e,typeName:_.ZodUnion,...nk(t)});let n9=e=>{if(e instanceof ii)return n9(e.schema);if(e instanceof ic)return n9(e.innerType());if(e instanceof is)return[e.value];if(e instanceof io)return e.options;if(e instanceof il)return y.objectValues(e.enum);else if(e instanceof ip)return n9(e._def.innerType);else if(e instanceof nZ)return[void 0];else if(e instanceof nJ)return[null];else if(e instanceof id)return[void 0,...n9(e.unwrap())];else if(e instanceof ih)return[null,...n9(e.unwrap())];else if(e instanceof iy)return n9(e.unwrap());else if(e instanceof ib)return n9(e.unwrap());else if(e instanceof ig)return n9(e._def.innerType);else return[]};class n5 extends nR{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==nl.object)return np(t,{code:nc.invalid_type,expected:nl.object,received:t.parsedType}),nm;let r=this.discriminator,n=t.data[r],i=this.optionsMap.get(n);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):(np(t,{code:nc.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),nm)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){let n=new Map;for(let r of t){let t=n9(r.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let i of t){if(n.has(i))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(i)}`);n.set(i,r)}}return new n5({typeName:_.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:n,...nk(r)})}}class n6 extends nR{_parse(e){let{status:t,ctx:r}=this._processInputParams(e),n=(e,n)=>{if(nb(e)||nb(n))return nm;let i=function e(t,r){let n=nu(t),i=nu(r);if(t===r)return{valid:!0,data:t};if(n===nl.object&&i===nl.object){let n=y.objectKeys(r),i=y.objectKeys(t).filter(e=>-1!==n.indexOf(e)),s={...t,...r};for(let n of i){let i=e(t[n],r[n]);if(!i.valid)return{valid:!1};s[n]=i.data}return{valid:!0,data:s}}if(n===nl.array&&i===nl.array){if(t.length!==r.length)return{valid:!1};let n=[];for(let i=0;i<t.length;i++){let s=e(t[i],r[i]);if(!s.valid)return{valid:!1};n.push(s.data)}return{valid:!0,data:n}}if(n===nl.date&&i===nl.date&&+t==+r)return{valid:!0,data:t};return{valid:!1}}(e.value,n.value);return i.valid?((n_(e)||n_(n))&&t.dirty(),{status:t.value,value:i.data}):(np(r,{code:nc.invalid_intersection_types}),nm)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>n(e,t)):n(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}n6.create=(e,t,r)=>new n6({left:e,right:t,typeName:_.ZodIntersection,...nk(r)});class n8 extends nR{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==nl.array)return np(r,{code:nc.invalid_type,expected:nl.array,received:r.parsedType}),nm;if(r.data.length<this._def.items.length)return np(r,{code:nc.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),nm;!this._def.rest&&r.data.length>this._def.items.length&&(np(r,{code:nc.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let n=[...r.data].map((e,t)=>{let n=this._def.items[t]||this._def.rest;return n?n._parse(new nx(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(n).then(e=>ng.mergeArray(t,e)):ng.mergeArray(t,n)}get items(){return this._def.items}rest(e){return new n8({...this._def,rest:e})}}n8.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new n8({items:e,typeName:_.ZodTuple,rest:null,...nk(t)})};class n7 extends nR{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==nl.object)return np(r,{code:nc.invalid_type,expected:nl.object,received:r.parsedType}),nm;let n=[],i=this._def.keyType,s=this._def.valueType;for(let e in r.data)n.push({key:i._parse(new nx(r,e,r.path,e)),value:s._parse(new nx(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?ng.mergeObjectAsync(t,n):ng.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,r){return new n7(t instanceof nR?{keyType:e,valueType:t,typeName:_.ZodRecord,...nk(r)}:{keyType:nH.create(),valueType:e,typeName:_.ZodRecord,...nk(t)})}}class ie extends nR{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==nl.map)return np(r,{code:nc.invalid_type,expected:nl.map,received:r.parsedType}),nm;let n=this._def.keyType,i=this._def.valueType,s=[...r.data.entries()].map(([e,t],s)=>({key:n._parse(new nx(r,e,r.path,[s,"key"])),value:i._parse(new nx(r,t,r.path,[s,"value"]))}));if(r.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let r of s){let n=await r.key,i=await r.value;if("aborted"===n.status||"aborted"===i.status)return nm;("dirty"===n.status||"dirty"===i.status)&&t.dirty(),e.set(n.value,i.value)}return{status:t.value,value:e}})}{let e=new Map;for(let r of s){let n=r.key,i=r.value;if("aborted"===n.status||"aborted"===i.status)return nm;("dirty"===n.status||"dirty"===i.status)&&t.dirty(),e.set(n.value,i.value)}return{status:t.value,value:e}}}}ie.create=(e,t,r)=>new ie({valueType:t,keyType:e,typeName:_.ZodMap,...nk(r)});class it extends nR{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==nl.set)return np(r,{code:nc.invalid_type,expected:nl.set,received:r.parsedType}),nm;let n=this._def;null!==n.minSize&&r.data.size<n.minSize.value&&(np(r,{code:nc.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),null!==n.maxSize&&r.data.size>n.maxSize.value&&(np(r,{code:nc.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());let i=this._def.valueType;function s(e){let r=new Set;for(let n of e){if("aborted"===n.status)return nm;"dirty"===n.status&&t.dirty(),r.add(n.value)}return{status:t.value,value:r}}let a=[...r.data.values()].map((e,t)=>i._parse(new nx(r,e,r.path,t)));return r.common.async?Promise.all(a).then(e=>s(e)):s(a)}min(e,t){return new it({...this._def,minSize:{value:e,message:b.toString(t)}})}max(e,t){return new it({...this._def,maxSize:{value:e,message:b.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}it.create=(e,t)=>new it({valueType:e,minSize:null,maxSize:null,typeName:_.ZodSet,...nk(t)});class ir extends nR{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==nl.function)return np(t,{code:nc.invalid_type,expected:nl.function,received:t.parsedType}),nm;function r(e,r){return nf({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,nh,nh].filter(e=>!!e),issueData:{code:nc.invalid_arguments,argumentsError:r}})}function n(e,r){return nf({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,nh,nh].filter(e=>!!e),issueData:{code:nc.invalid_return_type,returnTypeError:r}})}let i={errorMap:t.common.contextualErrorMap},s=t.data;if(this._def.returns instanceof iu){let e=this;return nv(async function(...t){let a=new nd([]),o=await e._def.args.parseAsync(t,i).catch(e=>{throw a.addIssue(r(t,e)),a}),l=await Reflect.apply(s,this,o);return await e._def.returns._def.type.parseAsync(l,i).catch(e=>{throw a.addIssue(n(l,e)),a})})}{let e=this;return nv(function(...t){let a=e._def.args.safeParse(t,i);if(!a.success)throw new nd([r(t,a.error)]);let o=Reflect.apply(s,this,a.data),l=e._def.returns.safeParse(o,i);if(!l.success)throw new nd([n(o,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new ir({...this._def,args:n8.create(e).rest(nQ.create())})}returns(e){return new ir({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new ir({args:e||n8.create([]).rest(nQ.create()),returns:t||nQ.create(),typeName:_.ZodFunction,...nk(r)})}}class ii extends nR{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}ii.create=(e,t)=>new ii({getter:e,typeName:_.ZodLazy,...nk(t)});class is extends nR{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return np(t,{received:t.data,code:nc.invalid_literal,expected:this._def.value}),nm}return{status:"valid",value:e.data}}get value(){return this._def.value}}function ia(e,t){return new io({values:e,typeName:_.ZodEnum,...nk(t)})}is.create=(e,t)=>new is({value:e,typeName:_.ZodLiteral,...nk(t)});class io extends nR{_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),r=this._def.values;return np(t,{expected:y.joinValues(r),received:t.parsedType,code:nc.invalid_type}),nm}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){let t=this._getOrReturnCtx(e),r=this._def.values;return np(t,{received:t.data,code:nc.invalid_enum_value,options:r}),nm}return nv(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return io.create(e,{...this._def,...t})}exclude(e,t=this._def){return io.create(this.options.filter(t=>!e.includes(t)),{...this._def,...t})}}io.create=ia;class il extends nR{_parse(e){let t=y.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==nl.string&&r.parsedType!==nl.number){let e=y.objectValues(t);return np(r,{expected:y.joinValues(e),received:r.parsedType,code:nc.invalid_type}),nm}if(this._cache||(this._cache=new Set(y.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){let e=y.objectValues(t);return np(r,{received:r.data,code:nc.invalid_enum_value,options:e}),nm}return nv(e.data)}get enum(){return this._def.values}}il.create=(e,t)=>new il({values:e,typeName:_.ZodNativeEnum,...nk(t)});class iu extends nR{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==nl.promise&&!1===t.common.async?(np(t,{code:nc.invalid_type,expected:nl.promise,received:t.parsedType}),nm):nv((t.parsedType===nl.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}iu.create=(e,t)=>new iu({type:e,typeName:_.ZodPromise,...nk(t)});class ic extends nR{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===_.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:r}=this._processInputParams(e),n=this._def.effect||null,i={addIssue:e=>{np(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===n.type){let e=n.transform(r.data,i);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return nm;let n=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===n.status?nm:"dirty"===n.status||"dirty"===t.value?ny(n.value):n});{if("aborted"===t.value)return nm;let n=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===n.status?nm:"dirty"===n.status||"dirty"===t.value?ny(n.value):n}}if("refinement"===n.type){let e=e=>{let t=n.refinement(e,i);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(r=>"aborted"===r.status?nm:("dirty"===r.status&&t.dirty(),e(r.value).then(()=>({status:t.value,value:r.value}))));{let n=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===n.status?nm:("dirty"===n.status&&t.dirty(),e(n.value),{status:t.value,value:n.value})}}if("transform"===n.type)if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>nw(e)?Promise.resolve(n.transform(e.value,i)).then(e=>({status:t.value,value:e})):nm);else{let e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!nw(e))return nm;let s=n.transform(e.value,i);if(s instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}y.assertNever(n)}}ic.create=(e,t,r)=>new ic({schema:e,typeName:_.ZodEffects,effect:t,...nk(r)}),ic.createWithPreprocess=(e,t,r)=>new ic({schema:t,effect:{type:"preprocess",transform:e},typeName:_.ZodEffects,...nk(r)});class id extends nR{_parse(e){return this._getType(e)===nl.undefined?nv(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}id.create=(e,t)=>new id({innerType:e,typeName:_.ZodOptional,...nk(t)});class ih extends nR{_parse(e){return this._getType(e)===nl.null?nv(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ih.create=(e,t)=>new ih({innerType:e,typeName:_.ZodNullable,...nk(t)});class ip extends nR{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return t.parsedType===nl.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}ip.create=(e,t)=>new ip({innerType:e,typeName:_.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...nk(t)});class ig extends nR{_parse(e){let{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return nE(n)?n.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new nd(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===n.status?n.value:this._def.catchValue({get error(){return new nd(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}ig.create=(e,t)=>new ig({innerType:e,typeName:_.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...nk(t)});class im extends nR{_parse(e){if(this._getType(e)!==nl.nan){let t=this._getOrReturnCtx(e);return np(t,{code:nc.invalid_type,expected:nl.nan,received:t.parsedType}),nm}return{status:"valid",value:e.data}}}im.create=e=>new im({typeName:_.ZodNaN,...nk(e)}),Symbol("zod_brand");class iy extends nR{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class iv extends nR{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?nm:"dirty"===e.status?(t.dirty(),ny(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{let e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?nm:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new iv({in:e,out:t,typeName:_.ZodPipeline})}}class ib extends nR{_parse(e){let t=this._def.innerType._parse(e),r=e=>(nw(e)&&(e.value=Object.freeze(e.value)),e);return nE(t)?t.then(e=>r(e)):r(t)}unwrap(){return this._def.innerType}}ib.create=(e,t)=>new ib({innerType:e,typeName:_.ZodReadonly,...nk(t)}),n4.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(_||(_={}));let i_=nH.create,iw=nV.create;im.create,nG.create;let iE=nK.create;nW.create,nX.create,nZ.create,nJ.create;let ix=nY.create;nQ.create,n0.create,n1.create;let iS=n2.create,ik=n4.create;n4.strictCreate;let iR=n3.create;n5.create,n6.create,n8.create,n7.create,ie.create,it.create,ir.create,ii.create,is.create;let iC=io.create;il.create,iu.create,ic.create,id.create,ih.create,ic.createWithPreprocess,iv.create;let iO="edge",iT=ik({model:i_().optional().default("gpt-3.5-turbo"),messages:iS(ik({role:iC(["user","assistant","system"]),content:iR([i_(),iS(ix())])})).min(1,{message:"Messages array cannot be empty."}),stream:iE().optional().default(!1),temperature:iw().min(0).max(2).optional(),max_tokens:iw().int().positive().optional(),top_p:iw().min(0).max(1).optional(),frequency_penalty:iw().min(-2).max(2).optional(),presence_penalty:iw().min(-2).max(2).optional(),stop:iR([i_(),iS(i_())]).optional(),n:iw().int().positive().optional().default(1),role:i_().optional()}).catchall(ix()),iP=new no;async function iA(e){try{let t,r=await iP.authenticateRequest(e);if(!r.success)return X.json({error:{message:r.error,type:"authentication_error",code:"invalid_api_key"}},{status:r.statusCode||401});let{userApiKey:n,userConfig:i,ipAddress:s}=r;if(!iP.hasPermission(n,"chat"))return X.json({error:{message:"API key does not have chat permission",type:"permission_error",code:"insufficient_permissions"}},{status:403});let a=await e.json(),o=iT.safeParse(a);if(!o.success)return X.json({error:{message:"Invalid request body",type:"invalid_request_error",code:"invalid_request",details:o.error.flatten().fieldErrors}},{status:400});let l=o.data;if(l.stream&&!iP.hasPermission(n,"streaming"))return X.json({error:{message:"API key does not have streaming permission",type:"permission_error",code:"streaming_not_allowed"}},{status:403});let u=!1;if(l.role||l.messages?.some(e=>e.content&&"string"==typeof e.content&&e.content.length>100))try{let e=await fetch("https://roukey.online/api/internal/classify-multi-role",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`},body:JSON.stringify({messages:l.messages,role:l.role,config_id:i.id})});e.ok&&(u=(await e.json()).isMultiRole)}catch(e){}let c=null,d=!1;u&&!l.stream&&(l.stream=!0,d=!0,c={type:"multi_role_detected",reason:"Gemini classifier detected this task requires multiple specialized roles working together",async_submit_url:"https://roukey.online/api/external/v1/async/submit",estimated_time_minutes:5,streaming_forced:!0,streaming_reason:"Automatically enabled streaming to prevent timeouts for multi-role orchestration",benefits:["No timeout limits for complex multi-role orchestration","Real-time progress tracking with role detection","Better responsiveness and user experience","Proper handling of role coordination"]});let h={custom_api_config_id:i.id,messages:l.messages,stream:l.stream,temperature:l.temperature,max_tokens:l.max_tokens,role:l.role,model:l.model,top_p:l.top_p,frequency_penalty:l.frequency_penalty,presence_penalty:l.presence_penalty,stop:l.stop,n:l.n,_internal_user_id:i.user_id},f=new URL("/api/v1/chat/completions",e.url),p=new AbortController,g=setTimeout(()=>p.abort(),55e3);try{t=await fetch(f.toString(),{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,"X-Forwarded-For":s||"","X-User-API-Key-ID":n.id,"X-External-Request":"true"},body:JSON.stringify(h),signal:p.signal})}catch(e){if(clearTimeout(g),"AbortError"===e.name)return X.json({error:{message:"Request timeout. For complex multi-role tasks, consider using async processing or breaking down the request.",type:"timeout_error",code:"request_timeout",suggestion:"Try reducing complexity or use streaming for better responsiveness"}},{status:408});throw e}clearTimeout(g);let m=[],y=l.model;if(!l.stream&&t.ok)try{let e=t.clone(),r=await e.json();r.rokey_metadata?.roles_used&&(m=r.rokey_metadata.roles_used),r.model&&(y=r.model)}catch(e){}if(iP.logApiUsage(n,e,{statusCode:t.status,modelUsed:y,providerUsed:m.length>0?m.join(", "):void 0},s).catch(e=>{}),l.stream){let e={"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","X-RouKey-Config":i.name};return c&&(e["X-RouKey-Async-Recommendation"]=c.type,e["X-RouKey-Multi-Role-Detected"]="true",d&&(e["X-RouKey-Stream-Forced"]="true",e["X-RouKey-Stream-Reason"]="Multi-role task requires streaming to prevent timeouts")),new Response(t.body,{status:t.status,headers:e})}{let e=await t.json();if(!t.ok)return X.json({error:{message:e.error||"Internal server error",type:"server_error",code:"internal_error"}},{status:t.status});{let r={...e,rokey_metadata:{roles_used:e.rokey_metadata?.roles_used||[],routing_strategy:i.routing_strategy,config_name:i.name,api_key_name:n.key_name,...c&&{async_recommendation:c},...e.rokey_metadata||{}}},s={"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","X-RouKey-Roles-Used":m.join(", ")||"none","X-RouKey-Config":i.name};return c&&(s["X-RouKey-Async-Recommendation"]=c.type,s["X-RouKey-Streaming-Suggestion"]="true"),X.json(r,{status:t.status,headers:s})}}}catch(e){return X.json({error:{message:"Internal server error",type:"server_error",code:"internal_error"}},{status:500})}}async function ij(){return new Response(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization, X-API-Key","Access-Control-Max-Age":"86400"}})}let iI=new eX.AppRouteRouteModule({definition:{kind:eZ.A.APP_ROUTE,page:"/api/external/v1/chat/completions/route",pathname:"/api/external/v1/chat/completions",filename:"route",bundlePath:"app/api/external/v1/chat/completions/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\external\\v1\\chat\\completions\\route.ts",nextConfigOutput:"",userland:E}),{workAsyncStorage:iN,workUnitAsyncStorage:i$,serverHooks:iD}=iI;function iM(){return(0,eJ.V5)({workAsyncStorage:iN,workUnitAsyncStorage:i$})}let iL=null==(w=self.__RSC_MANIFEST)?void 0:w["/api/external/v1/chat/completions/route"],iU=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);iL&&iU&&function({page:e,clientReferenceManifest:t,serverActionsManifest:r,serverModuleMap:n}){var i;let s=null==(i=globalThis[O])?void 0:i.clientReferenceManifestsPerPage;globalThis[O]={clientReferenceManifestsPerPage:{...s,[(0,C.Y)(e)]:t},serverActionsManifest:r,serverModuleMap:n}}({page:"/api/external/v1/chat/completions/route",clientReferenceManifest:iL,serverActionsManifest:iU,serverModuleMap:function({serverActionsManifest:e}){return new Proxy({},{get:(t,r)=>{var n,i,s;let a,o=null==(i=e.edge)||null==(n=i[r])?void 0:n.workers;if(!o)return;let l=k.J.getStore();if(!(a=l?o[s=l.page,(0,S.m)(s,"app")?s:"app"+s]:Object.values(o).at(0)))return;let{moduleId:u,async:c}=a;return{id:u,name:r,chunks:[],async:c}}})}({serverActionsManifest:iU})});let iq=x,iB=eW.wrap(iI,{nextConfig:{env:{},eslint:{ignoreDuringBuilds:!0},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.mjs",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp","image/avif"],dangerouslyAllowSVG:!0,contentSecurityPolicy:"default-src 'self'; script-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[{protocol:"https",hostname:"raw.githubusercontent.com",port:"",pathname:"/lobehub/lobe-icons/**"},{protocol:"https",hostname:"registry.npmmirror.com",port:"",pathname:"/@lobehub/icons-static-png/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@latest/icons/**"},{protocol:"https",hostname:"cdn.jsdelivr.net",port:"",pathname:"/npm/simple-icons@v11/icons/**"},{protocol:"https",hostname:"images.unsplash.com",port:"",pathname:"/**"},{protocol:"https",hostname:"cloud.gmelius.com",port:"",pathname:"/public/logos/**"},{protocol:"https",hostname:"upload.wikimedia.org",port:"",pathname:"/wikipedia/commons/**"},{protocol:"https",hostname:"kstatic.googleusercontent.com",port:"",pathname:"/files/**"}],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\RoKey App\\rokey-app",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:3,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!0,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!0,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["@heroicons/react","@headlessui/react","react-markdown","react-syntax-highlighter","@supabase/supabase-js","lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\RoKey App\\rokey-app\\next.config.mjs",configFileName:"next.config.mjs",serverExternalPackages:["pdf-parse","mammoth"],turbopack:{rules:{"*.svg":{loaders:["@svgr/webpack"],as:"*.js"}},root:"C:\\RoKey App\\rokey-app"},compiler:{removeConsole:!0,reactRemoveProperties:!0},_originalRedirects:[]}})},4705:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let n=r(808);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${n.version}`}},4818:e=>{"use strict";e.exports=function(){throw Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},4819:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},s=t.split(n),a=(r||{}).decode||e,o=0;o<s.length;o++){var l=s[o],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return i},t.serialize=function(e,t,n){var s=n||{},a=s.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var u=s.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(s.domain){if(!i.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!i.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},5293:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),i=r(172),s=r(930),a="context",o=new n.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,i.registerGlobal)(a,e,s.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,i.getGlobal)(a)||o}disable(){this._getContextManager().disable(),(0,i.unregisterGlobal)(a,s.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),i=r(912),s=r(957),a=r(172);class o{constructor(){function e(e){return function(...t){let r=(0,a.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:s.DiagLogLevel.INFO})=>{var n,o,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(n=e.stack)?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let u=(0,a.getGlobal)("diag"),c=(0,i.createLogLevelDiagLogger)(null!=(o=r.logLevel)?o:s.DiagLogLevel.INFO,e);if(u&&!r.suppressOverrideMessage){let e=null!=(l=Error().stack)?l:"<failed to generate stacktrace>";u.warn(`Current logger will be overwritten from ${e}`),c.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,a.registerGlobal)("diag",c,t,!0)},t.disable=()=>{(0,a.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new o),this._instance}}t.DiagAPI=o},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),i=r(172),s=r(930),a="metrics";class o{constructor(){}static getInstance(){return this._instance||(this._instance=new o),this._instance}setGlobalMeterProvider(e){return(0,i.registerGlobal)(a,e,s.DiagAPI.instance())}getMeterProvider(){return(0,i.getGlobal)(a)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,i.unregisterGlobal)(a,s.DiagAPI.instance())}}t.MetricsAPI=o},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),i=r(874),s=r(194),a=r(277),o=r(369),l=r(930),u="propagation",c=new i.NoopTextMapPropagator;class d{constructor(){this.createBaggage=o.createBaggage,this.getBaggage=a.getBaggage,this.getActiveBaggage=a.getActiveBaggage,this.setBaggage=a.setBaggage,this.deleteBaggage=a.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(u,e,l.DiagAPI.instance())}inject(e,t,r=s.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=s.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(u,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(u)||c}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),i=r(846),s=r(139),a=r(607),o=r(930),l="trace";class u{constructor(){this._proxyTracerProvider=new i.ProxyTracerProvider,this.wrapSpanContext=s.wrapSpanContext,this.isSpanContextValid=s.isSpanContextValid,this.deleteSpan=a.deleteSpan,this.getSpan=a.getSpan,this.getActiveSpan=a.getActiveSpan,this.getSpanContext=a.getSpanContext,this.setSpan=a.setSpan,this.setSpanContext=a.setSpanContext}static getInstance(){return this._instance||(this._instance=new u),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(l,this._proxyTracerProvider,o.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(l,o.DiagAPI.instance()),this._proxyTracerProvider=new i.ProxyTracerProvider}}t.TraceAPI=u},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),i=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function s(e){return e.getValue(i)||void 0}t.getBaggage=s,t.getActiveBaggage=function(){return s(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(i,t)},t.deleteBaggage=function(e){return e.deleteValue(i)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),i=r(993),s=r(830),a=n.DiagAPI.instance();t.createBaggage=function(e={}){return new i.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(a.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:s.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class i{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=i},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let i=new r(t._currentContext);return i._currentContext.set(e,n),i},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class i{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return s("debug",this._namespace,e)}error(...e){return s("error",this._namespace,e)}info(...e){return s("info",this._namespace,e)}warn(...e){return s("warn",this._namespace,e)}verbose(...e){return s("verbose",this._namespace,e)}}function s(e,t,r){let i=(0,n.getGlobal)("diag");if(i)return r.unshift(t),i[e](...r)}t.DiagComponentLogger=i},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),i=r(521),s=r(130),a=i.VERSION.split(".")[0],o=Symbol.for(`opentelemetry.js.api.${a}`),l=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var s;let a=l[o]=null!=(s=l[o])?s:{version:i.VERSION};if(!n&&a[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(a.version!==i.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${a.version} for ${e} does not match previously registered API v${i.VERSION}`);return r.error(t.stack||t.message),!1}return a[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${i.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null==(t=l[o])?void 0:t.version;if(n&&(0,s.isCompatible)(n))return null==(r=l[o])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${i.VERSION}.`);let r=l[o];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),i=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function s(e){let t=new Set([e]),r=new Set,n=e.match(i);if(!n)return()=>!1;let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=s.prerelease)return function(t){return t===e};function a(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(i);if(!n)return a(e);let o={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=o.prerelease||s.major!==o.major)return a(e);if(0===s.major)return s.minor===o.minor&&s.patch<=o.patch?(t.add(e),!0):a(e);return s.minor<=o.minor?(t.add(e),!0):a(e)}}t._makeCompatibilityCheck=s,t.isCompatible=s(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class i extends n{add(e,t){}}t.NoopCounterMetric=i;class s extends n{add(e,t){}}t.NoopUpDownCounterMetric=s;class a extends n{record(e,t){}}t.NoopHistogramMetric=a;class o{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=o;class l extends o{}t.NoopObservableCounterMetric=l;class u extends o{}t.NoopObservableGaugeMetric=u;class c extends o{}t.NoopObservableUpDownCounterMetric=c,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new i,t.NOOP_HISTOGRAM_METRIC=new a,t.NOOP_UP_DOWN_COUNTER_METRIC=new s,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new u,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new c,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class i{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=i,t.NOOP_METER_PROVIDER=new i},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class i{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=i},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),i=r(607),s=r(403),a=r(139),o=n.ContextAPI.getInstance();class l{startSpan(e,t,r=o.active()){var n;if(null==t?void 0:t.root)return new s.NonRecordingSpan;let l=r&&(0,i.getSpanContext)(r);return"object"==typeof(n=l)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,a.isSpanContextValid)(l)?new s.NonRecordingSpan(l):new s.NonRecordingSpan}startActiveSpan(e,t,r,n){let s,a,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(s=t,l=r):(s=t,a=r,l=n);let u=null!=a?a:o.active(),c=this.startSpan(e,s,u),d=(0,i.setSpan)(u,c);return o.with(d,l,void 0,c)}}t.NoopTracer=l},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class i{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=i},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class i{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=i},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),i=new(r(124)).NoopTracerProvider;class s{getTracer(e,t,r){var i;return null!=(i=this.getDelegateTracer(e,t,r))?i:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:i}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=s},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),i=r(403),s=r(491),a=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function o(e){return e.getValue(a)||void 0}function l(e,t){return e.setValue(a,t)}t.getSpan=o,t.getActiveSpan=function(){return o(s.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(a)},t.setSpanContext=function(e,t){return l(e,new i.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=o(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class i{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),i=r.indexOf("=");if(-1!==i){let s=r.slice(0,i),a=r.slice(i+1,t.length);(0,n.validateKey)(s)&&(0,n.validateValue)(a)&&e.set(s,a)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new i;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=i},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,i=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,s=RegExp(`^(?:${n}|${i})$`),a=/^[ -~]{0,255}[!-~]$/,o=/,|=/;t.validateKey=function(e){return s.test(e)},t.validateValue=function(e){return a.test(e)&&!o.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),i=r(403),s=/^([0-9a-f]{32})$/i,a=/^[0-9a-f]{16}$/i;function o(e){return s.test(e)&&e!==n.INVALID_TRACEID}function l(e){return a.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=o,t.isValidSpanId=l,t.isSpanContextValid=function(e){return o(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new i.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},n={};function i(e){var r=n[e];if(void 0!==r)return r.exports;var s=n[e]={exports:{}},a=!0;try{t[e].call(s.exports,s,s.exports,i),a=!1}finally{a&&delete n[e]}return s.exports}i.ab="//";var s={};(()=>{Object.defineProperty(s,"__esModule",{value:!0}),s.trace=s.propagation=s.metrics=s.diag=s.context=s.INVALID_SPAN_CONTEXT=s.INVALID_TRACEID=s.INVALID_SPANID=s.isValidSpanId=s.isValidTraceId=s.isSpanContextValid=s.createTraceState=s.TraceFlags=s.SpanStatusCode=s.SpanKind=s.SamplingDecision=s.ProxyTracerProvider=s.ProxyTracer=s.defaultTextMapSetter=s.defaultTextMapGetter=s.ValueType=s.createNoopMeter=s.DiagLogLevel=s.DiagConsoleLogger=s.ROOT_CONTEXT=s.createContextKey=s.baggageEntryMetadataFromString=void 0;var e=i(369);Object.defineProperty(s,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=i(780);Object.defineProperty(s,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(s,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=i(972);Object.defineProperty(s,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var n=i(957);Object.defineProperty(s,"DiagLogLevel",{enumerable:!0,get:function(){return n.DiagLogLevel}});var a=i(102);Object.defineProperty(s,"createNoopMeter",{enumerable:!0,get:function(){return a.createNoopMeter}});var o=i(901);Object.defineProperty(s,"ValueType",{enumerable:!0,get:function(){return o.ValueType}});var l=i(194);Object.defineProperty(s,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(s,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var u=i(125);Object.defineProperty(s,"ProxyTracer",{enumerable:!0,get:function(){return u.ProxyTracer}});var c=i(846);Object.defineProperty(s,"ProxyTracerProvider",{enumerable:!0,get:function(){return c.ProxyTracerProvider}});var d=i(996);Object.defineProperty(s,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var h=i(357);Object.defineProperty(s,"SpanKind",{enumerable:!0,get:function(){return h.SpanKind}});var f=i(847);Object.defineProperty(s,"SpanStatusCode",{enumerable:!0,get:function(){return f.SpanStatusCode}});var p=i(475);Object.defineProperty(s,"TraceFlags",{enumerable:!0,get:function(){return p.TraceFlags}});var g=i(98);Object.defineProperty(s,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var m=i(139);Object.defineProperty(s,"isSpanContextValid",{enumerable:!0,get:function(){return m.isSpanContextValid}}),Object.defineProperty(s,"isValidTraceId",{enumerable:!0,get:function(){return m.isValidTraceId}}),Object.defineProperty(s,"isValidSpanId",{enumerable:!0,get:function(){return m.isValidSpanId}});var y=i(476);Object.defineProperty(s,"INVALID_SPANID",{enumerable:!0,get:function(){return y.INVALID_SPANID}}),Object.defineProperty(s,"INVALID_TRACEID",{enumerable:!0,get:function(){return y.INVALID_TRACEID}}),Object.defineProperty(s,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return y.INVALID_SPAN_CONTEXT}});let v=i(67);Object.defineProperty(s,"context",{enumerable:!0,get:function(){return v.context}});let b=i(506);Object.defineProperty(s,"diag",{enumerable:!0,get:function(){return b.diag}});let _=i(886);Object.defineProperty(s,"metrics",{enumerable:!0,get:function(){return _.metrics}});let w=i(939);Object.defineProperty(s,"propagation",{enumerable:!0,get:function(){return w.propagation}});let E=i(845);Object.defineProperty(s,"trace",{enumerable:!0,get:function(){return E.trace}}),s.default={context:v.context,diag:b.diag,metrics:_.metrics,propagation:w.propagation,trace:E.trace}})(),e.exports=s})()},5325:(e,t,r)=>{"use strict";r.d(t,{q9:()=>h});var n=r(6129),i=r(6464),s=r(6237),a=r(4318),o=r(3543);r(897),r(5455);let l="__prerender_bypass";Symbol("__next_preview_data"),Symbol(l);class u{constructor(e,t,r,n){var s;let a=e&&function(e,t){let r=i.o.from(e.headers);return{isOnDemandRevalidate:r.get(o.kz)===t.previewModeId,revalidateOnlyGenerated:r.has(o.r4)}}(t,e).isOnDemandRevalidate,u=null==(s=r.get(l))?void 0:s.value;this._isEnabled=!!(!a&&u&&e&&u===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:l,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:l,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}var c=r(3936);function d(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of(0,c.RD)(r))n.append("set-cookie",e);for(let e of new a.VO(n).getAll())t.set(e)}}function h(e,t,r,o,l){return function(e,t,r,o,l,c,h,f,p,g,m){function y(e){r&&r.setHeader("Set-Cookie",e)}let v={};return{type:"request",phase:e,implicitTags:c,url:{pathname:o.pathname,search:o.search??""},rootParams:l,get headers(){return v.headers||(v.headers=function(e){let t=i.o.from(e);for(let e of n.KD)t.delete(e.toLowerCase());return i.o.seal(t)}(t.headers)),v.headers},get cookies(){if(!v.cookies){let e=new a.tm(i.o.from(t.headers));d(t,e),v.cookies=s.Ck.seal(e)}return v.cookies},set cookies(value){v.cookies=value},get mutableCookies(){if(!v.mutableCookies){let e=function(e,t){let r=new a.tm(i.o.from(e));return s.K8.wrap(r,t)}(t.headers,h||(r?y:void 0));d(t,e),v.mutableCookies=e}return v.mutableCookies},get userspaceMutableCookies(){return v.userspaceMutableCookies||(v.userspaceMutableCookies=(0,s.hm)(this.mutableCookies)),v.userspaceMutableCookies},get draftMode(){return v.draftMode||(v.draftMode=new u(p,t,this.cookies,this.mutableCookies)),v.draftMode},renderResumeDataCache:f??null,isHmrRefresh:g,serverComponentsHmrCache:m||globalThis.__serverComponentsHmrCache}}("action",e,void 0,t,{},r,o,void 0,l,!1,void 0)}},5356:e=>{"use strict";e.exports=require("node:buffer")},5375:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,s={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function l(e){if(!e)return;let[[t,r],...n]=o(e),{domain:i,expires:s,httponly:a,maxage:l,path:d,samesite:h,secure:f,partitioned:p,priority:g}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var m,y,v={name:t,value:decodeURIComponent(r),domain:i,...s&&{expires:new Date(s)},...a&&{httpOnly:!0},..."string"==typeof l&&{maxAge:Number(l)},path:d,...h&&{sameSite:u.includes(m=(m=h).toLowerCase())?m:void 0},...f&&{secure:!0},...g&&{priority:c.includes(y=(y=g).toLowerCase())?y:void 0},...p&&{partitioned:!0}};let e={};for(let t in v)v[t]&&(e[t]=v[t]);return e}}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(s,{RequestCookies:()=>d,ResponseCookies:()=>h,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,s,a,o)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let l of n(s))i.call(e,l)||l===a||t(e,l,{get:()=>s[l],enumerable:!(o=r(s,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),s);var u=["strict","lax","none"],c=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},h=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=i,a.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(i)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},5421:(e,t,r)=>{"use strict";r.d(t,{V5:()=>m});var n=r(5455),i=r(897),s=r(3543),a=r(6937),o=r(252),l=r(1092);function u(e){if(!e.body)return[e,e];let[t,r]=e.body.tee(),n=new Response(t,{status:e.status,statusText:e.statusText,headers:e.headers});Object.defineProperty(n,"url",{value:e.url});let i=new Response(r,{status:e.status,statusText:e.statusText,headers:e.headers});return Object.defineProperty(i,"url",{value:e.url}),[n,i]}var c=r(7753),d=r(1496),h=r(4195),f=r(5356).Buffer;let p=Symbol.for("next-patch");function g(e,t){var r;if(e&&(null==(r=e.requestEndedState)?!void 0:!r.ended))((process.env.NEXT_DEBUG_BUILD||"1"===process.env.NEXT_SSG_FETCH_METRICS)&&e.isStaticGeneration||0)&&(e.fetchMetrics??=[],e.fetchMetrics.push({...t,end:performance.timeOrigin+performance.now(),idx:e.nextFetchId||0}))}function m(e){if(!0===globalThis[p])return;let t=function(e){let t=l.cache(e=>[]);return function(r,n){let i,s;if(n&&n.signal)return e(r,n);if("string"!=typeof r||n){let t="string"==typeof r||r instanceof URL?new Request(r,n):r;if("GET"!==t.method&&"HEAD"!==t.method||t.keepalive)return e(r,n);s=JSON.stringify([t.method,Array.from(t.headers.entries()),t.mode,t.redirect,t.credentials,t.referrer,t.referrerPolicy,t.integrity]),i=t.url}else s='["GET",[],null,"follow",null,null,null,null]',i=r;let a=t(i);for(let e=0,t=a.length;e<t;e+=1){let[t,r]=a[e];if(t===s)return r.then(()=>{let t=a[e][2];if(!t)throw Object.defineProperty(new c.z("No cached response"),"__NEXT_ERROR_CODE",{value:"E579",enumerable:!1,configurable:!0});let[r,n]=u(t);return a[e][2]=n,r})}let o=e(r,n),l=[s,o,null];return a.push(l),o.then(e=>{let[t,r]=u(e);return l[2]=r,t})}}(globalThis.fetch);globalThis.fetch=function(e,{workAsyncStorage:t,workUnitAsyncStorage:r}){let l=async(l,c)=>{var p,m;let y;try{(y=new URL(l instanceof Request?l.url:l)).username="",y.password=""}catch{y=void 0}let v=(null==y?void 0:y.href)??"",b=(null==c||null==(p=c.method)?void 0:p.toUpperCase())||"GET",_=(null==c||null==(m=c.next)?void 0:m.internal)===!0,w="1"===process.env.NEXT_OTEL_FETCH_DISABLED,E=_?void 0:performance.timeOrigin+performance.now(),x=t.getStore(),S=r.getStore(),k=S&&"prerender"===S.type?S.cacheSignal:null;k&&k.beginRead();let R=(0,i.EK)().trace(_?n.Fx.internalFetch:n.Wc.fetch,{hideSpan:w,kind:i.v8.CLIENT,spanName:["fetch",b,v].filter(Boolean).join(" "),attributes:{"http.url":v,"http.method":b,"net.peer.name":null==y?void 0:y.hostname,"net.peer.port":(null==y?void 0:y.port)||void 0}},async()=>{var t;let r,n,i,p;if(_||!x||x.isDraftMode)return e(l,c);let m=l&&"object"==typeof l&&"string"==typeof l.method,y=e=>(null==c?void 0:c[e])||(m?l[e]:null),b=e=>{var t,r,n;return void 0!==(null==c||null==(t=c.next)?void 0:t[e])?null==c||null==(r=c.next)?void 0:r[e]:m?null==(n=l.next)?void 0:n[e]:void 0},w=b("revalidate"),R=function(e,t){let r=[],n=[];for(let i=0;i<e.length;i++){let a=e[i];if("string"!=typeof a?n.push({tag:a,reason:"invalid type, must be a string"}):a.length>s.qq?n.push({tag:a,reason:`exceeded max length of ${s.qq}`}):r.push(a),r.length>s.o7){console.warn(`Warning: exceeded max tag count for ${t}, dropped tags:`,e.slice(i).join(", "));break}}if(n.length>0)for(let{tag:e,reason:r}of(console.warn(`Warning: invalid tags passed to ${t}: `),n))console.log(`tag: "${e}" ${r}`);return r}(b("tags")||[],`fetch ${l.toString()}`),C=S&&("cache"===S.type||"prerender"===S.type||"prerender-ppr"===S.type||"prerender-legacy"===S.type)?S:void 0;if(C&&Array.isArray(R)){let e=C.tags??(C.tags=[]);for(let t of R)e.includes(t)||e.push(t)}let O=null==S?void 0:S.implicitTags,T=S&&"unstable-cache"===S.type?"force-no-store":x.fetchCache,P=!!x.isUnstableNoStore,A=y("cache"),j="";"string"==typeof A&&void 0!==w&&("force-cache"===A&&0===w||"no-store"===A&&(w>0||!1===w))&&(r=`Specified "cache: ${A}" and "revalidate: ${w}", only one should be specified.`,A=void 0,w=void 0);let I="no-cache"===A||"no-store"===A||"force-no-store"===T||"only-no-store"===T,N=!T&&!A&&!w&&x.forceDynamic;"force-cache"===A&&void 0===w?w=!1:(null==S?void 0:S.type)!=="cache"&&(I||N)&&(w=0),("no-cache"===A||"no-store"===A)&&(j=`cache: ${A}`),p=function(e,t){try{let r;if(!1===e)r=s.AR;else if("number"==typeof e&&!isNaN(e)&&e>-1)r=e;else if(void 0!==e)throw Object.defineProperty(Error(`Invalid revalidate value "${e}" on "${t}", must be a non-negative number or false`),"__NEXT_ERROR_CODE",{value:"E179",enumerable:!1,configurable:!0});return r}catch(e){if(e instanceof Error&&e.message.includes("Invalid revalidate"))throw e;return}}(w,x.route);let $=y("headers"),D="function"==typeof(null==$?void 0:$.get)?$:new Headers($||{}),M=D.get("authorization")||D.get("cookie"),L=!["get","head"].includes((null==(t=y("method"))?void 0:t.toLowerCase())||"get"),U=void 0==T&&(void 0==A||"default"===A)&&void 0==w,q=U&&!x.isPrerendering||(M||L)&&C&&0===C.revalidate;if(U&&void 0!==S&&"prerender"===S.type)return k&&(k.endRead(),k=null),(0,o.W)(S.renderSignal,"fetch()");switch(T){case"force-no-store":j="fetchCache = force-no-store";break;case"only-no-store":if("force-cache"===A||void 0!==p&&p>0)throw Object.defineProperty(Error(`cache: 'force-cache' used on fetch for ${v} with 'export const fetchCache = 'only-no-store'`),"__NEXT_ERROR_CODE",{value:"E448",enumerable:!1,configurable:!0});j="fetchCache = only-no-store";break;case"only-cache":if("no-store"===A)throw Object.defineProperty(Error(`cache: 'no-store' used on fetch for ${v} with 'export const fetchCache = 'only-cache'`),"__NEXT_ERROR_CODE",{value:"E521",enumerable:!1,configurable:!0});break;case"force-cache":(void 0===w||0===w)&&(j="fetchCache = force-cache",p=s.AR)}if(void 0===p?"default-cache"!==T||P?"default-no-store"===T?(p=0,j="fetchCache = default-no-store"):P?(p=0,j="noStore call"):q?(p=0,j="auto no cache"):(j="auto cache",p=C?C.revalidate:s.AR):(p=s.AR,j="fetchCache = default-cache"):j||(j=`revalidate: ${p}`),!(x.forceStatic&&0===p)&&!q&&C&&p<C.revalidate){if(0===p)if(S&&"prerender"===S.type)return k&&(k.endRead(),k=null),(0,o.W)(S.renderSignal,"fetch()");else(0,a.ag)(x,S,`revalidate: 0 fetch ${l} ${x.route}`);C&&w===p&&(C.revalidate=p)}let B="number"==typeof p&&p>0,{incrementalCache:F}=x,z=(null==S?void 0:S.type)==="request"||(null==S?void 0:S.type)==="cache"?S:void 0;if(F&&(B||(null==z?void 0:z.serverComponentsHmrCache)))try{n=await F.generateCacheKey(v,m?l:c)}catch(e){console.error("Failed to generate cache key for",l)}let H=x.nextFetchId??1;x.nextFetchId=H+1;let V=()=>Promise.resolve(),G=async(t,i)=>{let a=["cache","credentials","headers","integrity","keepalive","method","mode","redirect","referrer","referrerPolicy","window","duplex",...t?[]:["signal"]];if(m){let e=l,t={body:e._ogBody||e.body};for(let r of a)t[r]=e[r];l=new Request(e.url,t)}else if(c){let{_ogBody:e,body:r,signal:n,...i}=c;c={...i,body:e||r,signal:t?void 0:n}}let o={...c,next:{...null==c?void 0:c.next,fetchType:"origin",fetchIdx:H}};return e(l,o).then(async e=>{if(!t&&E&&g(x,{start:E,url:v,cacheReason:i||j,cacheStatus:0===p||i?"skip":"miss",cacheWarning:r,status:e.status,method:o.method||"GET"}),200===e.status&&F&&n&&(B||(null==z?void 0:z.serverComponentsHmrCache))){let t=p>=s.AR?s.qF:p;if(S&&"prerender"===S.type){let r=await e.arrayBuffer(),i={headers:Object.fromEntries(e.headers.entries()),body:f.from(r).toString("base64"),status:e.status,url:e.url};return await F.set(n,{kind:d.yD.FETCH,data:i,revalidate:t},{fetchCache:!0,fetchUrl:v,fetchIdx:H,tags:R}),await V(),new Response(r,{headers:e.headers,status:e.status,statusText:e.statusText})}{let[r,i]=u(e);return r.arrayBuffer().then(async e=>{var i;let s=f.from(e),a={headers:Object.fromEntries(r.headers.entries()),body:s.toString("base64"),status:r.status,url:r.url};null==z||null==(i=z.serverComponentsHmrCache)||i.set(n,a),B&&await F.set(n,{kind:d.yD.FETCH,data:a,revalidate:t},{fetchCache:!0,fetchUrl:v,fetchIdx:H,tags:R})}).catch(e=>console.warn("Failed to set fetch cache",l,e)).finally(V),i}}return await V(),e}).catch(e=>{throw V(),e})},K=!1,W=!1;if(n&&F){let e;if((null==z?void 0:z.isHmrRefresh)&&z.serverComponentsHmrCache&&(e=z.serverComponentsHmrCache.get(n),W=!0),B&&!e){V=await F.lock(n);let t=x.isOnDemandRevalidate?null:await F.get(n,{kind:d.Bs.FETCH,revalidate:p,fetchUrl:v,fetchIdx:H,tags:R,softTags:null==O?void 0:O.tags});if(U&&S&&"prerender"===S.type&&await (0,h.kf)(),t?await V():i="cache-control: no-cache (hard refresh)",(null==t?void 0:t.value)&&t.value.kind===d.yD.FETCH)if(x.isRevalidate&&t.isStale)K=!0;else{if(t.isStale&&(x.pendingRevalidates??={},!x.pendingRevalidates[n])){let e=G(!0).then(async e=>({body:await e.arrayBuffer(),headers:e.headers,status:e.status,statusText:e.statusText})).finally(()=>{x.pendingRevalidates??={},delete x.pendingRevalidates[n||""]});e.catch(console.error),x.pendingRevalidates[n]=e}e=t.value.data}}if(e){E&&g(x,{start:E,url:v,cacheReason:j,cacheStatus:W?"hmr":"hit",cacheWarning:r,status:e.status||200,method:(null==c?void 0:c.method)||"GET"});let t=new Response(f.from(e.body,"base64"),{headers:e.headers,status:e.status});return Object.defineProperty(t,"url",{value:e.url}),t}}if(x.isStaticGeneration&&c&&"object"==typeof c){let{cache:e}=c;if(delete c.cache,"no-store"===e)if(S&&"prerender"===S.type)return k&&(k.endRead(),k=null),(0,o.W)(S.renderSignal,"fetch()");else(0,a.ag)(x,S,`no-store fetch ${l} ${x.route}`);let t="next"in c,{next:r={}}=c;if("number"==typeof r.revalidate&&C&&r.revalidate<C.revalidate){if(0===r.revalidate)if(S&&"prerender"===S.type)return(0,o.W)(S.renderSignal,"fetch()");else(0,a.ag)(x,S,`revalidate: 0 fetch ${l} ${x.route}`);x.forceStatic&&0===r.revalidate||(C.revalidate=r.revalidate)}t&&delete c.next}if(!n||!K)return G(!1,i);{let e=n;x.pendingRevalidates??={};let t=x.pendingRevalidates[e];if(t){let e=await t;return new Response(e.body,{headers:e.headers,status:e.status,statusText:e.statusText})}let r=G(!0,i).then(u);return(t=r.then(async e=>{let t=e[0];return{body:await t.arrayBuffer(),headers:t.headers,status:t.status,statusText:t.statusText}}).finally(()=>{var t;(null==(t=x.pendingRevalidates)?void 0:t[e])&&delete x.pendingRevalidates[e]})).catch(()=>{}),x.pendingRevalidates[e]=t,r.then(e=>e[1])}});if(k)try{return await R}finally{k&&k.endRead()}return R};return l.__nextPatched=!0,l.__nextGetStaticStore=()=>t,l._nextOriginalFetch=e,globalThis[p]=!0,l}(t,e)}},5455:(e,t,r)=>{"use strict";r.d(t,{EI:()=>m,Fx:()=>a,KK:()=>g,Wc:()=>u,jM:()=>h,rd:()=>p});var n=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(n||{}),i=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(i||{}),s=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(s||{}),a=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(a||{}),o=function(e){return e.startServer="startServer.startServer",e}(o||{}),l=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(l||{}),u=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(u||{}),c=function(e){return e.executeRoute="Router.executeRoute",e}(c||{}),d=function(e){return e.runHandler="Node.runHandler",e}(d||{}),h=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(h||{}),f=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(f||{}),p=function(e){return e.execute="Middleware.execute",e}(p||{});let g=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],m=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"]},5481:(e,t,r)=>{"use strict";r.d(t,{X:()=>y});var n=r(2709),i=r.n(n),s=r(7753),a=r(2),o=r(4515),l=r(7472),u=r(7205),c=r(424),d=r(6534);class h{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(i()),this.callbackQueue.pause()}after(e){if((0,a.Q)(e))this.waitUntil||f(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){this.waitUntil||f();let t=c.FP.getStore();t&&this.workUnitStores.add(t);let r=d.Z.getStore(),n=r?r.rootTaskSpawnPhase:null==t?void 0:t.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let i=(0,u.cg)(async()=>{try{await d.Z.run({rootTaskSpawnPhase:n},()=>e())}catch(e){this.reportTaskError("function",e)}});this.callbackQueue.add(i)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=o.J.getStore();if(!e)throw Object.defineProperty(new s.z("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return(0,l.Y)(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new s.z("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function f(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}var p=r(556),g=r(6116),m=r(9908);function y({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:n,isPrefetchRequest:i,buildId:s,previouslyRevalidatedTags:a}){let o={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(0,p.Y)(e),incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:n,isPrefetchRequest:i,buildId:s,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new h({waitUntil:t,onClose:r,onTaskError:n})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:a,refreshTagsByCacheKind:function(){let e=new Map,t=(0,m.fs)();if(t)for(let[r,n]of t)"refreshTags"in n&&e.set(r,(0,g.a)(async()=>n.refreshTags()));return e}()};return r.store=o,o}},5521:e=>{"use strict";e.exports=require("node:async_hooks")},5565:(e,t,r)=>{"use strict";var n=r(5356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return s}});let i=r(1438),s={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:i,headers:s,body:a,cache:o,credentials:l,integrity:u,mode:c,redirect:d,referrer:h,referrerPolicy:f}=t;return{testData:e,api:"fetch",request:{url:r,method:i,headers:[...Array.from(s),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?n.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:u,mode:c,redirect:d,referrer:h,referrerPolicy:f}}}async function o(e,t){let r=(0,i.getTestReqInfo)(t,s);if(!r)return e(t);let{testData:o,proxyPort:l}=r,u=await a(o,t),c=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(u),next:{internal:!0}});if(!c.ok)throw Object.defineProperty(Error(`Proxy request failed: ${c.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let d=await c.json(),{api:h}=d;switch(h){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:f,headers:p,body:g}=d.response;return new Response(g?n.from(g,"base64"):null,{status:f,headers:new Headers(p)})}function l(e){return r.g.fetch=function(t,r){var n;return(null==r||null==(n=r.next)?void 0:n.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},5835:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},5912:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});let n=(0,r(2058).xl)()},5951:(e,t,r)=>{"use strict";r.d(t,{d:()=>i});let n=new WeakMap;function i(e,t){let r;if(!t)return{pathname:e};let i=n.get(t);i||(i=t.map(e=>e.toLowerCase()),n.set(t,i));let s=e.split("/",2);if(!s[1])return{pathname:e};let a=s[1].toLowerCase(),o=i.indexOf(a);return o<0?{pathname:e}:(r=t[o],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}},6116:(e,t,r)=>{"use strict";function n(e){let t,r={then:(n,i)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(n,i))};return r}r.d(t,{a:()=>n})},6129:(e,t,r)=>{"use strict";r.d(t,{KD:()=>a,Wc:()=>u,_A:()=>o,_V:()=>s,hY:()=>n,j9:()=>l,ts:()=>i});let n="RSC",i="Next-Action",s="Next-Router-Prefetch",a=[n,"Next-Router-State-Tree",s,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],o="_rsc",l="x-nextjs-rewritten-path",u="x-nextjs-rewritten-query"},6225:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});let n=(0,r(2058).xl)()},6237:(e,t,r)=>{"use strict";r.d(t,{Ck:()=>l,IN:()=>c,K8:()=>d,hm:()=>h});var n=r(4318),i=r(5835),s=r(4515),a=r(424);class o extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new o}}class l{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return o.callable;default:return i.l.get(e,t,r)}}})}}let u=Symbol.for("next.mutated.cookies");function c(e,t){let r=function(e){let t=e[u];return t&&Array.isArray(t)&&0!==t.length?t:[]}(t);if(0===r.length)return!1;let i=new n.VO(e),s=i.getAll();for(let e of r)i.set(e);for(let e of s)i.set(e);return!0}class d{static wrap(e,t){let r=new n.VO(new Headers);for(let t of e.getAll())r.set(t);let a=[],o=new Set,l=()=>{let e=s.J.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>o.has(e.name)),t){let e=[];for(let t of a){let r=new n.VO(new Headers);r.set(t),e.push(r.toString())}t(e)}},c=new Proxy(r,{get(e,t,r){switch(t){case u:return a;case"delete":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),c}finally{l()}};case"set":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),c}finally{l()}};default:return i.l.get(e,t,r)}}});return c}}function h(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return f("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return f("cookies().set"),e.set(...r),t};default:return i.l.get(e,r,n)}}});return t}function f(e){if("action"!==(0,a.XN)(e).phase)throw new o}},6243:(e,t,r)=>{"use strict";function n(e){return e.startsWith("/")?e:"/"+e}r.d(t,{A:()=>n})},6464:(e,t,r)=>{"use strict";r.d(t,{o:()=>s});var n=r(5835);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class s extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.l.get(t,r,i);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==a)return n.l.get(t,a,i)},set(t,r,i,s){if("symbol"==typeof r)return n.l.set(t,r,i,s);let a=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);return n.l.set(t,o??r,i,s)},has(t,r){if("symbol"==typeof r)return n.l.has(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==s&&n.l.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return n.l.deleteProperty(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===s||n.l.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.l.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new s(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},6487:()=>{},6534:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(7205).xl)()},6567:(e,t,r)=>{e.exports=r(4261)},6640:(e,t,r)=>{"use strict";r.d(t,{Q:()=>i,n:()=>n});let n=new Map,i=(e,t)=>{for(let r of e){let e=n.get(r);if("number"==typeof e&&e>=t)return!0}return!1}},6804:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var n=r(9691),i=r(3936),s=r(7779),a=r(4318);let o=Symbol("internal request");class l extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,i.qU)(r),e instanceof Request?super(e,t):super(r,t);let s=new n.X(r,{headers:(0,i.Cu)(this.headers),nextConfig:t.nextConfig});this[o]={cookies:new a.tm(this.headers),nextUrl:s,url:s.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[o].cookies}get nextUrl(){return this[o].nextUrl}get page(){throw new s.Yq}get ua(){throw new s.l_}get url(){return this[o].url}}},6937:(e,t,r)=>{"use strict";r.d(t,{t3:()=>d,uO:()=>o,gz:()=>l,ag:()=>u,Ui:()=>h,xI:()=>c});var n=r(1092),i=r(7),s=r(4319);r(424),r(4515),r(252);let a="function"==typeof n.unstable_postpone;function o(e){return{isDebugDynamicAccesses:e,dynamicAccesses:[],syncDynamicExpression:void 0,syncDynamicErrorWithStack:null}}function l(e){var t;return null==(t=e.dynamicAccesses[0])?void 0:t.expression}function u(e,t,r){if((!t||"cache"!==t.type&&"unstable-cache"!==t.type)&&!e.forceDynamic&&!e.forceStatic){if(e.dynamicShouldError)throw Object.defineProperty(new s.f(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${r}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(t){if("prerender-ppr"===t.type)h(e.route,r,t.dynamicTracking);else if("prerender-legacy"===t.type){t.revalidate=0;let n=Object.defineProperty(new i.DynamicServerError(`Route ${e.route} couldn't be rendered statically because it used ${r}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:!1,configurable:!0});throw e.dynamicUsageDescription=r,e.dynamicUsageStack=n.stack,n}}}}function c(e,t,r){let n=Object.defineProperty(new i.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function d(e,t,r,n){if(!1===n.controller.signal.aborted){let i=n.dynamicTracking;i&&null===i.syncDynamicErrorWithStack&&(i.syncDynamicExpression=t,i.syncDynamicErrorWithStack=r,!0===n.validating&&(i.syncDynamicLogged=!0)),function(e,t,r){let n=p(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let i=r.dynamicTracking;i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}(e,t,n)}throw p(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}function h(e,t,r){(function(){if(!a)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.unstable_postpone(f(e,t))}function f(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}if(!1===function(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}(f("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});function p(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest="NEXT_PRERENDER_INTERRUPTED",t}RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`)},7205:(e,t,r)=>{"use strict";r.d(t,{cg:()=>o,xl:()=>a});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let s="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return s?new s:new i}function o(e){return s?s.bind(e):i.bind(e)}},7404:(e,t,r)=>{"use strict";var n=r(3144),i=r(1092),s=Symbol.for("react.element"),a=Symbol.for("react.transitional.element"),o=Symbol.for("react.fragment"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.postpone");var g=Symbol.iterator;function m(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=g&&e[g]||e["@@iterator"])?e:null}var y=Symbol.asyncIterator;function v(e){tw(function(){throw e})}var b=Promise,_="function"==typeof queueMicrotask?queueMicrotask:function(e){b.resolve(null).then(e).catch(v)},w=null,E=0;function x(e,t){if(0!==t.byteLength)if(2048<t.byteLength)0<E&&(e.enqueue(new Uint8Array(w.buffer,0,E)),w=new Uint8Array(2048),E=0),e.enqueue(t);else{var r=w.length-E;r<t.byteLength&&(0===r?e.enqueue(w):(w.set(t.subarray(0,r),E),e.enqueue(w),t=t.subarray(r)),w=new Uint8Array(2048),E=0),w.set(t,E),E+=t.byteLength}return!0}var S=new TextEncoder;function k(e){return S.encode(e)}function R(e){return e.byteLength}function C(e,t){"function"==typeof e.error?e.error(t):e.close()}var O=Symbol.for("react.client.reference"),T=Symbol.for("react.server.reference");function P(e,t,r){return Object.defineProperties(e,{$$typeof:{value:O},$$id:{value:t},$$async:{value:r}})}var A=Function.prototype.bind,j=Array.prototype.slice;function I(){var e=A.apply(this,arguments);if(this.$$typeof===T){var t=j.call(arguments,1);return Object.defineProperties(e,{$$typeof:{value:T},$$id:{value:this.$$id},$$bound:t={value:this.$$bound?this.$$bound.concat(t):t},bind:{value:I,configurable:!0}})}return e}var N=Promise.prototype,$={get:function(e,t){switch(t){case"$$typeof":return e.$$typeof;case"$$id":return e.$$id;case"$$async":return e.$$async;case"name":return e.name;case"displayName":case"defaultProps":case"toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case"Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.");case"then":throw Error("Cannot await or return from a thenable. You cannot await a client module from a server component.")}throw Error("Cannot access "+String(e.name)+"."+String(t)+" on the server. You cannot dot into a client module from a server component. You can only pass the imported name through.")},set:function(){throw Error("Cannot assign to a client module from a server module.")}};function D(e,t){switch(t){case"$$typeof":return e.$$typeof;case"$$id":return e.$$id;case"$$async":return e.$$async;case"name":return e.name;case"defaultProps":case"toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case"__esModule":var r=e.$$id;return e.default=P(function(){throw Error("Attempted to call the default export of "+r+" from the server but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},e.$$id+"#",e.$$async),!0;case"then":if(e.then)return e.then;if(e.$$async)return;var n=P({},e.$$id,!0),i=new Proxy(n,M);return e.status="fulfilled",e.value=i,e.then=P(function(e){return Promise.resolve(e(i))},e.$$id+"#then",!1)}if("symbol"==typeof t)throw Error("Cannot read Symbol exports. Only named exports are supported on a client module imported on the server.");return(n=e[t])||(Object.defineProperty(n=P(function(){throw Error("Attempted to call "+String(t)+"() from the server but "+String(t)+" is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},e.$$id+"#"+t,e.$$async),"name",{value:t}),n=e[t]=new Proxy(n,$)),n}var M={get:function(e,t){return D(e,t)},getOwnPropertyDescriptor:function(e,t){var r=Object.getOwnPropertyDescriptor(e,t);return r||(r={value:D(e,t),writable:!1,configurable:!1,enumerable:!1},Object.defineProperty(e,t,r)),r},getPrototypeOf:function(){return N},set:function(){throw Error("Cannot assign to a client module from a server module.")}},L=n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,U=L.d;function q(e){if(null==e)return null;var t,r=!1,n={};for(t in e)null!=e[t]&&(r=!0,n[t]=e[t]);return r?n:null}L.d={f:U.f,r:U.r,D:function(e){if("string"==typeof e&&e){var t=ev();if(t){var r=t.hints,n="D|"+e;r.has(n)||(r.add(n),e_(t,"D",e))}else U.D(e)}},C:function(e,t){if("string"==typeof e){var r=ev();if(r){var n=r.hints,i="C|"+(null==t?"null":t)+"|"+e;n.has(i)||(n.add(i),"string"==typeof t?e_(r,"C",[e,t]):e_(r,"C",e))}else U.C(e,t)}},L:function(e,t,r){if("string"==typeof e){var n=ev();if(n){var i=n.hints,s="L";if("image"===t&&r){var a=r.imageSrcSet,o=r.imageSizes,l="";"string"==typeof a&&""!==a?(l+="["+a+"]","string"==typeof o&&(l+="["+o+"]")):l+="[][]"+e,s+="[image]"+l}else s+="["+t+"]"+e;i.has(s)||(i.add(s),(r=q(r))?e_(n,"L",[e,t,r]):e_(n,"L",[e,t]))}else U.L(e,t,r)}},m:function(e,t){if("string"==typeof e){var r=ev();if(r){var n=r.hints,i="m|"+e;if(n.has(i))return;return n.add(i),(t=q(t))?e_(r,"m",[e,t]):e_(r,"m",e)}U.m(e,t)}},X:function(e,t){if("string"==typeof e){var r=ev();if(r){var n=r.hints,i="X|"+e;if(n.has(i))return;return n.add(i),(t=q(t))?e_(r,"X",[e,t]):e_(r,"X",e)}U.X(e,t)}},S:function(e,t,r){if("string"==typeof e){var n=ev();if(n){var i=n.hints,s="S|"+e;if(i.has(s))return;return i.add(s),(r=q(r))?e_(n,"S",[e,"string"==typeof t?t:0,r]):"string"==typeof t?e_(n,"S",[e,t]):e_(n,"S",e)}U.S(e,t,r)}},M:function(e,t){if("string"==typeof e){var r=ev();if(r){var n=r.hints,i="M|"+e;if(n.has(i))return;return n.add(i),(t=q(t))?e_(r,"M",[e,t]):e_(r,"M",e)}U.M(e,t)}}};var B="function"==typeof AsyncLocalStorage,F=B?new AsyncLocalStorage:null;"object"==typeof async_hooks&&async_hooks.createHook,"object"==typeof async_hooks&&async_hooks.executionAsyncId;var z=Symbol.for("react.temporary.reference"),H={get:function(e,t){switch(t){case"$$typeof":return e.$$typeof;case"name":case"displayName":case"defaultProps":case"toJSON":return;case Symbol.toPrimitive:return Object.prototype[Symbol.toPrimitive];case Symbol.toStringTag:return Object.prototype[Symbol.toStringTag];case"Provider":throw Error("Cannot render a Client Context Provider on the Server. Instead, you can export a Client Component wrapper that itself renders a Client Context Provider.")}throw Error("Cannot access "+String(t)+" on the server. You cannot dot into a temporary client reference from a server component. You can only pass the value through to the client.")},set:function(){throw Error("Cannot assign to a temporary client reference from a server module.")}},V=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`.");function G(){}var K=null;function W(){if(null===K)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var e=K;return K=null,e}var X=null,Z=0,J=null;function Y(){var e=J||[];return J=null,e}var Q={readContext:er,use:function(e){if(null!==e&&"object"==typeof e||"function"==typeof e){if("function"==typeof e.then){var t=Z;Z+=1,null===J&&(J=[]);var r=J,n=e,i=t;switch(void 0===(i=r[i])?r.push(n):i!==n&&(n.then(G,G),n=i),n.status){case"fulfilled":return n.value;case"rejected":throw n.reason;default:switch("string"==typeof n.status?n.then(G,G):((r=n).status="pending",r.then(function(e){if("pending"===n.status){var t=n;t.status="fulfilled",t.value=e}},function(e){if("pending"===n.status){var t=n;t.status="rejected",t.reason=e}})),n.status){case"fulfilled":return n.value;case"rejected":throw n.reason}throw K=n,V}}e.$$typeof===l&&er()}if(e.$$typeof===O){if(null!=e.value&&e.value.$$typeof===l)throw Error("Cannot read a Client Context from a Server Component.");throw Error("Cannot use() an already resolved Client Reference.")}throw Error("An unsupported type was passed to use(): "+String(e))},useCallback:function(e){return e},useContext:er,useEffect:ee,useImperativeHandle:ee,useLayoutEffect:ee,useInsertionEffect:ee,useMemo:function(e){return e()},useReducer:ee,useRef:ee,useState:ee,useDebugValue:function(){},useDeferredValue:ee,useTransition:ee,useSyncExternalStore:ee,useId:function(){if(null===X)throw Error("useId can only be used while React is rendering");var e=X.identifierCount++;return":"+X.identifierPrefix+"S"+e.toString(32)+":"},useHostTransitionStatus:ee,useFormState:ee,useActionState:ee,useOptimistic:ee,useMemoCache:function(e){for(var t=Array(e),r=0;r<e;r++)t[r]=p;return t},useCacheRefresh:function(){return et}};function ee(){throw Error("This Hook is not supported in Server Components.")}function et(){throw Error("Refreshing the cache is not supported in Server Components.")}function er(){throw Error("Cannot read a Client Context from a Server Component.")}var en={getCacheForType:function(e){var t=(t=ev())?t.cache:new Map,r=t.get(e);return void 0===r&&(r=e(),t.set(e,r)),r}},ei=i.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;if(!ei)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');var es=Array.isArray,ea=Object.getPrototypeOf;function eo(e){return Object.prototype.toString.call(e).replace(/^\[object (.*)\]$/,function(e,t){return t})}function el(e){switch(typeof e){case"string":return JSON.stringify(10>=e.length?e:e.slice(0,10)+"...");case"object":if(es(e))return"[...]";if(null!==e&&e.$$typeof===eu)return"client";return"Object"===(e=eo(e))?"{...}":e;case"function":return e.$$typeof===eu?"client":(e=e.displayName||e.name)?"function "+e:"function";default:return String(e)}}var eu=Symbol.for("react.client.reference");function ec(e,t){var r=eo(e);if("Object"!==r&&"Array"!==r)return r;r=-1;var n=0;if(es(e)){for(var i="[",s=0;s<e.length;s++){0<s&&(i+=", ");var o=e[s];o="object"==typeof o&&null!==o?ec(o):el(o),""+s===t?(r=i.length,n=o.length,i+=o):i=10>o.length&&40>i.length+o.length?i+o:i+"..."}i+="]"}else if(e.$$typeof===a)i="<"+function e(t){if("string"==typeof t)return t;switch(t){case c:return"Suspense";case d:return"SuspenseList"}if("object"==typeof t)switch(t.$$typeof){case u:return e(t.render);case h:return e(t.type);case f:var r=t._payload;t=t._init;try{return e(t(r))}catch(e){}}return""}(e.type)+"/>";else{if(e.$$typeof===eu)return"client";for(o=0,i="{",s=Object.keys(e);o<s.length;o++){0<o&&(i+=", ");var l=s[o],p=JSON.stringify(l);i+=('"'+l+'"'===p?l:p)+": ",p="object"==typeof(p=e[l])&&null!==p?ec(p):el(p),l===t?(r=i.length,n=p.length,i+=p):i=10>p.length&&40>i.length+p.length?i+p:i+"..."}i+="}"}return void 0===t?i:-1<r&&0<n?"\n  "+i+"\n  "+(e=" ".repeat(r)+"^".repeat(n)):"\n  "+i}var ed=Object.prototype,eh=JSON.stringify;function ef(e){}function ep(){}function eg(e,t,r,n,i,s,a,o,l,u,c){if(null!==ei.A&&ei.A!==en)throw Error("Currently React only supports one RSC renderer at a time.");ei.A=en,l=new Set,o=[];var d=new Set;this.type=e,this.status=10,this.flushScheduled=!1,this.destination=this.fatalError=null,this.bundlerConfig=r,this.cache=new Map,this.pendingChunks=this.nextChunkId=0,this.hints=d,this.abortListeners=new Set,this.abortableTasks=l,this.pingedTasks=o,this.completedImportChunks=[],this.completedHintChunks=[],this.completedRegularChunks=[],this.completedErrorChunks=[],this.writtenSymbols=new Map,this.writtenClientReferences=new Map,this.writtenServerReferences=new Map,this.writtenObjects=new WeakMap,this.temporaryReferences=a,this.identifierPrefix=i||"",this.identifierCount=1,this.taintCleanupQueue=[],this.onError=void 0===n?ef:n,this.onPostpone=void 0===s?ep:s,this.onAllReady=u,this.onFatalError=c,e=eR(this,t,null,!1,l),o.push(e)}function em(){}var ey=null;function ev(){if(ey)return ey;if(B){var e=F.getStore();if(e)return e}return null}function eb(e,t,r){var n=eR(e,null,t.keyPath,t.implicitSlot,e.abortableTasks);switch(r.status){case"fulfilled":return n.model=r.value,ek(e,n),n.id;case"rejected":return eB(e,n,r.reason),n.id;default:if(12===e.status)return e.abortableTasks.delete(n),n.status=3,t=eh(eC(e.fatalError)),eM(e,n.id,t),n.id;"string"!=typeof r.status&&(r.status="pending",r.then(function(e){"pending"===r.status&&(r.status="fulfilled",r.value=e)},function(e){"pending"===r.status&&(r.status="rejected",r.reason=e)}))}return r.then(function(t){n.model=t,ek(e,n)},function(t){0===n.status&&(eB(e,n,t),eK(e))}),n.id}function e_(e,t,r){t=k(":H"+t+(r=eh(r))+"\n"),e.completedHintChunks.push(t),eK(e)}function ew(e){if("fulfilled"===e.status)return e.value;if("rejected"===e.status)throw e.reason;throw e}function eE(){}function ex(e,t,r,n,i){var s=t.thenableState;if(t.thenableState=null,Z=0,J=s,i=n(i,void 0),12===e.status)throw"object"==typeof i&&null!==i&&"function"==typeof i.then&&i.$$typeof!==O&&i.then(eE,eE),null;return i=function(e,t,r,n){if("object"!=typeof n||null===n||n.$$typeof===O)return n;if("function"==typeof n.then)return"fulfilled"===n.status?n.value:function(e){switch(e.status){case"fulfilled":case"rejected":break;default:"string"!=typeof e.status&&(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))}return{$$typeof:f,_payload:e,_init:ew}}(n);var i=m(n);return i?((e={})[Symbol.iterator]=function(){return i.call(n)},e):"function"!=typeof n[y]||"function"==typeof ReadableStream&&n instanceof ReadableStream?n:((e={})[y]=function(){return n[y]()},e)}(e,0,0,i),n=t.keyPath,s=t.implicitSlot,null!==r?t.keyPath=null===n?r:n+","+r:null===n&&(t.implicitSlot=!0),e=eI(e,t,eF,"",i),t.keyPath=n,t.implicitSlot=s,e}function eS(e,t,r){return null!==t.keyPath?(e=[a,o,t.keyPath,{children:r}],t.implicitSlot?[e]:e):r}function ek(e,t){var r=e.pingedTasks;r.push(t),1===r.length&&(e.flushScheduled=null!==e.destination,21===e.type||10===e.status?_(function(){return eH(e)}):tw(function(){return eH(e)},0))}function eR(e,t,r,n,i){e.pendingChunks++;var s=e.nextChunkId++;"object"!=typeof t||null===t||null!==r||n||e.writtenObjects.set(t,eC(s));var o={id:s,status:0,model:t,keyPath:r,implicitSlot:n,ping:function(){return ek(e,o)},toJSON:function(t,r){var n=o.keyPath,i=o.implicitSlot;try{var s=eI(e,o,this,t,r)}catch(u){if(t="object"==typeof(t=o.model)&&null!==t&&(t.$$typeof===a||t.$$typeof===f),12===e.status)o.status=3,n=e.fatalError,s=t?"$L"+n.toString(16):eC(n);else if("object"==typeof(r=u===V?W():u)&&null!==r&&"function"==typeof r.then){var l=(s=eR(e,o.model,o.keyPath,o.implicitSlot,e.abortableTasks)).ping;r.then(l,l),s.thenableState=Y(),o.keyPath=n,o.implicitSlot=i,s=t?"$L"+s.id.toString(16):eC(s.id)}else o.keyPath=n,o.implicitSlot=i,e.pendingChunks++,n=e.nextChunkId++,i=eN(e,r,o),eD(e,n,i),s=t?"$L"+n.toString(16):eC(n)}return s},thenableState:null};return i.add(o),o}function eC(e){return"$"+e.toString(16)}function eO(e,t,r){return e=eh(r),k(t=t.toString(16)+":"+e+"\n")}function eT(e,t,r,n){var i=n.$$async?n.$$id+"#async":n.$$id,s=e.writtenClientReferences,o=s.get(i);if(void 0!==o)return t[0]===a&&"1"===r?"$L"+o.toString(16):eC(o);try{var l=e.bundlerConfig,u=n.$$id;o="";var c=l[u];if(c)o=c.name;else{var d=u.lastIndexOf("#");if(-1!==d&&(o=u.slice(d+1),c=l[u.slice(0,d)]),!c)throw Error('Could not find the module "'+u+'" in the React Client Manifest. This is probably a bug in the React Server Components bundler.')}if(!0===c.async&&!0===n.$$async)throw Error('The module "'+u+'" is marked as an async ESM module but was loaded as a CJS proxy. This is probably a bug in the React Server Components bundler.');var h=!0===c.async||!0===n.$$async?[c.id,c.chunks,o,1]:[c.id,c.chunks,o];e.pendingChunks++;var f=e.nextChunkId++,p=eh(h),g=f.toString(16)+":I"+p+"\n",m=k(g);return e.completedImportChunks.push(m),s.set(i,f),t[0]===a&&"1"===r?"$L"+f.toString(16):eC(f)}catch(n){return e.pendingChunks++,t=e.nextChunkId++,r=eN(e,n,null),eD(e,t,r),eC(t)}}function eP(e,t){return t=eR(e,t,null,!1,e.abortableTasks),ez(e,t),t.id}function eA(e,t,r){e.pendingChunks++;var n=e.nextChunkId++;return eL(e,n,t,r),eC(n)}var ej=!1;function eI(e,t,r,n,i){if(t.model=i,i===a)return"$";if(null===i)return null;if("object"==typeof i){switch(i.$$typeof){case a:var l=null,c=e.writtenObjects;if(null===t.keyPath&&!t.implicitSlot){var d=c.get(i);if(void 0!==d)if(ej!==i)return d;else ej=null;else -1===n.indexOf(":")&&void 0!==(r=c.get(r))&&(l=r+":"+n,c.set(i,l))}return r=(n=i.props).ref,"object"==typeof(e=function e(t,r,n,i,s,l){if(null!=s)throw Error("Refs cannot be used in Server Components, nor passed to Client Components.");if("function"==typeof n&&n.$$typeof!==O&&n.$$typeof!==z)return ex(t,r,i,n,l);if(n===o&&null===i)return n=r.implicitSlot,null===r.keyPath&&(r.implicitSlot=!0),l=eI(t,r,eF,"",l.children),r.implicitSlot=n,l;if(null!=n&&"object"==typeof n&&n.$$typeof!==O)switch(n.$$typeof){case f:if(n=(0,n._init)(n._payload),12===t.status)throw null;return e(t,r,n,i,s,l);case u:return ex(t,r,i,n.render,l);case h:return e(t,r,n.type,i,s,l)}return t=i,i=r.keyPath,null===t?t=i:null!==i&&(t=i+","+t),l=[a,n,t,l],r=r.implicitSlot&&null!==t?[l]:l}(e,t,i.type,i.key,void 0!==r?r:null,n))&&null!==e&&null!==l&&(c.has(e)||c.set(e,l)),e;case f:if(t.thenableState=null,i=(n=i._init)(i._payload),12===e.status)throw null;return eI(e,t,eF,"",i);case s:throw Error('A React Element from an older version of React was rendered. This is not supported. It can happen if:\n- Multiple copies of the "react" package is used.\n- A library pre-bundled an old copy of "react" or "react/jsx-runtime".\n- A compiler tries to "inline" JSX instead of using the runtime.')}if(i.$$typeof===O)return eT(e,r,n,i);if(void 0!==e.temporaryReferences&&void 0!==(l=e.temporaryReferences.get(i)))return"$T"+l;if(c=(l=e.writtenObjects).get(i),"function"==typeof i.then){if(void 0!==c){if(null!==t.keyPath||t.implicitSlot)return"$@"+eb(e,t,i).toString(16);if(ej!==i)return c;ej=null}return e="$@"+eb(e,t,i).toString(16),l.set(i,e),e}if(void 0!==c)if(ej!==i)return c;else ej=null;else if(-1===n.indexOf(":")&&void 0!==(c=l.get(r))){if(d=n,es(r)&&r[0]===a)switch(n){case"1":d="type";break;case"2":d="key";break;case"3":d="props";break;case"4":d="_owner"}l.set(i,c+":"+d)}if(es(i))return eS(e,t,i);if(i instanceof Map)return"$Q"+eP(e,i=Array.from(i)).toString(16);if(i instanceof Set)return"$W"+eP(e,i=Array.from(i)).toString(16);if("function"==typeof FormData&&i instanceof FormData)return"$K"+eP(e,i=Array.from(i.entries())).toString(16);if(i instanceof Error)return"$Z";if(i instanceof ArrayBuffer)return eA(e,"A",new Uint8Array(i));if(i instanceof Int8Array)return eA(e,"O",i);if(i instanceof Uint8Array)return eA(e,"o",i);if(i instanceof Uint8ClampedArray)return eA(e,"U",i);if(i instanceof Int16Array)return eA(e,"S",i);if(i instanceof Uint16Array)return eA(e,"s",i);if(i instanceof Int32Array)return eA(e,"L",i);if(i instanceof Uint32Array)return eA(e,"l",i);if(i instanceof Float32Array)return eA(e,"G",i);if(i instanceof Float64Array)return eA(e,"g",i);if(i instanceof BigInt64Array)return eA(e,"M",i);if(i instanceof BigUint64Array)return eA(e,"m",i);if(i instanceof DataView)return eA(e,"V",i);if("function"==typeof Blob&&i instanceof Blob)return function(e,t){function r(t){o||(o=!0,e.abortListeners.delete(n),eB(e,s,t),eK(e),a.cancel(t).then(r,r))}function n(t){o||(o=!0,e.abortListeners.delete(n),eB(e,s,t),eK(e),a.cancel(t).then(r,r))}var i=[t.type],s=eR(e,i,null,!1,e.abortableTasks),a=t.stream().getReader(),o=!1;return e.abortListeners.add(n),a.read().then(function t(l){if(!o)if(!l.done)return i.push(l.value),a.read().then(t).catch(r);else e.abortListeners.delete(n),o=!0,ek(e,s)}).catch(r),"$B"+s.id.toString(16)}(e,i);if(l=m(i))return(n=l.call(i))===i?"$i"+eP(e,Array.from(n)).toString(16):eS(e,t,Array.from(n));if("function"==typeof ReadableStream&&i instanceof ReadableStream)return function(e,t,r){function n(t){l||(l=!0,e.abortListeners.delete(i),eB(e,o,t),eK(e),a.cancel(t).then(n,n))}function i(t){l||(l=!0,e.abortListeners.delete(i),eB(e,o,t),eK(e),a.cancel(t).then(n,n))}var s=r.supportsBYOB;if(void 0===s)try{r.getReader({mode:"byob"}).releaseLock(),s=!0}catch(e){s=!1}var a=r.getReader(),o=eR(e,t.model,t.keyPath,t.implicitSlot,e.abortableTasks);e.abortableTasks.delete(o),e.pendingChunks++,t=o.id.toString(16)+":"+(s?"r":"R")+"\n",e.completedRegularChunks.push(k(t));var l=!1;return e.abortListeners.add(i),a.read().then(function t(r){if(!l)if(r.done)e.abortListeners.delete(i),r=o.id.toString(16)+":C\n",e.completedRegularChunks.push(k(r)),eK(e),l=!0;else try{o.model=r.value,e.pendingChunks++,eq(e,o,o.model),eK(e),a.read().then(t,n)}catch(e){n(e)}},n),eC(o.id)}(e,t,i);if("function"==typeof(l=i[y]))return null!==t.keyPath?(e=[a,o,t.keyPath,{children:i}],e=t.implicitSlot?[e]:e):(n=l.call(i),e=function(e,t,r,n){function i(t){o||(o=!0,e.abortListeners.delete(s),eB(e,a,t),eK(e),"function"==typeof n.throw&&n.throw(t).then(i,i))}function s(t){o||(o=!0,e.abortListeners.delete(s),eB(e,a,t),eK(e),"function"==typeof n.throw&&n.throw(t).then(i,i))}r=r===n;var a=eR(e,t.model,t.keyPath,t.implicitSlot,e.abortableTasks);e.abortableTasks.delete(a),e.pendingChunks++,t=a.id.toString(16)+":"+(r?"x":"X")+"\n",e.completedRegularChunks.push(k(t));var o=!1;return e.abortListeners.add(s),n.next().then(function t(r){if(!o)if(r.done){if(e.abortListeners.delete(s),void 0===r.value)var l=a.id.toString(16)+":C\n";else try{var u=eP(e,r.value);l=a.id.toString(16)+":C"+eh(eC(u))+"\n"}catch(e){i(e);return}e.completedRegularChunks.push(k(l)),eK(e),o=!0}else try{a.model=r.value,e.pendingChunks++,eq(e,a,a.model),eK(e),n.next().then(t,i)}catch(e){i(e)}},i),eC(a.id)}(e,t,i,n)),e;if(i instanceof Date)return"$D"+i.toJSON();if((e=ea(i))!==ed&&(null===e||null!==ea(e)))throw Error("Only plain objects, and a few built-ins, can be passed to Client Components from Server Components. Classes or null prototypes are not supported."+ec(r,n));return i}if("string"==typeof i)return"Z"===i[i.length-1]&&r[n]instanceof Date?"$D"+i:1024<=i.length&&null!==R?(e.pendingChunks++,t=e.nextChunkId++,eU(e,t,i),eC(t)):e="$"===i[0]?"$"+i:i;if("boolean"==typeof i)return i;if("number"==typeof i)return Number.isFinite(i)?0===i&&-1/0==1/i?"$-0":i:1/0===i?"$Infinity":-1/0===i?"$-Infinity":"$NaN";if(void 0===i)return"$undefined";if("function"==typeof i){if(i.$$typeof===O)return eT(e,r,n,i);if(i.$$typeof===T)return void 0!==(n=(t=e.writtenServerReferences).get(i))?e="$F"+n.toString(16):(n=null===(n=i.$$bound)?null:Promise.resolve(n),e=eP(e,{id:i.$$id,bound:n}),t.set(i,e),e="$F"+e.toString(16)),e;if(void 0!==e.temporaryReferences&&void 0!==(e=e.temporaryReferences.get(i)))return"$T"+e;if(i.$$typeof===z)throw Error("Could not reference an opaque temporary reference. This is likely due to misconfiguring the temporaryReferences options on the server.");if(/^on[A-Z]/.test(n))throw Error("Event handlers cannot be passed to Client Component props."+ec(r,n)+"\nIf you need interactivity, consider converting part of this to a Client Component.");throw Error('Functions cannot be passed directly to Client Components unless you explicitly expose it by marking it with "use server". Or maybe you meant to call this function rather than return it.'+ec(r,n))}if("symbol"==typeof i){if(void 0!==(l=(t=e.writtenSymbols).get(i)))return eC(l);if(Symbol.for(l=i.description)!==i)throw Error("Only global symbols received from Symbol.for(...) can be passed to Client Components. The symbol Symbol.for("+i.description+") cannot be found among global symbols."+ec(r,n));return e.pendingChunks++,n=e.nextChunkId++,r=eO(e,n,"$S"+l),e.completedImportChunks.push(r),t.set(i,n),eC(n)}if("bigint"==typeof i)return"$n"+i.toString(10);throw Error("Type "+typeof i+" is not supported in Client Component props."+ec(r,n))}function eN(e,t){var r=ey;ey=null;try{var n=e.onError,i=B?F.run(void 0,n,t):n(t)}finally{ey=r}if(null!=i&&"string"!=typeof i)throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof i+'" instead');return i||""}function e$(e,t){(0,e.onFatalError)(t),null!==e.destination?(e.status=14,C(e.destination,t)):(e.status=13,e.fatalError=t)}function eD(e,t,r){r={digest:r},t=k(t=t.toString(16)+":E"+eh(r)+"\n"),e.completedErrorChunks.push(t)}function eM(e,t,r){t=k(t=t.toString(16)+":"+r+"\n"),e.completedRegularChunks.push(t)}function eL(e,t,r,n){e.pendingChunks++;var i=new Uint8Array(n.buffer,n.byteOffset,n.byteLength);i=(n=2048<n.byteLength?i.slice():i).byteLength,t=k(t=t.toString(16)+":"+r+i.toString(16)+","),e.completedRegularChunks.push(t,n)}function eU(e,t,r){if(null===R)throw Error("Existence of byteLengthOfChunk should have already been checked. This is a bug in React.");e.pendingChunks++;var n=(r=k(r)).byteLength;t=k(t=t.toString(16)+":T"+n.toString(16)+","),e.completedRegularChunks.push(t,r)}function eq(e,t,r){var n=t.id;"string"==typeof r&&null!==R?eU(e,n,r):r instanceof ArrayBuffer?eL(e,n,"A",new Uint8Array(r)):r instanceof Int8Array?eL(e,n,"O",r):r instanceof Uint8Array?eL(e,n,"o",r):r instanceof Uint8ClampedArray?eL(e,n,"U",r):r instanceof Int16Array?eL(e,n,"S",r):r instanceof Uint16Array?eL(e,n,"s",r):r instanceof Int32Array?eL(e,n,"L",r):r instanceof Uint32Array?eL(e,n,"l",r):r instanceof Float32Array?eL(e,n,"G",r):r instanceof Float64Array?eL(e,n,"g",r):r instanceof BigInt64Array?eL(e,n,"M",r):r instanceof BigUint64Array?eL(e,n,"m",r):r instanceof DataView?eL(e,n,"V",r):(r=eh(r,t.toJSON),eM(e,t.id,r))}function eB(e,t,r){e.abortableTasks.delete(t),t.status=4,r=eN(e,r,t),eD(e,t.id,r)}var eF={};function ez(e,t){if(0===t.status){t.status=5;try{ej=t.model;var r=eI(e,t,eF,"",t.model);if(ej=r,t.keyPath=null,t.implicitSlot=!1,"object"==typeof r&&null!==r)e.writtenObjects.set(r,eC(t.id)),eq(e,t,r);else{var n=eh(r);eM(e,t.id,n)}e.abortableTasks.delete(t),t.status=1}catch(r){if(12===e.status){e.abortableTasks.delete(t),t.status=3;var i=eh(eC(e.fatalError));eM(e,t.id,i)}else{var s=r===V?W():r;if("object"==typeof s&&null!==s&&"function"==typeof s.then){t.status=0,t.thenableState=Y();var a=t.ping;s.then(a,a)}else eB(e,t,s)}}finally{}}}function eH(e){var t=ei.H;ei.H=Q;var r=ey;X=ey=e;var n=0<e.abortableTasks.size;try{var i=e.pingedTasks;e.pingedTasks=[];for(var s=0;s<i.length;s++)ez(e,i[s]);null!==e.destination&&eV(e,e.destination),n&&0===e.abortableTasks.size&&(0,e.onAllReady)()}catch(t){eN(e,t,null),e$(e,t)}finally{ei.H=t,X=null,ey=r}}function eV(e,t){w=new Uint8Array(2048),E=0;try{for(var r=e.completedImportChunks,n=0;n<r.length;n++)e.pendingChunks--,x(t,r[n]);r.splice(0,n);var i=e.completedHintChunks;for(n=0;n<i.length;n++)x(t,i[n]);i.splice(0,n);var s=e.completedRegularChunks;for(n=0;n<s.length;n++)e.pendingChunks--,x(t,s[n]);s.splice(0,n);var a=e.completedErrorChunks;for(n=0;n<a.length;n++)e.pendingChunks--,x(t,a[n]);a.splice(0,n)}finally{e.flushScheduled=!1,w&&0<E&&(t.enqueue(new Uint8Array(w.buffer,0,E)),w=null,E=0)}0===e.pendingChunks&&(e.status=14,t.close(),e.destination=null)}function eG(e){e.flushScheduled=null!==e.destination,B?_(function(){F.run(e,eH,e)}):_(function(){return eH(e)}),tw(function(){10===e.status&&(e.status=11)},0)}function eK(e){!1===e.flushScheduled&&0===e.pingedTasks.length&&null!==e.destination&&(e.flushScheduled=!0,tw(function(){e.flushScheduled=!1;var t=e.destination;t&&eV(e,t)},0))}function eW(e,t){if(13===e.status)e.status=14,C(t,e.fatalError);else if(14!==e.status&&null===e.destination){e.destination=t;try{eV(e,t)}catch(t){eN(e,t,null),e$(e,t)}}}function eX(e,t){try{11>=e.status&&(e.status=12);var r=e.abortableTasks;if(0<r.size){var n=void 0===t?Error("The render was aborted by the server without a reason."):"object"==typeof t&&null!==t&&"function"==typeof t.then?Error("The render was aborted by the server with a promise."):t,i=eN(e,n,null),s=e.nextChunkId++;e.fatalError=s,e.pendingChunks++,eD(e,s,i,n),r.forEach(function(t){if(5!==t.status){t.status=3;var r=eC(s);t=eO(e,t.id,r),e.completedErrorChunks.push(t)}}),r.clear(),(0,e.onAllReady)()}var a=e.abortListeners;if(0<a.size){var o=void 0===t?Error("The render was aborted by the server without a reason."):"object"==typeof t&&null!==t&&"function"==typeof t.then?Error("The render was aborted by the server with a promise."):t;a.forEach(function(e){return e(o)}),a.clear()}null!==e.destination&&eV(e,e.destination)}catch(t){eN(e,t,null),e$(e,t)}}function eZ(e,t){var r="",n=e[t];if(n)r=n.name;else{var i=t.lastIndexOf("#");if(-1!==i&&(r=t.slice(i+1),n=e[t.slice(0,i)]),!n)throw Error('Could not find the module "'+t+'" in the React Server Manifest. This is probably a bug in the React Server Components bundler.')}return n.async?[n.id,n.chunks,r,1]:[n.id,n.chunks,r]}var eJ=new Map;function eY(e){var t=globalThis.__next_require__(e);return"function"!=typeof t.then||"fulfilled"===t.status?null:(t.then(function(e){t.status="fulfilled",t.value=e},function(e){t.status="rejected",t.reason=e}),t)}function eQ(){}function e0(e){for(var t=e[1],n=[],i=0;i<t.length;){var s=t[i++];t[i++];var a=eJ.get(s);if(void 0===a){a=r.e(s),n.push(a);var o=eJ.set.bind(eJ,s,null);a.then(o,eQ),eJ.set(s,a)}else null!==a&&n.push(a)}return 4===e.length?0===n.length?eY(e[0]):Promise.all(n).then(function(){return eY(e[0])}):0<n.length?Promise.all(n):null}function e1(e){var t=globalThis.__next_require__(e[0]);if(4===e.length&&"function"==typeof t.then)if("fulfilled"===t.status)t=t.value;else throw t.reason;return"*"===e[2]?t:""===e[2]?t.__esModule?t.default:t:t[e[2]]}var e2=Object.prototype.hasOwnProperty;function e4(e,t,r,n){this.status=e,this.value=t,this.reason=r,this._response=n}function e3(e){return new e4("pending",null,null,e)}function e9(e,t){for(var r=0;r<e.length;r++)(0,e[r])(t)}function e5(e,t){if("pending"!==e.status&&"blocked"!==e.status)e.reason.error(t);else{var r=e.reason;e.status="rejected",e.reason=t,null!==r&&e9(r,t)}}function e6(e,t,r){if("pending"!==e.status)e=e.reason,"C"===t[0]?e.close("C"===t?'"$undefined"':t.slice(1)):e.enqueueModel(t);else{var n=e.value,i=e.reason;if(e.status="resolved_model",e.value=t,e.reason=r,null!==n)switch(tr(e),e.status){case"fulfilled":e9(n,e.value);break;case"pending":case"blocked":case"cyclic":if(e.value)for(t=0;t<n.length;t++)e.value.push(n[t]);else e.value=n;if(e.reason){if(i)for(t=0;t<i.length;t++)e.reason.push(i[t])}else e.reason=i;break;case"rejected":i&&e9(i,e.reason)}}}function e8(e,t,r){return new e4("resolved_model",(r?'{"done":true,"value":':'{"done":false,"value":')+t+"}",-1,e)}function e7(e,t,r){e6(e,(r?'{"done":true,"value":':'{"done":false,"value":')+t+"}",-1)}e4.prototype=Object.create(Promise.prototype),e4.prototype.then=function(e,t){switch("resolved_model"===this.status&&tr(this),this.status){case"fulfilled":e(this.value);break;case"pending":case"blocked":case"cyclic":e&&(null===this.value&&(this.value=[]),this.value.push(e)),t&&(null===this.reason&&(this.reason=[]),this.reason.push(t));break;default:t(this.reason)}};var te=null,tt=null;function tr(e){var t=te,r=tt;te=e,tt=null;var n=-1===e.reason?void 0:e.reason.toString(16),i=e.value;e.status="cyclic",e.value=null,e.reason=null;try{var s=JSON.parse(i),a=function e(t,r,n,i,s){if("string"==typeof i)return function(e,t,r,n,i){if("$"===n[0]){switch(n[1]){case"$":return n.slice(1);case"@":return ti(e,t=parseInt(n.slice(2),16));case"F":return n=to(e,n=n.slice(2),t,r,td),function(e,t,r,n,i,s){var a=eZ(e._bundlerConfig,t);if(t=e0(a),r)r=Promise.all([r,t]).then(function(e){e=e[0];var t=e1(a);return t.bind.apply(t,[null].concat(e))});else{if(!t)return e1(a);r=Promise.resolve(t).then(function(){return e1(a)})}return r.then(ts(n,i,s,!1,e,td,[]),ta(n)),null}(e,n.id,n.bound,te,t,r);case"T":var s,a;if(void 0===i||void 0===e._temporaryReferences)throw Error("Could not reference an opaque temporary reference. This is likely due to misconfiguring the temporaryReferences options on the server.");return s=e._temporaryReferences,a=new Proxy(a=Object.defineProperties(function(){throw Error("Attempted to call a temporary Client Reference from the server but it is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},{$$typeof:{value:z}}),H),s.set(a,i),a;case"Q":return to(e,n=n.slice(2),t,r,tl);case"W":return to(e,n=n.slice(2),t,r,tu);case"K":t=n.slice(2);var o=e._prefix+t+"_",l=new FormData;return e._formData.forEach(function(e,t){t.startsWith(o)&&l.append(t.slice(o.length),e)}),l;case"i":return to(e,n=n.slice(2),t,r,tc);case"I":return 1/0;case"-":return"$-0"===n?-0:-1/0;case"N":return NaN;case"u":return;case"D":return new Date(Date.parse(n.slice(2)));case"n":return BigInt(n.slice(2))}switch(n[1]){case"A":return th(e,n,ArrayBuffer,1,t,r);case"O":return th(e,n,Int8Array,1,t,r);case"o":return th(e,n,Uint8Array,1,t,r);case"U":return th(e,n,Uint8ClampedArray,1,t,r);case"S":return th(e,n,Int16Array,2,t,r);case"s":return th(e,n,Uint16Array,2,t,r);case"L":return th(e,n,Int32Array,4,t,r);case"l":return th(e,n,Uint32Array,4,t,r);case"G":return th(e,n,Float32Array,4,t,r);case"g":return th(e,n,Float64Array,8,t,r);case"M":return th(e,n,BigInt64Array,8,t,r);case"m":return th(e,n,BigUint64Array,8,t,r);case"V":return th(e,n,DataView,1,t,r);case"B":return t=parseInt(n.slice(2),16),e._formData.get(e._prefix+t)}switch(n[1]){case"R":return tp(e,n,void 0);case"r":return tp(e,n,"bytes");case"X":return tm(e,n,!1);case"x":return tm(e,n,!0)}return to(e,n=n.slice(1),t,r,td)}return n}(t,r,n,i,s);if("object"==typeof i&&null!==i)if(void 0!==s&&void 0!==t._temporaryReferences&&t._temporaryReferences.set(i,s),Array.isArray(i))for(var a=0;a<i.length;a++)i[a]=e(t,i,""+a,i[a],void 0!==s?s+":"+a:void 0);else for(a in i)e2.call(i,a)&&(r=void 0!==s&&-1===a.indexOf(":")?s+":"+a:void 0,void 0!==(r=e(t,i,a,i[a],r))?i[a]=r:delete i[a]);return i}(e._response,{"":s},"",s,n);if(null!==tt&&0<tt.deps)tt.value=a,e.status="blocked";else{var o=e.value;e.status="fulfilled",e.value=a,null!==o&&e9(o,a)}}catch(t){e.status="rejected",e.reason=t}finally{te=t,tt=r}}function tn(e,t){e._closed=!0,e._closedReason=t,e._chunks.forEach(function(e){"pending"===e.status&&e5(e,t)})}function ti(e,t){var r=e._chunks,n=r.get(t);return n||(n=null!=(n=e._formData.get(e._prefix+t))?new e4("resolved_model",n,t,e):e._closed?new e4("rejected",null,e._closedReason,e):e3(e),r.set(t,n)),n}function ts(e,t,r,n,i,s,a){if(tt){var o=tt;n||o.deps++}else o=tt={deps:+!n,value:null};return function(n){for(var l=1;l<a.length;l++)n=n[a[l]];t[r]=s(i,n),""===r&&null===o.value&&(o.value=t[r]),o.deps--,0===o.deps&&"blocked"===e.status&&(n=e.value,e.status="fulfilled",e.value=o.value,null!==n&&e9(n,o.value))}}function ta(e){return function(t){return e5(e,t)}}function to(e,t,r,n,i){var s=parseInt((t=t.split(":"))[0],16);switch("resolved_model"===(s=ti(e,s)).status&&tr(s),s.status){case"fulfilled":for(n=1,r=s.value;n<t.length;n++)r=r[t[n]];return i(e,r);case"pending":case"blocked":case"cyclic":var a=te;return s.then(ts(a,r,n,"cyclic"===s.status,e,i,t),ta(a)),null;default:throw s.reason}}function tl(e,t){return new Map(t)}function tu(e,t){return new Set(t)}function tc(e,t){return t[Symbol.iterator]()}function td(e,t){return t}function th(e,t,r,n,i,s){return t=parseInt(t.slice(2),16),t=e._formData.get(e._prefix+t),t=r===ArrayBuffer?t.arrayBuffer():t.arrayBuffer().then(function(e){return new r(e)}),n=te,t.then(ts(n,i,s,!1,e,td,[]),ta(n)),null}function tf(e,t,r,n){var i=e._chunks;for(r=new e4("fulfilled",r,n,e),i.set(t,r),e=e._formData.getAll(e._prefix+t),t=0;t<e.length;t++)"C"===(i=e[t])[0]?n.close("C"===i?'"$undefined"':i.slice(1)):n.enqueueModel(i)}function tp(e,t,r){t=parseInt(t.slice(2),16);var n=null;r=new ReadableStream({type:r,start:function(e){n=e}});var i=null;return tf(e,t,r,{enqueueModel:function(t){if(null===i){var r=new e4("resolved_model",t,-1,e);tr(r),"fulfilled"===r.status?n.enqueue(r.value):(r.then(function(e){return n.enqueue(e)},function(e){return n.error(e)}),i=r)}else{r=i;var s=e3(e);s.then(function(e){return n.enqueue(e)},function(e){return n.error(e)}),i=s,r.then(function(){i===s&&(i=null),e6(s,t,-1)})}},close:function(){if(null===i)n.close();else{var e=i;i=null,e.then(function(){return n.close()})}},error:function(e){if(null===i)n.error(e);else{var t=i;i=null,t.then(function(){return n.error(e)})}}}),r}function tg(){return this}function tm(e,t,r){t=parseInt(t.slice(2),16);var n=[],i=!1,s=0,a={};return a[y]=function(){var t,r=0;return(t={next:t=function(t){if(void 0!==t)throw Error("Values cannot be passed to next() of AsyncIterables passed to Client Components.");if(r===n.length){if(i)return new e4("fulfilled",{done:!0,value:void 0},null,e);n[r]=e3(e)}return n[r++]}})[y]=tg,t},tf(e,t,r=r?a[y]():a,{enqueueModel:function(t){s===n.length?n[s]=e8(e,t,!1):e7(n[s],t,!1),s++},close:function(t){for(i=!0,s===n.length?n[s]=e8(e,t,!0):e7(n[s],t,!0),s++;s<n.length;)e7(n[s++],'"$undefined"',!0)},error:function(t){for(i=!0,s===n.length&&(n[s]=e3(e));s<n.length;)e5(n[s++],t)}}),r}function ty(e,t,r){var n=3<arguments.length&&void 0!==arguments[3]?arguments[3]:new FormData;return{_bundlerConfig:e,_prefix:t,_formData:n,_chunks:new Map,_closed:!1,_closedReason:null,_temporaryReferences:r}}function tv(e){tn(e,Error("Connection closed."))}function tb(e,t,r){var n=eZ(e,t);return e=e0(n),r?Promise.all([r,e]).then(function(e){e=e[0];var t=e1(n);return t.bind.apply(t,[null].concat(e))}):e?Promise.resolve(e).then(function(){return e1(n)}):Promise.resolve(e1(n))}function t_(e,t,r){if(tv(e=ty(t,r,void 0,e)),(e=ti(e,0)).then(function(){}),"fulfilled"!==e.status)throw e.reason;return e.value}t.createClientModuleProxy=function(e){return new Proxy(e=P({},e,!1),M)},t.createTemporaryReferenceSet=function(){return new WeakMap},t.decodeAction=function(e,t){var r=new FormData,n=null;return e.forEach(function(i,s){s.startsWith("$ACTION_")?s.startsWith("$ACTION_REF_")?(i=t_(e,t,i="$ACTION_"+s.slice(12)+":"),n=tb(t,i.id,i.bound)):s.startsWith("$ACTION_ID_")&&(n=tb(t,i=s.slice(11),null)):r.append(s,i)}),null===n?null:n.then(function(e){return e.bind(null,r)})},t.decodeFormState=function(e,t,r){var n=t.get("$ACTION_KEY");if("string"!=typeof n)return Promise.resolve(null);var i=null;if(t.forEach(function(e,n){n.startsWith("$ACTION_REF_")&&(i=t_(t,r,"$ACTION_"+n.slice(12)+":"))}),null===i)return Promise.resolve(null);var s=i.id;return Promise.resolve(i.bound).then(function(t){return null===t?null:[e,n,s,t.length-1]})},t.decodeReply=function(e,t,r){if("string"==typeof e){var n=new FormData;n.append("0",e),e=n}return t=ti(e=ty(t,"",r?r.temporaryReferences:void 0,e),0),tv(e),t},t.decodeReplyFromAsyncIterable=function(e,t,r){function n(e){tn(s,e),"function"==typeof i.throw&&i.throw(e).then(n,n)}var i=e[y](),s=ty(t,"",r?r.temporaryReferences:void 0);return i.next().then(function e(t){if(t.done)tv(s);else{var r=(t=t.value)[0];if("string"==typeof(t=t[1])){s._formData.append(r,t);var a=s._prefix;if(r.startsWith(a)){var o=s._chunks;r=+r.slice(a.length),(o=o.get(r))&&e6(o,t,r)}}else s._formData.append(r,t);i.next().then(e,n)}},n),ti(s,0)},t.registerClientReference=function(e,t,r){return P(e,t+"#"+r,!1)},t.registerServerReference=function(e,t,r){return Object.defineProperties(e,{$$typeof:{value:T},$$id:{value:null===r?t:t+"#"+r,configurable:!0},$$bound:{value:null,configurable:!0},bind:{value:I,configurable:!0}})};let tw="function"==typeof globalThis.setImmediate&&globalThis.propertyIsEnumerable("setImmediate")?globalThis.setImmediate:setTimeout;t.renderToReadableStream=function(e,t,r){var n=new eg(20,e,t,r?r.onError:void 0,r?r.identifierPrefix:void 0,r?r.onPostpone:void 0,r?r.temporaryReferences:void 0,void 0,void 0,em,em);if(r&&r.signal){var i=r.signal;if(i.aborted)eX(n,i.reason);else{var s=function(){eX(n,i.reason),i.removeEventListener("abort",s)};i.addEventListener("abort",s)}}return new ReadableStream({type:"bytes",start:function(){eG(n)},pull:function(e){eW(n,e)},cancel:function(e){n.destination=null,eX(n,e)}},{highWaterMark:0})},t.unstable_prerender=function(e,t,r){return new Promise(function(n,i){var s=new eg(21,e,t,r?r.onError:void 0,r?r.identifierPrefix:void 0,r?r.onPostpone:void 0,r?r.temporaryReferences:void 0,void 0,void 0,function(){n({prelude:new ReadableStream({type:"bytes",start:function(){eG(s)},pull:function(e){eW(s,e)},cancel:function(e){s.destination=null,eX(s,e)}},{highWaterMark:0})})},i);if(r&&r.signal){var a=r.signal;if(a.aborted)eX(s,a.reason);else{var o=function(){eX(s,a.reason),a.removeEventListener("abort",o)};a.addEventListener("abort",o)}}eG(s)})}},7472:(e,t,r)=>{"use strict";r.d(t,{C:()=>o,Y:()=>i});var n=r(9908);async function i(e,t){if(!e)return t();let r=s(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),n=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,s(e));await o(e,t)}}function s(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function a(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let i=(0,n.a1)();if(i)for(let t of i)r.push(t.expireTags(...e));await Promise.all(r)}async function o(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],n=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},i=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([a(r,e.incrementalCache),...Object.values(n),...i])}},7612:(e,t,r)=>{},7753:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});class n extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}},7779:(e,t,r)=>{"use strict";r.d(t,{CB:()=>n,Yq:()=>i,l_:()=>s});class n extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class i extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class s extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},7855:e=>{!function(){"use strict";var t={114:function(e){function t(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}function r(e,t){for(var r,n="",i=0,s=-1,a=0,o=0;o<=e.length;++o){if(o<e.length)r=e.charCodeAt(o);else if(47===r)break;else r=47;if(47===r){if(s===o-1||1===a);else if(s!==o-1&&2===a){if(n.length<2||2!==i||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2)){if(n.length>2){var l=n.lastIndexOf("/");if(l!==n.length-1){-1===l?(n="",i=0):i=(n=n.slice(0,l)).length-1-n.lastIndexOf("/"),s=o,a=0;continue}}else if(2===n.length||1===n.length){n="",i=0,s=o,a=0;continue}}t&&(n.length>0?n+="/..":n="..",i=2)}else n.length>0?n+="/"+e.slice(s+1,o):n=e.slice(s+1,o),i=o-s-1;s=o,a=0}else 46===r&&-1!==a?++a:a=-1}return n}var n={resolve:function(){for(var e,n,i="",s=!1,a=arguments.length-1;a>=-1&&!s;a--)a>=0?n=arguments[a]:(void 0===e&&(e=""),n=e),t(n),0!==n.length&&(i=n+"/"+i,s=47===n.charCodeAt(0));if(i=r(i,!s),s)if(i.length>0)return"/"+i;else return"/";return i.length>0?i:"."},normalize:function(e){if(t(e),0===e.length)return".";var n=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return(0!==(e=r(e,!n)).length||n||(e="."),e.length>0&&i&&(e+="/"),n)?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0==arguments.length)return".";for(var e,r=0;r<arguments.length;++r){var i=arguments[r];t(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":n.normalize(e)},relative:function(e,r){if(t(e),t(r),e===r||(e=n.resolve(e))===(r=n.resolve(r)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var s=e.length,a=s-i,o=1;o<r.length&&47===r.charCodeAt(o);++o);for(var l=r.length-o,u=a<l?a:l,c=-1,d=0;d<=u;++d){if(d===u){if(l>u){if(47===r.charCodeAt(o+d))return r.slice(o+d+1);else if(0===d)return r.slice(o+d)}else a>u&&(47===e.charCodeAt(i+d)?c=d:0===d&&(c=0));break}var h=e.charCodeAt(i+d);if(h!==r.charCodeAt(o+d))break;47===h&&(c=d)}var f="";for(d=i+c+1;d<=s;++d)(d===s||47===e.charCodeAt(d))&&(0===f.length?f+="..":f+="/..");return f.length>0?f+r.slice(o+c):(o+=c,47===r.charCodeAt(o)&&++o,r.slice(o))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var r=e.charCodeAt(0),n=47===r,i=-1,s=!0,a=e.length-1;a>=1;--a)if(47===(r=e.charCodeAt(a))){if(!s){i=a;break}}else s=!1;return -1===i?n?"/":".":n&&1===i?"//":e.slice(0,i)},basename:function(e,r){if(void 0!==r&&"string"!=typeof r)throw TypeError('"ext" argument must be a string');t(e);var n,i=0,s=-1,a=!0;if(void 0!==r&&r.length>0&&r.length<=e.length){if(r.length===e.length&&r===e)return"";var o=r.length-1,l=-1;for(n=e.length-1;n>=0;--n){var u=e.charCodeAt(n);if(47===u){if(!a){i=n+1;break}}else -1===l&&(a=!1,l=n+1),o>=0&&(u===r.charCodeAt(o)?-1==--o&&(s=n):(o=-1,s=l))}return i===s?s=l:-1===s&&(s=e.length),e.slice(i,s)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!a){i=n+1;break}}else -1===s&&(a=!1,s=n+1);return -1===s?"":e.slice(i,s)},extname:function(e){t(e);for(var r=-1,n=0,i=-1,s=!0,a=0,o=e.length-1;o>=0;--o){var l=e.charCodeAt(o);if(47===l){if(!s){n=o+1;break}continue}-1===i&&(s=!1,i=o+1),46===l?-1===r?r=o:1!==a&&(a=1):-1!==r&&(a=-1)}return -1===r||-1===i||0===a||1===a&&r===i-1&&r===n+1?"":e.slice(r,i)},format:function(e){var t,r;if(null===e||"object"!=typeof e)throw TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return t=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||""),t?t===e.root?t+r:t+"/"+r:r},parse:function(e){t(e);var r,n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var i=e.charCodeAt(0),s=47===i;s?(n.root="/",r=1):r=0;for(var a=-1,o=0,l=-1,u=!0,c=e.length-1,d=0;c>=r;--c){if(47===(i=e.charCodeAt(c))){if(!u){o=c+1;break}continue}-1===l&&(u=!1,l=c+1),46===i?-1===a?a=c:1!==d&&(d=1):-1!==a&&(d=-1)}return -1===a||-1===l||0===d||1===d&&a===l-1&&a===o+1?-1!==l&&(0===o&&s?n.base=n.name=e.slice(1,l):n.base=n.name=e.slice(o,l)):(0===o&&s?(n.name=e.slice(1,a),n.base=e.slice(1,l)):(n.name=e.slice(o,a),n.base=e.slice(o,l)),n.ext=e.slice(a,l)),o>0?n.dir=e.slice(0,o-1):s&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};n.posix=n,e.exports=n}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,n),a=!1}finally{a&&delete r[e]}return s.exports}n.ab="//",e.exports=n(114)}()},7903:(e,t,r)=>{"use strict";r.d(t,{l:()=>o});var n=r(3543),i=r(9908),s=r(6116);let a=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let n=r.slice(0,e).join("/");n&&(n.endsWith("/page")||n.endsWith("/route")||(n=`${n}${!n.endsWith("/")?"/":""}layout`),t.push(n))}}return t};async function o(e,t,r){let o=[],l=r&&r.size>0;for(let t of a(e))t=`${n.gW}${t}`,o.push(t);if(t.pathname&&!l){let e=`${n.gW}${t.pathname}`;o.push(e)}return{tags:o,expirationsByCacheKind:function(e){let t=new Map,r=(0,i.fs)();if(r)for(let[n,i]of r)"getExpiration"in i&&t.set(n,(0,s.a)(async()=>i.getExpiration(...e)));return t}(o)}}},7935:(e,t,r)=>{"use strict";function n(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}r.d(t,{R:()=>n})},8062:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return s},wrapRequestHandler:function(){return a}});let n=r(1438),i=r(5565);function s(){return(0,i.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,n.withRequest)(t,i.reader,()=>e(t,r))}},8123:(e,t,r)=>{"use strict";let n=Symbol.for("NextInternalRequestMeta");r(3936),r(6804);r(897),r(5455)},8335:()=>{},8622:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",s=r+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[s++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=s;continue}if("("===n){var o=1,l="",s=r+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '+s);for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at "+s);l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=s;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,s=void 0===n?"./":n,a="[^"+i(t.delimiter||"/#?")+"]+?",o=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},f=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var p=d("CHAR"),g=d("NAME"),m=d("PATTERN");if(g||m){var y=p||"";-1===s.indexOf(y)&&(c+=y,y=""),c&&(o.push(c),c=""),o.push({name:g||l++,prefix:y,suffix:"",pattern:m||a,modifier:d("MODIFIER")||""});continue}var v=p||d("ESCAPED_CHAR");if(v){c+=v;continue}if(c&&(o.push(c),c=""),d("OPEN")){var y=f(),b=d("NAME")||"",_=d("PATTERN")||"",w=f();h("CLOSE"),o.push({name:b||(_?l++:""),pattern:b&&!_?a:_,prefix:y,suffix:w,modifier:d("MODIFIER")||""});continue}h("END")}return o}function r(e,t){void 0===t&&(t={});var r=s(t),n=t.encode,i=void 0===n?function(e){return e}:n,a=t.validate,o=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var s=e[n];if("string"==typeof s){r+=s;continue}var a=t?t[s.name]:void 0,u="?"===s.modifier||"*"===s.modifier,c="*"===s.modifier||"+"===s.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+s.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+s.name+'" to not be empty')}for(var d=0;d<a.length;d++){var h=i(a[d],s);if(o&&!l[n].test(h))throw TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but got "'+h+'"');r+=s.prefix+h+s.suffix}continue}if("string"==typeof a||"number"==typeof a){var h=i(String(a),s);if(o&&!l[n].test(h))throw TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but got "'+h+'"');r+=s.prefix+h+s.suffix;continue}if(!u){var f=c?"an array":"a string";throw TypeError('Expected "'+s.name+'" to be '+f)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var s=n[0],a=n.index,o=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?o[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):o[r.name]=i(n[e],r)}}(l);return{path:s,index:a,params:o}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(e){return e&&e.sensitive?"":"i"}function a(e,t,r){void 0===r&&(r={});for(var n=r.strict,a=void 0!==n&&n,o=r.start,l=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,d="["+i(r.endsWith||"")+"]|$",h="["+i(r.delimiter||"/#?")+"]",f=void 0===o||o?"^":"",p=0;p<e.length;p++){var g=e[p];if("string"==typeof g)f+=i(c(g));else{var m=i(c(g.prefix)),y=i(c(g.suffix));if(g.pattern)if(t&&t.push(g),m||y)if("+"===g.modifier||"*"===g.modifier){var v="*"===g.modifier?"?":"";f+="(?:"+m+"((?:"+g.pattern+")(?:"+y+m+"(?:"+g.pattern+"))*)"+y+")"+v}else f+="(?:"+m+"("+g.pattern+")"+y+")"+g.modifier;else f+="("+g.pattern+")"+g.modifier;else f+="(?:"+m+y+")"+g.modifier}}if(void 0===l||l)a||(f+=h+"?"),f+=r.endsWith?"(?="+d+")":"$";else{var b=e[e.length-1],_="string"==typeof b?h.indexOf(b[b.length-1])>-1:void 0===b;a||(f+="(?:"+h+"(?="+d+"))?"),_||(f+="(?="+h+"|"+d+")")}return new RegExp(f,s(r))}function o(t,r,n){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,r,n).source}).join("|")+")",s(n)):a(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(o(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=a,t.pathToRegexp=o})(),e.exports=t})()},8730:(e,t,r)=>{"use strict";var n=r(1092);function i(){}var s={d:{f:i,r:function(){throw Error("Invalid form element. requestFormReset must be passed a form that was rendered by React.")},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null};if(!n.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');function a(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,t.preconnect=function(e,t){"string"==typeof e&&(t=t?"string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:null,s.d.C(e,t))},t.prefetchDNS=function(e){"string"==typeof e&&s.d.D(e)},t.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var r=t.as,n=a(r,t.crossOrigin),i="string"==typeof t.integrity?t.integrity:void 0,o="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===r?s.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:n,integrity:i,fetchPriority:o}):"script"===r&&s.d.X(e,{crossOrigin:n,integrity:i,fetchPriority:o,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var r=a(t.as,t.crossOrigin);s.d.M(e,{crossOrigin:r,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&s.d.M(e)},t.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var r=t.as,n=a(r,t.crossOrigin);s.d.L(e,r,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"==typeof e)if(t){var r=a(t.as,t.crossOrigin);s.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:r,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else s.d.m(e)},t.version="19.2.0-canary-3fbfb9ba-20250409"},9055:(e,t,r)=>{"use strict";function n(e){return e.replace(/\/$/,"")||"/"}r.d(t,{U:()=>n})},9574:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=n(r(96));class s{constructor(e,{headers:t={},schema:r,fetch:n}){this.url=e,this.headers=t,this.schema=r,this.fetch=n}select(e,{head:t=!1,count:r}={}){let n=!1,s=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!n?"":('"'===e&&(n=!n),e)).join("");return this.url.searchParams.set("select",s),r&&(this.headers.Prefer=`count=${r}`),new i.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){let n=[];if(this.headers.Prefer&&n.push(this.headers.Prefer),t&&n.push(`count=${t}`),r||n.push("missing=default"),this.headers.Prefer=n.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new i.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:n,defaultToNull:s=!0}={}){let a=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&a.push(this.headers.Prefer),n&&a.push(`count=${n}`),s||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new i.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new i.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new i.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=s},9691:(e,t,r)=>{"use strict";r.d(t,{X:()=>h});var n=r(9055),i=r(7935);function s(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:s}=(0,i.R)(e);return""+t+r+n+s}function a(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:s}=(0,i.R)(e);return""+r+t+n+s}var o=r(4181),l=r(5951);let u=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function c(e,t){return new URL(String(e).replace(u,"localhost"),t&&String(t).replace(u,"localhost"))}let d=Symbol("NextURLInternal");class h{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[d]={url:c(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let s=function(e,t){var r,n;let{basePath:i,i18n:s,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},u={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};i&&(0,o.m)(u.pathname,i)&&(u.pathname=function(e,t){if(!(0,o.m)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(u.pathname,i),u.basePath=i);let c=u.pathname;if(u.pathname.startsWith("/_next/data/")&&u.pathname.endsWith(".json")){let e=u.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");u.buildId=e[0],c="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(u.pathname=c)}if(s){let e=t.i18nProvider?t.i18nProvider.analyze(u.pathname):(0,l.d)(u.pathname,s.locales);u.locale=e.detectedLocale,u.pathname=null!=(n=e.pathname)?n:u.pathname,!e.detectedLocale&&u.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(c):(0,l.d)(c,s.locales)).detectedLocale&&(u.locale=e.detectedLocale)}return u}(this[d].url.pathname,{nextConfig:this[d].options.nextConfig,parseData:!0,i18nProvider:this[d].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[d].url,this[d].options.headers);this[d].domainLocale=this[d].options.i18nProvider?this[d].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let s of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=s.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===s.defaultLocale.toLowerCase()||(null==(i=s.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return s}}(null==(t=this[d].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,a);let u=(null==(r=this[d].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[d].options.nextConfig)||null==(n=i.i18n)?void 0:n.defaultLocale);this[d].url.pathname=s.pathname,this[d].defaultLocale=u,this[d].basePath=s.basePath??"",this[d].buildId=s.buildId,this[d].locale=s.locale??u,this[d].trailingSlash=s.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let i=e.toLowerCase();return!n&&((0,o.m)(i,"/api")||(0,o.m)(i,"/"+t.toLowerCase()))?e:s(e,"/"+t)}((e={basePath:this[d].basePath,buildId:this[d].buildId,defaultLocale:this[d].options.forceLocale?void 0:this[d].defaultLocale,locale:this[d].locale,pathname:this[d].url.pathname,trailingSlash:this[d].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=(0,n.U)(t)),e.buildId&&(t=a(s(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=s(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:a(t,"/"):(0,n.U)(t)}formatSearch(){return this[d].url.search}get buildId(){return this[d].buildId}set buildId(e){this[d].buildId=e}get locale(){return this[d].locale??""}set locale(e){var t,r;if(!this[d].locale||!(null==(r=this[d].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[d].locale=e}get defaultLocale(){return this[d].defaultLocale}get domainLocale(){return this[d].domainLocale}get searchParams(){return this[d].url.searchParams}get host(){return this[d].url.host}set host(e){this[d].url.host=e}get hostname(){return this[d].url.hostname}set hostname(e){this[d].url.hostname=e}get port(){return this[d].url.port}set port(e){this[d].url.port=e}get protocol(){return this[d].url.protocol}set protocol(e){this[d].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[d].url=c(e),this.analyze()}get origin(){return this[d].url.origin}get pathname(){return this[d].url.pathname}set pathname(e){this[d].url.pathname=e}get hash(){return this[d].url.hash}set hash(e){this[d].url.hash=e}get search(){return this[d].url.search}set search(e){this[d].url.search=e}get password(){return this[d].url.password}set password(e){this[d].url.password=e}get username(){return this[d].url.username}set username(e){this[d].url.username=e}get basePath(){return this[d].basePath}set basePath(e){this[d].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new h(String(this),this[d].options)}}},9908:(e,t,r)=>{"use strict";r.d(t,{fs:()=>l,a1:()=>o});var n=r(50);r(6640),r(5356).Buffer,new n.q(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let i=Symbol.for("@next/cache-handlers-map"),s=Symbol.for("@next/cache-handlers-set"),a=globalThis;function o(){if(a[s])return a[s].values()}function l(){if(a[i])return a[i].entries()}}},e=>{var t=e(e.s=4525);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/external/v1/chat/completions/route"]=t}]);
//# sourceMappingURL=route.js.map