"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/leac";
exports.ids = ["vendor-chunks/leac"];
exports.modules = {

/***/ "(rsc)/../node_modules/leac/lib/leac.mjs":
/*!*****************************************!*\
  !*** ../node_modules/leac/lib/leac.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLexer: () => (/* binding */ o)\n/* harmony export */ });\nconst e=/\\n/g;function n(n){const o=[...n.matchAll(e)].map((e=>e.index||0));o.unshift(-1);const s=t(o,0,o.length);return e=>r(s,e)}function t(e,n,r){if(r-n==1)return{offset:e[n],index:n+1};const o=Math.ceil((n+r)/2),s=t(e,n,o),l=t(e,o,r);return{offset:s.offset,low:s,high:l}}function r(e,n){return function(e){return Object.prototype.hasOwnProperty.call(e,\"index\")}(e)?{line:e.index,column:n-e.offset}:r(e.high.offset<n?e.high:e.low,n)}function o(e,t=\"\",r={}){const o=\"string\"!=typeof t?t:r,l=\"string\"==typeof t?t:\"\",c=e.map(s),f=!!o.lineNumbers;return function(e,t=0){const r=f?n(e):()=>({line:0,column:0});let o=t;const s=[];e:for(;o<e.length;){let n=!1;for(const t of c){t.regex.lastIndex=o;const c=t.regex.exec(e);if(c&&c[0].length>0){if(!t.discard){const e=r(o),n=\"string\"==typeof t.replace?c[0].replace(new RegExp(t.regex.source,t.regex.flags),t.replace):c[0];s.push({state:l,name:t.name,text:n,offset:o,len:c[0].length,line:e.line,column:e.column})}if(o=t.regex.lastIndex,n=!0,t.push){const n=t.push(e,o);s.push(...n.tokens),o=n.offset}if(t.pop)break e;break}}if(!n)break}return{tokens:s,offset:o,complete:e.length<=o}}}function s(e,n){return{...e,regex:l(e,n)}}function l(e,n){if(0===e.name.length)throw new Error(`Rule #${n} has empty name, which is not allowed.`);if(function(e){return Object.prototype.hasOwnProperty.call(e,\"regex\")}(e))return function(e){if(e.global)throw new Error(`Regular expression /${e.source}/${e.flags} contains the global flag, which is not allowed.`);return e.sticky?e:new RegExp(e.source,e.flags+\"y\")}(e.regex);if(function(e){return Object.prototype.hasOwnProperty.call(e,\"str\")}(e)){if(0===e.str.length)throw new Error(`Rule #${n} (\"${e.name}\") has empty \"str\" property, which is not allowed.`);return new RegExp(c(e.str),\"y\")}return new RegExp(c(e.name),\"y\")}function c(e){return e.replace(/[-[\\]{}()*+!<=:?./\\\\^$|#\\s,]/g,\"\\\\$&\")}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/leac/lib/leac.mjs\n");

/***/ })

};
;