(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4089],{29755:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var a=t(95155),r=t(12115),n=t(35695),l=t(6874),i=t.n(l),c=t(66766),o=t(55020),d=t(6865),u=t(10184),m=t(48987),x=t(52589),h=t(21394),b=t(52643),f=t(64198);function p(){let[e,s]=(0,r.useState)(""),[t,l]=(0,r.useState)(""),[p,w]=(0,r.useState)(!1),[g,j]=(0,r.useState)(!1),[y,N]=(0,r.useState)(!1),[v,k]=(0,r.useState)(""),[P,S]=(0,r.useState)(!1),[A,_]=(0,r.useState)(!0),[C,z]=(0,r.useState)(!1),E=(0,n.useRouter)(),I=(0,n.useSearchParams)(),R=(0,b.createSupabaseBrowserClient)(),{success:q,error:O}=(0,f.dj)();(0,r.useEffect)(()=>{(async()=>{try{let e=I.get("access_token"),s=I.get("refresh_token"),t=I.get("type");if(!e||!s||"recovery"!==t){k("Invalid or expired password reset link. Please request a new one."),_(!1);return}let{data:a,error:r}=await R.auth.setSession({access_token:e,refresh_token:s});r?k("Invalid or expired password reset link. Please request a new one."):a.session?z(!0):k("Unable to validate reset link. Please try again.")}catch(e){k("An error occurred while validating the reset link.")}finally{_(!1)}})()},[I,R.auth]);let Y=e=>e.length<8?"Password must be at least 8 characters long":/(?=.*[a-z])/.test(e)?/(?=.*[A-Z])/.test(e)?/(?=.*\d)/.test(e)?null:"Password must contain at least one number":"Password must contain at least one uppercase letter":"Password must contain at least one lowercase letter",U=async s=>{s.preventDefault(),N(!0),k("");let a=Y(e);if(a){k(a),N(!1);return}if(e!==t){k("Passwords do not match"),N(!1);return}try{let{error:s}=await R.auth.updateUser({password:e});if(s)throw s;S(!0),q("Password updated successfully!","You can now sign in with your new password."),setTimeout(()=>{E.push("/auth/signin?message=password_reset_success")},3e3)}catch(e){k(e.message||"Failed to reset password. Please try again."),O("Password reset failed",e.message||"Please try again.")}finally{N(!1)}};return A?(0,a.jsxs)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:[(0,a.jsx)(h.A,{gridSize:40,opacity:.03,color:"#000000",variant:"subtle",className:"absolute inset-0"}),(0,a.jsxs)("div",{className:"relative z-10 text-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Validating reset link..."})]})]}):C?P?(0,a.jsxs)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:[(0,a.jsx)(h.A,{gridSize:40,opacity:.03,color:"#000000",variant:"subtle",className:"absolute inset-0"}),(0,a.jsxs)("div",{className:"relative z-10 max-w-md mx-auto text-center p-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(d.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Password Reset Successful!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"Your password has been updated successfully. You will be redirected to the sign-in page shortly."}),(0,a.jsx)(i(),{href:"/auth/signin",className:"inline-flex items-center justify-center w-full bg-[#ff6b35] text-white py-3 px-4 rounded-xl font-medium hover:bg-[#e55a2b] transition-colors",children:"Continue to Sign In"})]})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:[(0,a.jsx)(h.A,{gridSize:40,opacity:.03,color:"#000000",variant:"subtle",className:"absolute inset-0"}),(0,a.jsx)("div",{className:"relative z-10 w-full max-w-md mx-auto p-8",children:(0,a.jsxs)(o.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"bg-white rounded-2xl shadow-xl border border-gray-100 p-8",children:[(0,a.jsx)("div",{className:"text-center mb-8",children:(0,a.jsx)(i(),{href:"/",className:"inline-block",children:(0,a.jsx)(c.default,{src:"/logo.png",alt:"RouKey",width:120,height:40,className:"mx-auto",priority:!0})})}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-black mb-2",children:"Reset Your Password"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Enter your new password below"})]}),v&&(0,a.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:v})}),(0,a.jsxs)("form",{onSubmit:U,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"password",type:p?"text":"password",value:e,onChange:e=>s(e.target.value),placeholder:"Enter your new password",className:"w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent transition-all duration-200",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>w(!p),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:p?(0,a.jsx)(m.A,{className:"h-5 w-5"}):(0,a.jsx)(u.A,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"confirmPassword",type:g?"text":"password",value:t,onChange:e=>l(e.target.value),placeholder:"Confirm your new password",className:"w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent transition-all duration-200",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>j(!g),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:g?(0,a.jsx)(m.A,{className:"h-5 w-5"}):(0,a.jsx)(u.A,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[(0,a.jsx)("p",{children:"Password must contain:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-2",children:[(0,a.jsx)("li",{children:"At least 8 characters"}),(0,a.jsx)("li",{children:"One uppercase letter"}),(0,a.jsx)("li",{children:"One lowercase letter"}),(0,a.jsx)("li",{children:"One number"})]})]}),(0,a.jsx)("button",{type:"submit",disabled:y,className:"w-full bg-[#ff6b35] text-white py-3 px-4 rounded-xl font-medium hover:bg-[#e55a2b] focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:y?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Updating Password..."]}):"Update Password"})]}),(0,a.jsx)("div",{className:"text-center mt-6",children:(0,a.jsx)(i(),{href:"/auth/signin",className:"text-gray-600 hover:text-gray-800 text-sm transition-colors",children:"← Back to Sign In"})})]})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:[(0,a.jsx)(h.A,{gridSize:40,opacity:.03,color:"#000000",variant:"subtle",className:"absolute inset-0"}),(0,a.jsxs)("div",{className:"relative z-10 max-w-md mx-auto text-center p-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)(x.A,{className:"h-8 w-8 text-red-600"})}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Invalid Reset Link"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:v}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(i(),{href:"/auth/signin",className:"block w-full bg-[#ff6b35] text-white py-3 px-4 rounded-xl font-medium hover:bg-[#e55a2b] transition-colors",children:"Back to Sign In"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Need a new reset link?"," ",(0,a.jsx)(i(),{href:"/auth/signin",className:"text-[#ff6b35] hover:text-[#e55a2b] font-medium",children:"Request password reset"})]})]})]})]})}function w(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,a.jsx)(p,{})})}},90492:(e,s,t)=>{Promise.resolve().then(t.bind(t,29755))}},e=>{var s=s=>e(e.s=s);e.O(0,[7125,8888,1459,5738,2396,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(90492)),_N_E=e.O()}]);