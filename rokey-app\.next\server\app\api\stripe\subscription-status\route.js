(()=>{var e={};e.id=6782,e.ids=[6782],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},25282:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>_,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var r={};s.r(r),s.d(r,{GET:()=>c,POST:()=>p});var i=s(96559),n=s(48088),a=s(37719),o=s(32190);let u=(0,s(39398).createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function c(e){try{let{searchParams:t}=new URL(e.url),s=t.get("userId");if(!s)return o.NextResponse.json({error:"Missing userId parameter"},{status:400});if("true"===process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE)return o.NextResponse.json({hasActiveSubscription:!0,tier:"free",status:"active",currentPeriodEnd:null,cancelAtPeriodEnd:!1,isFree:!0,fallback:!0,message:"Using fallback due to network connectivity issues"});let{data:r,error:i}=await u.from("subscriptions").select("*").eq("user_id",s).order("created_at",{ascending:!1}).limit(1).single(),{data:n,error:a}=await u.from("user_profiles").select("subscription_tier, updated_at").eq("id",s).single();if(a)return o.NextResponse.json({error:"User profile not found"},{status:404});if(i||!r){let e=n.subscription_tier||"free";return o.NextResponse.json({hasActiveSubscription:"free"!==e,tier:e,status:null,currentPeriodEnd:null,cancelAtPeriodEnd:!1,isFree:"free"===e})}let c="active"===r.status,p=new Date(r.current_period_end)<new Date;return o.NextResponse.json({hasActiveSubscription:c&&!p,tier:r.tier,status:r.status,currentPeriodEnd:r.current_period_end,currentPeriodStart:r.current_period_start,cancelAtPeriodEnd:r.cancel_at_period_end,stripeCustomerId:r.stripe_customer_id,stripeSubscriptionId:r.stripe_subscription_id,isFree:r.is_free_tier||!1})}catch(e){if(e instanceof Error&&(e.message.includes("fetch failed")||e.message.includes("network")))return o.NextResponse.json({hasActiveSubscription:!0,tier:"free",status:"active",currentPeriodEnd:null,cancelAtPeriodEnd:!1,isFree:!0,fallback:!0,message:"Using fallback due to network error"});return o.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e){try{let t,{userId:s}=await e.json();if(!s)return o.NextResponse.json({error:"Missing userId"},{status:400});if("true"===process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE)return o.NextResponse.json({tier:"free",usage:{configurations:0,apiKeys:0,apiRequests:0},limits:d("free"),canCreateConfig:!0,canCreateApiKey:!0,fallback:!0,message:"Using fallback due to network connectivity issues"});let r=new Date().toISOString().slice(0,7),{data:i,error:n}=await u.from("usage_tracking").select("*").eq("user_id",s).eq("month_year",r).single(),{data:a,error:c}=await u.from("subscriptions").select("tier, status, updated_at, stripe_subscription_id").eq("user_id",s).eq("status","active").order("created_at",{ascending:!1}).limit(1).single(),p=null;if(c){let{data:e}=await u.from("subscriptions").select("tier, status, updated_at, stripe_subscription_id").eq("user_id",s).order("updated_at",{ascending:!1}).limit(1).single();p=e}let{data:l,error:g}=await u.from("user_profiles").select("subscription_tier, updated_at").eq("id",s).single(),{data:f}=await u.from("subscriptions").select("tier, status, updated_at, stripe_subscription_id").eq("user_id",s).order("created_at",{ascending:!1});t=a?.tier&&a?.status==="active"?a.tier:p?.tier&&l?.subscription_tier===p.tier?p.tier:l?.subscription_tier||"free";let{count:_}=await u.from("custom_api_configs").select("*",{count:"exact",head:!0}).eq("user_id",s),{count:m}=await u.from("api_keys").select("*",{count:"exact",head:!0}).eq("user_id",s),x=d(t);return o.NextResponse.json({tier:t,usage:{configurations:_||0,apiKeys:m||0,apiRequests:i?.api_requests_count||0},limits:x,canCreateConfig:(_||0)<x.configurations,canCreateApiKey:(m||0)<x.apiKeysPerConfig})}catch(e){if(e instanceof Error&&(e.message.includes("fetch failed")||e.message.includes("network")))return o.NextResponse.json({tier:"free",usage:{configurations:0,apiKeys:0,apiRequests:0},limits:d("free"),canCreateConfig:!0,canCreateApiKey:!0,fallback:!0,message:"Using fallback due to network error"});return o.NextResponse.json({error:"Internal server error"},{status:500})}}function d(e){switch(e){case"free":default:return{configurations:1,apiKeysPerConfig:3,apiRequests:999999,canUseAdvancedRouting:!1,canUseCustomRoles:!1,maxCustomRoles:0,canUsePromptEngineering:!1,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1};case"starter":return{configurations:15,apiKeysPerConfig:5,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:3,canUsePromptEngineering:!0,canUseKnowledgeBase:!1,knowledgeBaseDocuments:0,canUseSemanticCaching:!1};case"professional":return{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:5,canUseSemanticCaching:!0};case"enterprise":return{configurations:999999,apiKeysPerConfig:999999,apiRequests:999999,canUseAdvancedRouting:!0,canUseCustomRoles:!0,maxCustomRoles:999999,canUsePromptEngineering:!0,canUseKnowledgeBase:!0,knowledgeBaseDocuments:999999,canUseSemanticCaching:!0}}}let l=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/stripe/subscription-status/route",pathname:"/api/stripe/subscription-status",filename:"route",bundlePath:"app/api/stripe/subscription-status/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\stripe\\subscription-status\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:_}=l;function m(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,9398],()=>s(25282));module.exports=r})();