(()=>{var e={};e.id=7522,e.ids=[7522],e.modules={1923:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["billing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,39616)),"C:\\Ro<PERSON>ey App\\rokey-app\\src\\app\\billing\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\RoKey App\\rokey-app\\src\\app\\billing\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/billing/page",pathname:"/billing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2643:(e,t,r)=>{"use strict";r.d(t,{$:()=>n});var s=r(60687),a=r(43210),i=r(9776);let n=(0,a.forwardRef)(({className:e="",variant:t="default",size:r="default",loading:a=!1,icon:n,iconPosition:l="left",children:o,disabled:c,...d},m)=>{let u={default:"h-5 w-5",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",icon:"h-5 w-5"},x=c||a;return(0,s.jsxs)("button",{ref:m,className:`inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed ${{default:"bg-orange-600 text-white hover:bg-orange-700",primary:"bg-orange-600 text-white hover:bg-orange-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500",destructive:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500",danger:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500"}[t]} ${{default:"px-4 py-2.5 text-sm",sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-6 py-3 text-base",icon:"h-10 w-10"}[r]} ${e}`,disabled:x,...d,children:[a&&(0,s.jsx)(i.Ay,{size:"lg"===r?"md":"sm",className:"mr-2"}),!a&&n&&"left"===l&&(0,s.jsx)("span",{className:`${u[r]} mr-2`,children:n}),o,!a&&n&&"right"===l&&(0,s.jsx)("span",{className:`${u[r]} ml-2`,children:n})]})});n.displayName="Button"},2969:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9776:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a,B0:()=>i});var s=r(60687);function a({size:e="md",className:t=""}){return(0,s.jsx)("div",{className:`animate-spin rounded-full border-2 border-gray-600 border-t-indigo-500 ${{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[e]} ${t}`})}function i({className:e=""}){return(0,s.jsx)("div",{className:`glass rounded-2xl p-6 animate-pulse ${e}`,children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl"}),(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),(0,s.jsx)("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20404:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(43210);function a(){let[e,t]=(0,s.useState)({isOpen:!1,isLoading:!1,title:"",message:"",confirmText:"Confirm",cancelText:"Cancel",type:"danger",onConfirm:()=>{}}),r=(0,s.useCallback)((e,r)=>{t({isOpen:!0,isLoading:!1,title:e.title,message:e.message,confirmText:e.confirmText||"Confirm",cancelText:e.cancelText||"Cancel",type:e.type||"danger",onConfirm:async()=>{t(e=>({...e,isLoading:!0}));try{await r(),t(e=>({...e,isOpen:!1,isLoading:!1}))}catch(e){throw t(e=>({...e,isLoading:!1})),e}}})},[]),a=(0,s.useCallback)(()=>{t(e=>({...e,isOpen:!1,isLoading:!1}))},[]);return{...e,showConfirmation:r,hideConfirmation:a}}},26403:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29605:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>j});class s{constructor(e=0,t="Network Error"){this.status=e,this.text=t}}let a={origin:"https://api.emailjs.com",blockHeadless:!1,storageProvider:(()=>{if("undefined"!=typeof localStorage)return{get:e=>Promise.resolve(localStorage.getItem(e)),set:(e,t)=>Promise.resolve(localStorage.setItem(e,t)),remove:e=>Promise.resolve(localStorage.removeItem(e))}})()},i=e=>e?"string"==typeof e?{publicKey:e}:"[object Object]"===e.toString()?e:{}:{},n=async(e,t,r={})=>{let i=await fetch(a.origin+e,{method:"POST",headers:r,body:t}),n=await i.text(),l=new s(i.status,n);if(i.ok)return l;throw l},l=(e,t,r)=>{if(!e||"string"!=typeof e)throw"The public key is required. Visit https://dashboard.emailjs.com/admin/account";if(!t||"string"!=typeof t)throw"The service ID is required. Visit https://dashboard.emailjs.com/admin";if(!r||"string"!=typeof r)throw"The template ID is required. Visit https://dashboard.emailjs.com/admin/templates"},o=e=>{if(e&&"[object Object]"!==e.toString())throw"The template params have to be the object. Visit https://www.emailjs.com/docs/sdk/send/"},c=e=>e.webdriver||!e.languages||0===e.languages.length,d=()=>new s(451,"Unavailable For Headless Browser"),m=(e,t)=>{if(!Array.isArray(e))throw"The BlockList list has to be an array";if("string"!=typeof t)throw"The BlockList watchVariable has to be a string"},u=e=>!e.list?.length||!e.watchVariable,x=(e,t)=>e instanceof FormData?e.get(t):e[t],p=(e,t)=>{if(u(e))return!1;m(e.list,e.watchVariable);let r=x(t,e.watchVariable);return"string"==typeof r&&e.list.includes(r)},g=()=>new s(403,"Forbidden"),h=(e,t)=>{if("number"!=typeof e||e<0)throw"The LimitRate throttle has to be a positive number";if(t&&"string"!=typeof t)throw"The LimitRate ID has to be a non-empty string"},b=async(e,t,r)=>{let s=Number(await r.get(e)||0);return t-Date.now()+s},f=async(e,t,r)=>{if(!t.throttle||!r)return!1;h(t.throttle,t.id);let s=t.id||e;return await b(s,t.throttle,r)>0||(await r.set(s,Date.now().toString()),!1)},y=()=>new s(429,"Too Many Requests"),v=e=>{if(!e||"FORM"!==e.nodeName)throw"The 3rd parameter is expected to be the HTML form element or the style selector of the form"},w=e=>"string"==typeof e?document.querySelector(e):e,j={init:(e,t="https://api.emailjs.com")=>{if(!e)return;let r=i(e);a.publicKey=r.publicKey,a.blockHeadless=r.blockHeadless,a.storageProvider=r.storageProvider,a.blockList=r.blockList,a.limitRate=r.limitRate,a.origin=r.origin||t},send:async(e,t,r,s)=>{let m=i(s),u=m.publicKey||a.publicKey,x=m.blockHeadless||a.blockHeadless,h=m.storageProvider||a.storageProvider,b={...a.blockList,...m.blockList},v={...a.limitRate,...m.limitRate};return x&&c(navigator)?Promise.reject(d()):(l(u,e,t),o(r),r&&p(b,r))?Promise.reject(g()):await f(location.pathname,v,h)?Promise.reject(y()):n("/api/v1.0/email/send",JSON.stringify({lib_version:"4.4.1",user_id:u,service_id:e,template_id:t,template_params:r}),{"Content-type":"application/json"})},sendForm:async(e,t,r,s)=>{let o=i(s),m=o.publicKey||a.publicKey,u=o.blockHeadless||a.blockHeadless,x=a.storageProvider||o.storageProvider,h={...a.blockList,...o.blockList},b={...a.limitRate,...o.limitRate};if(u&&c(navigator))return Promise.reject(d());let j=w(r);l(m,e,t),v(j);let N=new FormData(j);return p(h,N)?Promise.reject(g()):await f(location.pathname,b,x)?Promise.reject(y()):(N.append("lib_version","4.4.1"),N.append("service_id",e),N.append("template_id",t),N.append("user_id",m),n("/api/v1.0/email/send-form",N))},EmailJSResponseStatus:s}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36900:(e,t,r)=>{Promise.resolve().then(r.bind(r,39616))},39616:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\billing\\page.tsx","default")},44516:(e,t,r)=>{Promise.resolve().then(r.bind(r,58538))},50181:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(60687);r(43210);var a=r(26403),i=r(59168),n=r(81836);function l({isOpen:e,onClose:t,onConfirm:r,title:l,message:o,confirmText:c="Delete",cancelText:d="Cancel",type:m="danger",isLoading:u=!1}){let x=(()=>{switch(m){case"danger":return{iconBg:"bg-red-100",iconColor:"text-red-600",confirmButton:"bg-red-600 hover:bg-red-700 focus:ring-red-500",icon:a.A};case"warning":return{iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmButton:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",icon:i.A};default:return{iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmButton:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",icon:i.A}}})(),p=x.icon;return e?(0,s.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300",onClick:u?void 0:t}),(0,s.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 scale-100 opacity-100",children:[(0,s.jsx)("div",{className:"relative px-6 pt-6",children:(0,s.jsx)("button",{onClick:t,disabled:u,className:"absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,s.jsx)(n.A,{className:"h-5 w-5"})})}),(0,s.jsxs)("div",{className:"px-6 pb-6",children:[(0,s.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full mb-6",children:(0,s.jsx)("div",{className:`${x.iconBg} rounded-full p-3`,children:(0,s.jsx)(p,{className:`h-8 w-8 ${x.iconColor}`})})}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 text-center",children:l}),(0,s.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-8 text-center",children:o}),(0,s.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3",children:[(0,s.jsx)("button",{type:"button",onClick:t,disabled:u,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:d}),(0,s.jsx)("button",{type:"button",onClick:r,disabled:u,className:`w-full sm:w-auto px-6 py-3 text-sm font-medium text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ${x.confirmButton}`,children:u?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):c})]})]})]})})]}):null}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58538:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(60687),a=r(43210),i=r(16189),n=r(724),l=r(2969),o=r(58089),c=r(81521),d=r(70149),m=r(59168),u=r(64859),x=r(2643),p=r(52581),g=r(11016),h=r(50181);r(313);var b=r(20404),f=r(29605);let y=[{id:"free",name:"Free",price:0,interval:"forever",features:[{name:"Strict fallback routing only",included:!0},{name:"Basic analytics",included:!0},{name:"No custom roles",included:!1},{name:"Configurations",included:!0,limit:"1 max"},{name:"API keys per config",included:!0,limit:"3 max"},{name:"User-generated API keys",included:!0,limit:"3 max"}]},{id:"starter",name:"Starter",price:19,interval:"month",popular:!0,features:[{name:"All routing strategies",included:!0},{name:"Advanced analytics",included:!0},{name:"Custom roles",included:!0,limit:"Up to 3 roles"},{name:"Configurations",included:!0,limit:"5 max"},{name:"API keys per config",included:!0,limit:"15 max"},{name:"User-generated API keys",included:!0,limit:"50 max"},{name:"Browsing tasks",included:!0,limit:"15/month"}]},{id:"professional",name:"Professional",price:49,interval:"month",features:[{name:"Everything in Starter",included:!0},{name:"Unlimited configurations",included:!0},{name:"Unlimited API keys per config",included:!0},{name:"Unlimited user-generated API keys",included:!0},{name:"Knowledge base documents",included:!0,limit:"5 documents"},{name:"Priority support",included:!0}]},{id:"enterprise",name:"Enterprise",price:149,interval:"month",features:[{name:"Everything in Professional",included:!0},{name:"Unlimited knowledge base documents",included:!0},{name:"Advanced semantic caching",included:!0},{name:"Custom integrations",included:!0},{name:"Dedicated support + phone",included:!0},{name:"SLA guarantee",included:!0}]}];function v(){(0,i.useRouter)(),(0,i.useSearchParams)();let{user:e,subscriptionStatus:t,refreshStatus:r,createCheckoutSession:v,openCustomerPortal:w}=(0,g.R)(),j=(0,b.Z)(),[N,k]=(0,a.useState)(!1),[P,A]=(0,a.useState)(!1),[C,S]=(0,a.useState)(""),[_,R]=(0,a.useState)(""),[L,T]=(0,a.useState)(null),q=y.find(e=>e.id===t?.tier)||y[0],B=(0,a.useMemo)(()=>{if(t?.tier==="free"||!t?.currentPeriodEnd)return null;let e=new Date(t.currentPeriodEnd),r=new Date,s=Math.ceil((e.getTime()-r.getTime())/864e5);return s>0?s:null},[t?.tier,t?.currentPeriodEnd]),$=async()=>{try{k(!0);let e=t?.tier||"free",r=`${window.location.origin}/billing?portal_return=true&prev_tier=${e}`;await w(r)}catch(e){e.message.includes("No configuration provided")||e.message.includes("default configuration has not been created")?p.oR.error("Billing portal is being set up. Please contact support for plan changes."):p.oR.error("Failed to open billing portal. Please try again."),k(!1)}},E=async()=>{if(!C.trim())return void p.oR.error("Please select a reason for cancellation");k(!0);try{let t={user_email:e?.email||"Unknown",user_name:e?.user_metadata?.first_name||"User",current_plan:q.name,cancel_reason:C,additional_feedback:_,cancel_date:new Date().toLocaleDateString()};await f.Ay.send("service_2xtn7iv","template_pg7e1af",t,"lm7-ATth2Cql60KIN"),await new Promise(e=>setTimeout(e,1500)),p.oR.success("Subscription cancelled successfully. We've sent your feedback to our team."),A(!1),S(""),R(""),await r()}catch(e){p.oR.error("Failed to cancel subscription. Please contact support.")}finally{k(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,s.jsx)("div",{className:"border-b border-gray-800/50",children:(0,s.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Billing & Plans"}),(0,s.jsx)("div",{className:"flex items-center space-x-1",children:(0,s.jsx)("div",{className:"px-3 py-1 text-sm bg-cyan-500 text-white rounded",children:"Subscription"})})]}),(0,s.jsx)("div",{className:"flex items-center space-x-4"})]})})}),(0,s.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8",children:[(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6 hover:shadow-lg transition-all duration-200",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-2 bg-cyan-500/10 rounded-lg",children:(0,s.jsx)(n.A,{className:"h-5 w-5 text-cyan-400"})}),(0,s.jsx)("h2",{className:"text-lg font-semibold text-white",children:"Current Plan"})]}),t?.tier!=="free"&&(0,s.jsx)(x.$,{variant:"outline",onClick:()=>A(!0),className:"text-red-400 border-red-500/20 hover:bg-red-500/10 hover:border-red-500/40 transition-all duration-200 text-sm",size:"sm",children:"Cancel Subscription"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-3",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white",children:q.name}),q.popular&&(0,s.jsx)("span",{className:"px-2 py-1 text-xs bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full font-semibold",children:"⭐ Popular"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-white",children:0===q.price?(0,s.jsx)("span",{className:"text-green-400",children:"Free"}):(0,s.jsxs)("span",{children:["$",q.price,(0,s.jsxs)("span",{className:"text-gray-400 text-lg font-normal",children:["/",q.interval]})]})}),0===q.price&&(0,s.jsx)("p",{className:"text-green-400 font-medium",children:"Forever free"})]})]}),(0,s.jsx)("div",{className:"flex flex-col justify-center",children:B&&(0,s.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 text-sm text-gray-300 mb-1",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Next Billing"})]}),(0,s.jsxs)("div",{className:"text-white font-semibold",children:[B," days"]})]})})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-500/10 rounded-lg",children:(0,s.jsx)(o.A,{className:"h-5 w-5 text-blue-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Plan Features"})]}),(0,s.jsx)("div",{className:"space-y-4",children:q.features.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-3 py-2",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:e.included?(0,s.jsx)("div",{className:"p-1 bg-green-500/10 rounded-full",children:(0,s.jsx)(o.A,{className:"h-4 w-4 text-green-400"})}):(0,s.jsx)("div",{className:"p-1 bg-gray-700/50 rounded-full",children:(0,s.jsx)(c.A,{className:"h-4 w-4 text-gray-500"})})}),(0,s.jsxs)("span",{className:`text-sm ${e.included?"text-gray-300":"text-gray-500"}`,children:[e.name,e.limit&&(0,s.jsxs)("span",{className:"text-gray-400 font-medium",children:[" (",e.limit,")"]})]})]},t))})]}),(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,s.jsx)("div",{className:"p-2 bg-orange-500/10 rounded-lg",children:(0,s.jsx)(n.A,{className:"h-5 w-5 text-orange-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Manage Subscription"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center p-4 bg-gray-800/30 rounded-lg",children:[(0,s.jsxs)("div",{className:"inline-flex items-center gap-2 bg-green-500/10 text-green-400 px-3 py-1 rounded-full text-sm font-semibold mb-3 border border-green-500/20",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),"Active Plan"]}),(0,s.jsx)("h4",{className:"text-xl font-bold text-white mb-1",children:q.name}),(0,s.jsx)("div",{className:"text-2xl font-bold text-white",children:0===q.price?(0,s.jsx)("span",{className:"text-green-400",children:"Free"}):(0,s.jsxs)("span",{children:["$",q.price,(0,s.jsxs)("span",{className:"text-gray-400 text-base font-normal",children:["/",q.interval]})]})})]}),(0,s.jsx)(x.$,{onClick:$,disabled:N,className:"w-full bg-gradient-to-r from-cyan-500 to-cyan-600 hover:from-cyan-600 hover:to-cyan-700 text-white shadow-lg hover:shadow-xl transition-all duration-200",size:"lg",children:t?.tier==="free"?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"h-5 w-5 mr-2"}),"Upgrade Plan"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.A,{className:"h-5 w-5 mr-2"}),"Manage Subscription"]})}),(0,s.jsx)("p",{className:"text-center text-sm text-gray-400",children:t?.tier==="free"?"Unlock advanced features and higher limits":"Change plans, update billing, or cancel anytime"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,s.jsx)("div",{className:"p-2 bg-green-500/10 rounded-lg",children:(0,s.jsx)(n.A,{className:"h-5 w-5 text-green-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Billing Details"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-700/30",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Plan"}),(0,s.jsx)("span",{className:"font-medium text-white",children:q.name})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-700/30",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Billing Cycle"}),(0,s.jsx)("span",{className:"font-medium text-white",children:0===q.price?"N/A":"Monthly"})]}),B&&(0,s.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-700/30",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Next Billing"}),(0,s.jsx)("span",{className:"font-medium text-white",children:new Date(Date.now()+24*B*36e5).toLocaleDateString()})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,s.jsx)("span",{className:"text-gray-400 text-sm",children:"Status"}),(0,s.jsx)("span",{className:`px-2 py-1 text-xs rounded-full font-medium ${t?.tier==="free"?"bg-gray-700/50 text-gray-300":"bg-green-500/10 text-green-400"}`,children:t?.tier==="free"?"Free Plan":"Active"})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,s.jsx)("div",{className:"p-2 bg-purple-500/10 rounded-lg",children:(0,s.jsx)(m.A,{className:"h-5 w-5 text-purple-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Support"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("p",{className:"text-gray-300 text-sm",children:"Need help with your subscription or billing? We're here to assist you."}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(x.$,{variant:"outline",className:"w-full justify-start text-gray-300 border-gray-600 hover:bg-gray-800/50 hover:border-gray-500 text-sm",onClick:()=>window.open("/contact","_blank"),children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Contact Support"]}),(0,s.jsx)("div",{className:"text-xs text-gray-400 text-center",children:"Response time: Usually within 24 hours"})]})]})]})]}),P&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,s.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm border border-gray-800/50 rounded-xl shadow-2xl max-w-md w-full p-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,s.jsx)("div",{className:"p-2 bg-red-500/10 rounded-lg",children:(0,s.jsx)(m.A,{className:"h-5 w-5 text-red-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Cancel Subscription"})]}),(0,s.jsx)("p",{className:"text-gray-300 mb-6 text-sm",children:"We're sorry to see you go! Please help us improve by telling us why you're cancelling."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"Reason for cancellation *"}),(0,s.jsxs)("select",{value:C,onChange:e=>S(e.target.value),className:"w-full p-3 border border-gray-600 rounded-lg bg-gray-800 text-white focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-sm",children:[(0,s.jsx)("option",{value:"",children:"Select a reason..."}),["Too expensive","Not using enough features","Found a better alternative","Technical issues","Poor customer support","Missing features I need","Temporary financial constraints","Other"].map(e=>(0,s.jsx)("option",{value:e,children:e},e))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"Additional feedback (optional)"}),(0,s.jsx)("textarea",{value:_,onChange:e=>R(e.target.value),placeholder:"Tell us more about your experience or what we could do better...",rows:3,className:"w-full p-3 border border-gray-600 rounded-lg bg-gray-800 text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 text-sm"})]})]}),(0,s.jsxs)("div",{className:"flex gap-3 mt-6",children:[(0,s.jsx)(x.$,{variant:"outline",onClick:()=>A(!1),className:"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50 text-sm",children:"Keep Subscription"}),(0,s.jsx)(x.$,{onClick:E,disabled:N||!C.trim(),className:"flex-1 bg-red-600 hover:bg-red-700 text-white text-sm",children:N?"Cancelling...":"Cancel Subscription"})]})]})}),(0,s.jsx)(h.A,{isOpen:j.isOpen,onClose:j.hideConfirmation,onConfirm:j.onConfirm,title:j.title,message:j.message,confirmText:j.confirmText,cancelText:j.cancelText,type:j.type,isLoading:j.isLoading})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64859:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5449,8769,313,4912],()=>r(1923));module.exports=s})();