(()=>{var e={};e.id=9280,e.ids=[9280],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14689:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24245:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"}))})},25224:(e,t,r)=>{Promise.resolve().then(r.bind(r,40872))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38376:(e,t,r)=>{Promise.resolve().then(r.bind(r,66108))},40872:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var s=r(60687),a=r(43210),i=r(16189),n=r(97450),o=r(14689),l=r(85198),d=r(49579),c=r(51426),u=r(65963),m=r(24245),x=r(57891),p=r(30922);let h=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"}))});var f=r(52535);function b({workflow:e,onEdit:t,onDuplicate:r,onDelete:i,onRefreshApiKey:x,onTestInPlayground:p}){let[h,b]=(0,a.useState)(null),[g,w]=(0,a.useState)(!1),[y,v]=(0,a.useState)(!1),j=async()=>{if(h?.key_prefix)try{let e=`${h.key_prefix}${"*".repeat(32)}`;await navigator.clipboard.writeText(e),v(!0),setTimeout(()=>v(!1),2e3)}catch(e){}},k=async()=>{if(x){w(!0);try{await x(e.id);let t=await fetch(`/api/workflows?workflowId=${e.id}`);if(t.ok){let e=await t.json();b(e.api_key_info)}}catch(e){}finally{w(!1)}}};return(0,s.jsxs)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-2},className:"bg-gradient-to-br from-gray-900/60 to-gray-800/40 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 hover:border-[#ff6b35]/30 transition-all duration-300 group",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-2 group-hover:text-[#ff6b35] transition-colors",children:e.name}),(0,s.jsx)("p",{className:"text-gray-400 text-sm line-clamp-2",children:e.description||"No description provided"})]}),(0,s.jsx)("div",{className:"flex items-center gap-2 ml-4",children:(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${e.is_active?"bg-green-500/20 text-green-400 border border-green-500/30":"bg-gray-500/20 text-gray-400 border border-gray-500/30"}`,children:e.is_active?"Active":"Inactive"})})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[(0,s.jsxs)("span",{children:[e.node_count||0," nodes"]}),(0,s.jsxs)("span",{children:["Updated ",new Date(e.updated_at).toLocaleDateString()]})]}),h&&(0,s.jsxs)("div",{className:"bg-gray-900/30 rounded-lg p-3 mb-4 border border-gray-700/30",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.A,{className:"w-4 h-4 text-[#ff6b35]"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-300",children:"API Key"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("button",{onClick:j,className:"text-xs text-[#ff6b35] hover:text-[#e55a2b] transition-colors flex items-center gap-1",title:"Copy API Key Prefix",children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"w-3 h-3"}),"Copied!"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.A,{className:"w-3 h-3"}),"Copy"]})}),x&&(0,s.jsxs)("button",{onClick:k,disabled:g,className:"text-xs text-gray-400 hover:text-gray-300 transition-colors flex items-center gap-1 ml-2",title:"Generate New API Key",children:[(0,s.jsx)(d.A,{className:`w-3 h-3 ${g?"animate-spin":""}`}),"New"]})]})]}),(0,s.jsxs)("div",{className:"font-mono text-xs text-green-400 bg-gray-800/50 rounded px-2 py-1",children:[h.key_prefix,"*".repeat(32)]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-2 text-xs text-gray-500",children:[(0,s.jsxs)("span",{children:["Requests: ",h.total_requests||0]}),(0,s.jsx)("span",{children:h.last_used_at?`Last used: ${new Date(h.last_used_at).toLocaleDateString()}`:"Never used"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("button",{onClick:()=>t(e.id),className:"flex-1 bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2",children:[(0,s.jsx)(c.A,{className:"w-4 h-4"}),"Edit"]}),(0,s.jsx)("button",{onClick:()=>r(e.id),className:"bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors duration-200",title:"Duplicate",children:(0,s.jsx)(u.A,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>p(e.id),className:"bg-green-600 hover:bg-green-500 text-white px-3 py-2 rounded-lg transition-colors duration-200",title:"Test in Playground",children:(0,s.jsx)(m.A,{className:"w-4 h-4"})})]})]})}function g({template:e,onUse:t}){return(0,s.jsxs)(f.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-2},className:"bg-gradient-to-br from-blue-900/40 to-blue-800/20 backdrop-blur-sm border border-blue-700/50 rounded-xl p-6 hover:border-blue-500/50 transition-all duration-300 group",children:[(0,s.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white group-hover:text-blue-400 transition-colors",children:e.name}),e.is_official&&(0,s.jsx)("span",{className:"bg-blue-500/20 text-blue-400 border border-blue-500/30 px-2 py-0.5 rounded-full text-xs font-medium",children:"Official"})]}),(0,s.jsx)("p",{className:"text-gray-400 text-sm line-clamp-2",children:e.description})]})}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[(0,s.jsx)("span",{className:"bg-gray-700/50 px-2 py-1 rounded text-xs",children:e.category}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("span",{children:["⭐ ",e.rating.toFixed(1)]}),(0,s.jsxs)("span",{children:["↓ ",e.download_count]})]})]}),(0,s.jsx)("button",{onClick:()=>t(e.id),className:"w-full bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200",children:"Use Template"})]})}function w(){let e=(0,i.useRouter)(),[t,r]=(0,a.useState)([]),[n,o]=(0,a.useState)([]),[l,d]=(0,a.useState)(!0),[c,m]=(0,a.useState)(""),[f,w]=(0,a.useState)("workflows"),y=async()=>{try{let e=await fetch("/api/workflows");if(e.ok){let t=await e.json();r(t.workflows||[])}}catch(e){}},v=()=>{e.push("/manual-build/new")},j=t=>{e.push(`/manual-build/${t}`)},k=e=>{window.open(`/playground?workflow=${e}`,"_blank")},N=async e=>{},C=async e=>{if(confirm("Are you sure you want to delete this workflow? This action cannot be undone."))try{let t=await fetch(`/api/workflows?id=${e}`,{method:"DELETE"});if(t.ok)y(),alert("Workflow deleted successfully");else{let e=await t.json();throw Error(e.details||"Failed to delete workflow")}}catch(e){alert(`Failed to delete workflow: ${e instanceof Error?e.message:"Unknown error"}`)}},A=t=>{e.push(`/manual-build/new?template=${t}`)},E=async e=>{try{alert("API key regeneration will be implemented in a future update.")}catch(e){alert("Failed to refresh API key. Please try again.")}},_=t.filter(e=>e.name.toLowerCase().includes(c.toLowerCase())||e.description?.toLowerCase().includes(c.toLowerCase())),L=n.filter(e=>e.name.toLowerCase().includes(c.toLowerCase())||e.description.toLowerCase().includes(c.toLowerCase())||e.category.toLowerCase().includes(c.toLowerCase()));return l?(0,s.jsx)("div",{className:"min-h-screen bg-[#040716] flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-white",children:"Loading..."})}):(0,s.jsx)("div",{className:"min-h-screen bg-[#040716] p-6",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Manual Build"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Create custom AI workflows with visual node-based editor"})]}),(0,s.jsxs)("button",{onClick:v,className:"bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"w-5 h-5"}),"Create New Workflow"]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("div",{className:"flex items-center gap-4",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(p.A,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Search workflows and templates...",value:c,onChange:e=>m(e.target.value),className:"bg-gray-800/50 border border-gray-700/50 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:border-[#ff6b35]/50 transition-colors w-80"})]})}),(0,s.jsxs)("div",{className:"flex bg-gray-800/50 rounded-lg p-1",children:[(0,s.jsxs)("button",{onClick:()=>w("workflows"),className:`px-4 py-2 rounded-md font-medium transition-colors ${"workflows"===f?"bg-[#ff6b35] text-white":"text-gray-400 hover:text-white"}`,children:[(0,s.jsx)(h,{className:"w-4 h-4 inline mr-2"}),"My Workflows"]}),(0,s.jsxs)("button",{onClick:()=>w("templates"),className:`px-4 py-2 rounded-md font-medium transition-colors ${"templates"===f?"bg-[#ff6b35] text-white":"text-gray-400 hover:text-white"}`,children:[(0,s.jsx)(u.A,{className:"w-4 h-4 inline mr-2"}),"Templates"]})]})]}),"workflows"===f?(0,s.jsx)("div",{children:0===_.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(h,{className:"w-16 h-16 text-gray-600 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No workflows yet"}),(0,s.jsx)("p",{className:"text-gray-400 mb-6",children:"Create your first visual workflow to get started"}),(0,s.jsxs)("button",{onClick:v,className:"bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 inline-flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"w-5 h-5"}),"Create New Workflow"]})]}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:_.map(e=>(0,s.jsx)(b,{workflow:e,onEdit:j,onDuplicate:N,onDelete:C,onRefreshApiKey:E,onTestInPlayground:k},e.id))})}):(0,s.jsx)("div",{children:0===L.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(u.A,{className:"w-16 h-16 text-gray-600 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No templates found"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Try adjusting your search or check back later"})]}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:L.map(e=>(0,s.jsx)(g,{template:e,onUse:A},e.id))})})]})})}},41891:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["manual-build",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,66108)),"C:\\RoKey App\\rokey-app\\src\\app\\manual-build\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\manual-build\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/manual-build/page",pathname:"/manual-build",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},49579:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57891:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65963:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"}))})},66108:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\manual-build\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85198:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 7.5V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6V4.5c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v12c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V16.5a9 9 0 0 0-9-9Z"}))})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5449,2535,4912],()=>r(41891));module.exports=s})();