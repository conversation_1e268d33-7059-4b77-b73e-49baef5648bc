import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { sendWelcomeEmail } from '@/lib/email/welcomeEmail';

/**
 * Process welcome email queue - can be called by cron job or manually
 * This endpoint processes pending welcome emails from the database queue
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);
    
    // Optional: Add API key authentication for cron jobs
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.ROKEY_API_ACCESS_TOKEN;
    
    if (expectedToken && authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🔄 WELCOME-QUEUE: Processing welcome email queue...');

    // Get pending welcome emails (limit to 10 per batch to avoid timeouts)
    const { data: pendingEmails, error: fetchError } = await supabase
      .from('welcome_email_queue')
      .select('*')
      .eq('sent', false)
      .order('created_at', { ascending: true })
      .limit(10);

    if (fetchError) {
      console.error('❌ WELCOME-QUEUE: Error fetching pending emails:', fetchError);
      return NextResponse.json({ error: 'Database error' }, { status: 500 });
    }

    if (!pendingEmails || pendingEmails.length === 0) {
      console.log('✅ WELCOME-QUEUE: No pending emails to process');
      return NextResponse.json({ 
        success: true, 
        message: 'No pending emails',
        processed: 0 
      });
    }

    console.log(`📧 WELCOME-QUEUE: Found ${pendingEmails.length} pending emails`);

    let successCount = 0;
    let errorCount = 0;

    // Process each email
    for (const emailRecord of pendingEmails) {
      try {
        const success = await sendWelcomeEmail({
          userEmail: emailRecord.user_email,
          userName: emailRecord.user_name,
          userTier: emailRecord.user_tier
        });

        if (success) {
          // Mark as sent
          await supabase
            .from('welcome_email_queue')
            .update({ 
              sent: true, 
              sent_at: new Date().toISOString(),
              error_message: null 
            })
            .eq('id', emailRecord.id);
          
          successCount++;
          console.log(`✅ WELCOME-QUEUE: Sent welcome email to ${emailRecord.user_email}`);
        } else {
          // Mark error
          await supabase
            .from('welcome_email_queue')
            .update({ 
              error_message: 'Failed to send email via EmailJS',
              sent: false 
            })
            .eq('id', emailRecord.id);
          
          errorCount++;
          console.error(`❌ WELCOME-QUEUE: Failed to send to ${emailRecord.user_email}`);
        }
      } catch (error) {
        // Mark error with details
        await supabase
          .from('welcome_email_queue')
          .update({ 
            error_message: error instanceof Error ? error.message : 'Unknown error',
            sent: false 
          })
          .eq('id', emailRecord.id);
        
        errorCount++;
        console.error(`❌ WELCOME-QUEUE: Error processing ${emailRecord.user_email}:`, error);
      }

      // Small delay between emails to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`🎉 WELCOME-QUEUE: Processed ${successCount + errorCount} emails (${successCount} success, ${errorCount} errors)`);

    return NextResponse.json({
      success: true,
      processed: successCount + errorCount,
      successful: successCount,
      errors: errorCount
    });

  } catch (error) {
    console.error('❌ WELCOME-QUEUE: Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to check queue status
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);
    
    // Get queue statistics
    const { data: stats, error } = await supabase
      .from('welcome_email_queue')
      .select('sent, created_at')
      .order('created_at', { ascending: false })
      .limit(100);

    if (error) {
      return NextResponse.json({ error: 'Database error' }, { status: 500 });
    }

    const pending = stats?.filter(s => !s.sent).length || 0;
    const sent = stats?.filter(s => s.sent).length || 0;
    const total = stats?.length || 0;

    return NextResponse.json({
      queue_status: {
        pending,
        sent,
        total,
        last_24h: stats?.filter(s => 
          new Date(s.created_at) > new Date(Date.now() - 24 * 60 * 60 * 1000)
        ).length || 0
      }
    });

  } catch (error) {
    console.error('❌ WELCOME-QUEUE: Error getting stats:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
