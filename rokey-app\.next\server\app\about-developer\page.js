(()=>{var e={};e.id=7471,e.ids=[7471],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14832:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}))})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21078:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,metadata:()=>a});let a={title:"About the Developer - Okoro David Chukwunyerem | RouKey",description:"Meet Okoro David Chukwunyerem, the solo developer behind RouKey. Learn about his journey from startup failure to building the ultimate AI routing platform.",keywords:"Okoro David Chukwunyerem, RouKey developer, AI gateway founder, solo developer, startup journey",openGraph:{title:"About the Developer - Okoro David Chukwunyerem | RouKey",description:"Meet the solo developer behind RouKey - from startup failure to building the ultimate AI routing platform.",type:"website",url:"https://roukey.online/about-developer",images:[{url:"/founder.jpg",width:1200,height:630,alt:"Okoro David Chukwunyerem - RouKey Founder"}]},twitter:{card:"summary_large_image",title:"About the Developer - Okoro David Chukwunyerem | RouKey",description:"Meet the solo developer behind RouKey - from startup failure to building the ultimate AI routing platform.",images:["/founder.jpg"]}};function s({children:e}){return e}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56878:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(60687),s=r(76180),n=r.n(s);function o({className:e="",gridSize:t=40,opacity:r=.1,color:s="#000000",animated:o=!1,glowEffect:i=!1,variant:l="subtle"}){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{style:{...(()=>{let e=(e,t)=>"#000000"===e?`rgba(0, 0, 0, ${t})`:"#ffffff"===e?`rgba(255, 255, 255, ${t})`:"#ff6b35"===e?`rgba(255, 107, 53, ${t})`:`${e}${Math.round(255*t).toString(16).padStart(2,"0")}`,a=3.2*r*.8;switch(l){case"tech":return{backgroundImage:`
            linear-gradient(${e(s,a)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,a)} 1px, transparent 1px),
            radial-gradient(circle at 50% 50%, ${e(s,.5*a)} 2px, transparent 2px)
          `,backgroundSize:`${t}px ${t}px, ${t}px ${t}px, ${4*t}px ${4*t}px`,animation:o?"tech-grid-move 30s linear infinite":"none",mask:`
            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),
            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),
            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)
          `,WebkitMaskComposite:"source-in"};case"premium":return{backgroundImage:`
            linear-gradient(${e(s,a)} 0.5px, transparent 0.5px),
            linear-gradient(90deg, ${e(s,a)} 0.5px, transparent 0.5px),
            linear-gradient(${e(s,.7*a)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,.7*a)} 1px, transparent 1px)
          `,backgroundSize:`${t}px ${t}px, ${t}px ${t}px, ${5*t}px ${5*t}px, ${5*t}px ${5*t}px`,animation:o?"premium-grid-float 40s ease-in-out infinite":"none",mask:`
            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),
            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),
            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),
            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),
            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)
          `,WebkitMaskComposite:"source-in"};default:return{backgroundImage:`
            linear-gradient(${e(s,a)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,a)} 1px, transparent 1px)
          `,backgroundSize:`${t}px ${t}px`,animation:o?"subtle-grid-drift 25s linear infinite":"none",mask:`
            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),
            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),
            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)
          `,WebkitMaskComposite:"source-in"}}})(),zIndex:1,filter:i?"drop-shadow(0 0 10px rgba(255, 107, 53, 0.1))":"none"},className:n().dynamic([["cdf0235daf430a20",[t,t,.5*t,.3*t,t,.7*t,.3*t,t,.2*t,-.1*t,.1*t,.2*t,-.1*t,.1*t]]])+" "+`absolute inset-0 pointer-events-none ${e}`}),(0,a.jsx)(n(),{id:"cdf0235daf430a20",dynamic:[t,t,.5*t,.3*t,t,.7*t,.3*t,t,.2*t,-.1*t,.1*t,.2*t,-.1*t,.1*t],children:`@-webkit-keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-moz-keyframes subtle-grid-drift{0%{-moz-transform:translate(0,0);transform:translate(0,0)}100%{-moz-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-o-keyframes subtle-grid-drift{0%{-o-transform:translate(0,0);transform:translate(0,0)}100%{-o-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);-moz-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(${t}px,${t}px);-moz-transform:translate(${t}px,${t}px);-o-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-webkit-keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-webkit-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-webkit-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-moz-keyframes tech-grid-move{0%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-moz-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-moz-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-moz-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-o-keyframes tech-grid-move{0%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-o-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-o-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-o-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);-moz-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);-o-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-webkit-transform:translate(${t}px,${.7*t}px)rotate(0deg);-moz-transform:translate(${t}px,${.7*t}px)rotate(0deg);-o-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-webkit-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);-moz-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);-o-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-webkit-keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-webkit-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-webkit-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@-moz-keyframes premium-grid-float{0%,100%{-moz-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-moz-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-moz-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-moz-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@-o-keyframes premium-grid-float{0%,100%{-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-o-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-o-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-o-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);-moz-transform:translate(0,0)scale(1);-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);-moz-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);-o-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-webkit-transform:translate(${.1*t}px,${.2*t}px)scale(.99);-moz-transform:translate(${.1*t}px,${.2*t}px)scale(.99);-o-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-webkit-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);-moz-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);-o-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}`})]})}},57663:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(60687),s=r(76180),n=r.n(s),o=r(52535),i=r(85814),l=r.n(i),d=r(62392),c=r(96374),p=r(14832),x=r(57093),m=r(17457),f=r(56878);let b=[{icon:d.A,title:"Get intelligent routing",description:"Not just any model - the RIGHT model for each task. Smart algorithms match your requests to optimal AI models automatically."},{icon:c.A,title:"Build with role-based logic",description:"Define custom roles and let RouKey intelligently route based on context. Coding, writing, analysis - each gets the perfect model."},{icon:p.A,title:"Scale without limits",description:"Unlimited requests across 300+ models with intelligent failover. Your AI infrastructure that actually thinks."}];function u(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n(),{id:"b9f7c4426befc960",children:".perspective-1000.jsx-b9f7c4426befc960{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}.rotate-y-12.jsx-b9f7c4426befc960{-webkit-transform:rotatey(-12deg)rotatex(5deg);-moz-transform:rotatey(-12deg)rotatex(5deg);-ms-transform:rotatey(-12deg)rotatex(5deg);-o-transform:rotatey(-12deg)rotatex(5deg);transform:rotatey(-12deg)rotatex(5deg);-webkit-transition:-webkit-transform.3s ease;-moz-transition:-moz-transform.3s ease;-o-transition:-o-transform.3s ease;transition:-webkit-transform.3s ease;transition:-moz-transform.3s ease;transition:-o-transform.3s ease;transition:transform.3s ease}.rotate-y-12.jsx-b9f7c4426befc960:hover{-webkit-transform:rotatey(-8deg)rotatex(2deg)scale(1.02);-moz-transform:rotatey(-8deg)rotatex(2deg)scale(1.02);-ms-transform:rotatey(-8deg)rotatex(2deg)scale(1.02);-o-transform:rotatey(-8deg)rotatex(2deg)scale(1.02);transform:rotatey(-8deg)rotatex(2deg)scale(1.02)}"}),(0,a.jsxs)("div",{className:"jsx-b9f7c4426befc960 min-h-screen bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden",children:[(0,a.jsx)(x.A,{}),(0,a.jsx)(f.A,{gridSize:45,opacity:.06,color:"#ff6b35",variant:"premium",animated:!0,className:"absolute inset-0"}),(0,a.jsxs)("main",{className:"jsx-b9f7c4426befc960 pt-20 relative z-10",children:[(0,a.jsx)("section",{className:"jsx-b9f7c4426befc960 py-20",children:(0,a.jsx)("div",{className:"jsx-b9f7c4426befc960 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"jsx-b9f7c4426befc960 grid grid-cols-1 lg:grid-cols-2 gap-16 items-center",children:[(0,a.jsx)(o.P.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"relative flex justify-center",children:(0,a.jsxs)("div",{className:"jsx-b9f7c4426befc960 relative",children:[(0,a.jsxs)("div",{className:"jsx-b9f7c4426befc960 relative bg-gradient-to-br from-blue-600 to-purple-700 rounded-3xl p-8 shadow-2xl transform perspective-1000 rotate-y-12",children:[(0,a.jsx)("div",{className:"jsx-b9f7c4426befc960 absolute inset-0 opacity-20 rounded-3xl",children:(0,a.jsx)("div",{style:{backgroundImage:`
                            linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                            linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
                          `,backgroundSize:"20px 20px"},className:"jsx-b9f7c4426befc960 w-full h-full rounded-3xl"})}),(0,a.jsx)("div",{className:"jsx-b9f7c4426befc960 relative z-10",children:(0,a.jsx)("div",{className:"jsx-b9f7c4426befc960 w-80 h-80 mx-auto rounded-2xl overflow-hidden shadow-2xl",children:(0,a.jsx)("img",{src:"/founder.jpg",alt:"Okoro David Chukwunyerem - RouKey Founder",loading:"lazy",className:"jsx-b9f7c4426befc960 w-full h-full object-cover"})})})]}),(0,a.jsx)("div",{className:"jsx-b9f7c4426befc960 mt-8 text-center",children:(0,a.jsx)("div",{className:"jsx-b9f7c4426befc960 inline-block bg-gray-800/90 backdrop-blur-sm rounded-2xl px-8 py-4 border border-gray-600/50 shadow-xl",children:(0,a.jsxs)("div",{className:"jsx-b9f7c4426befc960 text-white font-bold text-xl tracking-wide",children:["HEY, I'M ",(0,a.jsx)("span",{className:"jsx-b9f7c4426befc960 text-[#ff6b35]",children:"OKORO DAVID"})]})})})]})}),(0,a.jsx)(o.P.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"space-y-8",children:(0,a.jsxs)("div",{className:"jsx-b9f7c4426befc960",children:[(0,a.jsxs)("h1",{className:"jsx-b9f7c4426befc960 text-5xl md:text-6xl font-bold text-white mb-6",children:["From Rate Limits to",(0,a.jsx)("span",{className:"jsx-b9f7c4426befc960 text-[#ff6b35] block",children:"Unlimited AI"})]}),(0,a.jsxs)("div",{className:"jsx-b9f7c4426befc960 space-y-6 text-lg text-gray-300 leading-relaxed",children:[(0,a.jsxs)("p",{className:"jsx-b9f7c4426befc960",children:["In early ",(0,a.jsx)("span",{className:"jsx-b9f7c4426befc960 text-[#ff6b35] font-bold",children:"2025"}),", I was deep in a coding session when I kept hitting rate limits on Gemini. Every time I got into the flow, ",(0,a.jsx)("span",{className:"jsx-b9f7c4426befc960 text-white font-bold",children:"boom - rate limit exceeded"}),". It was killing my productivity."]}),(0,a.jsxs)("p",{className:"jsx-b9f7c4426befc960",children:["I started with a simple Round Robin router, but then I realized something bigger: ",(0,a.jsx)("span",{className:"jsx-b9f7c4426befc960 text-white font-bold",children:"what if routing could be intelligent?"})," What if it could automatically choose the ",(0,a.jsx)("span",{className:"jsx-b9f7c4426befc960 text-[#ff6b35] font-bold",children:"best model for each specific task"})," - routing coding questions to Claude, creative tasks to GPT, and analysis to specialized models?"]}),(0,a.jsxs)("p",{className:"jsx-b9f7c4426befc960",children:["That's when I knew I wasn't just solving rate limits - I was building the future of AI routing. With my experience in ",(0,a.jsx)("span",{className:"jsx-b9f7c4426befc960 text-[#ff6b35] font-bold",children:"complex systems and game logic"}),", I developed smart algorithms that understand context, roles, and optimal model selection."]}),(0,a.jsxs)("p",{className:"jsx-b9f7c4426befc960",children:[(0,a.jsx)("span",{className:"jsx-b9f7c4426befc960 text-[#ff6b35] font-bold",children:"RouKey"})," became the world's first truly intelligent AI gateway - not just cycling through models, but ",(0,a.jsx)("span",{className:"jsx-b9f7c4426befc960 text-white font-bold",children:"intelligently matching each request to the perfect model"}),". Now thousands of developers get better results, faster responses, and unlimited access. Why? Because with RouKey, you can:"]})]})]})})]})})}),(0,a.jsx)("section",{className:"jsx-b9f7c4426befc960 py-20",children:(0,a.jsx)("div",{className:"jsx-b9f7c4426befc960 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"jsx-b9f7c4426befc960 grid grid-cols-1 md:grid-cols-3 gap-8",children:b.map((e,t)=>(0,a.jsx)(o.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*t},className:"bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 hover:bg-white/10 transition-all duration-300 group",children:(0,a.jsxs)("div",{className:"jsx-b9f7c4426befc960 flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"jsx-b9f7c4426befc960 flex-shrink-0",children:(0,a.jsx)("div",{className:"jsx-b9f7c4426befc960 w-12 h-12 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300",children:(0,a.jsx)(e.icon,{className:"w-6 h-6 text-white"})})}),(0,a.jsxs)("div",{className:"jsx-b9f7c4426befc960",children:[(0,a.jsxs)("h3",{className:"jsx-b9f7c4426befc960 text-xl font-bold text-white mb-3",children:[t+1,". ",e.title]}),(0,a.jsx)("p",{className:"jsx-b9f7c4426befc960 text-gray-300 leading-relaxed",children:e.description})]})]})},e.title))})})}),(0,a.jsx)("section",{className:"jsx-b9f7c4426befc960 py-20",children:(0,a.jsx)("div",{className:"jsx-b9f7c4426befc960 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-12",children:[(0,a.jsxs)("p",{className:"jsx-b9f7c4426befc960 text-2xl text-gray-300 leading-relaxed mb-8",children:["What started as a simple fix for my own rate limit frustration became the tool that ",(0,a.jsx)("span",{className:"jsx-b9f7c4426befc960 text-[#ff6b35] font-bold",children:"unlocks unlimited AI potential"})," for thousands of developers. ",(0,a.jsx)("span",{className:"jsx-b9f7c4426befc960 text-white font-bold",children:"Your next breakthrough is just one API call away!"})]}),(0,a.jsx)("div",{className:"jsx-b9f7c4426befc960 flex justify-center",children:(0,a.jsx)(l(),{href:"/pricing",children:(0,a.jsx)(o.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-12 py-4 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-200 shadow-lg cursor-pointer",children:"Get Started Now"})})})]})})})]}),(0,a.jsx)(m.A,{})]})]})}},59783:(e,t,r)=>{Promise.resolve().then(r.bind(r,57663))},62392:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},90757:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=r(65239),s=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["about-developer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94061)),"C:\\RoKey App\\rokey-app\\src\\app\\about-developer\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,21078)),"C:\\RoKey App\\rokey-app\\src\\app\\about-developer\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\about-developer\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/about-developer/page",pathname:"/about-developer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},94061:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about-developer\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\about-developer\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96374:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(43210);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5"}))})},96487:()=>{},99951:(e,t,r)=>{Promise.resolve().then(r.bind(r,94061))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,5449,2535,4912,7093,7457],()=>r(90757));module.exports=a})();