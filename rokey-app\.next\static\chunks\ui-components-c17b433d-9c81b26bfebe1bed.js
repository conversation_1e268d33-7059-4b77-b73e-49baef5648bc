"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6210],{6875:(e,t,r)=>{r.d(t,{L:()=>l,p:()=>c});var a=r(95155);r(12115);var n=r(55020),i=r(41448),s=r(83298),o=r(35695);function l(e){let{message:t="Unlock intelligent routing with Starter",variant:r="compact",className:l=""}=e,{subscriptionStatus:c,createCheckoutSession:d}=(0,s.R)(),g=(0,o.useRouter)();if(!c||"free"!==c.tier)return null;let x=async()=>{try{await d("starter")}catch(e){g.push("/pricing")}};return"compact"===r?(0,a.jsx)(n.PY1.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-gradient-to-r from-orange-500/10 to-orange-600/10 border border-orange-500/20 rounded-lg p-3 ".concat(l),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-1.5 bg-orange-500/20 rounded-lg",children:(0,a.jsx)(i.B,{className:"h-4 w-4 text-orange-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-white",children:t}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:"Starter plan gives you AI-powered routing and 15 configurations."})]})]}),(0,a.jsx)("button",{onClick:x,className:"inline-flex items-center px-3 py-1.5 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200 whitespace-nowrap",children:"Get Starter"})]})}):(0,a.jsx)(n.PY1.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-gradient-to-r from-orange-500/10 to-orange-600/10 border border-orange-500/20 rounded-xl p-6 ".concat(l),children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"p-2 bg-orange-500/20 rounded-xl",children:(0,a.jsx)(i.B,{className:"h-6 w-6 text-orange-400"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:t}),(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"Upgrade to Starter for intelligent routing, 15 configurations, and advanced features that optimize your AI costs and performance."}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("button",{onClick:x,className:"inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200",children:[(0,a.jsx)(i.K,{className:"w-4 h-4 mr-2"}),"Upgrade to Starter"]}),(0,a.jsx)("button",{onClick:()=>g.push("/pricing"),className:"inline-flex items-center px-4 py-2 text-sm font-medium text-orange-400 hover:text-orange-300 transition-colors duration-200",children:"View All Plans"})]})]})]})})}function c(e){let{className:t=""}=e,{subscriptionStatus:r}=(0,s.R)(),i=(0,o.useRouter)();return r&&"starter"===r.tier?(0,a.jsx)(n.PY1.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},className:"text-center py-2 ".concat(t),children:(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Need unlimited configurations?"," ",(0,a.jsx)("button",{onClick:()=>i.push("/billing"),className:"text-orange-400 hover:text-orange-300 underline transition-colors",children:"Upgrade to Professional"})]})}):null}},60993:(e,t,r)=>{r.d(t,{Jg:()=>m,yA:()=>b,sU:()=>u});var a=r(95155);r(12115);var n=r(83298),i=r(74857),s=r(55020),o=r(39499),l=r(35695);let c={custom_roles:"Custom Roles",knowledge_base:"Knowledge Base",advanced_routing:"Advanced Routing",prompt_engineering:"Prompt Engineering",semantic_caching:"Semantic Caching",configurations:"API Configurations"},d=e=>{for(let t of["starter","professional","enterprise"]){let r=i.v7[t];switch(e){case"custom_roles":if(r.limits.canUseCustomRoles)return t;break;case"knowledge_base":if(r.limits.canUseKnowledgeBase)return t;break;case"advanced_routing":if(r.limits.canUseAdvancedRouting)return t;break;case"prompt_engineering":if(r.limits.canUsePromptEngineering)return t;break;case"semantic_caching":if(r.limits.canUseSemanticCaching)return t;break;case"configurations":if(r.limits.configurations>i.v7.free.limits.configurations)return t}}return"starter"};function g(e){let{feature:t,currentTier:r,customMessage:g,size:x="md",variant:u="card",theme:m="light"}=e,{createCheckoutSession:h}=(0,n.R)(),p=(0,l.useRouter)(),f=d(t),b=i.v7[f],y=c[t],j=async()=>{try{"starter"===f?await h("starter"):"professional"===f?await h("professional"):p.push("/pricing")}catch(e){p.push("/pricing")}};return(0,a.jsx)(s.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"".concat({card:"dark"===m?"bg-gradient-to-br from-orange-900/20 to-orange-800/20 border border-orange-500/30 rounded-xl shadow-sm":"bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-xl shadow-sm",banner:"dark"===m?"bg-orange-900/20 border-l-4 border-orange-500 rounded-r-lg":"bg-orange-100 border-l-4 border-orange-500 rounded-r-lg",inline:"dark"===m?"bg-orange-900/20 border border-orange-500/30 rounded-lg":"bg-orange-50 border border-orange-200 rounded-lg"}[u]," ").concat({sm:"p-4 text-sm",md:"p-6 text-base",lg:"p-8 text-lg"}[x]),children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center",children:(0,a.jsx)(o.JD,{className:"w-5 h-5 text-white"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-2 ".concat("dark"===m?"text-white":"text-gray-900"),children:[y," - Premium Feature"]}),(0,a.jsx)("p",{className:"mb-4 ".concat("dark"===m?"text-gray-300":"text-gray-700"),children:g||"".concat(y," is available starting with the ").concat(b.name," plan.\n               Upgrade to unlock this powerful feature and enhance your RouKey experience.")}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("button",{onClick:j,className:"inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200",children:[(0,a.jsx)(o.Kp,{className:"w-4 h-4 mr-2"}),"Upgrade to ",b.name]}),(0,a.jsxs)("button",{onClick:()=>p.push("/pricing"),className:"inline-flex items-center px-4 py-2 text-sm font-medium transition-colors duration-200 ".concat("dark"===m?"text-orange-400 hover:text-orange-300":"text-orange-600 hover:text-orange-700"),children:[(0,a.jsx)(o.Gg,{className:"w-4 h-4 mr-2"}),"View All Plans"]})]})]})]})})}var x=r(74338);function u(e){let{feature:t,children:r,fallback:s,showUpgradePrompt:o=!0,customMessage:l,currentCount:c=0,theme:d="light"}=e,{subscriptionStatus:u,loading:m}=(0,n.R)();if(m)return(0,a.jsx)(x.Ay,{});let h=(null==u?void 0:u.tier)||"free";if("configurations"===t){if(c<(0,i.zX)(h).limits.configurations)return(0,a.jsx)(a.Fragment,{children:r})}else if((0,i.Nu)(h,t))return(0,a.jsx)(a.Fragment,{children:r});return s?(0,a.jsx)(a.Fragment,{children:s}):o?(0,a.jsx)(g,{feature:t,currentTier:h,customMessage:l,theme:d}):null}function m(e){let{current:t,limit:r,label:n,tier:i,showUpgradeHint:o=!0,className:c="",theme:d="light"}=e,g=(0,l.useRouter)(),x=r>=999999,u=x?0:t/r*100,m=u>=80,h=t>=r&&!x;return(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm ".concat(c),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{className:"dark"===d?"text-gray-300":"text-gray-600",children:[n,":"]}),!x&&(0,a.jsx)("div",{className:"w-16 rounded-full h-1.5 ".concat("dark"===d?"bg-gray-700":"bg-gray-200"),children:(0,a.jsx)(s.PY1.div,{className:"h-1.5 rounded-full ".concat(x?"bg-green-500":h?"bg-red-500":m?"bg-yellow-500":"bg-green-500"),initial:{width:0},animate:{width:"".concat(Math.min(u,100),"%")},transition:{duration:.5,ease:"easeOut"}})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-xs font-medium ".concat("dark"===d?x?"text-green-400":h?"text-red-400":m?"text-yellow-400":"text-green-400":x?"text-green-600":h?"text-red-600":m?"text-yellow-600":"text-green-600"),children:x?"Unlimited":"".concat(t,"/").concat(r)}),(h||m)&&o&&(0,a.jsx)("button",{className:"text-xs underline ml-2 ".concat("dark"===d?"text-orange-400 hover:text-orange-300":"text-orange-600 hover:text-orange-700"),onClick:()=>{g.push("/billing")},children:"Upgrade"})]})]})}r(47321);var h=r(17974);let p={free:{name:"Free",light:{color:"bg-gray-100 text-gray-800 border-gray-300",iconColor:"text-gray-600"},dark:{color:"bg-gray-800/50 text-gray-200 border-gray-600/50",iconColor:"text-gray-400"},icon:h.Zu},starter:{name:"Starter",light:{color:"bg-blue-100 text-blue-800 border-blue-300",iconColor:"text-blue-600"},dark:{color:"bg-blue-900/30 text-blue-200 border-blue-500/50",iconColor:"text-blue-400"},icon:h.Gg},professional:{name:"Professional",light:{color:"bg-orange-100 text-orange-800 border-orange-300",iconColor:"text-orange-600"},dark:{color:"bg-orange-900/30 text-orange-200 border-orange-500/50",iconColor:"text-orange-400"},icon:h.BZ},enterprise:{name:"Enterprise",light:{color:"bg-purple-100 text-purple-800 border-purple-300",iconColor:"text-purple-600"},dark:{color:"bg-purple-900/30 text-purple-200 border-purple-500/50",iconColor:"text-purple-400"},icon:h.OR}},f={sm:{container:"px-2 py-1 text-xs",icon:"w-3 h-3"},md:{container:"px-3 py-1 text-sm",icon:"w-4 h-4"},lg:{container:"px-4 py-2 text-base",icon:"w-5 h-5"}};function b(e){let{tier:t,size:r="md",showIcon:n=!0,className:i="",theme:s="light"}=e,o=p[t],l=f[r],c=o[s],d=o.icon;return(0,a.jsxs)("span",{className:"\n      inline-flex items-center space-x-1 font-medium border rounded-full\n      ".concat(c.color,"\n      ").concat(l.container,"\n      ").concat(i,"\n    "),children:[n&&(0,a.jsx)(d,{className:"".concat(l.icon," ").concat(c.iconColor)}),(0,a.jsx)("span",{children:o.name})]})}},95060:(e,t,r)=>{r.d(t,{A:()=>f});var a=r(95155),n=r(6874),i=r.n(n),s=r(66766),o=r(35695),l=r(12115),c=r(8652),d=r(18685),g=r(22261),x=r(99323),u=r(37843),m=r(24403),h=r(42724);let p=[{href:"/dashboard",label:"Dashboard",icon:c.fA,iconSolid:d.fA,description:"Overview & analytics"},{href:"/my-models",label:"My Models",icon:c.RY,iconSolid:d.RY,description:"API key management"},{href:"/playground",label:"Playground",icon:c.cu,iconSolid:d.cu,description:"Test your models"},{href:"/routing-setup",label:"Routing Setup",icon:c.sR,iconSolid:d.sR,description:"Configure routing"},{href:"/logs",label:"Logs",icon:c.AQ,iconSolid:d.AQ,description:"Request history"},{href:"/training",label:"Training",icon:c.tl,iconSolid:d.tl,description:"AI training & knowledge"},{href:"/analytics",label:"Analytics",icon:c.r9,iconSolid:d.r9,description:"Advanced insights"}];function f(){let e=(0,o.usePathname)(),{isCollapsed:t,isHovered:r,isHoverDisabled:n,setHovered:c}=(0,g.c)(),{navigateOptimistically:d}=(0,x.bu)()||{navigateOptimistically:()=>{}},{prefetchOnHover:f}=(0,u.C)(),{prefetchWhenIdle:b}=(0,u.e)(),{prefetchChatHistory:y}=(0,m.l2)(),{predictions:j,isLearning:v}=(0,h.x)(),w=(0,h.G)();(0,l.useEffect)(()=>{let t=p.map(e=>e.href),r=j.slice(0,2),a=w.filter(e=>"high"===e.priority).map(e=>e.route).slice(0,2);return b([...r,...a,...t.filter(t=>t!==e&&!r.includes(t)&&!a.includes(t)),"/playground","/logs"].slice(0,6))},[e,b,j,w,v]);let N=!t||r;return(0,a.jsxs)("aside",{className:"sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-[#030614] relative ".concat(N?"w-64":"w-16"),onMouseEnter:()=>!n&&c(!0),onMouseLeave:()=>!n&&c(!1),children:[(0,a.jsx)("div",{className:"absolute top-0 right-0 h-full w-px bg-gradient-to-b from-gray-500/30 via-gray-400/40 to-gray-500/30"}),(0,a.jsx)("div",{className:"absolute top-0 right-0 h-full w-0.5 bg-gray-400/15 blur-sm"}),(0,a.jsx)("div",{className:"flex-1 relative overflow-hidden",children:(0,a.jsxs)("div",{className:"p-6 transition-all duration-200 ease-out ".concat(N?"px-6":"px-3"),children:[(0,a.jsx)("div",{className:"mb-8 pt-4 transition-all duration-200 ease-out ".concat(N?"":"text-center"),children:(0,a.jsxs)("div",{className:"relative overflow-hidden",children:[(0,a.jsx)("div",{className:"transition-all duration-200 ease-out ".concat(N?"opacity-0 scale-75 -translate-y-2":"opacity-100 scale-100 translate-y-0"," ").concat(N?"absolute":"relative"," w-8 h-8 bg-black rounded-lg flex items-center justify-center mx-auto p-0.5"),children:(0,a.jsx)(s.default,{src:"/RouKey_Logo_GLOW.png",alt:"RouKey",width:28,height:28,className:"object-contain"})}),(0,a.jsxs)("div",{className:"transition-all duration-200 ease-out ".concat(N?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-75 translate-y-2"," ").concat(N?"relative":"absolute top-0 left-0 w-full"),children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white tracking-tight whitespace-nowrap",children:"RouKey"}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-1 whitespace-nowrap",children:"Smart LLM Router"})]})]})}),(0,a.jsx)("nav",{className:"space-y-2",children:p.map(t=>{let r=e===t.href||e.startsWith(t.href+"/"),n=r?t.iconSolid:t.icon,s=j.includes(t.href),o=w.find(e=>e.route===t.href),l="/playground"===t.href?{onMouseEnter:()=>{if("/playground"===t.href){f(t.href,50).onMouseEnter();let e=new URLSearchParams(window.location.search).get("config");e&&y(e)}}}:f(t.href,50);return(0,a.jsx)(i(),{href:t.href,onClick:e=>{e.preventDefault(),d(t.href)},className:"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left ".concat(r?"active":""," ").concat(N?"":"collapsed"),title:N?void 0:t.label,...l,children:(0,a.jsxs)("div",{className:"relative flex items-center w-full overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative flex items-center justify-center transition-all duration-200 ease-out ".concat(N?"w-5 h-5 mr-3":"w-10 h-10 rounded-xl"," ").concat(!N&&r?"bg-white shadow-sm":N?"":"bg-transparent hover:bg-white/10"),children:[(0,a.jsx)(n,{className:"transition-all duration-200 ease-out ".concat("h-5 w-5"," ").concat(r?"text-orange-500":"text-white")}),s&&!r&&(0,a.jsx)("div",{className:"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out ".concat(N?"-top-1 -right-1 w-2 h-2":"-top-1 -right-1 w-3 h-3"),title:"Predicted next destination"})]}),(0,a.jsxs)("div",{className:"flex-1 transition-all duration-200 ease-out ".concat(N?"opacity-100 translate-x-0 max-w-full":"opacity-0 translate-x-4 max-w-0"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between whitespace-nowrap",children:[(0,a.jsx)("div",{className:"font-medium text-sm",children:t.label}),o&&!r&&(0,a.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-full ml-2 ".concat("high"===o.priority?"bg-blue-500/20 text-blue-300":"bg-gray-500/20 text-gray-300"),children:"high"===o.priority?"!":"\xb7"})]}),(0,a.jsx)("div",{className:"text-xs transition-colors duration-200 whitespace-nowrap ".concat(r?"text-orange-400":"text-gray-400"),children:o?o.reason:t.description})]})]})},t.href)})})]})})]})}},96364:(e,t,r)=>{r.d(t,{V:()=>i});var a=r(95155);r(12115);var n=r(82880);let i=e=>{let{senderName:t,roleId:r}=e,i=(e=>{if(!e||"moderator"===e)return"from-blue-500 to-blue-600";let t=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return t[e.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%t.length]})(r),s=!r||"moderator"===r;return(0,a.jsx)("div",{className:"flex justify-start mb-4 opacity-75",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ".concat(i," flex items-center justify-center text-white shadow-sm animate-pulse"),children:(e=>e&&"moderator"!==e?(0,a.jsx)(n.Y,{className:"w-4 h-4"}):(0,a.jsx)(n.B,{className:"w-4 h-4"}))(r)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("span",{className:"text-sm font-semibold ".concat(s?"text-blue-700":"text-gray-700"),children:t}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{let t=["is thinking...","is working on this...","is analyzing...","is processing...","is crafting a response..."];return t[e.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%t.length]})(t)})]}),(0,a.jsx)("div",{className:"inline-block px-4 py-3 rounded-2xl shadow-sm ".concat(s?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"),children:(0,a.jsx)("div",{className:"flex items-center space-x-1",children:(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]})})})]})]})})}}}]);