(()=>{var e={};e.id=4833,e.ids=[4833],e.modules={2969:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3567:(e,t,r)=>{"use strict";var n=r(43210),s="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useSyncExternalStore,o=n.useRef,i=n.useEffect,l=n.useMemo,c=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,d){var u=o(null);if(null===u.current){var m={hasValue:!1,value:null};u.current=m}else m=u.current;var p=a(e,(u=l(function(){function e(e){if(!i){if(i=!0,a=e,e=n(e),void 0!==d&&m.hasValue){var t=m.value;if(d(t,e))return o=t}return o=e}if(t=o,s(a,e))return t;var r=n(e);return void 0!==d&&d(t,r)?(a=e,t):(a=e,o=r)}var a,o,i=!1,l=void 0===r?null:r;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]},[t,r,n,d]))[0],u[1]);return i(function(){m.hasValue=!0,m.value=p},[p]),c(p),p}},6895:(e,t,r)=>{"use strict";e.exports=r(3567)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14689:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24245:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"}))})},24840:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n8});var n,s,a,o,i,l=r(60687),c=r(43210),d=r.t(c,2),u=r(16189),m=r(15022),p=r(59922);r(71763);var f=r(44725),g=r(50515),x=r(51426),h=r(6854),b=r(89114),v=r(24245);function y({workflow:e,isDirty:t,isSaving:r,onSave:n,onExecute:s,onBack:a,onShare:o}){let[i,d]=(0,c.useState)(!1);return(0,l.jsxs)("div",{className:"bg-gray-900/80 backdrop-blur-sm border-b border-gray-700/50 px-6 py-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)("button",{onClick:a,className:"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50",title:"Back to workflows",children:(0,l.jsx)(f.A,{className:"w-5 h-5"})}),(0,l.jsx)("div",{className:"h-6 w-px bg-gray-700"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-lg font-semibold text-white",children:e?.name||"New Workflow"}),(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-400",children:[t&&(0,l.jsxs)("span",{className:"flex items-center gap-1",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),"Unsaved changes"]}),e?.updated_at&&(0,l.jsxs)("span",{className:"flex items-center gap-1",children:[(0,l.jsx)(g.A,{className:"w-3 h-3"}),"Last saved ",new Date(e.updated_at).toLocaleTimeString()]})]})]})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("button",{onClick:()=>d(!i),className:"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50",title:"Workflow settings",children:(0,l.jsx)(x.A,{className:"w-5 h-5"})}),(0,l.jsx)("button",{onClick:o,disabled:!e?.id||!o,className:"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800/50 disabled:opacity-50 disabled:cursor-not-allowed",title:"Share workflow",children:(0,l.jsx)(h.A,{className:"w-5 h-5"})})]}),(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsxs)("button",{onClick:n,disabled:r||!t,className:`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${t&&!r?"bg-blue-600 hover:bg-blue-500 text-white":"bg-gray-700 text-gray-400 cursor-not-allowed"}`,children:[(0,l.jsx)(b.A,{className:"w-4 h-4"}),r?"Saving...":"Save"]}),(0,l.jsxs)("button",{onClick:()=>{e?.id?window.open("/playground/workflows","_blank"):alert("Please save the workflow first to test it in the playground")},className:"bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2",children:[(0,l.jsx)(v.A,{className:"w-4 h-4"}),"Test in Playground"]})]})]}),i&&(0,l.jsxs)("div",{className:"mt-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700/50",children:[(0,l.jsx)("h3",{className:"text-white font-medium mb-3",children:"Workflow Settings"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Max Execution Time"}),(0,l.jsxs)("select",{className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,l.jsx)("option",{value:"300",children:"5 minutes"}),(0,l.jsx)("option",{value:"600",children:"10 minutes"}),(0,l.jsx)("option",{value:"1800",children:"30 minutes"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Retry Count"}),(0,l.jsxs)("select",{className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,l.jsx)("option",{value:"1",children:"1 retry"}),(0,l.jsx)("option",{value:"3",children:"3 retries"}),(0,l.jsx)("option",{value:"5",children:"5 retries"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Options"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("label",{className:"flex items-center",children:[(0,l.jsx)("input",{type:"checkbox",className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0",defaultChecked:!0}),(0,l.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable memory"})]}),(0,l.jsxs)("label",{className:"flex items-center",children:[(0,l.jsx)("input",{type:"checkbox",className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0",defaultChecked:!0}),(0,l.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable streaming"})]})]})]})]})]})]})}var w=r(64908),j=r(68589),N=r(61245),k=r(48544),C=r(66524),E=r(93635),S=r(55510),T=r(36920),A=r(74461),P=r(70143),O=r(27010),I=r(37132);let R={core:{label:"Core Nodes",description:"Essential workflow components",nodes:[{type:"userRequest",label:"User Request",description:"Starting point for user input",icon:w.A,category:"core",isAvailable:!0,defaultData:{label:"User Request",config:{},isConfigured:!0}},{type:"classifier",label:"Classifier",description:"Analyzes and categorizes requests",icon:j.A,category:"core",isAvailable:!0,defaultData:{label:"Classifier",config:{},isConfigured:!0}},{type:"output",label:"Output",description:"Final response to user",icon:N.A,category:"core",isAvailable:!0,defaultData:{label:"Output",config:{},isConfigured:!0}}]},ai:{label:"AI Providers",description:"AI model integrations",nodes:[{type:"provider",label:"AI Provider",description:"Connect to AI models (OpenAI, Claude, etc.)",icon:k.A,category:"ai",isAvailable:!0,defaultData:{label:"AI Provider",config:{providerId:"",modelId:"",apiKey:"",parameters:{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}},isConfigured:!1}},{type:"vision",label:"Vision AI",description:"Multimodal AI for image analysis and vision tasks",icon:C.A,category:"ai",isAvailable:!0,defaultData:{label:"Vision AI",config:{providerId:"",modelId:"",apiKey:"",parameters:{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}},isConfigured:!1}},{type:"roleAgent",label:"Role Agent",description:"Role plugin for AI providers (connect to role input)",icon:E.A,category:"ai",isAvailable:!0,defaultData:{label:"Role Agent",config:{roleId:"",roleName:"",roleType:"predefined",customPrompt:"",memoryEnabled:!1},isConfigured:!1}},{type:"centralRouter",label:"Central Router",description:"Smart routing hub for multiple AI providers and vision models",icon:j.A,category:"ai",isAvailable:!0,defaultData:{label:"Central Router",config:{routingStrategy:"smart",fallbackProvider:"",maxRetries:3,timeout:3e4,enableCaching:!0,debugMode:!1},isConfigured:!0}},{type:"planner",label:"Planner",description:"AI model that creates browsing strategies and todo lists",icon:S.A,category:"ai",isAvailable:!0,defaultData:{label:"Planner",config:{providerId:"",modelId:"",apiKey:"",parameters:{temperature:.7,maxTokens:1e3},maxSubtasks:10},isConfigured:!1}}]},tools:{label:"Tools & Integrations",description:"External service integrations",nodes:[{type:"tool",label:"Tools",description:"External tool integrations (Google Drive, Zapier, etc.)",icon:T.A,category:"tools",isAvailable:!0,defaultData:{label:"Tools",config:{toolType:"",toolConfig:{},timeout:30,connectionStatus:"disconnected",isAuthenticated:!1},isConfigured:!1}},{type:"memory",label:"Memory",description:"Store and retrieve data across workflow executions",icon:A.A,category:"advanced",isAvailable:!0,defaultData:{label:"Memory",config:{memoryName:"",maxSize:10240,encryption:!0,description:""},isConfigured:!1}}]},browsing:{label:"Web Browsing",description:"Intelligent web browsing and automation",nodes:[{type:"browsing",label:"Browsing Agent",description:"Intelligent web browsing agent with multi-step automation",icon:P.A,category:"advanced",isAvailable:!0,defaultData:{label:"Browsing Agent",config:{providerId:"",modelId:"",parameters:{temperature:.7,maxTokens:1e3,topP:1,frequencyPenalty:0,presencePenalty:0},maxSites:5,timeout:30,enableScreenshots:!0,enableFormFilling:!0,enableCaptchaSolving:!1,searchEngines:["google"],maxDepth:2,respectRobots:!0,enableJavaScript:!0},isConfigured:!1}}]}};function M({node:e,onAddNode:t}){let r=e.icon;return(0,l.jsx)("div",{draggable:!0,onDragStart:t=>{t.dataTransfer.setData("application/reactflow",e.type),t.dataTransfer.effectAllowed="move"},onClick:()=>{t(e.type)},className:`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${e.isAvailable?"bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50":"bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed"}`,title:e.description,children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:`p-2 rounded-lg ${e.isAvailable?"bg-[#ff6b35]/20 text-[#ff6b35]":"bg-gray-700/50 text-gray-500"}`,children:(0,l.jsx)(r,{className:"w-4 h-4"})}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsx)("div",{className:`font-medium text-sm ${e.isAvailable?"text-white":"text-gray-500"}`,children:e.label}),(0,l.jsx)("div",{className:"text-xs text-gray-400 truncate",children:e.description})]})]})})}function F({category:e,data:t,isExpanded:r,onToggle:n,onAddNode:s}){return(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsxs)("button",{onClick:n,className:"w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[r?(0,l.jsx)(O.A,{className:"w-4 h-4 text-gray-400"}):(0,l.jsx)(I.A,{className:"w-4 h-4 text-gray-400"}),(0,l.jsx)("span",{className:"font-medium text-white",children:t.label})]}),(0,l.jsx)("span",{className:"text-xs text-gray-400",children:t.nodes.length})]}),r&&(0,l.jsx)("div",{className:"mt-2 space-y-2",children:t.nodes.map(e=>(0,l.jsx)(M,{node:e,onAddNode:s},e.type))})]})}function L({onAddNode:e}){let[t,r]=(0,c.useState)(new Set(["core","ai"])),n=e=>{let n=new Set(t);n.has(e)?n.delete(e):n.add(e),r(n)},s=t=>{e(t,{x:400,y:200})};return(0,l.jsxs)("div",{className:"w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto",children:[(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-white mb-2",children:"Node Palette"}),(0,l.jsx)("p",{className:"text-sm text-gray-400",children:"Drag nodes to the canvas or click to add at center"})]}),(0,l.jsx)("div",{className:"space-y-1",children:Object.entries(R).filter(([e])=>"browsing"!==e).map(([e,r])=>(0,l.jsx)(F,{category:e,data:r,isExpanded:t.has(e),onToggle:()=>n(e),onAddNode:s},e))}),(0,l.jsxs)("div",{className:"mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg",children:[(0,l.jsx)("div",{className:"text-sm text-blue-300 font-medium mb-1",children:"\uD83D\uDCA1 Pro Tip"}),(0,l.jsx)("div",{className:"text-xs text-blue-200",children:"Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node."})]})]})}var D=r(30474),_=r(95753);let H=c.forwardRef(function({title:e,titleId:t,...r},n){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 15 12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9"}))});var B=r(14689),W=r(58089),$=r(59168);let q=c.forwardRef(function({title:e,titleId:t,...r},n){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"}))});var V=r(81836);let U="undefined"!=typeof document?c.useLayoutEffect:()=>{};function K(e){return e.nativeEvent=e,e.isDefaultPrevented=()=>e.defaultPrevented,e.isPropagationStopped=()=>e.cancelBubble,e.persist=()=>{},e}function z(e){let t=(0,c.useRef)({isFocused:!1,observer:null});U(()=>{let e=t.current;return()=>{e.observer&&(e.observer.disconnect(),e.observer=null)}},[]);let r=function(e){let t=(0,c.useRef)(null);return U(()=>{t.current=e},[e]),(0,c.useCallback)((...e)=>{let r=t.current;return null==r?void 0:r(...e)},[])}(t=>{null==e||e(t)});return(0,c.useCallback)(e=>{if(e.target instanceof HTMLButtonElement||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement){t.current.isFocused=!0;let n=e.target;n.addEventListener("focusout",e=>{t.current.isFocused=!1,n.disabled&&r(K(e)),t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)},{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&n.disabled){var e;null==(e=t.current.observer)||e.disconnect();let r=n===document.activeElement?null:document.activeElement;n.dispatchEvent(new FocusEvent("blur",{relatedTarget:r})),n.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:r}))}}),t.current.observer.observe(n,{attributes:!0,attributeFilter:["disabled"]})}},[r])}function G(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&((null==(t=window.navigator.userAgentData)?void 0:t.brands.some(t=>e.test(t.brand)))||e.test(window.navigator.userAgent))}function Y(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&e.test((null==(t=window.navigator.userAgentData)?void 0:t.platform)||window.navigator.platform)}function J(e){let t=null;return()=>(null==t&&(t=e()),t)}let Z=J(function(){return Y(/^Mac/i)}),X=J(function(){return Y(/^iPhone/i)}),Q=J(function(){return Y(/^iPad/i)||Z()&&navigator.maxTouchPoints>1}),ee=J(function(){return X()||Q()});J(function(){return Z()||ee()}),J(function(){return G(/AppleWebKit/i)&&!et()});let et=J(function(){return G(/Chrome/i)}),er=J(function(){return G(/Android/i)});J(function(){return G(/Firefox/i)});let en=e=>{var t;return null!=(t=null==e?void 0:e.ownerDocument)?t:document},es=e=>e&&"window"in e&&e.window===e?e:en(e).defaultView||window,ea=null,eo=new Set,ei=new Map,el=!1,ec=!1,ed={Tab:!0,Escape:!0};function eu(e,t){for(let r of eo)r(e,t)}function em(e){el=!0,e.metaKey||!Z()&&e.altKey||e.ctrlKey||"Control"===e.key||"Shift"===e.key||"Meta"===e.key||(ea="keyboard",eu("keyboard",e))}function ep(e){ea="pointer",("mousedown"===e.type||"pointerdown"===e.type)&&(el=!0,eu("pointer",e))}function ef(e){(0===e.mozInputSource&&e.isTrusted||(er()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType))&&(el=!0,ea="virtual")}function eg(e){e.target!==window&&e.target!==document&&e.isTrusted&&(el||ec||(ea="virtual",eu("virtual",e)),el=!1,ec=!1)}function ex(){el=!1,ec=!0}function eh(e){if("undefined"==typeof window||"undefined"==typeof document||ei.get(es(e)))return;let t=es(e),r=en(e),n=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){el=!0,n.apply(this,arguments)},r.addEventListener("keydown",em,!0),r.addEventListener("keyup",em,!0),r.addEventListener("click",ef,!0),t.addEventListener("focus",eg,!0),t.addEventListener("blur",ex,!1),"undefined"!=typeof PointerEvent&&(r.addEventListener("pointerdown",ep,!0),r.addEventListener("pointermove",ep,!0),r.addEventListener("pointerup",ep,!0)),t.addEventListener("beforeunload",()=>{eb(e)},{once:!0}),ei.set(t,{focus:n})}let eb=(e,t)=>{let r=es(e),n=en(e);t&&n.removeEventListener("DOMContentLoaded",t),ei.has(r)&&(r.HTMLElement.prototype.focus=ei.get(r).focus,n.removeEventListener("keydown",em,!0),n.removeEventListener("keyup",em,!0),n.removeEventListener("click",ef,!0),r.removeEventListener("focus",eg,!0),r.removeEventListener("blur",ex,!1),"undefined"!=typeof PointerEvent&&(n.removeEventListener("pointerdown",ep,!0),n.removeEventListener("pointermove",ep,!0),n.removeEventListener("pointerup",ep,!0)),ei.delete(r))};function ev(){return"pointer"!==ea}"undefined"!=typeof document&&function(e){let t,r=en(void 0);"loading"!==r.readyState?eh(void 0):(t=()=>{eh(e)},r.addEventListener("DOMContentLoaded",t)),()=>eb(e,t)}();let ey=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function ew(e,t){1;return!!t&&!!e&&e.contains(t)}let ej=(e=document)=>{var t;1;return e.activeElement};function eN(e){return 0,e.target}function ek(){let e=(0,c.useRef)(new Map),t=(0,c.useCallback)((t,r,n,s)=>{let a=(null==s?void 0:s.once)?(...t)=>{e.current.delete(n),n(...t)}:n;e.current.set(n,{type:r,eventTarget:t,fn:a,options:s}),t.addEventListener(r,a,s)},[]),r=(0,c.useCallback)((t,r,n,s)=>{var a;let o=(null==(a=e.current.get(n))?void 0:a.fn)||n;t.removeEventListener(r,o,s),e.current.delete(n)},[]),n=(0,c.useCallback)(()=>{e.current.forEach((e,t)=>{r(e.eventTarget,e.type,t,e.options)})},[r]);return(0,c.useEffect)(()=>n,[n]),{addGlobalListener:t,removeGlobalListener:r,removeAllGlobalListeners:n}}let eC=!1,eE=0;function eS(e){"touch"===e.pointerType&&(eC=!0,setTimeout(()=>{eC=!1},50))}function eT(){if("undefined"!=typeof document)return"undefined"!=typeof PointerEvent&&document.addEventListener("pointerup",eS),eE++,()=>{--eE>0||"undefined"!=typeof PointerEvent&&document.removeEventListener("pointerup",eS)}}var eA=r(51215),eP=Object.defineProperty,eO=(e,t,r)=>t in e?eP(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,eI=(e,t,r)=>(eO(e,"symbol"!=typeof t?t+"":t,r),r);class eR{constructor(){eI(this,"current",this.detect()),eI(this,"handoffState","pending"),eI(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"server"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let eM=new eR;function eF(e){var t,r;return eM.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(r=null==(t=e.current)?void 0:t.ownerDocument)?r:document:null:document}function eL(){let e=[],t={addEventListener:(e,r,n,s)=>(e.addEventListener(r,n,s),t.add(()=>e.removeEventListener(r,n,s))),requestAnimationFrame(...e){let r=requestAnimationFrame(...e);return t.add(()=>cancelAnimationFrame(r))},nextFrame:(...e)=>t.requestAnimationFrame(()=>t.requestAnimationFrame(...e)),setTimeout(...e){let r=setTimeout(...e);return t.add(()=>clearTimeout(r))},microTask(...e){var r;let n={current:!0};return r=()=>{n.current&&e[0]()},"function"==typeof queueMicrotask?queueMicrotask(r):Promise.resolve().then(r).catch(e=>setTimeout(()=>{throw e})),t.add(()=>{n.current=!1})},style(e,t,r){let n=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:r}),this.add(()=>{Object.assign(e.style,{[t]:n})})},group(e){let t=eL();return e(t),this.add(()=>t.dispose())},add:t=>(e.includes(t)||e.push(t),()=>{let r=e.indexOf(t);if(r>=0)for(let t of e.splice(r,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}function eD(){let[e]=(0,c.useState)(eL);return e}let e_=(e,t)=>{eM.isServer?(0,c.useEffect)(e,t):(0,c.useLayoutEffect)(e,t)};function eH(e){let t=(0,c.useRef)(e);return e_(()=>{t.current=e},[e]),t}let eB=function(e){let t=eH(e);return c.useCallback((...e)=>t.current(...e),[t])};class eW extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}}var e$=Object.defineProperty,eq=(e,t,r)=>t in e?e$(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,eV=(e,t,r)=>(eq(e,"symbol"!=typeof t?t+"":t,r),r),eU=(e,t,r)=>{if(!t.has(e))throw TypeError("Cannot "+r)},eK=(e,t,r)=>(eU(e,t,"read from private field"),r?r.call(e):t.get(e)),ez=(e,t,r)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,r)},eG=(e,t,r,n)=>(eU(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r);class eY{constructor(e){ez(this,n,{}),ez(this,s,new eW(()=>new Set)),ez(this,a,new Set),eV(this,"disposables",eL()),eG(this,n,e)}dispose(){this.disposables.dispose()}get state(){return eK(this,n)}subscribe(e,t){let r={selector:e,callback:t,current:e(eK(this,n))};return eK(this,a).add(r),this.disposables.add(()=>{eK(this,a).delete(r)})}on(e,t){return eK(this,s).get(e).add(t),this.disposables.add(()=>{eK(this,s).get(e).delete(t)})}send(e){let t=this.reduce(eK(this,n),e);if(t!==eK(this,n)){for(let e of(eG(this,n,t),eK(this,a))){let t=e.selector(eK(this,n));eJ(e.current,t)||(e.current=t,e.callback(t))}for(let t of eK(this,s).get(e.type))t(eK(this,n),e)}}}function eJ(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&eZ(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&eZ(e.entries(),t.entries()):!!(eX(e)&&eX(t))&&eZ(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function eZ(e,t){for(;;){let r=e.next(),n=t.next();if(r.done&&n.done)return!0;if(r.done||n.done||!Object.is(r.value,n.value))return!1}}function eX(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}function eQ(e){let[t,r]=e(),n=eL();return(...e)=>{t(...e),n.dispose(),n.microTask(r)}}function e0(e,t,...r){if(e in t){let n=t[e];return"function"==typeof n?n(...r):n}let n=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,e0),n}n=new WeakMap,s=new WeakMap,a=new WeakMap;var e1=Object.defineProperty,e2=(e,t,r)=>t in e?e1(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,e5=(e,t,r)=>(e2(e,"symbol"!=typeof t?t+"":t,r),r),e3=(e=>(e[e.Push=0]="Push",e[e.Pop=1]="Pop",e))(e3||{});let e4={0(e,t){let r=t.id,n=e.stack,s=e.stack.indexOf(r);if(-1!==s){let t=e.stack.slice();return t.splice(s,1),t.push(r),n=t,{...e,stack:n}}return{...e,stack:[...e.stack,r]}},1(e,t){let r=t.id,n=e.stack.indexOf(r);if(-1===n)return e;let s=e.stack.slice();return s.splice(n,1),{...e,stack:s}}};class e6 extends eY{constructor(){super(...arguments),e5(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),e5(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}static new(){return new e6({stack:[]})}reduce(e,t){return e0(t.type,e4,e,t)}}let e7=new eW(()=>e6.new());var e8=r(6895);function e9(e,t,r=eJ){return(0,e8.useSyncExternalStoreWithSelector)(eB(t=>e.subscribe(te,t)),eB(()=>e.state),eB(()=>e.state),eB(t),r)}function te(e){return e}function tt(e,t){let r=(0,c.useId)(),n=e7.get(t),[s,a]=e9(n,(0,c.useCallback)(e=>[n.selectors.isTop(e,r),n.selectors.inStack(e,r)],[n,r]));return e_(()=>{if(e)return n.actions.push(r),()=>n.actions.pop(r)},[n,e,r]),!!e&&(!a||s)}let tr=new Map,tn=new Map;function ts(e){var t;let r=null!=(t=tn.get(e))?t:0;return tn.set(e,r+1),0!==r||(tr.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let r=null!=(t=tn.get(e))?t:1;if(1===r?tn.delete(e):tn.set(e,r-1),1!==r)return;let n=tr.get(e);n&&(null===n["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",n["aria-hidden"]),e.inert=n.inert,tr.delete(e))})(e)}function ta(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function to(e){return ta(e)&&"tagName"in e}function ti(e){return to(e)&&"accessKey"in e}function tl(e){return to(e)&&"tabIndex"in e}function tc(e){return ti(e)&&"INPUT"===e.nodeName}function td(e){return ti(e)&&"LABEL"===e.nodeName}function tu(e){return ti(e)&&"LEGEND"===e.nodeName}let tm=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),tp=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var tf=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(tf||{}),tg=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(tg||{}),tx=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(tx||{});function th(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(tm)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var tb=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(tb||{});function tv(e,t=0){var r;return e!==(null==(r=eF(e))?void 0:r.body)&&e0(t,{0:()=>e.matches(tm),1(){let t=e;for(;null!==t;){if(t.matches(tm))return!0;t=t.parentElement}return!1}})}var ty=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(ty||{});function tw(e,t=e=>e){return e.slice().sort((e,r)=>{let n=t(e),s=t(r);if(null===n||null===s)return 0;let a=n.compareDocumentPosition(s);return a&Node.DOCUMENT_POSITION_FOLLOWING?-1:a&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function tj(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function tN(){return tj()||/Android/gi.test(window.navigator.userAgent)}function tk(...e){return(0,c.useMemo)(()=>eF(...e),[...e])}var tC=(e=>(e[e.Ignore=0]="Ignore",e[e.Select=1]="Select",e[e.Close=2]="Close",e))(tC||{});let tE={Ignore:{kind:0},Select:e=>({kind:1,target:e}),Close:{kind:2}},tS=function(e,t){let r=e(),n=new Set;return{getSnapshot:()=>r,subscribe:e=>(n.add(e),()=>n.delete(e)),dispatch(e,...s){let a=t[e].call(r,...s);a&&(r=a,n.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var r;let n=null!=(r=this.get(e))?r:{doc:e,count:0,d:eL(),meta:new Set};return n.count++,n.meta.add(t),this.set(e,n),this},POP(e,t){let r=this.get(e);return r&&(r.count--,r.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:r}){let n,s={doc:e,d:t,meta:function(e){let t={};for(let r of e)Object.assign(t,r(t));return t}(r)},a=[tj()?{before({doc:e,d:t,meta:r}){function n(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var r;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let r=eL();r.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>r.dispose()))}let s=null!=(r=window.scrollY)?r:window.pageYOffset,a=null;t.addEventListener(e,"click",t=>{if(tl(t.target))try{let r=t.target.closest("a");if(!r)return;let{hash:s}=new URL(r.href),o=e.querySelector(s);tl(o)&&!n(o)&&(a=o)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{var r;if(tl(e.target)&&to(r=e.target)&&"style"in r)if(n(e.target)){let r=e.target;for(;r.parentElement&&n(r.parentElement);)r=r.parentElement;t.style(r,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}),t.addEventListener(e,"touchmove",e=>{if(tl(e.target)&&!tc(e.target))if(n(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()},{passive:!1}),t.add(()=>{var e;s!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,s),a&&a.isConnected&&(a.scrollIntoView({block:"nearest"}),a=null)})})}}:{},{before({doc:e}){var t;let r=e.documentElement;n=Math.max(0,(null!=(t=e.defaultView)?t:window).innerWidth-r.clientWidth)},after({doc:e,d:t}){let r=e.documentElement,s=Math.max(0,r.clientWidth-r.offsetWidth),a=Math.max(0,n-s);t.style(r,"paddingRight",`${a}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];a.forEach(({before:e})=>null==e?void 0:e(s)),a.forEach(({after:e})=>null==e?void 0:e(s))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});tS.subscribe(()=>{let e=tS.getSnapshot(),t=new Map;for(let[r]of e)t.set(r,r.documentElement.style.overflow);for(let r of e.values()){let e="hidden"===t.get(r.doc),n=0!==r.count;(n&&!e||!n&&e)&&tS.dispatch(r.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",r),0===r.count&&tS.dispatch("TEARDOWN",r)}});let tT=Symbol();function tA(...e){let t=(0,c.useRef)(e),r=eB(e=>{for(let r of t.current)null!=r&&("function"==typeof r?r(e):r.current=e)});return e.every(e=>null==e||(null==e?void 0:e[tT]))?void 0:r}let tP=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function tO(e){var t,r;let n=null!=(t=e.innerText)?t:"",s=e.cloneNode(!0);if(!ti(s))return n;let a=!1;for(let e of s.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))e.remove(),a=!0;let o=a?null!=(r=s.innerText)?r:"":n;return tP.test(o)&&(o=o.replace(tP,"")),o}function tI(e){return[e.screenX,e.screenY]}"undefined"!=typeof process&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(o=null==process?void 0:process.env)?void 0:o.NODE_ENV)==="test"&&void 0===(null==(i=null==Element?void 0:Element.prototype)?void 0:i.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});var tR=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(tR||{});function tM(e){let t={};for(let r in e)!0===e[r]&&(t[`data-${r}`]="");return t}function tF(e,t,r,n){let[s,a]=(0,c.useState)(r),{hasFlag:o,addFlag:i,removeFlag:l}=function(e=0){let[t,r]=(0,c.useState)(e),n=(0,c.useCallback)(e=>r(e),[t]),s=(0,c.useCallback)(e=>r(t=>t|e),[t]),a=(0,c.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:n,addFlag:s,hasFlag:a,removeFlag:(0,c.useCallback)(e=>r(t=>t&~e),[r]),toggleFlag:(0,c.useCallback)(e=>r(t=>t^e),[r])}}(e&&s?3:0),d=(0,c.useRef)(!1),u=(0,c.useRef)(!1);return e_(()=>{var s;if(e){if(r&&a(!0),!t){r&&i(3);return}return null==(s=null==n?void 0:n.start)||s.call(n,r),function(e,{prepare:t,run:r,done:n,inFlight:s}){let a=eL();return function(e,{inFlight:t,prepare:r}){if(null!=t&&t.current)return r();let n=e.style.transition;e.style.transition="none",r(),e.offsetHeight,e.style.transition=n}(e,{prepare:t,inFlight:s}),a.nextFrame(()=>{r(),a.requestAnimationFrame(()=>{a.add(function(e,t){var r,n;let s=eL();if(!e)return s.dispose;let a=!1;s.add(()=>{a=!0});let o=null!=(n=null==(r=e.getAnimations)?void 0:r.call(e).filter(e=>e instanceof CSSTransition))?n:[];return 0===o.length?t():Promise.allSettled(o.map(e=>e.finished)).then(()=>{a||t()}),s.dispose}(e,n))})}),a.dispose}(t,{inFlight:d,prepare(){u.current?u.current=!1:u.current=d.current,d.current=!0,u.current||(r?(i(3),l(4)):(i(4),l(2)))},run(){u.current?r?(l(3),i(4)):(l(4),i(3)):r?l(1):i(1)},done(){var e;u.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(d.current=!1,l(7),r||a(!1),null==(e=null==n?void 0:n.end)||e.call(n,r))}})}},[e,r,t,eD()]),e?[s,{closed:o(1),enter:o(2),leave:o(4),transition:o(2)||o(4)}]:[r,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}let tL=(0,c.createContext)(void 0);function tD(){return(0,c.useContext)(tL)}function t_(){let e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(e=>{let{brand:t,version:r}=e;return t+"/"+r}).join(" "):navigator.userAgent}var tH=r(43749),tB=r(10867),tW=r(78488),t$="undefined"!=typeof document?c.useLayoutEffect:function(){};function tq(e,t){let r,n,s;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!tq(e[n],t[n]))return!1;return!0}if((r=(s=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,s[n]))return!1;for(n=r;0!=n--;){let r=s[n];if(("_owner"!==r||!e.$$typeof)&&!tq(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function tV(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function tU(e,t){let r=tV(e);return Math.round(t*r)/r}function tK(e){let t=c.useRef(e);return t$(()=>{t.current=e}),t}let tz=(e,t)=>({...(0,tW.cY)(e),options:[e,t]}),tG=(e,t)=>({...(0,tW.BN)(e),options:[e,t]}),tY=(e,t)=>({...(0,tW.UU)(e),options:[e,t]}),tJ=(e,t)=>({...(0,tW.Ej)(e),options:[e,t]}),tZ={...d},tX=tZ.useInsertionEffect||(e=>e());function tQ(e){let t=c.useRef(()=>{});return tX(()=>{t.current=e}),c.useCallback(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return null==t.current?void 0:t.current(...r)},[])}let t0="ArrowUp",t1="ArrowDown",t2="ArrowLeft",t5="ArrowRight";function t3(e,t){let{startingIndex:r=-1,decrement:n=!1,disabledIndices:s,amount:a=1}=void 0===t?{}:t,o=e.current,i=r;do i+=n?-a:a;while(i>=0&&i<=o.length-1&&function(e,t,r){if(r)return r.includes(t);let n=e[t];return null==n||n.hasAttribute("disabled")||"true"===n.getAttribute("aria-disabled")}(o,i,s));return i}var t4="undefined"!=typeof document?c.useLayoutEffect:c.useEffect;let t6=!1,t7=0,t8=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+t7++,t9=tZ.useId||function(){let[e,t]=c.useState(()=>t6?t8():void 0);return t4(()=>{null==e&&t(t8())},[]),c.useEffect(()=>{t6=!0},[]),e},re=c.createContext(null),rt=c.createContext(null),rr=()=>{var e;return(null==(e=c.useContext(re))?void 0:e.id)||null},rn=()=>c.useContext(rt),rs=()=>{},ra=c.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:rs,setState:rs,isInstantPhase:!1}),ro=0,ri=new WeakMap,rl=new WeakSet,rc={},rd=0,ru=e=>e&&(e.host||ru(e.parentNode)),rm=(e,t)=>t.map(t=>{if(e.contains(t))return t;let r=ru(t);return e.contains(r)?r:null}).filter(e=>null!=e),rp=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function rf(e,t){let r=tabbable(e,rp());"prev"===t&&r.reverse();let n=r.indexOf(activeElement(getDocument(e)));return r.slice(n+1)[0]}let rg="data-floating-ui-focusable",rx=null,rh="active",rb="selected";function rv(e,t,r){let n=new Map,s="item"===r,a=e;if(s&&e){let{[rh]:t,[rb]:r,...n}=e;a=n}return{..."floating"===r&&{tabIndex:-1,[rg]:""},...a,...t.map(t=>{let n=t?t[r]:null;return"function"==typeof n?e?n(e):null:n}).concat(e).reduce((e,t)=>(t&&Object.entries(t).forEach(t=>{let[r,a]=t;if(!(s&&[rh,rb].includes(r)))if(0===r.indexOf("on")){if(n.has(r)||n.set(r,[]),"function"==typeof a){var o;null==(o=n.get(r))||o.push(a),e[r]=function(){for(var e,t=arguments.length,s=Array(t),a=0;a<t;a++)s[a]=arguments[a];return null==(e=n.get(r))?void 0:e.map(e=>e(...s)).find(e=>void 0!==e)}}}else e[r]=a}),e),{})}}function ry(e,t,r){switch(e){case"vertical":return t;case"horizontal":return r;default:return t||r}}function rw(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}let rj=e=>({name:"inner",options:e,async fn(t){let{listRef:r,overflowRef:n,onFallbackChange:s,offset:a=0,index:o=0,minItemsVisible:i=4,referenceOverflowThreshold:l=0,scrollRef:c,...d}=(0,tH._3)(e,t),{rects:u,elements:{floating:m}}=t,p=r.current[o],f=(null==c?void 0:c.current)||m,g=m.clientTop||f.clientTop,x=0!==m.clientTop,h=0!==f.clientTop,b=m===f;if(!p)return{};let v={...t,...await tz(-p.offsetTop-m.clientTop-u.reference.height/2-p.offsetHeight/2-a).fn(t)},y=await (0,tW.__)(rw(v,f.scrollHeight+g+m.clientTop),d),w=await (0,tW.__)(v,{...d,elementContext:"reference"}),j=(0,tH.T9)(0,y.top),N=v.y+j,k=(f.scrollHeight>f.clientHeight?e=>e:tH.LI)((0,tH.T9)(0,f.scrollHeight+(x&&b||h?2*g:0)-j-(0,tH.T9)(0,y.bottom)));if(f.style.maxHeight=k+"px",f.scrollTop=j,s){let e=f.offsetHeight<p.offsetHeight*(0,tH.jk)(i,r.current.length)-1||w.top>=-l||w.bottom>=-l;eA.flushSync(()=>s(e))}return n&&(n.current=await (0,tW.__)(rw({...v,y:N},f.offsetHeight+g+m.clientTop),d)),{y:N}}}),rN=(0,c.createContext)({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});rN.displayName="FloatingContext";let rk=(0,c.createContext)(null);function rC({children:e,enabled:t=!0}){var r,n,s,a,o,i;let l,d,u,[m,p]=(0,c.useState)(null),[f,g]=(0,c.useState)(0),x=(0,c.useRef)(null),[h,b]=(0,c.useState)(null);e_(()=>{if(!r)return;let e=new MutationObserver(()=>{let e=window.getComputedStyle(r).maxHeight,t=parseFloat(e);if(isNaN(t))return;let n=parseInt(e);isNaN(n)||t!==n&&(r.style.maxHeight=`${Math.ceil(t)}px`)});return e.observe(r,{attributes:!0,attributeFilter:["style"]}),()=>{e.disconnect()}},[r=h]);let v=t&&null!==m&&null!==h,{to:y="bottom",gap:w=0,offset:j=0,padding:N=0,inner:k}=(n=m,s=h,l=rE(null!=(a=null==n?void 0:n.gap)?a:"var(--anchor-gap, 0)",s),d=rE(null!=(o=null==n?void 0:n.offset)?o:"var(--anchor-offset, 0)",s),u=rE(null!=(i=null==n?void 0:n.padding)?i:"var(--anchor-padding, 0)",s),{...n,gap:l,offset:d,padding:u}),[C,E="center"]=y.split(" ");e_(()=>{v&&g(0)},[v]);let{refs:S,floatingStyles:T,context:A}=function(e){void 0===e&&(e={});let{nodeId:t}=e,r=function(e){let{open:t=!1,onOpenChange:r,elements:n}=e,s=t9(),a=c.useRef({}),[o]=c.useState(()=>(function(){let e=new Map;return{emit(t,r){var n;null==(n=e.get(t))||n.forEach(e=>e(r))},on(t,r){e.set(t,[...e.get(t)||[],r])},off(t,r){var n;e.set(t,(null==(n=e.get(t))?void 0:n.filter(e=>e!==r))||[])}}})()),i=null!=rr(),[l,d]=c.useState(n.reference),u=tQ((e,t,n)=>{a.current.openEvent=e?t:void 0,o.emit("openchange",{open:e,event:t,reason:n,nested:i}),null==r||r(e,t,n)}),m=c.useMemo(()=>({setPositionReference:d}),[]),p=c.useMemo(()=>({reference:l||n.reference||null,floating:n.floating||null,domReference:n.reference}),[l,n.reference,n.floating]);return c.useMemo(()=>({dataRef:a,open:t,onOpenChange:u,elements:p,events:o,floatingId:s,refs:m}),[t,u,p,o,s,m])}({...e,elements:{reference:null,floating:null,...e.elements}}),n=e.rootContext||r,s=n.elements,[a,o]=c.useState(null),[i,l]=c.useState(null),d=(null==s?void 0:s.domReference)||a,u=c.useRef(null),m=rn();t4(()=>{d&&(u.current=d)},[d]);let p=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:s,elements:{reference:a,floating:o}={},transform:i=!0,whileElementsMounted:l,open:d}=e,[u,m]=c.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,f]=c.useState(n);tq(p,n)||f(n);let[g,x]=c.useState(null),[h,b]=c.useState(null),v=c.useCallback(e=>{e!==N.current&&(N.current=e,x(e))},[]),y=c.useCallback(e=>{e!==k.current&&(k.current=e,b(e))},[]),w=a||g,j=o||h,N=c.useRef(null),k=c.useRef(null),C=c.useRef(u),E=null!=l,S=tK(l),T=tK(s),A=tK(d),P=c.useCallback(()=>{if(!N.current||!k.current)return;let e={placement:t,strategy:r,middleware:p};T.current&&(e.platform=T.current),(0,tW.rD)(N.current,k.current,e).then(e=>{let t={...e,isPositioned:!1!==A.current};O.current&&!tq(C.current,t)&&(C.current=t,eA.flushSync(()=>{m(t)}))})},[p,t,r,T,A]);t$(()=>{!1===d&&C.current.isPositioned&&(C.current.isPositioned=!1,m(e=>({...e,isPositioned:!1})))},[d]);let O=c.useRef(!1);t$(()=>(O.current=!0,()=>{O.current=!1}),[]),t$(()=>{if(w&&(N.current=w),j&&(k.current=j),w&&j){if(S.current)return S.current(w,j,P);P()}},[w,j,P,S,E]);let I=c.useMemo(()=>({reference:N,floating:k,setReference:v,setFloating:y}),[v,y]),R=c.useMemo(()=>({reference:w,floating:j}),[w,j]),M=c.useMemo(()=>{let e={position:r,left:0,top:0};if(!R.floating)return e;let t=tU(R.floating,u.x),n=tU(R.floating,u.y);return i?{...e,transform:"translate("+t+"px, "+n+"px)",...tV(R.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,i,R.floating,u.x,u.y]);return c.useMemo(()=>({...u,update:P,refs:I,elements:R,floatingStyles:M}),[u,P,I,R,M])}({...e,elements:{...s,...i&&{reference:i}}}),f=c.useCallback(e=>{let t=(0,tB.vq)(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;l(t),p.refs.setReference(t)},[p.refs]),g=c.useCallback(e=>{((0,tB.vq)(e)||null===e)&&(u.current=e,o(e)),((0,tB.vq)(p.refs.reference.current)||null===p.refs.reference.current||null!==e&&!(0,tB.vq)(e))&&p.refs.setReference(e)},[p.refs]),x=c.useMemo(()=>({...p.refs,setReference:g,setPositionReference:f,domReference:u}),[p.refs,g,f]),h=c.useMemo(()=>({...p.elements,domReference:d}),[p.elements,d]),b=c.useMemo(()=>({...p,...n,refs:x,elements:h,nodeId:t}),[p,x,h,t,n]);return t4(()=>{n.dataRef.current.floatingContext=b;let e=null==m?void 0:m.nodesRef.current.find(e=>e.id===t);e&&(e.context=b)}),c.useMemo(()=>({...p,context:b,refs:x,elements:h}),[p,x,h,b])}({open:v,placement:"selection"===C?"center"===E?"bottom":`bottom-${E}`:"center"===E?`${C}`:`${C}-${E}`,strategy:"absolute",transform:!1,middleware:[tz({mainAxis:"selection"===C?0:w,crossAxis:j}),tG({padding:N}),"selection"!==C&&tY({padding:N}),"selection"===C&&k?rj({...k,padding:N,overflowRef:x,offset:f,minItemsVisible:4,referenceOverflowThreshold:N,onFallbackChange(e){var t,r;if(!e)return;let n=A.elements.floating;if(!n)return;let s=parseFloat(getComputedStyle(n).scrollPaddingBottom)||0,a=Math.min(4,n.childElementCount),o=0,i=0;for(let e of null!=(r=null==(t=A.elements.floating)?void 0:t.childNodes)?r:[])if(ti(e)){let t=e.offsetTop,r=t+e.clientHeight+s,l=n.scrollTop,c=l+n.clientHeight;if(t>=l&&r<=c)a--;else{i=Math.max(0,Math.min(r,c)-Math.max(t,l)),o=e.clientHeight;break}}a>=1&&g(e=>{let t=o*a-i+s;return e>=t?e:t})}}):null,tJ({padding:N,apply({availableWidth:e,availableHeight:t,elements:r}){Object.assign(r.floating.style,{overflow:"auto",maxWidth:`${e}px`,maxHeight:`min(var(--anchor-max-height, 100vh), ${t}px)`})}})].filter(Boolean),whileElementsMounted:tW.ll}),[P=C,O=E]=A.placement.split("-");"selection"===C&&(P="selection");let I=(0,c.useMemo)(()=>({anchor:[P,O].filter(Boolean).join(" ")}),[P,O]),{getReferenceProps:R,getFloatingProps:M}=function(e){void 0===e&&(e=[]);let t=e.map(e=>null==e?void 0:e.reference),r=e.map(e=>null==e?void 0:e.floating),n=e.map(e=>null==e?void 0:e.item),s=c.useCallback(t=>rv(t,e,"reference"),t),a=c.useCallback(t=>rv(t,e,"floating"),r),o=c.useCallback(t=>rv(t,e,"item"),n);return c.useMemo(()=>({getReferenceProps:s,getFloatingProps:a,getItemProps:o}),[s,a,o])}([function(e,t){let{open:r,elements:n}=e,{enabled:s=!0,overflowRef:a,scrollRef:o,onChange:i}=t,l=tQ(i),d=c.useRef(!1),u=c.useRef(null),m=c.useRef(null);c.useEffect(()=>{if(!s)return;function e(e){if(e.ctrlKey||!t||null==a.current)return;let r=e.deltaY,n=a.current.top>=-.5,s=a.current.bottom>=-.5,o=t.scrollHeight-t.clientHeight,i=r<0?-1:1,c=r<0?"max":"min";!(t.scrollHeight<=t.clientHeight)&&(!n&&r>0||!s&&r<0?(e.preventDefault(),eA.flushSync(()=>{l(e=>e+Math[c](r,o*i))})):/firefox/i.test(t_())&&(t.scrollTop+=r))}let t=(null==o?void 0:o.current)||n.floating;if(r&&t)return t.addEventListener("wheel",e),requestAnimationFrame(()=>{u.current=t.scrollTop,null!=a.current&&(m.current={...a.current})}),()=>{u.current=null,m.current=null,t.removeEventListener("wheel",e)}},[s,r,n.floating,a,o,l]);let p=c.useMemo(()=>({onKeyDown(){d.current=!0},onWheel(){d.current=!1},onPointerMove(){d.current=!1},onScroll(){let e=(null==o?void 0:o.current)||n.floating;if(a.current&&e&&d.current){if(null!==u.current){let t=e.scrollTop-u.current;(a.current.bottom<-.5&&t<-1||a.current.top<-.5&&t>1)&&eA.flushSync(()=>l(e=>e+t))}requestAnimationFrame(()=>{u.current=e.scrollTop})}}}),[n.floating,l,a,o]);return c.useMemo(()=>s?{floating:p}:{},[s,p])}(A,{overflowRef:x,onChange:g})]),F=eB(e=>{b(e),S.setFloating(e)});return c.createElement(rk.Provider,{value:p},c.createElement(rN.Provider,{value:{setFloating:F,setReference:S.setReference,styles:T,getReferenceProps:R,getFloatingProps:M,slot:I}},e))}function rE(e,t,r){let n=eD(),s=eB((e,t)=>{if(null==e)return[r,null];if("number"==typeof e)return[e,null];if("string"==typeof e){if(!t)return[r,null];let s=rS(e,t);return[s,r=>{let a=function e(t){let r=/var\((.*)\)/.exec(t);if(r){let t=r[1].indexOf(",");if(-1===t)return[r[1]];let n=r[1].slice(0,t).trim(),s=r[1].slice(t+1).trim();return s?[n,...e(s)]:[n]}return[]}(e);{let o=a.map(e=>window.getComputedStyle(t).getPropertyValue(e));n.requestAnimationFrame(function i(){n.nextFrame(i);let l=!1;for(let[e,r]of a.entries()){let n=window.getComputedStyle(t).getPropertyValue(r);if(o[e]!==n){o[e]=n,l=!0;break}}if(!l)return;let c=rS(e,t);s!==c&&(r(c),s=c)})}return n.dispose}]}return[r,null]}),a=(0,c.useMemo)(()=>s(e,t)[0],[e,t]),[o=a,i]=(0,c.useState)();return e_(()=>{let[r,n]=s(e,t);if(i(r),n)return n(i)},[e,t]),o}function rS(e,t){let r=document.createElement("div");t.appendChild(r),r.style.setProperty("margin-top","0px","important"),r.style.setProperty("margin-top",e,"important");let n=parseFloat(window.getComputedStyle(r).marginTop)||0;return t.removeChild(r),n}rk.displayName="PlacementContext";function rT(e,t){return e?e+"["+t+"]":t}function rA(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}var rP=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(rP||{}),rO=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(rO||{});function rI(){let e,t,r=(e=(0,c.useRef)([]),t=(0,c.useCallback)(t=>{for(let r of e.current)null!=r&&("function"==typeof r?r(t):r.current=t)},[]),(...r)=>{if(!r.every(e=>null==e))return e.current=r,t});return(0,c.useCallback)(e=>(function({ourProps:e,theirProps:t,slot:r,defaultTag:n,features:s,visible:a=!0,name:o,mergeRefs:i}){i=null!=i?i:rM;let l=rF(t,e);if(a)return rR(l,r,n,o,i);let c=null!=s?s:0;if(2&c){let{static:e=!1,...t}=l;if(e)return rR(t,r,n,o,i)}if(1&c){let{unmount:e=!0,...t}=l;return e0(+!e,{0:()=>null,1:()=>rR({...t,hidden:!0,style:{display:"none"}},r,n,o,i)})}return rR(l,r,n,o,i)})({mergeRefs:r,...e}),[r])}function rR(e,t={},r,n,s){let{as:a=r,children:o,refName:i="ref",...l}=rH(e,["unmount","static"]),d=void 0!==e.ref?{[i]:e.ref}:{},u="function"==typeof o?o(t):o;"className"in l&&l.className&&"function"==typeof l.className&&(l.className=l.className(t)),l["aria-labelledby"]&&l["aria-labelledby"]===l.id&&(l["aria-labelledby"]=void 0);let m={};if(t){let e=!1,r=[];for(let[n,s]of Object.entries(t))"boolean"==typeof s&&(e=!0),!0===s&&r.push(n.replace(/([A-Z])/g,e=>`-${e.toLowerCase()}`));if(e)for(let e of(m["data-headlessui-state"]=r.join(" "),r))m[`data-${e}`]=""}if(a===c.Fragment&&(Object.keys(r_(l)).length>0||Object.keys(r_(m)).length>0))if(!(0,c.isValidElement)(u)||Array.isArray(u)&&u.length>1){if(Object.keys(r_(l)).length>0)throw Error(['Passing props on "Fragment"!',"",`The current component <${n} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(r_(l)).concat(Object.keys(r_(m))).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`))}else{var p;let e=u.props,t=null==e?void 0:e.className,r="function"==typeof t?(...e)=>rA(t(...e),l.className):rA(t,l.className),n=rF(u.props,r_(rH(l,["ref"])));for(let e in m)e in n&&delete m[e];return(0,c.cloneElement)(u,Object.assign({},n,m,d,{ref:s((p=u,c.version.split(".")[0]>="19"?p.props.ref:p.ref),d.ref)},r?{className:r}:{}))}return(0,c.createElement)(a,Object.assign({},rH(l,["ref"]),a!==c.Fragment&&d,a!==c.Fragment&&m),u)}function rM(...e){return e.every(e=>null==e)?void 0:t=>{for(let r of e)null!=r&&("function"==typeof r?r(t):r.current=t)}}function rF(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},r={};for(let n of e)for(let e in n)e.startsWith("on")&&"function"==typeof n[e]?(null!=r[e]||(r[e]=[]),r[e].push(n[e])):t[e]=n[e];if(t.disabled||t["aria-disabled"])for(let e in r)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(r[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in r)Object.assign(t,{[e](t,...n){for(let s of r[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;s(t,...n)}}});return t}function rL(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},r={};for(let n of e)for(let e in n)e.startsWith("on")&&"function"==typeof n[e]?(null!=r[e]||(r[e]=[]),r[e].push(n[e])):t[e]=n[e];for(let e in r)Object.assign(t,{[e](...t){for(let n of r[e])null==n||n(...t)}});return t}function rD(e){var t;return Object.assign((0,c.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function r_(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function rH(e,t=[]){let r=Object.assign({},e);for(let e of t)e in r&&delete r[e];return r}var rB=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(rB||{});let rW=rD(function(e,t){var r;let{features:n=1,...s}=e,a={ref:t,"aria-hidden":(2&n)==2||(null!=(r=s["aria-hidden"])?r:void 0),hidden:(4&n)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&n)==4&&(2&n)!=2&&{display:"none"}}};return rI()({ourProps:a,theirProps:s,slot:{},defaultTag:"span",name:"Hidden"})}),r$=(0,c.createContext)(null);function rq({children:e}){let t=(0,c.useContext)(r$);if(!t)return c.createElement(c.Fragment,null,e);let{target:r}=t;return r?(0,eA.createPortal)(c.createElement(c.Fragment,null,e),r):null}function rV({data:e,form:t,disabled:r,onReset:n,overrides:s}){let[a,o]=(0,c.useState)(null);return eD(),c.createElement(rq,null,c.createElement(rU,{setForm:o,formId:t}),(function e(t={},r=null,n=[]){for(let[s,a]of Object.entries(t))!function t(r,n,s){if(Array.isArray(s))for(let[e,a]of s.entries())t(r,rT(n,e.toString()),a);else s instanceof Date?r.push([n,s.toISOString()]):"boolean"==typeof s?r.push([n,s?"1":"0"]):"string"==typeof s?r.push([n,s]):"number"==typeof s?r.push([n,`${s}`]):null==s?r.push([n,""]):e(s,n,r)}(n,rT(r,s),a);return n})(e).map(([e,n])=>c.createElement(rW,{features:rB.Hidden,...r_({key:e,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:t,disabled:r,name:e,value:n,...s})})))}function rU({setForm:e,formId:t}){return t?null:c.createElement(rW,{features:rB.Hidden,as:"input",type:"hidden",hidden:!0,readOnly:!0,ref:t=>{if(!t)return;let r=t.closest("form");r&&e(r)}})}let rK=(0,c.createContext)(void 0);function rz(){return(0,c.useContext)(rK)}let rG=(0,c.createContext)(null);rG.displayName="OpenClosedContext";var rY=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(rY||{});function rJ(){return(0,c.useContext)(rG)}function rZ({value:e,children:t}){return c.createElement(rG.Provider,{value:e},t)}var rX=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(rX||{});function rQ(e,t){let r=t.resolveItems();if(r.length<=0)return null;let n=t.resolveActiveIndex(),s=null!=n?n:-1;switch(e.focus){case 0:for(let e=0;e<r.length;++e)if(!t.resolveDisabled(r[e],e,r))return e;return n;case 1:-1===s&&(s=r.length);for(let e=s-1;e>=0;--e)if(!t.resolveDisabled(r[e],e,r))return e;return n;case 2:for(let e=s+1;e<r.length;++e)if(!t.resolveDisabled(r[e],e,r))return e;return n;case 3:for(let e=r.length-1;e>=0;--e)if(!t.resolveDisabled(r[e],e,r))return e;return n;case 4:for(let n=0;n<r.length;++n)if(t.resolveId(r[n],n,r)===e.id)return n;return n;case 5:return null;default:throw Error("Unexpected object: "+e)}}let r0=(0,c.createContext)(null);r0.displayName="DescriptionContext",Object.assign(rD(function(e,t){let r=(0,c.useId)(),n=tD(),{id:s=`headlessui-description-${r}`,...a}=e,o=function e(){let t=(0,c.useContext)(r0);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),i=tA(t);e_(()=>o.register(s),[s,o.register]);let l=n||!1,d=(0,c.useMemo)(()=>({...o.slot,disabled:l}),[o.slot,l]),u={ref:i,...o.props,id:s};return rI()({ourProps:u,theirProps:a,slot:d,defaultTag:"p",name:o.name||"Description"})}),{});var r1=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(r1||{});let r2=(0,c.createContext)(null);function r5(e){var t,r,n;let s=null!=(r=null==(t=(0,c.useContext)(r2))?void 0:t.value)?r:void 0;return(null!=(n=null==e?void 0:e.length)?n:0)>0?[s,...e].filter(Boolean).join(" "):s}r2.displayName="LabelContext";let r3=Object.assign(rD(function(e,t){var r;let n=(0,c.useId)(),s=function e(){let t=(0,c.useContext)(r2);if(null===t){let t=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),a=rz(),o=tD(),{id:i=`headlessui-label-${n}`,htmlFor:l=null!=a?a:null==(r=s.props)?void 0:r.htmlFor,passive:d=!1,...u}=e,m=tA(t);e_(()=>s.register(i),[i,s.register]);let p=eB(e=>{var t;let r=e.currentTarget;if(!(e.target!==e.currentTarget&&to(t=e.target)&&t.matches('a[href],audio[controls],button,details,embed,iframe,img[usemap],input:not([type="hidden"]),label,select,textarea,video[controls]'))&&(td(r)&&e.preventDefault(),s.props&&"onClick"in s.props&&"function"==typeof s.props.onClick&&s.props.onClick(e),td(r))){let e=document.getElementById(r.htmlFor);if(e){let t=e.getAttribute("disabled");if("true"===t||""===t)return;let r=e.getAttribute("aria-disabled");if("true"===r||""===r)return;(tc(e)&&("file"===e.type||"radio"===e.type||"checkbox"===e.type)||"radio"===e.role||"checkbox"===e.role||"switch"===e.role)&&e.click(),e.focus({preventScroll:!0})}}}),f=o||!1,g=(0,c.useMemo)(()=>({...s.slot,disabled:f}),[s.slot,f]),x={ref:m,...s.props,id:i,htmlFor:l,onClick:p};return d&&("onClick"in x&&(delete x.htmlFor,delete x.onClick),"onClick"in u&&delete u.onClick),rI()({ourProps:x,theirProps:u,slot:g,defaultTag:l?"label":"div",name:s.name||"Label"})}),{});function r4(e){eB(e),(0,c.useRef)(!1)}function r6(){let e,t=(e="undefined"==typeof document,"useSyncExternalStore"in d&&(0,d.useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[r,n]=c.useState(eM.isHandoffComplete);return r&&!1===eM.isHandoffComplete&&n(!1),c.useEffect(()=>{!0!==r&&n(!0)},[r]),c.useEffect(()=>eM.handoff(),[]),!t&&r}let r7=(0,c.createContext)(!1),r8=c.Fragment,r9=rD(function(e,t){let{ownerDocument:r=null,...n}=e,s=(0,c.useRef)(null),a=tA(function(e,t=!0){return Object.assign(e,{[tT]:t})}(e=>{s.current=e}),t),o=tk(s),i=null!=r?r:o,l=function(e){let t=(0,c.useContext)(r7),r=(0,c.useContext)(nt),[n,s]=(0,c.useState)(()=>{var n;if(!t&&null!==r)return null!=(n=r.current)?n:null;if(eM.isServer)return null;let s=null==e?void 0:e.getElementById("headlessui-portal-root");if(s)return s;if(null===e)return null;let a=e.createElement("div");return a.setAttribute("id","headlessui-portal-root"),e.body.appendChild(a)});return n}(i),[d]=(0,c.useState)(()=>{var e;return eM.isServer?null:null!=(e=null==i?void 0:i.createElement("div"))?e:null}),u=(0,c.useContext)(nr),m=r6();e_(()=>{!l||!d||l.contains(d)||(d.setAttribute("data-headlessui-portal",""),l.appendChild(d))},[l,d]),e_(()=>{if(d&&u)return u.register(d)},[u,d]),r4(()=>{var e;l&&d&&(ta(d)&&l.contains(d)&&l.removeChild(d),l.childNodes.length<=0&&(null==(e=l.parentElement)||e.removeChild(l)))});let p=rI();return m&&l&&d?(0,eA.createPortal)(p({ourProps:{ref:a},theirProps:n,slot:{},defaultTag:r8,name:"Portal"}),d):null}),ne=c.Fragment,nt=(0,c.createContext)(null),nr=(0,c.createContext)(null),nn=Object.assign(rD(function(e,t){let r=tA(t),{enabled:n=!0,ownerDocument:s,...a}=e,o=rI();return n?c.createElement(r9,{...a,ownerDocument:s,ref:r}):o({ourProps:{ref:r},theirProps:a,slot:{},defaultTag:r8,name:"Portal"})}),{Group:rD(function(e,t){let{target:r,...n}=e,s={ref:tA(t)},a=rI();return c.createElement(nt.Provider,{value:r},a({ourProps:s,theirProps:n,defaultTag:ne,name:"Popover.Group"}))})});var ns=Object.defineProperty,na=(e,t,r)=>t in e?ns(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,no=(e,t,r)=>(na(e,"symbol"!=typeof t?t+"":t,r),r),ni=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(ni||{}),nl=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(nl||{}),nc=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(nc||{}),nd=(e=>(e[e.OpenListbox=0]="OpenListbox",e[e.CloseListbox=1]="CloseListbox",e[e.GoToOption=2]="GoToOption",e[e.Search=3]="Search",e[e.ClearSearch=4]="ClearSearch",e[e.RegisterOptions=5]="RegisterOptions",e[e.UnregisterOptions=6]="UnregisterOptions",e[e.SetButtonElement=7]="SetButtonElement",e[e.SetOptionsElement=8]="SetOptionsElement",e[e.SortOptions=9]="SortOptions",e))(nd||{});function nu(e,t=e=>e){let r=null!==e.activeOptionIndex?e.options[e.activeOptionIndex]:null,n=tw(t(e.options.slice()),e=>e.dataRef.current.domRef.current),s=r?n.indexOf(r):null;return -1===s&&(s=null),{options:n,activeOptionIndex:s}}let nm={1:e=>e.dataRef.current.disabled||1===e.listboxState?e:{...e,activeOptionIndex:null,pendingFocus:{focus:rX.Nothing},listboxState:1,__demoMode:!1},0(e,t){if(e.dataRef.current.disabled||0===e.listboxState)return e;let r=e.activeOptionIndex,{isSelected:n}=e.dataRef.current,s=e.options.findIndex(e=>n(e.dataRef.current.value));return -1!==s&&(r=s),{...e,pendingFocus:t.focus,listboxState:0,activeOptionIndex:r,__demoMode:!1}},2(e,t){var r,n,s,a,o;if(e.dataRef.current.disabled||1===e.listboxState)return e;let i={...e,searchQuery:"",activationTrigger:null!=(r=t.trigger)?r:1,__demoMode:!1};if(t.focus===rX.Nothing)return{...i,activeOptionIndex:null};if(t.focus===rX.Specific)return{...i,activeOptionIndex:e.options.findIndex(e=>e.id===t.id)};if(t.focus===rX.Previous){let r=e.activeOptionIndex;if(null!==r){let a=e.options[r].dataRef.current.domRef,o=rQ(t,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});if(null!==o){let t=e.options[o].dataRef.current.domRef;if((null==(n=a.current)?void 0:n.previousElementSibling)===t.current||(null==(s=t.current)?void 0:s.previousElementSibling)===null)return{...i,activeOptionIndex:o}}}}else if(t.focus===rX.Next){let r=e.activeOptionIndex;if(null!==r){let n=e.options[r].dataRef.current.domRef,s=rQ(t,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});if(null!==s){let t=e.options[s].dataRef.current.domRef;if((null==(a=n.current)?void 0:a.nextElementSibling)===t.current||(null==(o=t.current)?void 0:o.nextElementSibling)===null)return{...i,activeOptionIndex:s}}}}let l=nu(e),c=rQ(t,{resolveItems:()=>l.options,resolveActiveIndex:()=>l.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...i,...l,activeOptionIndex:c}},3:(e,t)=>{if(e.dataRef.current.disabled||1===e.listboxState)return e;let r=+(""===e.searchQuery),n=e.searchQuery+t.value.toLowerCase(),s=(null!==e.activeOptionIndex?e.options.slice(e.activeOptionIndex+r).concat(e.options.slice(0,e.activeOptionIndex+r)):e.options).find(e=>{var t;return!e.dataRef.current.disabled&&(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(n))}),a=s?e.options.indexOf(s):-1;return -1===a||a===e.activeOptionIndex?{...e,searchQuery:n}:{...e,searchQuery:n,activeOptionIndex:a,activationTrigger:1}},4:e=>e.dataRef.current.disabled||1===e.listboxState||""===e.searchQuery?e:{...e,searchQuery:""},5:(e,t)=>{let r=e.options.concat(t.options),n=e.activeOptionIndex;if(e.pendingFocus.focus!==rX.Nothing&&(n=rQ(e.pendingFocus,{resolveItems:()=>r,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled})),null===e.activeOptionIndex){let{isSelected:t}=e.dataRef.current;if(t){let e=r.findIndex(e=>null==t?void 0:t(e.dataRef.current.value));-1!==e&&(n=e)}}return{...e,options:r,activeOptionIndex:n,pendingFocus:{focus:rX.Nothing},pendingShouldSort:!0}},6:(e,t)=>{let r=e.options,n=[],s=new Set(t.options);for(let[e,t]of r.entries())if(s.has(t.id)&&(n.push(e),s.delete(t.id),0===s.size))break;if(n.length>0)for(let e of(r=r.slice(),n.reverse()))r.splice(e,1);return{...e,options:r,activationTrigger:1}},7:(e,t)=>e.buttonElement===t.element?e:{...e,buttonElement:t.element},8:(e,t)=>e.optionsElement===t.element?e:{...e,optionsElement:t.element},9:e=>e.pendingShouldSort?{...e,...nu(e),pendingShouldSort:!1}:e};class np extends eY{constructor(e){super(e),no(this,"actions",{onChange:e=>{let{onChange:t,compare:r,mode:n,value:s}=this.state.dataRef.current;return e0(n,{0:()=>null==t?void 0:t(e),1:()=>{let n=s.slice(),a=n.findIndex(t=>r(t,e));return -1===a?n.push(e):n.splice(a,1),null==t?void 0:t(n)}})},registerOption:eQ(()=>{let e=[],t=new Set;return[(r,n)=>{t.has(n)||(t.add(n),e.push({id:r,dataRef:n}))},()=>(t.clear(),this.send({type:5,options:e.splice(0)}))]}),unregisterOption:eQ(()=>{let e=[];return[t=>e.push(t),()=>{this.send({type:6,options:e.splice(0)})}]}),goToOption:eQ(()=>{let e=null;return[(t,r)=>{e={type:2,...t,trigger:r}},()=>e&&this.send(e)]}),closeListbox:()=>{this.send({type:1})},openListbox:e=>{this.send({type:0,focus:e})},selectActiveOption:()=>{if(null!==this.state.activeOptionIndex){let{dataRef:e,id:t}=this.state.options[this.state.activeOptionIndex];this.actions.onChange(e.current.value),this.send({type:2,focus:rX.Specific,id:t})}},selectOption:e=>{let t=this.state.options.find(t=>t.id===e);t&&this.actions.onChange(t.dataRef.current.value)},search:e=>{this.send({type:3,value:e})},clearSearch:()=>{this.send({type:4})},setButtonElement:e=>{this.send({type:7,element:e})},setOptionsElement:e=>{this.send({type:8,element:e})}}),no(this,"selectors",{activeDescendantId(e){var t;let r=e.activeOptionIndex,n=e.options;return null===r||null==(t=n[r])?void 0:t.id},isActive(e,t){var r;let n=e.activeOptionIndex,s=e.options;return null!==n&&(null==(r=s[n])?void 0:r.id)===t},shouldScrollIntoView(e,t){return!e.__demoMode&&0===e.listboxState&&0!==e.activationTrigger&&this.isActive(e,t)}}),this.on(5,()=>{requestAnimationFrame(()=>{this.send({type:9})})});{let e=this.state.id,t=e7.get(null);this.disposables.add(t.on(e3.Push,r=>{t.selectors.isTop(r,e)||0!==this.state.listboxState||this.actions.closeListbox()})),this.on(0,()=>t.actions.push(e)),this.on(1,()=>t.actions.pop(e))}}static new({id:e,__demoMode:t=!1}){return new np({id:e,dataRef:{current:{}},listboxState:+!t,options:[],searchQuery:"",activeOptionIndex:null,activationTrigger:1,buttonElement:null,optionsElement:null,pendingShouldSort:!1,pendingFocus:{focus:rX.Nothing},__demoMode:t})}reduce(e,t){return e0(t.type,nm,e,t)}}let nf=(0,c.createContext)(null);function ng(e){let t=(0,c.useContext)(nf);if(null===t){let t=Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,nx),t}return t}function nx({id:e,__demoMode:t=!1}){let r=(0,c.useMemo)(()=>np.new({id:e,__demoMode:t}),[]);return r4(()=>r.dispose()),r}let nh=(0,c.createContext)(null);function nb(e){let t=(0,c.useContext)(nh);if(null===t){let t=Error(`<${e} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,nb),t}return t}nh.displayName="ListboxDataContext";let nv=c.Fragment,ny=(0,c.createContext)(!1),nw=rP.RenderStrategy|rP.Static,nj=c.Fragment,nN=Object.assign(rD(function(e,t){var r;let n,s,a,o,i=(0,c.useId)(),l=tD(),{value:d,defaultValue:u,form:m,name:p,onChange:f,by:g,invalid:x=!1,disabled:h=l||!1,horizontal:b=!1,multiple:v=!1,__demoMode:y=!1,...w}=e,j=b?"horizontal":"vertical",N=tA(t),k=function(e){let[t]=(0,c.useState)(e);return t}(u),[C=v?[]:void 0,E]=function(e,t,r){let[n,s]=(0,c.useState)(r),a=void 0!==e,o=(0,c.useRef)(a),i=(0,c.useRef)(!1),l=(0,c.useRef)(!1);return!a||o.current||i.current?a||!o.current||l.current||(l.current=!0,o.current=a,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")):(i.current=!0,o.current=a,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")),[a?e:n,eB(e=>(a||s(e),null==t?void 0:t(e)))]}(d,f,k),S=nx({id:i,__demoMode:y}),T=(0,c.useRef)({static:!1,hold:!1}),A=(0,c.useRef)(new Map),P=function(e=function(e,t){return null!==e&&null!==t&&"object"==typeof e&&"object"==typeof t&&"id"in e&&"id"in t?e.id===t.id:e===t}){return(0,c.useCallback)((t,r)=>"string"==typeof e?(null==t?void 0:t[e])===(null==r?void 0:r[e]):e(t,r),[e])}(g),O=(0,c.useCallback)(e=>e0(I.mode,{[nl.Multi]:()=>C.some(t=>P(t,e)),[nl.Single]:()=>P(C,e)}),[C]),I=(0,c.useMemo)(()=>({value:C,disabled:h,invalid:x,mode:v?nl.Multi:nl.Single,orientation:j,onChange:E,compare:P,isSelected:O,optionsPropsRef:T,listRef:A}),[C,h,x,v,j,E,P,O,T,A]);e_(()=>{S.state.dataRef.current=I},[I]);let R=e9(S,e=>e.listboxState),M=e7.get(null),F=e9(M,(0,c.useCallback)(e=>M.selectors.isTop(e,i),[M,i])),[L,D]=e9(S,e=>[e.buttonElement,e.optionsElement]);r=[L,D],n=eH((e,t)=>{S.send({type:nd.CloseListbox}),tv(t,tb.Loose)||(e.preventDefault(),null==L||L.focus())}),s=(0,c.useCallback)(function(e,t){if(e.defaultPrevented)return;let s=t(e);if(null!==s&&s.getRootNode().contains(s)&&s.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(r))if(null!==t&&(t.contains(s)||e.composed&&e.composedPath().includes(t)))return;return tv(s,tb.Loose)||-1===s.tabIndex||e.preventDefault(),n.current(e,s)}},[n,r]),a=(0,c.useRef)(null),eH(e=>{var t,r;tN()||(a.current=(null==(r=null==(t=e.composedPath)?void 0:t.call(e))?void 0:r[0])||e.target)}),eH(e=>{if(tN()||!a.current)return;let t=a.current;return a.current=null,s(e,()=>t)}),o=(0,c.useRef)({x:0,y:0}),eH(e=>{o.current.x=e.touches[0].clientX,o.current.y=e.touches[0].clientY}),eH(e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-o.current.x)>=30||Math.abs(t.y-o.current.y)>=30))return s(e,()=>tl(e.target)?e.target:null)}),eH(e=>s(e,()=>{var e;return ti(e=window.document.activeElement)&&"IFRAME"===e.nodeName?window.document.activeElement:null}));let _=(0,c.useMemo)(()=>({open:R===ni.Open,disabled:h,invalid:x,value:C}),[R,h,x,C]),[H,B]=function({inherit:e=!1}={}){let t=r5(),[r,n]=(0,c.useState)([]),s=e?[t,...r].filter(Boolean):r;return[s.length>0?s.join(" "):void 0,(0,c.useMemo)(()=>function(e){let t=eB(e=>(n(t=>[...t,e]),()=>n(t=>{let r=t.slice(),n=r.indexOf(e);return -1!==n&&r.splice(n,1),r}))),r=(0,c.useMemo)(()=>({register:t,slot:e.slot,name:e.name,props:e.props,value:e.value}),[t,e.slot,e.name,e.props,e.value]);return c.createElement(r2.Provider,{value:r},e.children)},[n])]}({inherit:!0}),W=(0,c.useCallback)(()=>{if(void 0!==k)return null==E?void 0:E(k)},[E,k]),$=rI();return c.createElement(B,{value:H,props:{htmlFor:null==L?void 0:L.id},slot:{open:R===ni.Open,disabled:h}},c.createElement(rC,null,c.createElement(nf.Provider,{value:S},c.createElement(nh.Provider,{value:I},c.createElement(rZ,{value:e0(R,{[ni.Open]:rY.Open,[ni.Closed]:rY.Closed})},null!=p&&null!=C&&c.createElement(rV,{disabled:h,data:{[p]:C},form:m,onReset:W}),$({ourProps:{ref:N},theirProps:w,slot:_,defaultTag:nv,name:"Listbox"}))))))}),{Button:rD(function(e,t){var r,n;let s=(0,c.useId)(),a=rz(),o=nb("Listbox.Button"),i=ng("Listbox.Button"),{id:l=a||`headlessui-listbox-button-${s}`,disabled:d=o.disabled||!1,autoFocus:u=!1,...m}=e,p=tA(t,(0,c.useContext)(rN).setReference,i.actions.setButtonElement),f=(0,c.useContext)(rN).getReferenceProps,[g,x,h]=e9(i,e=>[e.listboxState,e.buttonElement,e.optionsElement]);!function(e,{trigger:t,action:r,close:n,select:s}){let a=(0,c.useRef)(null);eH(e=>{ta(null==e?void 0:e.target)&&null!=t&&t.contains(e.target)&&(a.current=new Date)}),eH(e=>{if(null===a.current||!tl(e.target))return;let t=r(e),o=new Date().getTime()-a.current.getTime();switch(a.current=null,t.kind){case 0:return;case 1:o>200&&(s(t.target),n());break;case 2:n()}})}(ni.Open,{trigger:x,action:(0,c.useCallback)(e=>{if(null!=x&&x.contains(e.target))return tE.Ignore;let t=e.target.closest('[role="option"]:not([data-disabled])');return ti(t)?tE.Select(t):null!=h&&h.contains(e.target)?tE.Ignore:tE.Close},[x,h]),close:i.actions.closeListbox,select:i.actions.selectActiveOption});let b=eB(e=>{switch(e.key){case r1.Enter:!function(e){var t,r;let n=null!=(t=null==e?void 0:e.form)?t:e.closest("form");if(n){for(let t of n.elements)if(t!==e&&("INPUT"===t.tagName&&"submit"===t.type||"BUTTON"===t.tagName&&"submit"===t.type||"INPUT"===t.nodeName&&"image"===t.type))return void t.click();null==(r=n.requestSubmit)||r.call(n)}}(e.currentTarget);break;case r1.Space:case r1.ArrowDown:e.preventDefault(),i.actions.openListbox({focus:o.value?rX.Nothing:rX.First});break;case r1.ArrowUp:e.preventDefault(),i.actions.openListbox({focus:o.value?rX.Nothing:rX.Last})}}),v=eB(e=>{e.key===r1.Space&&e.preventDefault()}),y=eB(e=>{var t;if(0===e.button){if(function(e){var t;let r=e.parentElement,n=null;for(;r&&!(ti(t=r)&&"FIELDSET"===t.nodeName);)tu(r)&&(n=r),r=r.parentElement;let s=(null==r?void 0:r.getAttribute("disabled"))==="";return!(s&&function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(tu(t))return!1;t=t.previousElementSibling}return!0}(n))&&s}(e.currentTarget))return e.preventDefault();i.state.listboxState===ni.Open?((0,eA.flushSync)(()=>i.actions.closeListbox()),null==(t=i.state.buttonElement)||t.focus({preventScroll:!0})):(e.preventDefault(),i.actions.openListbox({focus:rX.Nothing}))}}),w=eB(e=>e.preventDefault()),j=r5([l]),N=null!=(n=null==(r=(0,c.useContext)(r0))?void 0:r.value)?n:void 0,{isFocusVisible:k,focusProps:C}=function(e={}){var t,r,n;let{autoFocus:s=!1,isTextInput:a,within:o}=e,i=(0,c.useRef)({isFocused:!1,isFocusVisible:s||ev()}),[l,d]=(0,c.useState)(!1),[u,m]=(0,c.useState)(()=>i.current.isFocused&&i.current.isFocusVisible),p=(0,c.useCallback)(()=>m(i.current.isFocused&&i.current.isFocusVisible),[]),f=(0,c.useCallback)(e=>{i.current.isFocused=e,d(e),p()},[p]);t=e=>{i.current.isFocusVisible=e,p()},r=[],n={isTextInput:a},eh(),(0,c.useEffect)(()=>{let e=(e,r)=>{(function(e,t,r){let n=en(null==r?void 0:r.target),s="undefined"!=typeof window?es(null==r?void 0:r.target).HTMLInputElement:HTMLInputElement,a="undefined"!=typeof window?es(null==r?void 0:r.target).HTMLTextAreaElement:HTMLTextAreaElement,o="undefined"!=typeof window?es(null==r?void 0:r.target).HTMLElement:HTMLElement,i="undefined"!=typeof window?es(null==r?void 0:r.target).KeyboardEvent:KeyboardEvent;return!((e=e||n.activeElement instanceof s&&!ey.has(n.activeElement.type)||n.activeElement instanceof a||n.activeElement instanceof o&&n.activeElement.isContentEditable)&&"keyboard"===t&&r instanceof i&&!ed[r.key])})(!!(null==n?void 0:n.isTextInput),e,r)&&t(ev())};return eo.add(e),()=>{eo.delete(e)}},r);let{focusProps:g}=function(e){let{isDisabled:t,onFocus:r,onBlur:n,onFocusChange:s}=e,a=(0,c.useCallback)(e=>{if(e.target===e.currentTarget)return n&&n(e),s&&s(!1),!0},[n,s]),o=z(a),i=(0,c.useCallback)(e=>{let t=en(e.target),n=t?ej(t):ej();e.target===e.currentTarget&&n===eN(e.nativeEvent)&&(r&&r(e),s&&s(!0),o(e))},[s,r,o]);return{focusProps:{onFocus:!t&&(r||s||n)?i:void 0,onBlur:!t&&(n||s)?a:void 0}}}({isDisabled:o,onFocusChange:f}),{focusWithinProps:x}=function(e){let{isDisabled:t,onBlurWithin:r,onFocusWithin:n,onFocusWithinChange:s}=e,a=(0,c.useRef)({isFocusWithin:!1}),{addGlobalListener:o,removeAllGlobalListeners:i}=ek(),l=(0,c.useCallback)(e=>{e.currentTarget.contains(e.target)&&a.current.isFocusWithin&&!e.currentTarget.contains(e.relatedTarget)&&(a.current.isFocusWithin=!1,i(),r&&r(e),s&&s(!1))},[r,s,a,i]),d=z(l),u=(0,c.useCallback)(e=>{if(!e.currentTarget.contains(e.target))return;let t=en(e.target),r=ej(t);if(!a.current.isFocusWithin&&r===eN(e.nativeEvent)){n&&n(e),s&&s(!0),a.current.isFocusWithin=!0,d(e);let r=e.currentTarget;o(t,"focus",e=>{if(a.current.isFocusWithin&&!ew(r,e.target)){let n=new t.defaultView.FocusEvent("blur",{relatedTarget:e.target});Object.defineProperty(n,"target",{value:r}),Object.defineProperty(n,"currentTarget",{value:r}),l(K(n))}},{capture:!0})}},[n,s,d,o,l]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:u,onBlur:l}}}({isDisabled:!o,onFocusWithinChange:f});return{isFocused:l,isFocusVisible:u,focusProps:o?x:g}}({autoFocus:u}),{isHovered:E,hoverProps:S}=function(e){let{onHoverStart:t,onHoverChange:r,onHoverEnd:n,isDisabled:s}=e,[a,o]=(0,c.useState)(!1),i=(0,c.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,c.useEffect)(eT,[]);let{addGlobalListener:l,removeAllGlobalListeners:d}=ek(),{hoverProps:u,triggerHoverEnd:m}=(0,c.useMemo)(()=>{let e=(e,n)=>{if(i.pointerType=n,s||"touch"===n||i.isHovered||!e.currentTarget.contains(e.target))return;i.isHovered=!0;let c=e.currentTarget;i.target=c,l(en(e.target),"pointerover",e=>{i.isHovered&&i.target&&!ew(i.target,e.target)&&a(e,e.pointerType)},{capture:!0}),t&&t({type:"hoverstart",target:c,pointerType:n}),r&&r(!0),o(!0)},a=(e,t)=>{let s=i.target;i.pointerType="",i.target=null,"touch"!==t&&i.isHovered&&s&&(i.isHovered=!1,d(),n&&n({type:"hoverend",target:s,pointerType:t}),r&&r(!1),o(!1))},c={};return"undefined"!=typeof PointerEvent&&(c.onPointerEnter=t=>{eC&&"mouse"===t.pointerType||e(t,t.pointerType)},c.onPointerLeave=e=>{!s&&e.currentTarget.contains(e.target)&&a(e,e.pointerType)}),{hoverProps:c,triggerHoverEnd:a}},[t,r,n,s,i,l,d]);return(0,c.useEffect)(()=>{s&&m({currentTarget:i.target},i.pointerType)},[s]),{hoverProps:u,isHovered:a}}({isDisabled:d}),{pressed:T,pressProps:A}=function({disabled:e=!1}={}){let t=(0,c.useRef)(null),[r,n]=(0,c.useState)(!1),s=eD(),a=eB(()=>{t.current=null,n(!1),s.dispose()}),o=eB(e=>{if(s.dispose(),null===t.current){t.current=e.currentTarget,n(!0);{let r=eF(e.currentTarget);s.addEventListener(r,"pointerup",a,!1),s.addEventListener(r,"pointermove",e=>{if(t.current){var r,s;let a,o;n((a=e.width/2,o=e.height/2,r={top:e.clientY-o,right:e.clientX+a,bottom:e.clientY+o,left:e.clientX-a},s=t.current.getBoundingClientRect(),!(!r||!s||r.right<s.left||r.left>s.right||r.bottom<s.top||r.top>s.bottom)))}},!1),s.addEventListener(r,"pointercancel",a,!1)}}});return{pressed:r,pressProps:e?{}:{onPointerDown:o,onPointerUp:a,onClick:a}}}({disabled:d}),P=(0,c.useMemo)(()=>({open:g===ni.Open,active:T||g===ni.Open,disabled:d,invalid:o.invalid,value:o.value,hover:E,focus:k,autofocus:u}),[g,o.value,d,E,k,T,o.invalid,u]),O=e9(i,e=>e.listboxState===ni.Open),I=rL(f(),{ref:p,id:l,type:(0,c.useMemo)(()=>{var t;if(e.type)return e.type;let r=null!=(t=e.as)?t:"button";if("string"==typeof r&&"button"===r.toLowerCase()||(null==x?void 0:x.tagName)==="BUTTON"&&!x.hasAttribute("type"))return"button"},[e.type,e.as,x]),"aria-haspopup":"listbox","aria-controls":null==h?void 0:h.id,"aria-expanded":O,"aria-labelledby":j,"aria-describedby":N,disabled:d||void 0,autoFocus:u,onKeyDown:b,onKeyUp:v,onKeyPress:w,onPointerDown:y},C,S,A);return rI()({ourProps:I,theirProps:m,slot:P,defaultTag:"button",name:"Listbox.Button"})}),Label:r3,Options:rD(function(e,t){var r;let n=(0,c.useId)(),{id:s=`headlessui-listbox-options-${n}`,anchor:a,portal:o=!1,modal:i=!0,transition:l=!1,...d}=e,u=(0,c.useMemo)(()=>a?"string"==typeof a?{to:a}:a:null,[a]),[m,p]=(0,c.useState)(null);u&&(o=!0);let f=nb("Listbox.Options"),g=ng("Listbox.Options"),[x,h,b,v]=e9(g,e=>[e.listboxState,e.buttonElement,e.optionsElement,e.__demoMode]),y=tk(h),w=tk(b),j=rJ(),[N,k]=tF(l,m,null!==j?(j&rY.Open)===rY.Open:x===ni.Open);r=g.actions.closeListbox,eH(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&r()}),function(e,t,r=()=>[document.body]){!function(e,t,r=()=>({containers:[]})){let n=(0,c.useSyncExternalStore)(tS.subscribe,tS.getSnapshot,tS.getSnapshot),s=t?n.get(t):void 0;s&&s.count,e_(()=>{if(!(!t||!e))return tS.dispatch("PUSH",t,r),()=>tS.dispatch("POP",t,r)},[e,t])}(tt(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],r]}})}(!v&&i&&x===ni.Open,w),function(e,{allowed:t,disallowed:r}={}){let n=tt(e,"inert-others");e_(()=>{var e,s;if(!n)return;let a=eL();for(let t of null!=(e=null==r?void 0:r())?e:[])t&&a.add(ts(t));let o=null!=(s=null==t?void 0:t())?s:[];for(let e of o){if(!e)continue;let t=eF(e);if(!t)continue;let r=e.parentElement;for(;r&&r!==t.body;){for(let e of r.children)o.some(t=>e.contains(t))||a.add(ts(e));r=r.parentElement}}return a.dispose},[n,t,r])}(!v&&i&&x===ni.Open,{allowed:(0,c.useCallback)(()=>[h,b],[h,b])});let C=!function(e,t){let r=(0,c.useRef)({left:0,top:0});if(e_(()=>{if(!t)return;let e=t.getBoundingClientRect();e&&(r.current=e)},[e,t]),null==t||!e||t===document.activeElement)return!1;let n=t.getBoundingClientRect();return n.top!==r.current.top||n.left!==r.current.left}(x!==ni.Open,h)&&N,E=function(e,t){let[r,n]=(0,c.useState)(t);return e||r===t||n(t),e?r:t}(N&&x===ni.Closed,f.value),S=eB(e=>f.compare(E,e)),T=e9(g,e=>{var t;if(null==u||!(null!=(t=null==u?void 0:u.to)&&t.includes("selection")))return null;let r=e.options.findIndex(e=>S(e.dataRef.current.value));return -1===r&&(r=0),r}),[A,P]=function(e=null){!1===e&&(e=null),"string"==typeof e&&(e={to:e});let t=(0,c.useContext)(rk),r=(0,c.useMemo)(()=>e,[JSON.stringify(e,(e,t)=>{var r;return null!=(r=null==t?void 0:t.outerHTML)?r:t})]);e_(()=>{null==t||t(null!=r?r:null)},[t,r]);let n=(0,c.useContext)(rN);return(0,c.useMemo)(()=>[n.setFloating,e?n.styles:{}],[n.setFloating,e,n.styles])}((()=>{if(null==u)return;if(null===T)return{...u,inner:void 0};let e=Array.from(f.listRef.current.values());return{...u,inner:{listRef:{current:e},index:T}}})()),O=function(){let{getFloatingProps:e,slot:t}=(0,c.useContext)(rN);return(0,c.useCallback)((...r)=>Object.assign({},e(...r),{"data-anchor":t.anchor}),[e,t])}(),I=tA(t,u?A:null,g.actions.setOptionsElement,p),R=eD(),M=eB(e=>{var t,r,n,s;switch(R.dispose(),e.key){case r1.Space:if(""!==g.state.searchQuery)return e.preventDefault(),e.stopPropagation(),g.actions.search(e.key);case r1.Enter:if(e.preventDefault(),e.stopPropagation(),null!==g.state.activeOptionIndex){let{dataRef:e}=g.state.options[g.state.activeOptionIndex];g.actions.onChange(e.current.value)}f.mode===nl.Single&&((0,eA.flushSync)(()=>g.actions.closeListbox()),null==(t=g.state.buttonElement)||t.focus({preventScroll:!0}));break;case e0(f.orientation,{vertical:r1.ArrowDown,horizontal:r1.ArrowRight}):return e.preventDefault(),e.stopPropagation(),g.actions.goToOption({focus:rX.Next});case e0(f.orientation,{vertical:r1.ArrowUp,horizontal:r1.ArrowLeft}):return e.preventDefault(),e.stopPropagation(),g.actions.goToOption({focus:rX.Previous});case r1.Home:case r1.PageUp:return e.preventDefault(),e.stopPropagation(),g.actions.goToOption({focus:rX.First});case r1.End:case r1.PageDown:return e.preventDefault(),e.stopPropagation(),g.actions.goToOption({focus:rX.Last});case r1.Escape:e.preventDefault(),e.stopPropagation(),(0,eA.flushSync)(()=>g.actions.closeListbox()),null==(r=g.state.buttonElement)||r.focus({preventScroll:!0});return;case r1.Tab:e.preventDefault(),e.stopPropagation(),(0,eA.flushSync)(()=>g.actions.closeListbox()),n=g.state.buttonElement,s=e.shiftKey?tf.Previous:tf.Next,function(e,t,{sorted:r=!0,relativeTo:n=null,skipElements:s=[]}={}){var a,o,i;let l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,c=Array.isArray(e)?r?tw(e):e:64&t?function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(tp)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):th(e);s.length>0&&c.length>1&&(c=c.filter(e=>!s.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),n=null!=n?n:l.activeElement;let d=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,c.indexOf(n))-1;if(4&t)return Math.max(0,c.indexOf(n))+1;if(8&t)return c.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),m=32&t?{preventScroll:!0}:{},p=0,f=c.length,g;do{if(p>=f||p+f<=0)return 0;let e=u+p;if(16&t)e=(e+f)%f;else{if(e<0)return 3;if(e>=f)return 1}null==(g=c[e])||g.focus(m),p+=d}while(g!==l.activeElement);6&t&&null!=(i=null==(o=null==(a=g)?void 0:a.matches)?void 0:o.call(a,"textarea,input"))&&i&&g.select()}(th(),s,{relativeTo:n});break;default:1===e.key.length&&(g.actions.search(e.key),R.setTimeout(()=>g.actions.clearSearch(),350))}}),F=e9(g,e=>{var t;return null==(t=e.buttonElement)?void 0:t.id}),L=(0,c.useMemo)(()=>({open:x===ni.Open}),[x]),D=rL(u?O():{},{id:s,ref:I,"aria-activedescendant":e9(g,g.selectors.activeDescendantId),"aria-multiselectable":f.mode===nl.Multi||void 0,"aria-labelledby":F,"aria-orientation":f.orientation,onKeyDown:M,role:"listbox",tabIndex:x===ni.Open?0:void 0,style:{...d.style,...P,"--button-width":function(e,t=!1){let[r,n]=(0,c.useReducer)(()=>({}),{}),s=(0,c.useMemo)(()=>(function(e){if(null===e)return{width:0,height:0};let{width:t,height:r}=e.getBoundingClientRect();return{width:t,height:r}})(e),[e,r]);return e_(()=>{if(!e)return;let t=new ResizeObserver(n);return t.observe(e),()=>{t.disconnect()}},[e]),t?{width:`${s.width}px`,height:`${s.height}px`}:s}(h,!0).width},...tM(k)}),_=rI(),H=(0,c.useMemo)(()=>f.mode===nl.Multi?f:{...f,isSelected:S},[f,S]);return c.createElement(nn,{enabled:!!o&&(e.static||N),ownerDocument:y},c.createElement(nh.Provider,{value:H},_({ourProps:D,theirProps:d,slot:L,defaultTag:"div",features:nw,visible:C,name:"Listbox.Options"})))}),Option:rD(function(e,t){let r,n,s,a=(0,c.useId)(),{id:o=`headlessui-listbox-option-${a}`,disabled:i=!1,value:l,...d}=e,u=!0===(0,c.useContext)(ny),m=nb("Listbox.Option"),p=ng("Listbox.Option"),f=e9(p,e=>p.selectors.isActive(e,o)),g=m.isSelected(l),x=(0,c.useRef)(null),h=(r=(0,c.useRef)(""),n=(0,c.useRef)(""),eB(()=>{let e=x.current;if(!e)return"";let t=e.innerText;if(r.current===t)return n.current;let s=(function(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let r=e.getAttribute("aria-labelledby");if(r){let e=r.split(" ").map(e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():tO(t).trim()}return null}).filter(Boolean);if(e.length>0)return e.join(", ")}return tO(e).trim()})(e).trim().toLowerCase();return r.current=t,n.current=s,s})),b=eH({disabled:i,value:l,domRef:x,get textValue(){return h()}}),v=tA(t,x,e=>{e?m.listRef.current.set(o,e):m.listRef.current.delete(o)}),y=e9(p,e=>p.selectors.shouldScrollIntoView(e,o));e_(()=>{if(y)return eL().requestAnimationFrame(()=>{var e,t;null==(t=null==(e=x.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})},[y,x]),e_(()=>{if(!u)return p.actions.registerOption(o,b),()=>p.actions.unregisterOption(o)},[b,o,u]);let w=eB(e=>{var t;if(i)return e.preventDefault();p.actions.onChange(l),m.mode===nl.Single&&((0,eA.flushSync)(()=>p.actions.closeListbox()),null==(t=p.state.buttonElement)||t.focus({preventScroll:!0}))}),j=eB(()=>{if(i)return p.actions.goToOption({focus:rX.Nothing});p.actions.goToOption({focus:rX.Specific,id:o})}),N=(s=(0,c.useRef)([-1,-1]),{wasMoved(e){let t=tI(e);return(s.current[0]!==t[0]||s.current[1]!==t[1])&&(s.current=t,!0)},update(e){s.current=tI(e)}}),k=eB(e=>{N.update(e),!i&&(f||p.actions.goToOption({focus:rX.Specific,id:o},nc.Pointer))}),C=eB(e=>{N.wasMoved(e)&&(i||f||p.actions.goToOption({focus:rX.Specific,id:o},nc.Pointer))}),E=eB(e=>{N.wasMoved(e)&&(i||f&&p.actions.goToOption({focus:rX.Nothing}))}),S=(0,c.useMemo)(()=>({active:f,focus:f,selected:g,disabled:i,selectedOption:g&&u}),[f,g,i,u]),T=u?{}:{id:o,ref:v,role:"option",tabIndex:!0===i?void 0:-1,"aria-disabled":!0===i||void 0,"aria-selected":g,disabled:void 0,onClick:w,onFocus:j,onPointerEnter:k,onMouseEnter:k,onPointerMove:C,onMouseMove:C,onPointerLeave:E,onMouseLeave:E},A=rI();return!g&&u?null:A({ourProps:T,theirProps:d,slot:S,defaultTag:"div",name:"Listbox.Option"})}),SelectedOption:rD(function(e,t){let{options:r,placeholder:n,...s}=e,a={ref:tA(t)},o=nb("ListboxSelectedOption"),i=(0,c.useMemo)(()=>({}),[]),l=void 0===o.value||null===o.value||o.mode===nl.Multi&&Array.isArray(o.value)&&0===o.value.length,d=rI();return c.createElement(ny.Provider,{value:!0},d({ourProps:a,theirProps:{...s,children:c.createElement(c.Fragment,null,n&&l?n:r)},slot:i,defaultTag:nj,name:"ListboxSelectedOption"}))})});function nk(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:nP)!==c.Fragment||1===c.Children.count(e.children)}let nC=(0,c.createContext)(null);nC.displayName="TransitionContext";var nE=(e=>(e.Visible="visible",e.Hidden="hidden",e))(nE||{});let nS=(0,c.createContext)(null);function nT(e){return"children"in e?nT(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function nA(e,t){let r,n=eH(e),s=(0,c.useRef)([]),a=(r=(0,c.useRef)(!1),e_(()=>(r.current=!0,()=>{r.current=!1}),[]),r),o=eD(),i=eB((e,t=rO.Hidden)=>{let r=s.current.findIndex(({el:t})=>t===e);-1!==r&&(e0(t,{[rO.Unmount](){s.current.splice(r,1)},[rO.Hidden](){s.current[r].state="hidden"}}),o.microTask(()=>{var e;!nT(s)&&a.current&&(null==(e=n.current)||e.call(n))}))}),l=eB(e=>{let t=s.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):s.current.push({el:e,state:"visible"}),()=>i(e,rO.Unmount)}),d=(0,c.useRef)([]),u=(0,c.useRef)(Promise.resolve()),m=(0,c.useRef)({enter:[],leave:[]}),p=eB((e,r,n)=>{d.current.splice(0),t&&(t.chains.current[r]=t.chains.current[r].filter(([t])=>t!==e)),null==t||t.chains.current[r].push([e,new Promise(e=>{d.current.push(e)})]),null==t||t.chains.current[r].push([e,new Promise(e=>{Promise.all(m.current[r].map(([e,t])=>t)).then(()=>e())})]),"enter"===r?u.current=u.current.then(()=>null==t?void 0:t.wait.current).then(()=>n(r)):n(r)}),f=eB((e,t,r)=>{Promise.all(m.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=d.current.shift())||e()}).then(()=>r(t))});return(0,c.useMemo)(()=>({children:s,register:l,unregister:i,onStart:p,onStop:f,wait:u,chains:m}),[l,i,s,p,f,m,u])}nS.displayName="NestingContext";let nP=c.Fragment,nO=rP.RenderStrategy,nI=rD(function(e,t){let{show:r,appear:n=!1,unmount:s=!0,...a}=e,o=(0,c.useRef)(null),i=tA(...nk(e)?[o,t]:null===t?[]:[t]);r6();let l=rJ();if(void 0===r&&null!==l&&(r=(l&rY.Open)===rY.Open),void 0===r)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[d,u]=(0,c.useState)(r?"visible":"hidden"),m=nA(()=>{r||u("hidden")}),[p,f]=(0,c.useState)(!0),g=(0,c.useRef)([r]);e_(()=>{!1!==p&&g.current[g.current.length-1]!==r&&(g.current.push(r),f(!1))},[g,r]);let x=(0,c.useMemo)(()=>({show:r,appear:n,initial:p}),[r,n,p]);e_(()=>{r?u("visible"):nT(m)||null===o.current||u("hidden")},[r,m]);let h={unmount:s},b=eB(()=>{var t;p&&f(!1),null==(t=e.beforeEnter)||t.call(e)}),v=eB(()=>{var t;p&&f(!1),null==(t=e.beforeLeave)||t.call(e)}),y=rI();return c.createElement(nS.Provider,{value:m},c.createElement(nC.Provider,{value:x},y({ourProps:{...h,as:c.Fragment,children:c.createElement(nR,{ref:i,...h,...a,beforeEnter:b,beforeLeave:v})},theirProps:{},defaultTag:c.Fragment,features:nO,visible:"visible"===d,name:"Transition"})))}),nR=rD(function(e,t){var r,n;let{transition:s=!0,beforeEnter:a,afterEnter:o,beforeLeave:i,afterLeave:l,enter:d,enterFrom:u,enterTo:m,entered:p,leave:f,leaveFrom:g,leaveTo:x,...h}=e,[b,v]=(0,c.useState)(null),y=(0,c.useRef)(null),w=nk(e),j=tA(...w?[y,t,v]:null===t?[]:[t]),N=null==(r=h.unmount)||r?rO.Unmount:rO.Hidden,{show:k,appear:C,initial:E}=function(){let e=(0,c.useContext)(nC);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[S,T]=(0,c.useState)(k?"visible":"hidden"),A=function(){let e=(0,c.useContext)(nS);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:P,unregister:O}=A;e_(()=>P(y),[P,y]),e_(()=>{if(N===rO.Hidden&&y.current)return k&&"visible"!==S?void T("visible"):e0(S,{hidden:()=>O(y),visible:()=>P(y)})},[S,y,P,O,k,N]);let I=r6();e_(()=>{if(w&&I&&"visible"===S&&null===y.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[y,S,I,w]);let R=C&&k&&E,M=(0,c.useRef)(!1),F=nA(()=>{M.current||(T("hidden"),O(y))},A),[,L]=tF(!(!s||!w||!I||E&&!C),b,k,{start:eB(e=>{M.current=!0,F.onStart(y,e?"enter":"leave",e=>{"enter"===e?null==a||a():"leave"===e&&(null==i||i())})}),end:eB(e=>{let t=e?"enter":"leave";M.current=!1,F.onStop(y,t,e=>{"enter"===e?null==o||o():"leave"===e&&(null==l||l())}),"leave"!==t||nT(F)||(T("hidden"),O(y))})}),D=r_({ref:j,className:(null==(n=rA(h.className,R&&d,R&&u,L.enter&&d,L.enter&&L.closed&&u,L.enter&&!L.closed&&m,L.leave&&f,L.leave&&!L.closed&&g,L.leave&&L.closed&&x,!L.transition&&k&&p))?void 0:n.trim())||void 0,...tM(L)}),_=0;"visible"===S&&(_|=rY.Open),"hidden"===S&&(_|=rY.Closed),k&&"hidden"===S&&(_|=rY.Opening),k||"visible"!==S||(_|=rY.Closing);let H=rI();return c.createElement(nS.Provider,{value:F},c.createElement(rZ,{value:_},H({ourProps:D,theirProps:h,defaultTag:nP,features:nO,visible:"visible"===S,name:"Transition.Child"})))}),nM=rD(function(e,t){let r=null!==(0,c.useContext)(nC),n=null!==rJ();return c.createElement(c.Fragment,null,!r&&n?c.createElement(nI,{ref:t,...e}):c.createElement(nR,{ref:t,...e}))}),nF=Object.assign(nI,{Child:nM,Root:nI});var nL=r(66368),nD=r(62525),n_=r(19163);let nH=nL.MG.map(e=>({value:e.id,label:e.name}));function nB({node:e,onUpdate:t,onClose:r}){let[n,s]=(0,c.useState)(e.data.config),[a,o]=(0,c.useState)(null),[i,d]=(0,c.useState)(!1),[u,m]=(0,c.useState)(null),[p,f]=(0,c.useState)(null),[g,h]=(0,c.useState)(!1),[b,v]=(0,c.useState)(null),[y,w]=(0,c.useState)([]),[j,N]=(0,c.useState)(!1),[k,C]=(0,c.useState)(null);(0,c.useCallback)(async()=>{d(!0),m(null),o(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch models from database.");t.models?o(t.models):o([])}catch(e){m(e.message),o([])}finally{d(!1)}},[]),(0,c.useCallback)(async()=>{N(!0),C(null);try{let e=await fetch("/api/user/custom-roles");if(!e.ok)throw Error("Failed to fetch custom roles");let t=await e.json();w(t)}catch(e){C(e.message),w([])}finally{N(!1)}},[]);let E=(r,a)=>{let o={...n,[r]:a};s(o),t({config:o,isConfigured:P(e.type,o)})},S=(r,a)=>{let o={...n,[r]:a};"parameters"!==r&&n.parameters||(o.parameters={maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0,...n.parameters,..."parameters"===r?a:{}}),s(o),t({config:o,isConfigured:P(e.type,o)})},T=(0,c.useMemo)(()=>{if(a&&("provider"===e.type||"vision"===e.type||"planner"===e.type||"browsing"===e.type)){let t=nL.MG.find(e=>e.id===n.providerId);if(!t)return[];let r=t=>"vision"===e.type?t.filter(e=>e.modality&&(e.modality.includes("multimodal")||e.modality.includes("vision")||e.modality.includes("image"))):t;if("openrouter"===t.id)return r(a).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===t.id){let t=[],r=a.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id);r&&("provider"===e.type||"planner"===e.type||"browsing"===e.type||"vision"===e.type&&r.modality?.includes("multimodal"))&&t.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"});let n=a.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id);return n&&("provider"===e.type||"planner"===e.type||"browsing"===e.type||"vision"===e.type&&n.modality?.includes("multimodal"))&&t.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),t.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return r(a.filter(e=>e.provider_id===t.id)).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[a,n,e.type]),A=(0,c.useMemo)(()=>{if(!a||"provider"!==e.type&&"vision"!==e.type&&"planner"!==e.type&&"browsing"!==e.type||!n?.modelId)return{maxTokens:4096,minTokens:1};let t=a.find(e=>e.id===n.modelId);return t?{maxTokens:t.output_token_limit||t.context_window||4096,minTokens:1}:{maxTokens:4096,minTokens:1}},[a,n,e.type]),P=(e,t)=>{switch(e){case"provider":case"vision":case"planner":return!!(t.providerId&&t.modelId&&t.apiKey);case"roleAgent":if("new"===t.roleType)return!!(t.newRoleName&&t.customPrompt);return!!(t.roleId&&t.roleName);case"centralRouter":return!!t.routingStrategy;case"tool":return!!t.toolType;case"browsing":return!!(t.providerId&&t.modelId);case"memory":return!!t.memoryName;default:return!0}},O=()=>(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,l.jsxs)("select",{value:n?.providerId||"",onChange:r=>{let a={...n,providerId:r.target.value,modelId:"",parameters:n.parameters||{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,l.jsx)("option",{value:"",children:"Select Provider"}),nH.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key *"}),(0,l.jsx)("input",{type:"password",value:n?.apiKey||"",onChange:e=>S("apiKey",e.target.value),placeholder:"Enter your API key for this provider",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]",required:!0}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required: Enter your own API key for this AI provider (BYOK)"}),i&&null===a&&(0,l.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,l.jsx)(_.A,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),u&&(0,l.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error: ",u]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Model Variant"}),(0,l.jsx)("select",{value:n?.modelId||"",onChange:r=>{let o=r.target.value,i={...n,modelId:o};if(o&&a){let e=a.find(e=>e.id===o);if(e){let t=e.output_token_limit||e.context_window||4096,r=Math.min(t,Math.max(1024,Math.floor(.75*t))),s=n?.parameters||{};i={...i,parameters:{...s,maxTokens:r}}}}s(i),t({config:i,isConfigured:P(e.type,i)})},disabled:!n?.providerId||!T.length,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30",children:n?.providerId?T.length>0?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("option",{value:"",children:"Select Model"}),T.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,l.jsx)("option",{value:"",disabled:!0,children:i?"Loading models...":"No models available"}):(0,l.jsx)("option",{value:"",disabled:!0,children:"Select a provider first"})})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature",(0,l.jsx)("span",{className:"text-xs text-gray-400 ml-1",children:"(0.0 - 2.0)"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:n?.parameters?.temperature||1,onChange:e=>{let t=parseFloat(e.target.value);S("parameters",{...n?.parameters||{},temperature:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,l.jsx)("div",{className:"flex items-center space-x-2",children:(0,l.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:n?.parameters?.temperature||1,onChange:e=>{let t=Math.min(2,Math.max(0,parseFloat(e.target.value)||1));S("parameters",{...n?.parameters||{},temperature:t})},className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,l.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"maxTokens",className:"block text-sm font-medium text-gray-300 mb-2",children:["Max Tokens",(0,l.jsxs)("span",{className:"text-xs text-gray-400 ml-1",children:["(",A.minTokens," - ",A.maxTokens.toLocaleString(),")"]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("input",{type:"range",id:"maxTokens",min:A.minTokens,max:A.maxTokens,step:"1",value:n?.parameters?.maxTokens||A.maxTokens,onChange:e=>{let t=parseInt(e.target.value);S("parameters",{...n?.parameters||{},maxTokens:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-xs text-gray-400",children:"Minimal"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"number",min:A.minTokens,max:A.maxTokens,step:"1",value:n?.parameters?.maxTokens||A.maxTokens,onChange:e=>{let t=Math.min(A.maxTokens,Math.max(A.minTokens,parseInt(e.target.value)||A.maxTokens));S("parameters",{...n?.parameters||{},maxTokens:t})},className:"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"}),(0,l.jsx)("button",{type:"button",onClick:()=>{S("parameters",{...n?.parameters||{},maxTokens:A.maxTokens})},className:"text-xs text-orange-400 hover:text-orange-300 underline",children:"Max"})]}),(0,l.jsx)("span",{className:"text-xs text-gray-400",children:"Maximum"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-400",children:"Controls the maximum number of tokens the model can generate. Higher values allow longer responses but cost more."})]})]}),n?.providerId==="openrouter"&&(0,l.jsxs)("div",{className:"p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg",children:[(0,l.jsx)("div",{className:"text-sm text-blue-300 font-medium mb-1",children:"\uD83C\uDF10 OpenRouter"}),(0,l.jsx)("div",{className:"text-xs text-blue-200",children:"Access to 300+ models from multiple providers with a single API key."})]})]}),I=()=>(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,l.jsxs)("select",{value:n?.providerId||"",onChange:r=>{let a={...n,providerId:r.target.value,modelId:"",parameters:n.parameters||{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,l.jsx)("option",{value:"",children:"Select Provider"}),nH.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key *"}),(0,l.jsx)("input",{type:"password",value:n?.apiKey||"",onChange:e=>S("apiKey",e.target.value),placeholder:"Enter your API key for this provider",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]",required:!0}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required: Enter your own API key for this AI provider (BYOK)"}),i&&null===a&&(0,l.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,l.jsx)(_.A,{className:"w-4 h-4 mr-2"}),"Fetching models..."]}),u&&(0,l.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error: ",u]})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Vision Model",(0,l.jsx)("span",{className:"text-xs text-purple-400 ml-1",children:"(Multimodal Only)"})]}),(0,l.jsx)("select",{value:n?.modelId||"",onChange:r=>{let o=r.target.value,i={...n,modelId:o};if(o&&a){let e=a.find(e=>e.id===o);if(e){let t=e.output_token_limit||e.context_window||4096,r=Math.min(t,Math.max(1024,Math.floor(.75*t))),s=n?.parameters||{};i={...i,parameters:{...s,maxTokens:r}}}}s(i),t({config:i,isConfigured:P(e.type,i)})},disabled:!n?.providerId||!T.length,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35] disabled:opacity-50 disabled:bg-gray-800/30",children:n?.providerId?T.length>0?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("option",{value:"",children:"Select Vision Model"}),T.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,l.jsx)("option",{value:"",disabled:!0,children:i?"Loading models...":"No vision models available"}):(0,l.jsx)("option",{value:"",disabled:!0,children:"Select a provider first"})}),0===T.length&&n?.providerId&&!i&&(0,l.jsx)("p",{className:"mt-2 text-xs text-yellow-400 bg-yellow-900/20 p-2 rounded-lg",children:"⚠️ No multimodal models found for this provider. Vision nodes require models with image processing capabilities."})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-300 mb-2",children:"Temperature (0.0 - 2.0)"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:n?.parameters?.temperature||1,onChange:e=>{let t=parseFloat(e.target.value);S("parameters",{...n?.parameters||{},temperature:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-xs text-gray-400",children:"Conservative"}),(0,l.jsx)("div",{className:"flex items-center space-x-2",children:(0,l.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:n?.parameters?.temperature||1,onChange:e=>{let t=Math.min(2,Math.max(0,parseFloat(e.target.value)||1));S("parameters",{...n?.parameters||{},temperature:t})},className:"w-16 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"})}),(0,l.jsx)("span",{className:"text-xs text-gray-400",children:"Creative"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-400",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{htmlFor:"maxTokens",className:"block text-sm font-medium text-gray-300 mb-2",children:["Max Tokens",(0,l.jsxs)("span",{className:"text-xs text-gray-400 ml-1",children:["(",A.minTokens," - ",A.maxTokens.toLocaleString(),")"]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("input",{type:"range",id:"maxTokens",min:A.minTokens,max:A.maxTokens,step:"1",value:n?.parameters?.maxTokens||A.maxTokens,onChange:e=>{let t=parseInt(e.target.value);S("parameters",{...n?.parameters||{},maxTokens:t})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-xs text-gray-400",children:"Minimal"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"number",min:A.minTokens,max:A.maxTokens,step:"1",value:n?.parameters?.maxTokens||A.maxTokens,onChange:e=>{let t=Math.min(A.maxTokens,Math.max(A.minTokens,parseInt(e.target.value)||A.maxTokens));S("parameters",{...n?.parameters||{},maxTokens:t})},className:"w-20 px-2 py-1 text-xs border border-gray-700 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center bg-gray-800/50 text-white"}),(0,l.jsx)("button",{type:"button",onClick:()=>{S("parameters",{...n?.parameters||{},maxTokens:A.maxTokens})},className:"text-xs text-orange-400 hover:text-orange-300 underline",children:"Max"})]}),(0,l.jsx)("span",{className:"text-xs text-gray-400",children:"Maximum"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-400",children:"Controls the maximum number of tokens the model can generate for vision analysis."})]})]}),n?.providerId==="openrouter"&&(0,l.jsxs)("div",{className:"p-3 bg-purple-900/20 border border-purple-700/30 rounded-lg",children:[(0,l.jsx)("div",{className:"text-sm text-purple-300 font-medium mb-1",children:"\uD83D\uDC41️ Vision Models"}),(0,l.jsx)("div",{className:"text-xs text-purple-200",children:"Access to multimodal models from multiple providers for image analysis and vision tasks."})]})]}),R=()=>{let r=[...nD.p2.map(e=>({id:e.id,name:e.name,description:e.description,type:"predefined"})),...y.map(e=>({id:e.role_id,name:e.name,description:e.description,type:"custom"}))],a=a=>{if("create_new"===a){let r={...n,roleType:"new",roleId:"",roleName:"",newRoleName:"",newRoleDescription:"",customPrompt:""};s(r),t({config:r,isConfigured:P(e.type,r)})}else{let o=r.find(e=>e.id===a);if(o){let r={...n,roleType:o.type,roleId:o.id,roleName:o.name,customPrompt:o.description||""};s(r),t({config:r,isConfigured:P(e.type,r)})}}},o=(r,a)=>{let o={...n,[r]:a};s(o),t({config:o,isConfigured:P(e.type,o)})};return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Role"}),j?(0,l.jsx)("div",{className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-gray-400",children:"Loading roles..."}):(0,l.jsxs)("select",{value:n?.roleType==="new"?"create_new":n?.roleId||"",onChange:e=>a(e.target.value),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,l.jsx)("option",{value:"",children:"Select a role..."}),(0,l.jsx)("optgroup",{label:"System Roles",children:nD.p2.map(e=>(0,l.jsx)("option",{value:e.id,children:e.name},e.id))}),y.length>0&&(0,l.jsx)("optgroup",{label:"Your Custom Roles",children:y.map(e=>(0,l.jsx)("option",{value:e.role_id,children:e.name},e.role_id))}),(0,l.jsx)("optgroup",{label:"Create New",children:(0,l.jsx)("option",{value:"create_new",children:"+ Create New Role"})})]}),k&&(0,l.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error loading roles: ",k]})]}),n?.roleType!=="new"&&n?.roleId&&(0,l.jsxs)("div",{className:"p-3 bg-gray-800/50 border border-gray-700/50 rounded-lg",children:[(0,l.jsx)("div",{className:"text-sm font-medium text-white mb-1",children:n.roleName}),n.customPrompt&&(0,l.jsx)("div",{className:"text-xs text-gray-300",children:n.customPrompt})]}),n?.roleType==="new"&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"New Role Name"}),(0,l.jsx)("input",{type:"text",value:n.newRoleName||"",onChange:e=>o("newRoleName",e.target.value),placeholder:"e.g., Data Analyst, Creative Writer",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Role Description"}),(0,l.jsx)("input",{type:"text",value:n.newRoleDescription||"",onChange:e=>o("newRoleDescription",e.target.value),placeholder:"Brief description of this role's purpose",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions"}),(0,l.jsx)("textarea",{value:n.customPrompt||"",onChange:e=>o("customPrompt",e.target.value),placeholder:"Enter detailed instructions for this role...",rows:4,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"flex items-center",children:[(0,l.jsx)("input",{type:"checkbox",checked:n?.memoryEnabled||!1,onChange:e=>E("memoryEnabled",e.target.checked),className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"}),(0,l.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable memory"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1 ml-6",children:"Allow this role to remember context from previous interactions"})]})]})},M=()=>(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Label"}),(0,l.jsx)("input",{type:"text",value:e.data.label,onChange:e=>t({label:e.target.value}),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description"}),(0,l.jsx)("textarea",{value:e.data.description||"",onChange:e=>t({description:e.target.value}),rows:3,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-[#ff6b35]"})]})]}),F=()=>(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Routing Strategy"}),(0,l.jsxs)("select",{value:n?.routingStrategy||"smart",onChange:r=>{let a={...n,routingStrategy:r.target.value};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:[(0,l.jsx)("option",{value:"smart",children:"Smart Routing"}),(0,l.jsx)("option",{value:"round_robin",children:"Round Robin"}),(0,l.jsx)("option",{value:"load_balanced",children:"Load Balanced"}),(0,l.jsx)("option",{value:"priority",children:"Priority Based"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"How the router selects between available AI providers"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Retries"}),(0,l.jsx)("input",{type:"number",min:"0",max:"10",value:n?.maxRetries||3,onChange:r=>{let a={...n,maxRetries:parseInt(r.target.value)||3};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Number of retry attempts on failure"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (ms)"}),(0,l.jsx)("input",{type:"number",min:"1000",max:"300000",step:"1000",value:n?.timeout||3e4,onChange:r=>{let a={...n,timeout:parseInt(r.target.value)||3e4};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Request timeout in milliseconds"})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Enable Caching"}),(0,l.jsx)("input",{type:"checkbox",checked:n?.enableCaching??!0,onChange:r=>{let a={...n,enableCaching:r.target.checked};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-400",children:"Cache responses to improve performance"})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Debug Mode"}),(0,l.jsx)("input",{type:"checkbox",checked:n?.debugMode??!1,onChange:r=>{let a={...n,debugMode:r.target.checked};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-4 h-4 text-[#ff6b35] bg-gray-700 border-gray-600 rounded focus:ring-[#ff6b35] focus:ring-2"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-400",children:"Enable detailed logging for debugging"})]})]}),L=()=>{let r=[{value:"",label:"Select a tool...",icon:null,description:""},...Object.keys(n_.RW).filter(e=>"supabase"!==e).map(e=>({value:e,label:n_.RW[e],icon:n_.s0[e],description:n_.r0[e]}))],a=async e=>{if(e)try{let t=await fetch(`/api/auth/tools/status?tool=${e}`);if(t.ok){let e=await t.json();f(e)}}catch(e){}},o=async r=>{if(v(null),!r){let a={...n,toolType:r,toolConfig:{},connectionStatus:"disconnected",isAuthenticated:!1};s(a),t({config:a,isConfigured:P(e.type,a)});return}try{let a=await fetch(`/api/auth/tools/status?tool=${r}`);if(a.ok){let o=await a.json();f(o);let i={...n,toolType:r,toolConfig:{},connectionStatus:o.is_connected?"connected":"disconnected",isAuthenticated:o.is_connected||!1,providerUserEmail:o.provider_user_email,providerUserName:o.provider_user_name};s(i),t({config:i,isConfigured:P(e.type,i)})}else{let a={...n,toolType:r,toolConfig:{},connectionStatus:"disconnected",isAuthenticated:!1};s(a),t({config:a,isConfigured:P(e.type,a)})}}catch(o){let a={...n,toolType:r,toolConfig:{},connectionStatus:"disconnected",isAuthenticated:!1};s(a),t({config:a,isConfigured:P(e.type,a)})}},i=async()=>{if(n?.toolType){h(!0),v(null);try{let r="google";"notion"===n.toolType&&(r="notion");let o=await fetch(`/api/auth/tools/${r}/authorize`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({toolType:n.toolType,returnUrl:window.location.pathname})});if(!o.ok)throw Error("Failed to initiate OAuth flow");let{authUrl:i}=await o.json(),l=window.open(i,"oauth-popup","width=600,height=700,scrollbars=yes,resizable=yes"),c=r=>{if(r.origin===window.location.origin)if("OAUTH_SUCCESS"===r.data.type){l?.close(),a(n.toolType);let r={...n,connectionStatus:"connected",isAuthenticated:!0};s(r),t({config:r,isConfigured:P(e.type,r)}),h(!1),window.removeEventListener("message",c)}else"OAUTH_ERROR"===r.data.type&&(l?.close(),v(r.data.error||"Authentication failed"),h(!1),window.removeEventListener("message",c))};window.addEventListener("message",c);let d=setInterval(()=>{l?.closed&&(clearInterval(d),window.removeEventListener("message",c),h(!1),setTimeout(()=>a(n.toolType),1e3))},1e3)}catch(e){v(e instanceof Error?e.message:"Failed to link account"),h(!1)}}},d=async()=>{if(n?.toolType)try{(await fetch(`/api/auth/tools/status?tool=${n.toolType}`,{method:"DELETE"})).ok&&(f(null),a(n.toolType))}catch(e){v("Failed to disconnect tool")}};return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Tool Type"}),(0,l.jsx)(nN,{value:n?.toolType||"",onChange:o,children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)(nN.Button,{className:"relative w-full cursor-pointer rounded-lg bg-gray-700 border border-gray-600 py-2 pl-3 pr-10 text-left text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:[(0,l.jsxs)("span",{className:"flex items-center",children:[n?.toolType&&r.find(e=>e.value===n.toolType)?.icon?(0,l.jsx)(D.default,{src:r.find(e=>e.value===n.toolType)?.icon||"",alt:"",width:20,height:20,className:"mr-3 h-5 w-5 flex-shrink-0 rounded-sm"}):null,(0,l.jsx)("span",{className:"block truncate",children:r.find(e=>e.value===(n?.toolType||""))?.label||"Select a tool..."})]}),(0,l.jsx)("span",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2",children:(0,l.jsx)(H,{className:"h-5 w-5 text-gray-400","aria-hidden":"true"})})]}),(0,l.jsx)(nF,{as:c.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,l.jsx)(nN.Options,{className:"absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-gray-700 border border-gray-600 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none",children:r.map(e=>(0,l.jsx)(nN.Option,{className:({active:e})=>`relative cursor-pointer select-none py-2 pl-3 pr-9 ${e?"bg-[#ff6b35] text-white":"text-gray-300"}`,value:e.value,children:({selected:t,active:r})=>(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"flex items-center",children:[e.icon?(0,l.jsx)(D.default,{src:e.icon,alt:"",width:20,height:20,className:"mr-3 h-5 w-5 flex-shrink-0 rounded-sm"}):(0,l.jsx)("div",{className:"mr-3 h-5 w-5 flex-shrink-0"}),(0,l.jsx)("span",{className:`block truncate ${t?"font-medium":"font-normal"}`,children:e.label})]}),t?(0,l.jsx)("span",{className:`absolute inset-y-0 right-0 flex items-center pr-4 ${r?"text-white":"text-[#ff6b35]"}`,children:(0,l.jsx)(B.A,{className:"h-5 w-5","aria-hidden":"true"})}):null]})},e.value))})})]})}),n?.toolType&&(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:r.find(e=>e.value===n.toolType)?.description})]}),n?.toolType&&p&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-700 rounded-md",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[p.icon&&p.icon.startsWith("http")?(0,l.jsx)(D.default,{src:p.icon,alt:p.display_name,width:20,height:20,className:"rounded-sm"}):(0,l.jsx)("span",{className:"text-lg",children:p.icon||"\uD83D\uDD27"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-sm font-medium text-white",children:p.display_name}),p.provider_user_email&&(0,l.jsx)("div",{className:"text-xs text-gray-400",children:p.provider_user_email})]})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[p.is_connected?(0,l.jsx)(W.A,{className:"h-5 w-5 text-green-400"}):(0,l.jsx)($.A,{className:"h-5 w-5 text-yellow-400"}),(0,l.jsx)("span",{className:`text-xs ${p.is_connected?"text-green-400":"text-yellow-400"}`,children:p.is_connected?"Connected":"Not Connected"})]})]}),(0,l.jsx)("div",{className:"flex gap-2",children:p.is_connected?(0,l.jsxs)("button",{onClick:d,className:"flex-1 px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors flex items-center justify-center gap-2",children:[(0,l.jsx)($.A,{className:"h-4 w-4"}),"Disconnect"]}):(0,l.jsxs)("button",{onClick:i,disabled:g,className:"flex-1 px-3 py-2 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 text-white text-sm rounded-md transition-colors flex items-center justify-center gap-2",children:[(0,l.jsx)(q,{className:"h-4 w-4"}),g?"Connecting...":"Link Account"]})})]}),n?.toolType&&(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (seconds)"}),(0,l.jsx)("input",{type:"number",min:"5",max:"300",value:n?.timeout||30,onChange:r=>{let a={...n,timeout:parseInt(r.target.value)||30};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum time to wait for the tool operation to complete"})]}),b&&(0,l.jsx)("div",{className:"p-3 bg-red-900/20 border border-red-500/20 rounded-md",children:(0,l.jsx)("p",{className:"text-sm text-red-400",children:b})})]})},U=()=>(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Provider"}),(0,l.jsxs)("select",{value:n?.providerId||"",onChange:r=>{let a={...n,providerId:r.target.value,modelId:"",parameters:n.parameters||{temperature:.7,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",children:[(0,l.jsx)("option",{value:"",children:"Select Provider"}),(0,l.jsx)("option",{value:"openai",children:"OpenAI"}),(0,l.jsx)("option",{value:"anthropic",children:"Anthropic"}),(0,l.jsx)("option",{value:"google",children:"Google"}),(0,l.jsx)("option",{value:"deepseek",children:"DeepSeek"}),(0,l.jsx)("option",{value:"xai",children:"xAI"}),(0,l.jsx)("option",{value:"openrouter",children:"OpenRouter"})]})]}),n?.providerId&&(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Model"}),(0,l.jsx)("select",{value:n?.modelId||"",onChange:r=>{let o=r.target.value,i={...n,modelId:o};if(o&&a){let e=a.find(e=>e.id===o);if(e){let t=e.output_token_limit||e.context_window||4096,r=Math.min(t,Math.max(1024,Math.floor(.75*t))),s=n?.parameters||{};i={...i,parameters:{...s,maxTokens:r}}}}s(i),t({config:i,isConfigured:P(e.type,i)})},disabled:!n?.providerId||!T.length,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent disabled:opacity-50 disabled:bg-gray-800/30",children:n?.providerId?T.length>0?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("option",{value:"",children:"Select Model"}),T.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]}):(0,l.jsx)("option",{value:"",disabled:!0,children:i?"Loading models...":"No models available"}):(0,l.jsx)("option",{value:"",disabled:!0,children:"Select a provider first"})}),i&&(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Loading models..."}),u&&(0,l.jsx)("p",{className:"text-xs text-red-400 mt-1",children:u})]}),n?.modelId&&(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key *"}),(0,l.jsx)("input",{type:"password",value:n?.apiKey||"",onChange:r=>{let a={...n,apiKey:r.target.value};s(a),t({config:a,isConfigured:P(e.type,a)})},placeholder:"Enter your API key for this provider",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",required:!0}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Required: Enter your own API key for this AI provider (BYOK)"})]}),n?.modelId&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Max Tokens: ",n?.parameters?.maxTokens||"Auto"]}),(0,l.jsx)("input",{type:"range",min:A.minTokens,max:A.maxTokens,value:n?.parameters?.maxTokens||A.maxTokens,onChange:r=>{let a={...n,parameters:{...n.parameters,maxTokens:parseInt(r.target.value)}};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),(0,l.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,l.jsx)("span",{children:A.minTokens}),(0,l.jsx)("span",{children:A.maxTokens})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Temperature: ",n?.parameters?.temperature||.7]}),(0,l.jsx)("input",{type:"range",min:"0",max:"2",step:"0.1",value:n?.parameters?.temperature||.7,onChange:r=>{let a={...n,parameters:{...n.parameters,temperature:parseFloat(r.target.value)}};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),(0,l.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,l.jsx)("span",{children:"0 (Focused)"}),(0,l.jsx)("span",{children:"2 (Creative)"})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Subtasks"}),(0,l.jsx)("input",{type:"number",min:"1",max:"50",value:n?.maxSubtasks||10,onChange:r=>{let a={...n,maxSubtasks:parseInt(r.target.value)||10};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum number of subtasks the planner can create"})]})]}),K=()=>(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"p-4 bg-green-900/20 border border-green-700 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,l.jsx)("span",{className:"text-green-400",children:"\uD83C\uDF10"}),(0,l.jsx)("span",{className:"text-sm font-medium text-green-400",children:"Intelligent Browsing Agent"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-300",children:"Autonomous web browsing with dedicated AI provider configuration."})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"AI Provider *"}),(0,l.jsxs)("select",{value:n?.providerId||"",onChange:r=>{let a={...n,providerId:r.target.value,modelId:"",parameters:n?.parameters||{temperature:.7,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,l.jsx)("option",{value:"",children:"Select Provider"}),nH.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]}),i&&null===a&&(0,l.jsxs)("p",{className:"mt-2 text-xs text-orange-400 flex items-center bg-orange-900/20 p-2 rounded-lg",children:[(0,l.jsx)(_.A,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),u&&(0,l.jsxs)("p",{className:"mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded-lg",children:["Error: ",u]})]}),n?.providerId&&(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Model *"}),(0,l.jsxs)("select",{value:n?.modelId||"",onChange:r=>{let o=r.target.value,i={...n,modelId:o};if(o&&a){let e=a.find(e=>e.id===o);if(e){let t=e.output_token_limit||e.context_window||4096,r=Math.min(t,Math.max(1024,Math.floor(.75*t))),s=n?.parameters||{};i={...i,parameters:{...s,maxTokens:r}}}}s(i),t({config:i,isConfigured:P(e.type,i)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,l.jsx)("option",{value:"",children:"Select Model"}),T.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"API Key (Optional)"}),(0,l.jsx)("input",{type:"password",value:n?.apiKey||"",onChange:e=>{let r={...n,apiKey:e.target.value};s(r),t({config:r,isConfigured:!!(n?.providerId&&n?.modelId)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Leave empty to use configured keys"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Optional: Override the configured API key for this browsing node"})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-300",children:"AI Parameters"}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:["Temperature: ",n?.parameters?.temperature||.7]}),(0,l.jsx)("input",{type:"range",min:"0",max:"1",step:"0.1",value:n?.parameters?.temperature||.7,onChange:r=>{let a={...n,parameters:{...n?.parameters,temperature:parseFloat(r.target.value)}};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Max Tokens"}),(0,l.jsx)("input",{type:"number",min:A.minTokens,max:A.maxTokens,value:n?.parameters?.maxTokens||1e3,onChange:r=>{let a={...n,parameters:{...n?.parameters,maxTokens:parseInt(r.target.value)||1e3}};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,l.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Range: ",A.minTokens," - ",A.maxTokens," tokens"]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Sites to Visit"}),(0,l.jsx)("input",{type:"number",min:"1",max:"20",value:n?.maxSites||5,onChange:r=>{let a={...n,maxSites:parseInt(r.target.value)||5};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout per Operation (seconds)"}),(0,l.jsx)("input",{type:"number",min:"10",max:"300",value:n?.timeout||30,onChange:r=>{let a={...n,timeout:parseInt(r.target.value)||30};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"text-sm font-semibold text-white border-b border-gray-700 pb-2",children:"Basic Settings"}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Sites"}),(0,l.jsx)("input",{type:"number",min:"1",max:"20",value:n?.maxSites||5,onChange:r=>{let a={...n,maxSites:parseInt(r.target.value)};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeout (seconds)"}),(0,l.jsx)("input",{type:"number",min:"10",max:"300",value:n?.timeout||30,onChange:r=>{let a={...n,timeout:parseInt(r.target.value)};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Search Engines"}),(0,l.jsx)("div",{className:"flex gap-2",children:["google","bing"].map(r=>(0,l.jsxs)("label",{className:"flex items-center gap-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:n?.searchEngines?.includes(r)??"google"===r,onChange:a=>{let o=n?.searchEngines||["google"],i=a.target.checked?[...o.filter(e=>e!==r),r]:o.filter(e=>e!==r),l={...n,searchEngines:i.length>0?i:["google"]};s(l),t({config:l,isConfigured:P(e.type,l)})},className:"rounded"}),(0,l.jsx)("span",{className:"text-sm text-gray-300 capitalize",children:r})]},r))})]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("h4",{className:"text-sm font-semibold text-white border-b border-gray-700 pb-2",children:"Capabilities"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:n?.enableScreenshots??!0,onChange:r=>{let a={...n,enableScreenshots:r.target.checked};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"rounded"}),(0,l.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDCF8 Take Screenshots"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:n?.enableFormFilling??!0,onChange:r=>{let a={...n,enableFormFilling:r.target.checked};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"rounded"}),(0,l.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDCDD Fill Forms Automatically"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:n?.enableCaptchaSolving??!1,onChange:r=>{let a={...n,enableCaptchaSolving:r.target.checked};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"rounded"}),(0,l.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDD10 Solve CAPTCHAs"}),(0,l.jsx)("span",{className:"text-xs text-yellow-400",children:"(Beta)"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:n?.enableJavaScript??!0,onChange:r=>{let a={...n,enableJavaScript:r.target.checked};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"rounded"}),(0,l.jsx)("span",{className:"text-sm text-gray-300",children:"⚡ Enable JavaScript"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:n?.respectRobots??!0,onChange:r=>{let a={...n,respectRobots:r.target.checked};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"rounded"}),(0,l.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83E\uDD16 Respect robots.txt"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("h4",{className:"text-sm font-semibold text-white border-b border-gray-700 pb-2",children:"Advanced Settings"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Browsing Depth"}),(0,l.jsx)("input",{type:"number",min:"1",max:"5",value:n?.maxDepth||2,onChange:r=>{let a={...n,maxDepth:parseInt(r.target.value)};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"How many levels deep to follow links (1-5)"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom User Agent"}),(0,l.jsx)("input",{type:"text",value:n?.userAgent||"",onChange:r=>{let a={...n,userAgent:r.target.value};s(a),t({config:a,isConfigured:P(e.type,a)})},placeholder:"Leave empty for default",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Custom user agent string (optional)"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Extraction Goals"}),(0,l.jsx)("textarea",{value:n?.extractionGoals?.join(", ")||"",onChange:r=>{let a=r.target.value.split(",").map(e=>e.trim()).filter(e=>e),o={...n,extractionGoals:a};s(o),t({config:o,isConfigured:P(e.type,o)})},placeholder:"prices, contact info, products, links",rows:2,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35]"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Comma-separated list of what to extract (e.g., prices, contact, products)"})]})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:n?.enableFormFilling??!0,onChange:r=>{let a={...n,enableFormFilling:r.target.checked};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"rounded"}),(0,l.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDCDD Fill Forms Automatically"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:n?.enableCaptchaSolving??!1,onChange:r=>{let a={...n,enableCaptchaSolving:r.target.checked};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"rounded"}),(0,l.jsx)("span",{className:"text-sm text-gray-300",children:"\uD83D\uDD10 Attempt CAPTCHA Solving"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Search Engines"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:n?.searchEngines?.includes("google")??!0,onChange:r=>{let a=n?.searchEngines||["google"],o=r.target.checked?[...a.filter(e=>"google"!==e),"google"]:a.filter(e=>"google"!==e),i={...n,searchEngines:o.length>0?o:["google"]};s(i),t({config:i,isConfigured:P(e.type,i)})},className:"rounded"}),(0,l.jsx)("span",{className:"text-sm text-gray-300",children:"Google"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:n?.searchEngines?.includes("bing")??!1,onChange:r=>{let a=n?.searchEngines||["google"],o=r.target.checked?[...a.filter(e=>"bing"!==e),"bing"]:a.filter(e=>"bing"!==e),i={...n,searchEngines:o.length>0?o:["google"]};s(i),t({config:i,isConfigured:P(e.type,i)})},className:"rounded"}),(0,l.jsx)("span",{className:"text-sm text-gray-300",children:"Bing"})]})]})]})]}),z=()=>(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"bg-blue-900/20 border border-blue-500/30 rounded-lg p-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),(0,l.jsx)("h3",{className:"text-sm font-medium text-blue-300",children:"Plug & Play Memory"})]}),(0,l.jsx)("p",{className:"text-xs text-blue-200/80",children:"This memory node automatically acts as a brain for any connected node. It handles storing, retrieving, session data, and persistent memory intelligently without manual configuration."})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Memory Name *"}),(0,l.jsx)("input",{type:"text",value:n?.memoryName||"",onChange:r=>{let a={...n,memoryName:r.target.value};s(a),t({config:a,isConfigured:P(e.type,a)})},placeholder:"Enter a name for this memory (e.g., Browsing Memory, Router Memory)",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent",required:!0}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Give this memory a descriptive name for easy identification"})]}),n?.memoryName&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Max Storage Size (MB)"}),(0,l.jsx)("input",{type:"number",min:"1",max:"100",value:Math.round((n?.maxSize||10240)/1024),onChange:r=>{let a=parseInt(r.target.value)||10,o={...n,maxSize:1024*a};s(o),t({config:o,isConfigured:P(e.type,o)})},className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum storage size limit (default: 10MB)"})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"flex items-center",children:[(0,l.jsx)("input",{type:"checkbox",checked:n?.encryption!==!1,onChange:r=>{let a={...n,encryption:r.target.checked};s(a),t({config:a,isConfigured:P(e.type,a)})},className:"rounded border-gray-600 bg-gray-700 text-[#ff6b35] focus:ring-[#ff6b35] focus:ring-offset-0"}),(0,l.jsx)("span",{className:"ml-2 text-sm text-gray-300",children:"Enable encryption (recommended)"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1 ml-6",children:"Encrypt stored data for security (enabled by default)"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description (Optional)"}),(0,l.jsx)("textarea",{value:n?.description||"",onChange:r=>{let a={...n,description:r.target.value};s(a),t({config:a,isConfigured:P(e.type,a)})},placeholder:"Describe what this memory will be used for...",rows:2,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent resize-none"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Optional description of this memory's purpose"})]})]})]});return(0,l.jsxs)("div",{className:"w-80 bg-gray-900/90 backdrop-blur-sm border-l border-gray-700/50 p-6 overflow-y-auto",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"p-2 bg-[#ff6b35]/20 rounded-lg",children:(0,l.jsx)(x.A,{className:"w-5 h-5 text-[#ff6b35]"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Configure Node"}),(0,l.jsx)("p",{className:"text-sm text-gray-400",children:e.data.label})]})]}),(0,l.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-white transition-colors p-1 rounded",children:(0,l.jsx)(V.A,{className:"w-5 h-5"})})]}),(0,l.jsx)("div",{className:"space-y-6",children:(()=>{switch(e.type){case"provider":return O();case"vision":return I();case"roleAgent":return R();case"centralRouter":return F();case"tool":return L();case"planner":return U();case"browsing":return K();case"memory":return z();default:return M()}})()}),(0,l.jsxs)("div",{className:"mt-6 p-3 rounded-lg border border-gray-700/50",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,l.jsx)("div",{className:`w-2 h-2 rounded-full ${e.data.isConfigured?"bg-green-500":"bg-yellow-500"}`}),(0,l.jsx)("span",{className:"text-sm font-medium text-white",children:e.data.isConfigured?"Configured":"Needs Configuration"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-400",children:e.data.isConfigured?"This node is properly configured and ready to use.":"Complete the configuration to use this node in your workflow."})]})]})}var nW=r(65963),n$=r(26403);let nq=c.forwardRef(function({title:e,titleId:t,...r},n){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.181 8.68a4.503 4.503 0 0 1 1.903 6.405m-9.768-2.782L3.56 14.06a4.5 4.5 0 0 0 6.364 6.365l3.129-3.129m5.614-5.615 1.757-1.757a4.5 4.5 0 0 0-6.364-6.365l-4.5 4.5c-.258.26-.479.541-.661.84m1.903 6.405a4.495 4.495 0 0 1-1.242-.88 4.483 4.483 0 0 1-1.062-1.683m6.587 2.345 5.907 5.907m-5.907-5.907L8.898 8.898M2.991 2.99 8.898 8.9"}))});function nV({id:e,top:t,left:r,right:n,bottom:s,type:a,nodeType:o,onClose:i,onDelete:d,onDuplicate:u,onConfigure:m,onDisconnect:p}){let f=(0,c.useCallback)(e=>{e(),i()},[i]),g="edge"===a||!["userRequest","classifier","output"].includes(o||"");return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"fixed inset-0 z-40",onClick:i}),(0,l.jsxs)("div",{className:"fixed z-50 bg-gray-800 border border-gray-700 rounded-lg shadow-xl py-1 min-w-[160px]",style:{top:s?void 0:t,left:n?void 0:r,right:n?window.innerWidth-n:void 0,bottom:s?window.innerHeight-s:void 0},children:["node"===a&&(0,l.jsxs)(l.Fragment,{children:[m&&(0,l.jsxs)("button",{onClick:()=>f(()=>m(e)),className:"w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2",children:[(0,l.jsx)(x.A,{className:"w-4 h-4"}),"Configure"]}),u&&(0,l.jsxs)("button",{onClick:()=>f(()=>u(e)),className:"w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2",children:[(0,l.jsx)(nW.A,{className:"w-4 h-4"}),"Duplicate"]}),g&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"border-t border-gray-700 my-1"}),(0,l.jsxs)("button",{onClick:()=>f(()=>d(e)),className:"w-full px-3 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 flex items-center gap-2",children:[(0,l.jsx)(n$.A,{className:"w-4 h-4"}),"Delete Node"]})]}),!g&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"border-t border-gray-700 my-1"}),(0,l.jsx)("div",{className:"px-3 py-2 text-xs text-gray-500",children:"Core nodes cannot be deleted"})]})]}),"edge"===a&&(0,l.jsxs)(l.Fragment,{children:[p&&(0,l.jsxs)("button",{onClick:()=>f(()=>p(e)),className:"w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2",children:[(0,l.jsx)(nq,{className:"w-4 h-4"}),"Disconnect"]}),(0,l.jsx)("div",{className:"border-t border-gray-700 my-1"}),(0,l.jsxs)("button",{onClick:()=>f(()=>d(e)),className:"w-full px-3 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 flex items-center gap-2",children:[(0,l.jsx)(n$.A,{className:"w-4 h-4"}),"Delete Connection"]})]})]})]})}var nU=r(71050);let nK=c.forwardRef(function({title:e,titleId:t,...r},n){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 12.75c1.148 0 2.278.08 3.383.237 1.037.146 1.866.966 1.866 2.013 0 3.728-2.35 6.75-5.25 6.75S6.75 18.728 6.75 15c0-1.046.83-1.867 1.866-2.013A24.204 24.204 0 0 1 12 12.75Zm0 0c2.883 0 5.647.508 8.207 1.44a23.91 23.91 0 0 1-1.152 6.06M12 12.75c-2.883 0-5.647.508-8.208 1.44.125 2.104.52 4.136 1.153 6.06M12 12.75a2.25 2.25 0 0 0 2.248-2.354M12 12.75a2.25 2.25 0 0 1-2.248-2.354M12 8.25c.995 0 1.971-.08 2.922-.236.403-.066.74-.358.795-.762a3.778 3.778 0 0 0-.399-2.25M12 8.25c-.995 0-1.97-.08-2.922-.236-.402-.066-.74-.358-.795-.762a3.734 3.734 0 0 1 .4-2.253M12 8.25a2.25 2.25 0 0 0-2.248 2.146M12 8.25a2.25 2.25 0 0 1 2.248 2.146M8.683 5a6.032 6.032 0 0 1-1.155-1.002c.07-.63.27-1.222.574-1.747m.581 2.749A3.75 3.75 0 0 1 15.318 5m0 0c.427-.283.815-.62 1.155-.999a4.471 4.471 0 0 0-.575-1.752M4.921 6a24.048 24.048 0 0 0-.392 3.314c1.668.546 3.416.914 5.223 1.082M19.08 6c.205 1.08.337 2.187.392 3.314a23.882 23.882 0 0 1-5.223 1.082"}))});var nz=r(49579);class nG extends c.Component{constructor(e){super(e),this.reportError=async(e,t)=>{try{e.message,e.stack,t.componentStack,this.state.errorId,new Date().toISOString(),navigator.userAgent,window.location.href}catch(e){}},this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null,errorId:""})},this.handleReload=()=>{window.location.reload()},this.copyErrorDetails=()=>{let e={error:this.state.error?.message,stack:this.state.error?.stack,componentStack:this.state.errorInfo?.componentStack,errorId:this.state.errorId,timestamp:new Date().toISOString()};navigator.clipboard.writeText(JSON.stringify(e,null,2)).then(()=>{alert("Error details copied to clipboard")}).catch(()=>{alert("Failed to copy error details")})},this.state={hasError:!1,error:null,errorInfo:null,errorId:""}}static getDerivedStateFromError(e){return{hasError:!0,error:e,errorId:`error_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t}),this.props.onError&&this.props.onError(e,t),this.reportError(e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,l.jsx)("div",{className:"min-h-screen bg-[#040716] flex items-center justify-center p-6",children:(0,l.jsx)("div",{className:"max-w-2xl w-full",children:(0,l.jsxs)("div",{className:"bg-red-900/20 border border-red-700/50 rounded-lg p-8",children:[(0,l.jsx)("div",{className:"flex items-center justify-center mb-6",children:(0,l.jsx)("div",{className:"p-4 bg-red-900/30 rounded-full",children:(0,l.jsx)($.A,{className:"w-12 h-12 text-red-400"})})}),(0,l.jsxs)("div",{className:"text-center mb-6",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Something went wrong"}),(0,l.jsx)("p",{className:"text-gray-400",children:"An unexpected error occurred in the workflow editor. We've been notified and are working to fix this issue."})]}),(0,l.jsx)("div",{className:"bg-gray-800/50 rounded-lg p-4 mb-6",children:(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,l.jsx)(nK,{className:"w-4 h-4 text-gray-400"}),(0,l.jsx)("span",{className:"text-gray-400",children:"Error ID:"}),(0,l.jsx)("code",{className:"text-gray-300 font-mono",children:this.state.errorId})]})}),this.state.error&&(0,l.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 mb-6",children:[(0,l.jsx)("h3",{className:"text-sm font-medium text-gray-300 mb-2",children:"Error Message:"}),(0,l.jsx)("p",{className:"text-red-300 text-sm font-mono",children:this.state.error.message})]}),this.props.showDetails&&this.state.error&&(0,l.jsxs)("details",{className:"mb-6",children:[(0,l.jsx)("summary",{className:"text-gray-300 cursor-pointer hover:text-white transition-colors",children:"Technical Details"}),(0,l.jsx)("div",{className:"mt-4 bg-gray-900/50 rounded-lg p-4",children:(0,l.jsx)("div",{className:"text-xs font-mono text-gray-400 whitespace-pre-wrap overflow-auto max-h-40",children:this.state.error.stack})})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,l.jsxs)("button",{onClick:this.handleRetry,className:"flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors",children:[(0,l.jsx)(nz.A,{className:"w-5 h-5"}),"Try Again"]}),(0,l.jsxs)("button",{onClick:this.handleReload,className:"flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:[(0,l.jsx)(nz.A,{className:"w-5 h-5"}),"Reload Page"]}),(0,l.jsxs)("button",{onClick:this.copyErrorDetails,className:"flex items-center justify-center gap-2 px-4 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:[(0,l.jsx)(N.A,{className:"w-5 h-5"}),"Copy Details"]})]}),(0,l.jsx)("div",{className:"mt-6 text-center",children:(0,l.jsx)("p",{className:"text-sm text-gray-400",children:"If this problem persists, please contact support with the error ID above."})})]})})}):this.props.children}}var nY=r(81521),nJ=r(99127);let nZ=c.forwardRef(function({title:e,titleId:t,...r},n){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 15.75 7.5-7.5 7.5 7.5"}))});function nX({errors:e,onRetry:t,onSkip:r,onManualFix:n,isVisible:s,onClose:a}){let[o,i]=(0,c.useState)(new Set),[d,u]=(0,c.useState)({}),m=e=>{let t=new Set(o);t.has(e)?t.delete(e):t.add(e),i(t)},p=e=>{switch(e){case"pending":default:return(0,l.jsx)($.A,{className:"w-5 h-5 text-yellow-400"});case"retrying":return(0,l.jsx)(g.A,{className:"w-5 h-5 text-blue-400 animate-spin"});case"recovered":return(0,l.jsx)(W.A,{className:"w-5 h-5 text-green-400"});case"failed":return(0,l.jsx)(nY.A,{className:"w-5 h-5 text-red-400"});case"skipped":return(0,l.jsx)(nJ.A,{className:"w-5 h-5 text-gray-400"})}},f=e=>{switch(e){case"pending":default:return"border-yellow-500 bg-yellow-900/20";case"retrying":return"border-blue-500 bg-blue-900/20";case"recovered":return"border-green-500 bg-green-900/20";case"failed":return"border-red-500 bg-red-900/20";case"skipped":return"border-gray-500 bg-gray-900/20"}},x=(e,s)=>{switch(s){case"retry":t(e);break;case"skip":r(e);break;case"manual":n(e)}};return s&&0!==e.length?(0,l.jsxs)("div",{className:"fixed bottom-4 right-4 w-96 max-h-[70vh] bg-gray-800 border border-gray-700 rounded-lg shadow-xl overflow-hidden z-50",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700 bg-gray-900/50",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)($.A,{className:"w-5 h-5 text-yellow-400"}),(0,l.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["Error Recovery (",e.length,")"]})]}),(0,l.jsx)("button",{onClick:a,className:"text-gray-400 hover:text-white transition-colors",children:"✕"})]}),(0,l.jsx)("div",{className:"overflow-y-auto max-h-[calc(70vh-80px)]",children:e.map(e=>(0,l.jsxs)("div",{className:`border-l-4 ${f(e.status)} m-2 rounded-r-lg`,children:[(0,l.jsx)("div",{className:"p-4 cursor-pointer hover:bg-gray-700/30 transition-colors",onClick:()=>m(e.id),children:(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)("div",{className:"flex items-start gap-3 flex-1",children:[p(e.status),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,l.jsx)("span",{className:"text-sm font-medium text-white",children:e.nodeLabel}),(0,l.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700 px-2 py-0.5 rounded",children:e.nodeType})]}),(0,l.jsx)("p",{className:"text-sm text-gray-300 truncate",children:e.message}),(0,l.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-xs text-gray-400",children:[(0,l.jsxs)("span",{children:["Attempt ",e.attempt,"/",e.maxRetries]}),(0,l.jsx)("span",{children:new Date(e.timestamp).toLocaleTimeString()})]})]})]}),(0,l.jsx)("div",{className:"ml-2",children:o.has(e.id)?(0,l.jsx)(nZ,{className:"w-4 h-4 text-gray-400"}):(0,l.jsx)(O.A,{className:"w-4 h-4 text-gray-400"})})]})}),o.has(e.id)&&(0,l.jsxs)("div",{className:"px-4 pb-4 border-t border-gray-700/50",children:[(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Error Details:"}),(0,l.jsx)("div",{className:"bg-gray-900/50 rounded p-3 text-sm text-gray-300 font-mono",children:e.message})]}),(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Recovery Options:"}),(0,l.jsx)("div",{className:"space-y-2",children:e.recoveryStrategies.map((t,r)=>(0,l.jsx)("div",{className:`p-3 rounded border ${t.available?t.recommended?"border-green-500/50 bg-green-900/20":"border-gray-600 bg-gray-800/50":"border-gray-700 bg-gray-900/50 opacity-50"}`,children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,l.jsx)("span",{className:"text-sm font-medium text-white capitalize",children:t.type.replace("_"," ")}),t.recommended&&(0,l.jsx)("span",{className:"text-xs bg-green-900/30 text-green-300 px-2 py-0.5 rounded",children:"Recommended"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-400",children:t.description})]}),t.available&&"pending"===e.status&&(0,l.jsx)("button",{onClick:()=>x(e.id,t.type),className:`ml-3 px-3 py-1 text-xs rounded transition-colors ${t.recommended?"bg-green-600 text-white hover:bg-green-500":"bg-gray-600 text-white hover:bg-gray-500"}`,children:"Apply"})]})},r))})]}),e.context&&(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Context:"}),(0,l.jsx)("div",{className:"bg-gray-900/50 rounded p-3 text-xs text-gray-400 font-mono max-h-20 overflow-y-auto",children:JSON.stringify(e.context,null,2)})]})]})]},e.id))}),(0,l.jsx)("div",{className:"p-4 border-t border-gray-700 bg-gray-900/50",children:(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)("button",{onClick:()=>e.forEach(e=>"pending"===e.status&&t(e.id)),className:"flex-1 px-3 py-2 bg-[#ff6b35] text-white text-sm rounded hover:bg-[#ff6b35]/80 transition-colors",disabled:!e.some(e=>"pending"===e.status),children:[(0,l.jsx)(nz.A,{className:"w-4 h-4 inline mr-1"}),"Retry All"]}),(0,l.jsx)("button",{onClick:()=>e.forEach(e=>"pending"===e.status&&r(e.id)),className:"flex-1 px-3 py-2 bg-gray-600 text-white text-sm rounded hover:bg-gray-500 transition-colors",disabled:!e.some(e=>"pending"===e.status),children:"Skip All"})]})})]}):null}var nQ=r(50942),n0=r(71178),n1=r(2969),n2=r(85198),n5=r(44108);function n3({workflowId:e,workflowName:t,isOpen:r,onClose:n}){let[s,a]=(0,c.useState)([]),[o,i]=(0,c.useState)(!1),[d,u]=(0,c.useState)(""),[m,p]=(0,c.useState)("view"),[f,g]=(0,c.useState)(!1),[x,b]=(0,c.useState)(""),[v,y]=(0,c.useState)(null),w=async()=>{i(!0);try{let t=await fetch(`/api/manual-build/workflows/${e}/shares`);if(t.ok){let e=await t.json();a(e.shares||[])}}catch(e){}finally{i(!1)}},j=async()=>{if(d||f)try{(await fetch(`/api/manual-build/workflows/${e}/shares`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sharedWith:d?[d]:void 0,permissionLevel:m,isPublic:f,expiresAt:x?new Date(x).toISOString():void 0})})).ok&&(u(""),g(!1),b(""),await w())}catch(e){}},N=async t=>{try{(await fetch(`/api/manual-build/workflows/${e}/shares/${t}`,{method:"DELETE"})).ok&&await w()}catch(e){}},k=async e=>{let t=`${window.location.origin}/manual-build/shared/${e}`;try{await navigator.clipboard.writeText(t),y(e),setTimeout(()=>y(null),2e3)}catch(e){}},S=e=>{switch(e){case"admin":return(0,l.jsx)(nQ.A,{className:"w-4 h-4 text-red-400"});case"edit":return(0,l.jsx)(n0.A,{className:"w-4 h-4 text-yellow-400"});default:return(0,l.jsx)(C.A,{className:"w-4 h-4 text-blue-400"})}},T=e=>{switch(e){case"admin":return"text-red-400 bg-red-900/20 border-red-700/30";case"edit":return"text-yellow-400 bg-yellow-900/20 border-yellow-700/30";default:return"text-blue-400 bg-blue-900/20 border-blue-700/30"}};return r?(0,l.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,l.jsxs)("div",{className:"bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-700",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)(h.A,{className:"w-6 h-6 text-[#ff6b35]"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-xl font-bold text-white",children:"Share Workflow"}),(0,l.jsx)("p",{className:"text-gray-400 text-sm",children:t})]})]}),(0,l.jsx)("button",{onClick:n,className:"text-gray-400 hover:text-white transition-colors",children:(0,l.jsx)(V.A,{className:"w-6 h-6"})})]}),(0,l.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Create New Share"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex gap-4",children:[(0,l.jsxs)("label",{className:"flex items-center gap-2 cursor-pointer",children:[(0,l.jsx)("input",{type:"radio",name:"shareType",checked:!f,onChange:()=>g(!1),className:"text-[#ff6b35] focus:ring-[#ff6b35]"}),(0,l.jsx)(E.A,{className:"w-5 h-5 text-gray-400"}),(0,l.jsx)("span",{className:"text-white",children:"Share with specific users"})]}),(0,l.jsxs)("label",{className:"flex items-center gap-2 cursor-pointer",children:[(0,l.jsx)("input",{type:"radio",name:"shareType",checked:f,onChange:()=>g(!0),className:"text-[#ff6b35] focus:ring-[#ff6b35]"}),(0,l.jsx)(P.A,{className:"w-5 h-5 text-gray-400"}),(0,l.jsx)("span",{className:"text-white",children:"Public link"})]})]}),!f&&(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,l.jsx)("input",{type:"email",value:d,onChange:e=>u(e.target.value),placeholder:"<EMAIL>",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Permission Level"}),(0,l.jsxs)("select",{value:m,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,l.jsx)("option",{value:"view",children:"View Only"}),(0,l.jsx)("option",{value:"edit",children:"Can Edit"}),(0,l.jsx)("option",{value:"admin",children:"Admin Access"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Expires At (Optional)"}),(0,l.jsx)("input",{type:"datetime-local",value:x,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]"})]}),(0,l.jsx)("button",{onClick:j,disabled:!d&&!f,className:"w-full px-4 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Create Share Link"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Active Shares"}),o?(0,l.jsx)("div",{className:"text-center py-8",children:(0,l.jsx)("div",{className:"text-gray-400",children:"Loading shares..."})}):0===s.length?(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)(h.A,{className:"w-12 h-12 text-gray-500 mx-auto mb-3"}),(0,l.jsx)("div",{className:"text-gray-400",children:"No active shares"})]}):(0,l.jsx)("div",{className:"space-y-3",children:s.map(e=>(0,l.jsx)("div",{className:"bg-gray-700/50 border border-gray-600 rounded-lg p-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 flex-1",children:[e.is_public?(0,l.jsx)(P.A,{className:"w-5 h-5 text-green-400"}):(0,l.jsx)(E.A,{className:"w-5 h-5 text-blue-400"}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,l.jsx)("span",{className:"text-white font-medium",children:e.is_public?"Public Link":e.shared_with||"Unknown User"}),(0,l.jsxs)("span",{className:`px-2 py-0.5 text-xs rounded-full border ${T(e.permission_level)}`,children:[S(e.permission_level),(0,l.jsx)("span",{className:"ml-1 capitalize",children:e.permission_level})]})]}),(0,l.jsxs)("div",{className:"flex items-center gap-4 text-xs text-gray-400",children:[(0,l.jsxs)("span",{children:["Created ",new Date(e.created_at).toLocaleDateString()]}),e.expires_at&&(0,l.jsxs)("span",{className:"flex items-center gap-1",children:[(0,l.jsx)(n1.A,{className:"w-3 h-3"}),"Expires ",new Date(e.expires_at).toLocaleDateString()]})]})]})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("button",{onClick:()=>k(e.share_token),className:"p-2 text-gray-400 hover:text-white transition-colors",title:"Copy share link",children:v===e.share_token?(0,l.jsx)(n5.A,{className:"w-4 h-4 text-green-400"}):(0,l.jsx)(n2.A,{className:"w-4 h-4"})}),(0,l.jsx)("button",{onClick:()=>N(e.id),className:"p-2 text-gray-400 hover:text-red-400 transition-colors",title:"Revoke share",children:(0,l.jsx)(n$.A,{className:"w-4 h-4"})})]})]})},e.id))})]})]}),(0,l.jsx)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-gray-700",children:(0,l.jsx)("button",{onClick:n,className:"px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:"Close"})})]})}):null}let n4=c.forwardRef(function({title:e,titleId:t,...r},n){return c.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?c.createElement("title",{id:t},e):null,c.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.125 2.25h-4.5c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125v-9M10.125 2.25h.375a9 9 0 0 1 9 9v.375M10.125 2.25A3.375 3.375 0 0 1 13.5 5.625v1.5c0 .621.504 1.125 1.125 1.125h1.5a3.375 3.375 0 0 1 3.375 3.375M9 15l2.25 2.25L15 12"}))});var n6=r(97450);function n7({isOpen:e,onClose:t,mode:r,apiKey:n,workflowName:s,errorMessage:a,onSave:o,isSaving:i=!1}){let[d,u]=(0,c.useState)(""),[m,p]=(0,c.useState)(""),[f,g]=(0,c.useState)(!1),x=async()=>{if(n)try{await navigator.clipboard.writeText(n),g(!0),setTimeout(()=>g(!1),2e3)}catch(e){}};return e?(0,l.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,l.jsxs)("div",{className:"bg-gray-800 rounded-xl max-w-md w-full border border-gray-700/50 shadow-2xl",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-700/50",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:["save"===r&&(0,l.jsx)(n4,{className:"w-6 h-6 text-[#ff6b35]"}),"success"===r&&(0,l.jsx)(n6.A,{className:"w-6 h-6 text-green-500"}),"error"===r&&(0,l.jsx)($.A,{className:"w-6 h-6 text-red-500"}),(0,l.jsxs)("h2",{className:"text-xl font-bold text-white",children:["save"===r&&"Save Workflow","success"===r&&"Workflow Saved Successfully!","error"===r&&"Save Failed"]})]}),!i&&(0,l.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-white transition-colors",children:(0,l.jsx)(V.A,{className:"w-6 h-6"})})]}),(0,l.jsxs)("div",{className:"p-6",children:["save"===r&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Workflow Name *"}),(0,l.jsx)("input",{type:"text",value:d,onChange:e=>u(e.target.value),placeholder:"Enter workflow name",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]",autoFocus:!0})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description (optional)"}),(0,l.jsx)("textarea",{value:m,onChange:e=>p(e.target.value),placeholder:"Enter workflow description",rows:3,className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35] resize-none"})]})]}),"success"===r&&n&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("p",{className:"text-gray-300",children:["Your workflow ",(0,l.jsx)("span",{className:"font-semibold text-white",children:s})," has been saved successfully!"]}),(0,l.jsxs)("div",{className:"bg-gray-900/50 rounded-lg p-4 border border-gray-700/50",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"API Key (save this - it won't be shown again!)"}),(0,l.jsx)("button",{onClick:x,className:"flex items-center gap-1 text-xs text-[#ff6b35] hover:text-[#e55a2b] transition-colors",children:f?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(B.A,{className:"w-3 h-3"}),"Copied!"]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(n2.A,{className:"w-3 h-3"}),"Copy"]})})]}),(0,l.jsx)("div",{className:"bg-gray-800 rounded px-3 py-2 font-mono text-sm text-green-400 break-all",children:n})]}),(0,l.jsx)("div",{className:"bg-amber-900/20 border border-amber-700/50 rounded-lg p-3",children:(0,l.jsxs)("p",{className:"text-amber-200 text-sm",children:[(0,l.jsx)("strong",{children:"Important:"})," This API key will not be displayed again. Save it securely to use your workflow via API."]})})]}),"error"===r&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-300",children:"Failed to save the workflow. Please try again."}),a&&(0,l.jsx)("div",{className:"bg-red-900/20 border border-red-700/50 rounded-lg p-3",children:(0,l.jsx)("p",{className:"text-red-200 text-sm",children:a})})]})]}),(0,l.jsxs)("div",{className:"flex gap-3 p-6 border-t border-gray-700/50",children:["save"===r&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("button",{onClick:t,disabled:i,className:"flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-700 disabled:opacity-50 text-white rounded-lg transition-colors",children:"Cancel"}),(0,l.jsx)("button",{onClick:()=>{d.trim()&&o&&o(d.trim(),m.trim())},disabled:!d.trim()||i,className:"flex-1 px-4 py-2 bg-[#ff6b35] hover:bg-[#e55a2b] disabled:bg-gray-600 disabled:opacity-50 text-white rounded-lg transition-colors flex items-center justify-center gap-2",children:i?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Saving..."]}):"Save Workflow"})]}),("success"===r||"error"===r)&&(0,l.jsx)("button",{onClick:t,className:"w-full px-4 py-2 bg-[#ff6b35] hover:bg-[#e55a2b] text-white rounded-lg transition-colors",children:"success"===r?"Continue":"Try Again"})]})]})}):null}function n8({params:e}){let t=(0,u.useParams)(),r=(0,u.useRouter)(),n=t?.workflowId,[s,a]=(0,c.useState)(null),[o,i,d]=(0,m.ck)([]),[f,g,x]=(0,m.fM)([]),[h,b]=(0,c.useState)(null),[v,w]=(0,c.useState)(!0),[j,N]=(0,c.useState)(!1),[k,C]=(0,c.useState)(!1),[E,S]=(0,c.useState)(null),[T,A]=(0,c.useState)({isOpen:!1,mode:"save"}),[P,O]=(0,c.useState)(null),[I,R]=function(e,t={}){let{autoConnect:r=!0,maxEvents:n=100,reconnectInterval:s=5e3,onEvent:a,onConnect:o,onDisconnect:i,onError:l}=t,[d,u]=(0,c.useState)({isConnected:!1,isConnecting:!1,error:null,lastEvent:null,events:[],connectionCount:0}),m=(0,c.useRef)(null),p=(0,c.useRef)(null),f=(0,c.useRef)(!1),g=(0,c.useRef)(!1),x=(0,c.useRef)(!1),h=(0,c.useCallback)(()=>{if(e&&!g.current&&!x.current){x.current=!0,u(e=>({...e,isConnecting:!0,error:null})),f.current=!1;try{let t=new EventSource(`/api/workflow/stream/${e}`);m.current=t,t.onopen=()=>{g.current=!0,x.current=!1,u(e=>({...e,isConnected:!0,isConnecting:!1,error:null,connectionCount:e.connectionCount+1})),o?.()},t.onmessage=e=>{try{let t=JSON.parse(e.data);u(e=>{let r=[...e.events,t];return r.length>n&&r.splice(0,r.length-n),{...e,lastEvent:t,events:r}}),a?.(t)}catch(e){}},t.onerror=e=>{g.current=!1,x.current=!1,u(e=>({...e,isConnected:!1,isConnecting:!1,error:"Connection error"})),l?.("Connection error"),!f.current&&s>0&&(p.current=setTimeout(()=>{h()},s))}}catch(e){x.current=!1,u(e=>({...e,isConnecting:!1,error:"Failed to create connection"})),l?.("Failed to create connection")}}},[e,n,s,o,a,l]),b=(0,c.useCallback)(()=>{f.current=!0,g.current=!1,x.current=!1,p.current&&(clearTimeout(p.current),p.current=null),m.current&&(m.current.close(),m.current=null),u(e=>({...e,isConnected:!1,isConnecting:!1,error:null})),i?.()},[i,e]),v=(0,c.useCallback)(async(t,r,n)=>{if(!e)throw Error("No workflow ID provided");try{let s=await fetch(`/api/workflow/stream/${e}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({eventType:t,data:r,executionId:n})});if(!s.ok)throw Error(`Failed to send event: ${s.statusText}`)}catch(e){throw e}},[e]),y=(0,c.useCallback)(()=>{u(e=>({...e,events:[],lastEvent:null}))},[]),w=(0,c.useCallback)(()=>{b(),setTimeout(h,100)},[b,h]);return[d,{connect:h,disconnect:b,sendEvent:v,clearEvents:y,reconnect:w}]}("new"!==n?n:null,{autoConnect:!0,onEvent:e=>{"workflow_started"===e.type||"node_started"===e.type||"node_completed"===e.type||"workflow_completed"===e.type||e.type},onConnect:()=>{},onDisconnect:()=>{},onError:e=>{}}),[M,F]=(0,c.useState)([]),[D,_]=(0,c.useState)(!1),[H,B]=(0,c.useState)(!1),W=(0,c.useCallback)(e=>{let t={...e,id:`e${f.length+1}`,type:"smoothstep",animated:!0};g(e=>(0,p.rN)(t,e)),C(!0)},[f.length,g]),$=(0,c.useCallback)((e,t)=>{b(t)},[]),q=(0,c.useCallback)(()=>{b(null),S(null)},[]),V=(0,c.useCallback)((e,t)=>{e.preventDefault(),S({id:t.id,type:"node",nodeType:t.type,x:e.clientX,y:e.clientY})},[]),U=(0,c.useCallback)((e,t)=>{e.preventDefault(),S({id:t.id,type:"edge",x:e.clientX,y:e.clientY})},[]),K=(0,c.useCallback)(e=>{["user-request","classifier","output"].includes(e)||(i(t=>t.filter(t=>t.id!==e)),g(t=>t.filter(t=>t.source!==e&&t.target!==e)),C(!0),h?.id===e&&b(null))},[h,i,g]),z=(0,c.useCallback)(e=>{g(t=>t.filter(t=>t.id!==e)),C(!0)},[g]),G=(0,c.useCallback)(e=>{let t=o.find(t=>t.id===e);if(!t)return;let r={...t,id:`${t.type}-${Date.now()}`,position:{x:t.position.x+50,y:t.position.y+50},data:{...t.data,label:`${t.data.label} Copy`}};i(e=>[...e,r]),C(!0)},[o,i]),Y=(0,c.useCallback)(e=>{let t=o.find(t=>t.id===e);t&&b(t)},[o]),J=async()=>{s||"new"!==n?await X():A({isOpen:!0,mode:"save"})},Z=async(e,t)=>{N(!0);try{let r=await fetch("/api/workflows",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e,description:t,nodes:o,edges:f,settings:{}})});if(!r.ok){let e=await r.json();throw Error(e.details||"Failed to save workflow")}let n=await r.json();A({isOpen:!0,mode:"success",apiKey:n.api_key,workflowName:e}),O(n.workflow.id)}catch(e){A({isOpen:!0,mode:"error",errorMessage:e instanceof Error?e.message:"Unknown error"})}finally{N(!1)}},X=async()=>{if(s){N(!0);try{let e=await fetch("/api/workflows",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:n,name:s?.name,description:s?.description,nodes:o,edges:f,settings:s?.settings||{}})});if(!e.ok){let t=await e.json();throw Error(t.details||"Failed to update workflow")}C(!1),A({isOpen:!0,mode:"success",workflowName:s?.name||"Workflow"})}catch(e){A({isOpen:!0,mode:"error",errorMessage:e instanceof Error?e.message:"Unknown error"})}finally{N(!1)}}},Q=(e,t)=>{i(r=>r.map(r=>r.id===e?{...r,data:{...r.data,...t}}:r)),C(!0)};return v?(0,l.jsx)("div",{className:"h-screen bg-[#040716] flex items-center justify-center",children:(0,l.jsx)("div",{className:"text-white",children:"Loading workflow..."})}):(0,l.jsx)(nG,{showDetails:!1,onError:(e,t)=>{let r={id:`error-${Date.now()}`,nodeId:"editor",nodeType:"editor",nodeLabel:"Workflow Editor",message:e.message,timestamp:new Date().toISOString(),attempt:1,maxRetries:3,status:"pending",recoveryStrategies:[{type:"retry",description:"Reload the editor",available:!0,recommended:!0}]};F(e=>[...e,r]),_(!0)},children:(0,l.jsxs)("div",{className:"h-screen bg-[#040716] flex flex-col",children:[(0,l.jsx)(y,{workflow:s,isDirty:k,isSaving:j,onSave:J,onExecute:()=>{s?.id?window.open(`/playground?workflow=${s.id}`,"_blank"):A({isOpen:!0,mode:"error",errorMessage:"Please save the workflow first to test it in the playground"})},onBack:()=>r.push("/manual-build"),onShare:()=>B(!0)}),(0,l.jsxs)("div",{className:"flex-1 flex",children:[(0,l.jsx)(L,{onAddNode:(e,t)=>{let r={},n=!0;"provider"===e?(r={providerId:"",modelId:"",apiKey:"",parameters:{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}},n=!1):"centralRouter"===e&&(r={routingStrategy:"smart",fallbackProvider:"",maxRetries:3,timeout:3e4,enableCaching:!0,debugMode:!1},n=!0);let s={id:`${e}-${Date.now()}`,type:e,position:t,data:{label:"centralRouter"===e?"Central Router":e.charAt(0).toUpperCase()+e.slice(1),config:r,isConfigured:n,description:`${e} node`}};i(e=>[...e,s]),C(!0)}}),(0,l.jsxs)("div",{className:"flex-1 relative manual-build-canvas",children:[(0,l.jsxs)(m.Gc,{nodes:o,edges:f,onNodesChange:d,onEdgesChange:x,onConnect:W,onNodeClick:$,onNodeContextMenu:V,onEdgeContextMenu:U,onPaneClick:q,nodeTypes:nU.c_,fitView:!0,className:"bg-[#040716]",defaultViewport:{x:0,y:0,zoom:.8},minZoom:.1,maxZoom:2,connectionLineStyle:{stroke:"#ff6b35",strokeWidth:2},defaultEdgeOptions:{style:{stroke:"#ff6b35",strokeWidth:2},type:"smoothstep",animated:!0},proOptions:{hideAttribution:!0},children:[(0,l.jsx)(m.VS,{color:"#1f2937",gap:20,size:1}),(0,l.jsx)(m.H2,{className:"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm",showInteractive:!1}),(0,l.jsx)(m.of,{className:"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm",nodeColor:"#ff6b35",maskColor:"rgba(0, 0, 0, 0.2)"})]}),E&&(0,l.jsx)(nV,{id:E.id,type:E.type,nodeType:E.nodeType,top:E.y,left:E.x,onClose:()=>S(null),onDelete:"node"===E.type?K:z,onDuplicate:"node"===E.type?G:void 0,onConfigure:"node"===E.type?Y:void 0,onDisconnect:"edge"===E.type?z:void 0})]}),h&&(0,l.jsx)(nB,{node:h,onUpdate:e=>Q(h.id,e),onClose:()=>b(null)})]}),(0,l.jsx)(nX,{errors:M,onRetry:e=>{},onSkip:e=>{},onManualFix:e=>{},isVisible:D,onClose:()=>_(!1)}),s&&(0,l.jsx)(n3,{workflowId:s.id,workflowName:s.name,isOpen:H,onClose:()=>B(!1)}),(0,l.jsx)(n7,{isOpen:T.isOpen,mode:T.mode,apiKey:T.apiKey,workflowName:T.workflowName,errorMessage:T.errorMessage,isSaving:j,onClose:()=>{let e="success"===T.mode&&T.apiKey;A({isOpen:!1,mode:"save"}),e&&"new"===n&&P&&r.push(`/manual-build/${P}`)},onSave:Z})]})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35133:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var n=r(65239),s=r(48088),a=r(88170),o=r.n(a),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["manual-build",{children:["[workflowId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72539)),"C:\\RoKey App\\rokey-app\\src\\app\\manual-build\\[workflowId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\RoKey App\\rokey-app\\src\\app\\manual-build\\[workflowId]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/manual-build/[workflowId]/page",pathname:"/manual-build/[workflowId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},37132:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))})},44108:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{fillRule:"evenodd",d:"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z",clipRule:"evenodd"}))})},49579:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))})},50515:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},52868:(e,t,r)=>{Promise.resolve().then(r.bind(r,24840))},55510:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62525:(e,t,r)=>{"use strict";r.d(t,{Dc:()=>s,p2:()=>n});let n=[{id:"general_chat",name:"General Chat",description:"Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives."},{id:"coding_frontend",name:"Coding - Frontend",description:"Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development."},{id:"coding_backend",name:"Coding - Backend",description:"Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture."},{id:"research_synthesis",name:"Research & Synthesis",description:"Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information."},{id:"translation_localization",name:"Translation & Localization",description:"Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance."},{id:"image_generation",name:"Image Generation",description:"Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing."},{id:"data_extractor",name:"Data Extractor",description:"Extracting specific data from web pages, scraping content, and gathering information from websites."},{id:"form_filler",name:"Form Filler",description:"Filling out web forms, submitting data, and handling form-based interactions on websites."},{id:"verification_agent",name:"Verification Agent",description:"Verifying information on websites, fact-checking, and validating data accuracy."},{id:"research_assistant",name:"Research Assistant",description:"Conducting web-based research, gathering information from multiple sources, and compiling research findings."},{id:"shopping_assistant",name:"Shopping Assistant",description:"Helping with online shopping, price comparisons, product research, and e-commerce tasks."},{id:"price_comparison",name:"Price Comparison",description:"Comparing prices across different websites, finding deals, and analyzing product pricing."},{id:"fact_checker",name:"Fact Checker",description:"Verifying facts and information across multiple web sources, cross-referencing data for accuracy."},{id:"task_executor",name:"Task Executor",description:"General task execution and automation, handling various web-based tasks and workflows."}],s=e=>n.find(t=>t.id===e)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66368:(e,t,r)=>{"use strict";r.d(t,{MG:()=>n});let n=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},72539:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\manual-build\\[workflowId]\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85198:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 7.5V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6V4.5c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v12c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V16.5a9 9 0 0 0-9-9Z"}))})},89114:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);let s=n.forwardRef(function({title:e,titleId:t,...r},s){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99716:(e,t,r)=>{Promise.resolve().then(r.bind(r,72539))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,5449,4370,4999,4912,1050],()=>r(35133));module.exports=n})();