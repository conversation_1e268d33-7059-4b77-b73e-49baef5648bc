(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6424],{89667:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(95155),l=t(12115),r=t(35695),n=t(25052),i=t(99695),c=t(10184),d=t(55233),o=t(78046),x=t(27305),m=t(62486);let h=["All","Content Creation","Data Processing","Web Automation","AI Workflows","Business Process","Research & Analysis","Communication","Development"];function g(){let e=(0,r.useRouter)(),[s,t]=(0,l.useState)([]),[g,u]=(0,l.useState)([]),[p,f]=(0,l.useState)(!0),[b,j]=(0,l.useState)(""),[y,N]=(0,l.useState)("All"),[w,v]=(0,l.useState)("popular"),[C,_]=(0,l.useState)(!1),[k,A]=(0,l.useState)(null);(0,l.useEffect)(()=>{S()},[]),(0,l.useEffect)(()=>{let e=s;b&&(e=e.filter(e=>e.name.toLowerCase().includes(b.toLowerCase())||e.description.toLowerCase().includes(b.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(b.toLowerCase())))),"All"!==y&&(e=e.filter(e=>e.category===y)),e.sort((e,s)=>{switch(w){case"popular":return s.download_count-e.download_count;case"recent":return new Date(s.updated_at).getTime()-new Date(e.updated_at).getTime();case"rating":return s.rating-e.rating;default:return 0}}),u(e)},[s,b,y,w]);let S=async()=>{try{let e=await fetch("/api/manual-build/templates");if(e.ok){let s=await e.json();t(s.templates||[])}}catch(e){}finally{f(!1)}},T=async s=>{try{let t=await fetch("/api/manual-build/workflows",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:"".concat(s.name," (Copy)"),description:s.description,nodes:s.template_nodes,edges:s.template_edges,settings:s.default_settings,template_id:s.id})});if(t.ok){let s=await t.json();e.push("/manual-build/".concat(s.workflow.id))}}catch(e){}},L=e=>Array.from({length:5},(s,t)=>(0,a.jsx)("span",{children:t<Math.floor(e)?(0,a.jsx)(m.A,{className:"w-4 h-4 text-yellow-400"}):(0,a.jsx)(x.A,{className:"w-4 h-4 text-gray-400"})},t));return p?(0,a.jsx)("div",{className:"min-h-screen bg-[#040716] flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white",children:"Loading templates..."})}):(0,a.jsxs)("div",{className:"min-h-screen bg-[#040716] text-white",children:[(0,a.jsx)("div",{className:"border-b border-gray-800 bg-gray-900/50 backdrop-blur-sm",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Workflow Templates"}),(0,a.jsx)("p",{className:"text-gray-400 mt-1",children:"Choose from pre-built workflows to get started quickly"})]}),(0,a.jsx)("button",{onClick:()=>e.push("/manual-build/new"),className:"px-4 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors",children:"Create from Scratch"})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-6",children:[(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search templates...",value:b,onChange:e=>j(e.target.value),className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]"})]}),(0,a.jsx)("select",{value:y,onChange:e=>N(e.target.value),className:"px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]",children:h.map(e=>(0,a.jsx)("option",{value:e,children:e},e))}),(0,a.jsxs)("select",{value:w,onChange:e=>v(e.target.value),className:"px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,a.jsx)("option",{value:"popular",children:"Most Popular"}),(0,a.jsx)("option",{value:"recent",children:"Most Recent"}),(0,a.jsx)("option",{value:"rating",children:"Highest Rated"})]}),(0,a.jsxs)("button",{onClick:()=>_(!C),className:"px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white hover:bg-gray-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"w-5 h-5"}),"Filters"]})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("p",{className:"text-gray-400",children:[g.length," template",1!==g.length?"s":""," found"]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map(e=>(0,a.jsxs)("div",{className:"bg-gray-800/50 border border-gray-700 rounded-lg overflow-hidden hover:border-[#ff6b35]/50 transition-all duration-200 group",children:[e.preview_image_url?(0,a.jsx)("img",{src:e.preview_image_url,alt:e.name,className:"w-full h-48 object-cover"}):(0,a.jsx)("div",{className:"w-full h-48 bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center",children:(0,a.jsx)(i.A,{className:"w-16 h-16 text-gray-500"})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white group-hover:text-[#ff6b35] transition-colors",children:e.name}),e.is_official&&(0,a.jsx)("span",{className:"px-2 py-1 bg-blue-900/30 text-blue-300 text-xs rounded-full border border-blue-700/30",children:"Official"})]}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-4 line-clamp-2",children:e.description}),e.tags&&e.tags.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1 mb-4",children:[e.tags.slice(0,3).map(e=>(0,a.jsx)("span",{className:"px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded-full",children:e},e)),e.tags.length>3&&(0,a.jsxs)("span",{className:"px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded-full",children:["+",e.tags.length-3]})]}),(0,a.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-400",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(n.A,{className:"w-4 h-4"}),e.download_count]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[L(e.rating),(0,a.jsx)("span",{className:"ml-1",children:e.rating.toFixed(1)})]})]})}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>T(e),className:"flex-1 px-4 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors text-sm font-medium",children:"Use Template"}),(0,a.jsx)("button",{onClick:()=>A(e),className:"px-3 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:(0,a.jsx)(c.A,{className:"w-4 h-4"})})]})]})]},e.id))}),0===g.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(i.A,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No templates found"}),(0,a.jsx)("p",{className:"text-gray-400 mb-6",children:"Try adjusting your search criteria or create a new workflow from scratch."}),(0,a.jsx)("button",{onClick:()=>e.push("/manual-build/new"),className:"px-6 py-3 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors",children:"Create New Workflow"})]})]}),k&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-700",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:k.name}),(0,a.jsx)("p",{className:"text-gray-400 mt-1",children:k.description})]}),(0,a.jsx)("button",{onClick:()=>A(null),className:"text-gray-400 hover:text-white transition-colors",children:"✕"})]}),(0,a.jsx)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Template Details"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Category:"}),(0,a.jsx)("span",{className:"text-white",children:k.category})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Downloads:"}),(0,a.jsx)("span",{className:"text-white",children:k.download_count})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Rating:"}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[L(k.rating),(0,a.jsx)("span",{className:"text-white ml-1",children:k.rating.toFixed(1)})]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Nodes:"}),(0,a.jsx)("span",{className:"text-white",children:k.template_nodes.length})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Connections:"}),(0,a.jsx)("span",{className:"text-white",children:k.template_edges.length})]})]})]}),k.tags&&k.tags.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-md font-medium text-white mb-2",children:"Tags"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:k.tags.map(e=>(0,a.jsx)("span",{className:"px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded-full",children:e},e))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Workflow Structure"}),(0,a.jsx)("div",{className:"bg-gray-900 rounded-lg p-4 h-64 overflow-auto",children:(0,a.jsxs)("div",{className:"text-sm text-gray-300",children:[(0,a.jsxs)("div",{className:"mb-2 font-medium",children:["Nodes (",k.template_nodes.length,"):"]}),(0,a.jsx)("ul",{className:"space-y-1 mb-4",children:k.template_nodes.map((e,s)=>{var t;return(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-[#ff6b35] rounded-full"}),(0,a.jsx)("span",{children:(null==(t=e.data)?void 0:t.label)||e.type}),(0,a.jsxs)("span",{className:"text-gray-500",children:["(",e.type,")"]})]},s)})}),(0,a.jsxs)("div",{className:"mb-2 font-medium",children:["Connections (",k.template_edges.length,"):"]}),(0,a.jsxs)("div",{className:"text-gray-400 text-xs",children:[k.template_edges.length," connections between nodes"]})]})})]})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-gray-700",children:[(0,a.jsx)("button",{onClick:()=>A(null),className:"px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:"Close"}),(0,a.jsx)("button",{onClick:()=>{T(k),A(null)},className:"px-6 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors",children:"Use This Template"})]})]})})]})}},95279:(e,s,t)=>{Promise.resolve().then(t.bind(t,89667))}},e=>{var s=s=>e(e.s=s);e.O(0,[9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(95279)),_N_E=e.O()}]);