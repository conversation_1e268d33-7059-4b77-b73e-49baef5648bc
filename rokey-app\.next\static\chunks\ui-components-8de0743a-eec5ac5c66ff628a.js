"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[721],{27223:(e,s,r)=>{r.d(s,{A:()=>n});var t=r(95155),a=r(12115),l=r(28707);function n(e){let{errors:s,onRetry:r,onSkip:n,onManualFix:i,isVisible:o,onClose:d}=e,[c,x]=(0,a.useState)(new Set),[m,h]=(0,a.useState)({}),g=e=>{let s=new Set(c);s.has(e)?s.delete(e):s.add(e),x(s)},u=e=>{switch(e){case"pending":default:return(0,t.jsx)(l.Pi,{className:"w-5 h-5 text-yellow-400"});case"retrying":return(0,t.jsx)(l.O4,{className:"w-5 h-5 text-blue-400 animate-spin"});case"recovered":return(0,t.jsx)(l.C1,{className:"w-5 h-5 text-green-400"});case"failed":return(0,t.jsx)(l.qh,{className:"w-5 h-5 text-red-400"});case"skipped":return(0,t.jsx)(l.KS,{className:"w-5 h-5 text-gray-400"})}},p=e=>{switch(e){case"pending":default:return"border-yellow-500 bg-yellow-900/20";case"retrying":return"border-blue-500 bg-blue-900/20";case"recovered":return"border-green-500 bg-green-900/20";case"failed":return"border-red-500 bg-red-900/20";case"skipped":return"border-gray-500 bg-gray-900/20"}},b=(e,s)=>{switch(s){case"retry":r(e);break;case"skip":n(e);break;case"manual":i(e)}};return o&&0!==s.length?(0,t.jsxs)("div",{className:"fixed bottom-4 right-4 w-96 max-h-[70vh] bg-gray-800 border border-gray-700 rounded-lg shadow-xl overflow-hidden z-50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-700 bg-gray-900/50",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(l.Pi,{className:"w-5 h-5 text-yellow-400"}),(0,t.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["Error Recovery (",s.length,")"]})]}),(0,t.jsx)("button",{onClick:d,className:"text-gray-400 hover:text-white transition-colors",children:"✕"})]}),(0,t.jsx)("div",{className:"overflow-y-auto max-h-[calc(70vh-80px)]",children:s.map(e=>(0,t.jsxs)("div",{className:"border-l-4 ".concat(p(e.status)," m-2 rounded-r-lg"),children:[(0,t.jsx)("div",{className:"p-4 cursor-pointer hover:bg-gray-700/30 transition-colors",onClick:()=>g(e.id),children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-start gap-3 flex-1",children:[u(e.status),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-white",children:e.nodeLabel}),(0,t.jsx)("span",{className:"text-xs text-gray-400 bg-gray-700 px-2 py-0.5 rounded",children:e.nodeType})]}),(0,t.jsx)("p",{className:"text-sm text-gray-300 truncate",children:e.message}),(0,t.jsxs)("div",{className:"flex items-center gap-4 mt-2 text-xs text-gray-400",children:[(0,t.jsxs)("span",{children:["Attempt ",e.attempt,"/",e.maxRetries]}),(0,t.jsx)("span",{children:new Date(e.timestamp).toLocaleTimeString()})]})]})]}),(0,t.jsx)("div",{className:"ml-2",children:c.has(e.id)?(0,t.jsx)(l.Mt,{className:"w-4 h-4 text-gray-400"}):(0,t.jsx)(l.D3,{className:"w-4 h-4 text-gray-400"})})]})}),c.has(e.id)&&(0,t.jsxs)("div",{className:"px-4 pb-4 border-t border-gray-700/50",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Error Details:"}),(0,t.jsx)("div",{className:"bg-gray-900/50 rounded p-3 text-sm text-gray-300 font-mono",children:e.message})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Recovery Options:"}),(0,t.jsx)("div",{className:"space-y-2",children:e.recoveryStrategies.map((s,r)=>(0,t.jsx)("div",{className:"p-3 rounded border ".concat(s.available?s.recommended?"border-green-500/50 bg-green-900/20":"border-gray-600 bg-gray-800/50":"border-gray-700 bg-gray-900/50 opacity-50"),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-white capitalize",children:s.type.replace("_"," ")}),s.recommended&&(0,t.jsx)("span",{className:"text-xs bg-green-900/30 text-green-300 px-2 py-0.5 rounded",children:"Recommended"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-400",children:s.description})]}),s.available&&"pending"===e.status&&(0,t.jsx)("button",{onClick:()=>b(e.id,s.type),className:"ml-3 px-3 py-1 text-xs rounded transition-colors ".concat(s.recommended?"bg-green-600 text-white hover:bg-green-500":"bg-gray-600 text-white hover:bg-gray-500"),children:"Apply"})]})},r))})]}),e.context&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Context:"}),(0,t.jsx)("div",{className:"bg-gray-900/50 rounded p-3 text-xs text-gray-400 font-mono max-h-20 overflow-y-auto",children:JSON.stringify(e.context,null,2)})]})]})]},e.id))}),(0,t.jsx)("div",{className:"p-4 border-t border-gray-700 bg-gray-900/50",children:(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)("button",{onClick:()=>s.forEach(e=>"pending"===e.status&&r(e.id)),className:"flex-1 px-3 py-2 bg-[#ff6b35] text-white text-sm rounded hover:bg-[#ff6b35]/80 transition-colors",disabled:!s.some(e=>"pending"===e.status),children:[(0,t.jsx)(l.EF,{className:"w-4 h-4 inline mr-1"}),"Retry All"]}),(0,t.jsx)("button",{onClick:()=>s.forEach(e=>"pending"===e.status&&n(e.id)),className:"flex-1 px-3 py-2 bg-gray-600 text-white text-sm rounded hover:bg-gray-500 transition-colors",disabled:!s.some(e=>"pending"===e.status),children:"Skip All"})]})})]}):null}},69200:(e,s,r)=>{r.d(s,{Ay:()=>n});var t=r(95155),a=r(12115),l=r(4351);class n extends a.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e,errorId:"error_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}}componentDidCatch(e,s){this.setState({error:e,errorInfo:s}),this.props.onError&&this.props.onError(e,s),this.reportError(e,s)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,t.jsx)("div",{className:"min-h-screen bg-[#040716] flex items-center justify-center p-6",children:(0,t.jsx)("div",{className:"max-w-2xl w-full",children:(0,t.jsxs)("div",{className:"bg-red-900/20 border border-red-700/50 rounded-lg p-8",children:[(0,t.jsx)("div",{className:"flex items-center justify-center mb-6",children:(0,t.jsx)("div",{className:"p-4 bg-red-900/30 rounded-full",children:(0,t.jsx)(l.Pi,{className:"w-12 h-12 text-red-400"})})}),(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Something went wrong"}),(0,t.jsx)("p",{className:"text-gray-400",children:"An unexpected error occurred in the workflow editor. We've been notified and are working to fix this issue."})]}),(0,t.jsx)("div",{className:"bg-gray-800/50 rounded-lg p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,t.jsx)(l.N7,{className:"w-4 h-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-gray-400",children:"Error ID:"}),(0,t.jsx)("code",{className:"text-gray-300 font-mono",children:this.state.errorId})]})}),this.state.error&&(0,t.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4 mb-6",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-300 mb-2",children:"Error Message:"}),(0,t.jsx)("p",{className:"text-red-300 text-sm font-mono",children:this.state.error.message})]}),this.props.showDetails&&this.state.error&&(0,t.jsxs)("details",{className:"mb-6",children:[(0,t.jsx)("summary",{className:"text-gray-300 cursor-pointer hover:text-white transition-colors",children:"Technical Details"}),(0,t.jsx)("div",{className:"mt-4 bg-gray-900/50 rounded-lg p-4",children:(0,t.jsx)("div",{className:"text-xs font-mono text-gray-400 whitespace-pre-wrap overflow-auto max-h-40",children:this.state.error.stack})})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,t.jsxs)("button",{onClick:this.handleRetry,className:"flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors",children:[(0,t.jsx)(l.EF,{className:"w-5 h-5"}),"Try Again"]}),(0,t.jsxs)("button",{onClick:this.handleReload,className:"flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:[(0,t.jsx)(l.EF,{className:"w-5 h-5"}),"Reload Page"]}),(0,t.jsxs)("button",{onClick:this.copyErrorDetails,className:"flex items-center justify-center gap-2 px-4 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:[(0,t.jsx)(l.AQ,{className:"w-5 h-5"}),"Copy Details"]})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"If this problem persists, please contact support with the error ID above."})})]})})}):this.props.children}constructor(e){super(e),this.reportError=async(e,s)=>{try{e.message,e.stack,s.componentStack,this.state.errorId,new Date().toISOString(),navigator.userAgent,window.location.href}catch(e){}},this.handleRetry=()=>{this.setState({hasError:!1,error:null,errorInfo:null,errorId:""})},this.handleReload=()=>{window.location.reload()},this.copyErrorDetails=()=>{var e,s,r;let t={error:null==(e=this.state.error)?void 0:e.message,stack:null==(s=this.state.error)?void 0:s.stack,componentStack:null==(r=this.state.errorInfo)?void 0:r.componentStack,errorId:this.state.errorId,timestamp:new Date().toISOString()};navigator.clipboard.writeText(JSON.stringify(t,null,2)).then(()=>{alert("Error details copied to clipboard")}).catch(()=>{alert("Failed to copy error details")})},this.state={hasError:!1,error:null,errorInfo:null,errorId:""}}}},71848:(e,s,r)=>{r.d(s,{A:()=>i});var t=r(95155),a=r(23405);let l=e=>{let{label:s,value:r}=e;return(0,t.jsxs)("div",{className:"py-2 sm:grid sm:grid-cols-3 sm:gap-4",children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-300",children:s}),(0,t.jsx)("dd",{className:"mt-1 text-sm text-white sm:mt-0 sm:col-span-2 break-words",children:null!=r&&""!==r?r:"N/A"})]})},n=e=>{let s,{title:r,data:a}=e;if(null==a)s="N/A";else if("string"==typeof a)s=a;else try{s=JSON.stringify(a,null,2)}catch(e){s="Invalid JSON data"}return(0,t.jsxs)("div",{className:"py-2",children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-300 mb-1",children:r}),(0,t.jsx)("dd",{className:"mt-1 text-sm text-gray-300 bg-gray-800/50 p-3 rounded-lg border border-gray-700",children:(0,t.jsx)("pre",{className:"whitespace-pre-wrap break-all",children:s})})]})};function i(e){var s;let{log:r,onClose:i,apiConfigNameMap:o}=e;if(!r)return null;let d=r.custom_api_config_id?o[r.custom_api_config_id]||"Unknown Model":"N/A";return(0,t.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out",onClick:i,children:(0,t.jsxs)("div",{className:"bg-gray-900/95 backdrop-blur-sm rounded-lg border border-gray-800/50 max-w-2xl w-full max-h-[90vh] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-800/50",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["Log Details (ID: ",r.id.substring(0,8),"...)"]}),(0,t.jsx)("button",{onClick:i,className:"text-gray-400 hover:text-white hover:bg-gray-800/50 p-1 rounded-lg transition-colors duration-200",children:(0,t.jsx)(a.f,{className:"h-6 w-6"})})]}),(0,t.jsxs)("div",{className:"p-6 space-y-4 overflow-y-auto",children:[(0,t.jsxs)("dl",{className:"divide-y divide-gray-700",children:[(0,t.jsx)(l,{label:"Timestamp",value:new Date(r.request_timestamp).toLocaleString()}),(0,t.jsx)(l,{label:"API Model Used",value:d}),(0,t.jsx)(l,{label:"Role Requested",value:r.role_requested}),(0,t.jsx)(l,{label:"Role Used",value:r.role_used}),(0,t.jsx)(l,{label:"Status",value:null===(s=r.status_code)?(0,t.jsx)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-gray-600 text-gray-100",children:"N/A"}):s>=200&&s<300?(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-green-600 text-green-100",children:["Success (",s,")"]}):s>=400?(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-red-600 text-red-100",children:["Error (",s,")"]}):(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-yellow-500 text-yellow-100",children:["Other (",s,")"]})}),(0,t.jsx)(l,{label:"LLM Provider",value:r.llm_provider_name}),(0,t.jsx)(l,{label:"LLM Model Name",value:(()=>{var e,s;if((null==(e=r.role_used)?void 0:e.includes("RouKey_Multi Roles_"))&&(null==(s=r.response_payload_summary)?void 0:s.models_used)){let e=r.response_payload_summary.models_used;return e.length<=3?e.join(", "):"".concat(e.slice(0,3).join(", "),"...")}return r.llm_model_name})()}),(0,t.jsx)(l,{label:"LLM Latency",value:null!==r.llm_provider_latency_ms?"".concat(r.llm_provider_latency_ms," ms"):"N/A"}),(0,t.jsx)(l,{label:"RoKey Latency",value:null!==r.processing_duration_ms?"".concat(r.processing_duration_ms," ms"):"N/A"}),(0,t.jsx)(l,{label:"Input Tokens",value:null!==r.input_tokens?r.input_tokens:"N/A"}),(0,t.jsx)(l,{label:"Output Tokens",value:null!==r.output_tokens?r.output_tokens:"N/A"}),(0,t.jsx)(l,{label:"Cost",value:null!==r.cost?"$".concat(r.cost.toFixed(6)):"N/A"}),(0,t.jsx)(l,{label:"Multimodal Request",value:r.is_multimodal?"Yes":"No"}),(0,t.jsx)(l,{label:"IP Address",value:r.ip_address}),r.user_id&&(0,t.jsx)(l,{label:"User ID",value:r.user_id}),r.error_message&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l,{label:"Error Message",value:r.error_message}),(0,t.jsx)(l,{label:"Error Source",value:r.error_source})]}),r.llm_provider_status_code&&(0,t.jsx)(l,{label:"LLM Provider Status",value:r.llm_provider_status_code})]}),r.request_payload_summary&&(0,t.jsx)(n,{title:"Request Payload Summary",data:r.request_payload_summary}),r.response_payload_summary&&(0,t.jsx)(n,{title:"Response Payload Summary",data:r.response_payload_summary}),r.error_details_zod&&(0,t.jsx)(n,{title:"Zod Validation Error Details",data:r.error_details_zod})]}),(0,t.jsx)("div",{className:"p-6 border-t border-gray-800/50 text-right",children:(0,t.jsx)("button",{onClick:i,className:"px-4 py-2 bg-gray-800/50 text-gray-300 border border-gray-700 rounded-lg hover:border-gray-600 transition-all duration-200",children:"Close"})})]})})}},82416:(e,s,r)=>{r.d(s,{A:()=>n});var t=r(95155),a=r(12115),l=r(63071);function n(e){let{id:s,top:r,left:n,right:i,bottom:o,type:d,nodeType:c,onClose:x,onDelete:m,onDuplicate:h,onConfigure:g,onDisconnect:u}=e,p=(0,a.useCallback)(e=>{e(),x()},[x]),b="edge"===d||!["userRequest","classifier","output"].includes(c||"");return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 z-40",onClick:x}),(0,t.jsxs)("div",{className:"fixed z-50 bg-gray-800 border border-gray-700 rounded-lg shadow-xl py-1 min-w-[160px]",style:{top:o?void 0:r,left:i?void 0:n,right:i?window.innerWidth-i:void 0,bottom:o?window.innerHeight-o:void 0},children:["node"===d&&(0,t.jsxs)(t.Fragment,{children:[g&&(0,t.jsxs)("button",{onClick:()=>p(()=>g(s)),className:"w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2",children:[(0,t.jsx)(l.Vy,{className:"w-4 h-4"}),"Configure"]}),h&&(0,t.jsxs)("button",{onClick:()=>p(()=>h(s)),className:"w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2",children:[(0,t.jsx)(l.gF,{className:"w-4 h-4"}),"Duplicate"]}),b&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"border-t border-gray-700 my-1"}),(0,t.jsxs)("button",{onClick:()=>p(()=>m(s)),className:"w-full px-3 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 flex items-center gap-2",children:[(0,t.jsx)(l.uc,{className:"w-4 h-4"}),"Delete Node"]})]}),!b&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"border-t border-gray-700 my-1"}),(0,t.jsx)("div",{className:"px-3 py-2 text-xs text-gray-500",children:"Core nodes cannot be deleted"})]})]}),"edge"===d&&(0,t.jsxs)(t.Fragment,{children:[u&&(0,t.jsxs)("button",{onClick:()=>p(()=>u(s)),className:"w-full px-3 py-2 text-left text-sm text-gray-300 hover:bg-gray-700 flex items-center gap-2",children:[(0,t.jsx)(l.vk,{className:"w-4 h-4"}),"Disconnect"]}),(0,t.jsx)("div",{className:"border-t border-gray-700 my-1"}),(0,t.jsxs)("button",{onClick:()=>p(()=>m(s)),className:"w-full px-3 py-2 text-left text-sm text-red-400 hover:bg-red-900/20 flex items-center gap-2",children:[(0,t.jsx)(l.uc,{className:"w-4 h-4"}),"Delete Connection"]})]})]})]})}}}]);