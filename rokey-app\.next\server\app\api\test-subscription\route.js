(()=>{var e={};e.id=812,e.ids=[812],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},38871:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>a});var i=t(96559),o=t(48088),u=t(37719),n=t(32190);let p=(0,t(39398).createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function a(e){try{let{searchParams:r}=new URL(e.url),t=r.get("userId");if(!t)return n.NextResponse.json({error:"Missing userId parameter"},{status:400});let{data:s,error:i}=await p.from("user_profiles").select("*").eq("id",t).single();if(i)return n.NextResponse.json({error:"Profile not found",details:i},{status:404});let{data:o,error:u}=await p.from("subscriptions").select("*").eq("user_id",t).single();return n.NextResponse.json({success:!0,profile:s,subscription:o,subscriptionError:u?u.message:null,timestamp:new Date().toISOString()})}catch(e){return n.NextResponse.json({error:"Internal server error",details:e},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/test-subscription/route",pathname:"/api/test-subscription",filename:"route",bundlePath:"app/api/test-subscription/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\test-subscription\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function q(){return(0,u.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398],()=>t(38871));module.exports=s})();