"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/utils/streamingUtils.ts":
/*!*************************************!*\
  !*** ./src/utils/streamingUtils.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERFORMANCE_THRESHOLDS: () => (/* binding */ PERFORMANCE_THRESHOLDS),\n/* harmony export */   createFirstTokenTrackingStream: () => (/* binding */ createFirstTokenTrackingStream),\n/* harmony export */   estimateTokenCount: () => (/* binding */ estimateTokenCount),\n/* harmony export */   evaluatePerformance: () => (/* binding */ evaluatePerformance),\n/* harmony export */   getProviderModelFromContext: () => (/* binding */ getProviderModelFromContext),\n/* harmony export */   logStreamingPerformance: () => (/* binding */ logStreamingPerformance)\n/* harmony export */ });\n// Streaming utilities for first token tracking, performance monitoring, and cost calculation\n// Interface for streaming usage data\nfunction createFirstTokenTrackingStream(originalStream, provider, model, onUsageExtracted) {\n    const reader = originalStream.getReader();\n    const decoder = new TextDecoder();\n    const encoder = new TextEncoder();\n    return new ReadableStream({\n        async start (controller) {\n            let firstTokenSent = false;\n            let accumulatedContent = '';\n            const streamStartTime = Date.now();\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        // Final opportunity to extract usage data if not found in chunks\n                        if (onUsageExtracted && !firstTokenSent) {\n                            // Fallback: estimate tokens from accumulated content\n                            const estimatedUsage = estimateUsageFromContent(accumulatedContent, provider);\n                            console.log(\"\\uD83D\\uDCB0 [\".concat(provider, \" Cost] Fallback token estimation: \").concat(estimatedUsage.totalTokens, \" tokens\"));\n                            onUsageExtracted(estimatedUsage);\n                        }\n                        controller.close();\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    // Check if this chunk contains actual content (first token)\n                    if (!firstTokenSent && chunk.includes('delta')) {\n                        try {\n                            // Parse SSE data to check for content\n                            const lines = chunk.split('\\n');\n                            for (const line of lines){\n                                if (line.startsWith('data: ') && !line.includes('[DONE]')) {\n                                    const jsonData = line.substring(6);\n                                    try {\n                                        var _parsed_choices__delta, _parsed_choices_, _parsed_choices;\n                                        const parsed = JSON.parse(jsonData);\n                                        if ((_parsed_choices = parsed.choices) === null || _parsed_choices === void 0 ? void 0 : (_parsed_choices_ = _parsed_choices[0]) === null || _parsed_choices_ === void 0 ? void 0 : (_parsed_choices__delta = _parsed_choices_.delta) === null || _parsed_choices__delta === void 0 ? void 0 : _parsed_choices__delta.content) {\n                                            const firstTokenTime = Date.now() - streamStartTime;\n                                            console.log(\"\\uD83D\\uDE80 \".concat(provider.toUpperCase(), \" FIRST TOKEN: \").concat(firstTokenTime, \"ms (\").concat(model, \")\"));\n                                            firstTokenSent = true;\n                                            // Accumulate content for fallback estimation\n                                            accumulatedContent += parsed.choices[0].delta.content;\n                                            break;\n                                        }\n                                    } catch (e) {\n                                    // Ignore JSON parse errors for individual chunks\n                                    }\n                                }\n                            }\n                        } catch (e) {\n                            // Ignore parsing errors, just track timing\n                            if (!firstTokenSent) {\n                                const firstTokenTime = Date.now() - streamStartTime;\n                                console.log(\"\\uD83D\\uDE80 \".concat(provider.toUpperCase(), \" FIRST TOKEN: \").concat(firstTokenTime, \"ms (\").concat(model, \") [fallback detection]\"));\n                                firstTokenSent = true;\n                            }\n                        }\n                    }\n                    // Extract usage data from streaming chunks\n                    if (onUsageExtracted && chunk.includes('usage')) {\n                        try {\n                            const usageData = extractUsageFromStreamChunk(chunk, provider);\n                            if (usageData) {\n                                console.log(\"\\uD83D\\uDCB0 [\".concat(provider, \" Cost] Usage data extracted from stream: \").concat(usageData.totalTokens, \" tokens\"));\n                                onUsageExtracted(usageData);\n                            }\n                        } catch (e) {\n                        // Ignore usage extraction errors\n                        }\n                    }\n                    // Continue accumulating content for fallback\n                    if (chunk.includes('delta')) {\n                        try {\n                            const lines = chunk.split('\\n');\n                            for (const line of lines){\n                                if (line.startsWith('data: ') && !line.includes('[DONE]')) {\n                                    const jsonData = line.substring(6);\n                                    try {\n                                        var _parsed_choices__delta1, _parsed_choices_1, _parsed_choices1;\n                                        const parsed = JSON.parse(jsonData);\n                                        if ((_parsed_choices1 = parsed.choices) === null || _parsed_choices1 === void 0 ? void 0 : (_parsed_choices_1 = _parsed_choices1[0]) === null || _parsed_choices_1 === void 0 ? void 0 : (_parsed_choices__delta1 = _parsed_choices_1.delta) === null || _parsed_choices__delta1 === void 0 ? void 0 : _parsed_choices__delta1.content) {\n                                            accumulatedContent += parsed.choices[0].delta.content;\n                                        }\n                                    } catch (e) {\n                                    // Ignore JSON parse errors\n                                    }\n                                }\n                            }\n                        } catch (e) {\n                        // Ignore parsing errors\n                        }\n                    }\n                    // Forward the chunk unchanged\n                    controller.enqueue(value);\n                }\n            } catch (error) {\n                console.error(\"[\".concat(provider, \" Stream Tracking] Error:\"), error);\n                // Phase 1 Optimization: Graceful error handling for connection resets\n                if (error instanceof Error && (error.message.includes('aborted') || error.message.includes('ECONNRESET'))) {\n                    console.log(\"[\".concat(provider, \" Stream] Connection reset detected - closing stream gracefully\"));\n                    controller.close();\n                } else {\n                    controller.error(error);\n                }\n            }\n        }\n    });\n}\n// Enhanced logging for streaming performance\nfunction logStreamingPerformance(provider, model, metrics) {\n    console.log(\"\\uD83D\\uDCCA STREAMING PERFORMANCE: \".concat(provider, \"/\").concat(model));\n    if (metrics.timeToFirstToken !== undefined) {\n        console.log(\"   ⏱️ Time to First Token: \".concat(metrics.timeToFirstToken.toFixed(1), \"ms\"));\n        // Performance categories\n        if (metrics.timeToFirstToken < 500) {\n            console.log(\"   ⚡ EXCELLENT first token performance\");\n        } else if (metrics.timeToFirstToken < 1000) {\n            console.log(\"   ✅ GOOD first token performance\");\n        } else if (metrics.timeToFirstToken < 2000) {\n            console.log(\"   ⚠️ SLOW first token performance\");\n        } else {\n            console.log(\"   \\uD83D\\uDC0C VERY SLOW first token performance\");\n        }\n    }\n    if (metrics.totalStreamTime !== undefined) {\n        console.log(\"   \\uD83D\\uDD04 Total Stream Time: \".concat(metrics.totalStreamTime.toFixed(1), \"ms\"));\n    }\n    if (metrics.totalTokens !== undefined) {\n        console.log(\"   \\uD83C\\uDFAF Total Tokens: \".concat(metrics.totalTokens));\n    }\n    if (metrics.averageTokenLatency !== undefined) {\n        console.log(\"   \\uD83D\\uDCC8 Avg Token Latency: \".concat(metrics.averageTokenLatency.toFixed(1), \"ms/token\"));\n    }\n}\n// Utility to extract provider and model from request context\nfunction getProviderModelFromContext(providerName, modelId) {\n    return {\n        provider: providerName || 'unknown',\n        model: modelId || 'unknown'\n    };\n}\n// Simple token counter for rough estimation\nfunction estimateTokenCount(text) {\n    // Rough estimation: 1 token ≈ 4 characters for English text\n    // This is a simplified approach, real tokenization would be more accurate\n    return Math.ceil(text.length / 4);\n}\n// Performance thresholds for different providers\nconst PERFORMANCE_THRESHOLDS = {\n    EXCELLENT_FIRST_TOKEN: 500,\n    GOOD_FIRST_TOKEN: 1000,\n    SLOW_FIRST_TOKEN: 2000,\n    // Anything above 2000ms is considered very slow\n    EXCELLENT_TOTAL: 3000,\n    GOOD_TOTAL: 5000,\n    SLOW_TOTAL: 10000,\n    TARGET_TOKEN_LATENCY: 50\n};\n// Check if performance meets targets\nfunction evaluatePerformance(metrics) {\n    const firstTokenGrade = !metrics.timeToFirstToken ? 'very_slow' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN ? 'excellent' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN ? 'good' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN ? 'slow' : 'very_slow';\n    const totalTimeGrade = !metrics.totalStreamTime ? 'very_slow' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.EXCELLENT_TOTAL ? 'excellent' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.GOOD_TOTAL ? 'good' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.SLOW_TOTAL ? 'slow' : 'very_slow';\n    const tokenLatencyGrade = !metrics.averageTokenLatency ? 'very_slow' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY ? 'excellent' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 2 ? 'good' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 4 ? 'slow' : 'very_slow';\n    // Overall grade is the worst of the three\n    const grades = [\n        firstTokenGrade,\n        totalTimeGrade,\n        tokenLatencyGrade\n    ];\n    const gradeOrder = [\n        'excellent',\n        'good',\n        'slow',\n        'very_slow'\n    ];\n    const overallGrade = grades.reduce((worst, current)=>{\n        return gradeOrder.indexOf(current) > gradeOrder.indexOf(worst) ? current : worst;\n    }, 'excellent');\n    return {\n        firstTokenGrade,\n        totalTimeGrade,\n        tokenLatencyGrade,\n        overallGrade\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/streamingUtils.ts\n"));

/***/ })

});