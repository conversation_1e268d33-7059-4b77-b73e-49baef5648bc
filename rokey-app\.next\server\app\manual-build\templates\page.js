(()=>{var e={};e.id=6424,e.ids=[6424],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3633:(e,t,s)=>{Promise.resolve().then(s.bind(s,92314))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13361:(e,t,s)=>{Promise.resolve().then(s.bind(s,84480))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},48935:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z"}))})},53884:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{fillRule:"evenodd",d:"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z",clipRule:"evenodd"}))})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64251:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),l=s(88170),n=s.n(l),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["manual-build",{children:["templates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,84480)),"C:\\RoKey App\\rokey-app\\src\\app\\manual-build\\templates\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\manual-build\\templates\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/manual-build/templates/page",pathname:"/manual-build/templates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},65963:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"}))})},66524:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},73559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84480:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\templates\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\manual-build\\templates\\page.tsx","default")},91164:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(43210);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))})},91645:e=>{"use strict";e.exports=require("net")},92314:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(60687),a=s(43210),l=s(16189),n=s(48935),i=s(30922),o=s(73559),d=s(65963),c=s(91164),x=s(66524),m=s(53884);let p=["All","Content Creation","Data Processing","Web Automation","AI Workflows","Business Process","Research & Analysis","Communication","Development"];function u(){let e=(0,l.useRouter)(),[t,s]=(0,a.useState)([]),[u,h]=(0,a.useState)([]),[g,f]=(0,a.useState)(!0),[b,j]=(0,a.useState)(""),[v,y]=(0,a.useState)("All"),[w,N]=(0,a.useState)("popular"),[k,C]=(0,a.useState)(!1),[A,_]=(0,a.useState)(null),E=async t=>{try{let s=await fetch("/api/manual-build/workflows",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:`${t.name} (Copy)`,description:t.description,nodes:t.template_nodes,edges:t.template_edges,settings:t.default_settings,template_id:t.id})});if(s.ok){let t=await s.json();e.push(`/manual-build/${t.workflow.id}`)}}catch(e){}},R=e=>Array.from({length:5},(t,s)=>(0,r.jsx)("span",{children:s<Math.floor(e)?(0,r.jsx)(m.A,{className:"w-4 h-4 text-yellow-400"}):(0,r.jsx)(n.A,{className:"w-4 h-4 text-gray-400"})},s));return g?(0,r.jsx)("div",{className:"min-h-screen bg-[#040716] flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-white",children:"Loading templates..."})}):(0,r.jsxs)("div",{className:"min-h-screen bg-[#040716] text-white",children:[(0,r.jsx)("div",{className:"border-b border-gray-800 bg-gray-900/50 backdrop-blur-sm",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Workflow Templates"}),(0,r.jsx)("p",{className:"text-gray-400 mt-1",children:"Choose from pre-built workflows to get started quickly"})]}),(0,r.jsx)("button",{onClick:()=>e.push("/manual-build/new"),className:"px-4 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors",children:"Create from Scratch"})]})})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-6",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)(i.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search templates...",value:b,onChange:e=>j(e.target.value),className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#ff6b35]"})]}),(0,r.jsx)("select",{value:v,onChange:e=>y(e.target.value),className:"px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]",children:p.map(e=>(0,r.jsx)("option",{value:e,children:e},e))}),(0,r.jsxs)("select",{value:w,onChange:e=>N(e.target.value),className:"px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-[#ff6b35]",children:[(0,r.jsx)("option",{value:"popular",children:"Most Popular"}),(0,r.jsx)("option",{value:"recent",children:"Most Recent"}),(0,r.jsx)("option",{value:"rating",children:"Highest Rated"})]}),(0,r.jsxs)("button",{onClick:()=>C(!k),className:"px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white hover:bg-gray-700 transition-colors flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"w-5 h-5"}),"Filters"]})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("p",{className:"text-gray-400",children:[u.length," template",1!==u.length?"s":""," found"]})}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:u.map(e=>(0,r.jsxs)("div",{className:"bg-gray-800/50 border border-gray-700 rounded-lg overflow-hidden hover:border-[#ff6b35]/50 transition-all duration-200 group",children:[e.preview_image_url?(0,r.jsx)("img",{src:e.preview_image_url,alt:e.name,className:"w-full h-48 object-cover"}):(0,r.jsx)("div",{className:"w-full h-48 bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"w-16 h-16 text-gray-500"})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white group-hover:text-[#ff6b35] transition-colors",children:e.name}),e.is_official&&(0,r.jsx)("span",{className:"px-2 py-1 bg-blue-900/30 text-blue-300 text-xs rounded-full border border-blue-700/30",children:"Official"})]}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mb-4 line-clamp-2",children:e.description}),e.tags&&e.tags.length>0&&(0,r.jsxs)("div",{className:"flex flex-wrap gap-1 mb-4",children:[e.tags.slice(0,3).map(e=>(0,r.jsx)("span",{className:"px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded-full",children:e},e)),e.tags.length>3&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-gray-700/50 text-gray-300 text-xs rounded-full",children:["+",e.tags.length-3]})]}),(0,r.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-400",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(c.A,{className:"w-4 h-4"}),e.download_count]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[R(e.rating),(0,r.jsx)("span",{className:"ml-1",children:e.rating.toFixed(1)})]})]})}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{onClick:()=>E(e),className:"flex-1 px-4 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors text-sm font-medium",children:"Use Template"}),(0,r.jsx)("button",{onClick:()=>_(e),className:"px-3 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:(0,r.jsx)(x.A,{className:"w-4 h-4"})})]})]})]},e.id))}),0===u.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(d.A,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"No templates found"}),(0,r.jsx)("p",{className:"text-gray-400 mb-6",children:"Try adjusting your search criteria or create a new workflow from scratch."}),(0,r.jsx)("button",{onClick:()=>e.push("/manual-build/new"),className:"px-6 py-3 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors",children:"Create New Workflow"})]})]}),A&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-700",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white",children:A.name}),(0,r.jsx)("p",{className:"text-gray-400 mt-1",children:A.description})]}),(0,r.jsx)("button",{onClick:()=>_(null),className:"text-gray-400 hover:text-white transition-colors",children:"✕"})]}),(0,r.jsx)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Template Details"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Category:"}),(0,r.jsx)("span",{className:"text-white",children:A.category})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Downloads:"}),(0,r.jsx)("span",{className:"text-white",children:A.download_count})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Rating:"}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[R(A.rating),(0,r.jsx)("span",{className:"text-white ml-1",children:A.rating.toFixed(1)})]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Nodes:"}),(0,r.jsx)("span",{className:"text-white",children:A.template_nodes.length})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Connections:"}),(0,r.jsx)("span",{className:"text-white",children:A.template_edges.length})]})]})]}),A.tags&&A.tags.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-md font-medium text-white mb-2",children:"Tags"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:A.tags.map(e=>(0,r.jsx)("span",{className:"px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded-full",children:e},e))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"Workflow Structure"}),(0,r.jsx)("div",{className:"bg-gray-900 rounded-lg p-4 h-64 overflow-auto",children:(0,r.jsxs)("div",{className:"text-sm text-gray-300",children:[(0,r.jsxs)("div",{className:"mb-2 font-medium",children:["Nodes (",A.template_nodes.length,"):"]}),(0,r.jsx)("ul",{className:"space-y-1 mb-4",children:A.template_nodes.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-[#ff6b35] rounded-full"}),(0,r.jsx)("span",{children:e.data?.label||e.type}),(0,r.jsxs)("span",{className:"text-gray-500",children:["(",e.type,")"]})]},t))}),(0,r.jsxs)("div",{className:"mb-2 font-medium",children:["Connections (",A.template_edges.length,"):"]}),(0,r.jsxs)("div",{className:"text-gray-400 text-xs",children:[A.template_edges.length," connections between nodes"]})]})})]})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-gray-700",children:[(0,r.jsx)("button",{onClick:()=>_(null),className:"px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:"Close"}),(0,r.jsx)("button",{onClick:()=>{E(A),_(null)},className:"px-6 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors",children:"Use This Template"})]})]})})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,5449,4912],()=>s(64251));module.exports=r})();