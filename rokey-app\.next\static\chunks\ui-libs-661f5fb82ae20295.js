"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7874],{6232:(e,t,n)=>{n.d(t,{Y:()=>l});var r=n(12115),o=n(21231);function l(e){let t=(0,r.useRef)(e);return(0,o.s)(()=>{t.current=e},[e]),t}},7856:(e,t,n)=>{n.d(t,{_:()=>r});function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}},20379:(e,t,n)=>{n.d(t,{x:()=>r});function r(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.from(new Set(t.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}},21231:(e,t,n)=>{n.d(t,{s:()=>l});var r=n(12115),o=n(87657);let l=(e,t)=>{o._.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},27279:(e,t,n)=>{n.d(t,{Y:()=>r});function r(e,t){for(var n=arguments.length,o=Array(n>2?n-2:0),l=2;l<n;l++)o[l-2]=arguments[l];if(e in t){let n=t[e];return"function"==typeof n?n(...o):n}let i=Error('Tried to handle "'.concat(e,'" but there is no handler defined. Only defined handlers are: ').concat(Object.keys(t).map(e=>'"'.concat(e,'"')).join(", "),"."));throw Error.captureStackTrace&&Error.captureStackTrace(i,r),i}},30797:(e,t,n)=>{n.d(t,{_:()=>l});var r=n(12115),o=n(6232);let l=function(e){let t=(0,o.Y)(e);return r.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.current(...n)},[t])}},45261:(e,t,n)=>{n.d(t,{e:()=>function e(){let t=[],n={addEventListener:(e,t,r,o)=>(e.addEventListener(t,r,o),n.add(()=>e.removeEventListener(t,r,o))),requestAnimationFrame(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let o=requestAnimationFrame(...t);return n.add(()=>cancelAnimationFrame(o))},nextFrame(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return n.requestAnimationFrame(()=>n.requestAnimationFrame(...t))},setTimeout(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let o=setTimeout(...t);return n.add(()=>clearTimeout(o))},microTask(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];let l={current:!0};return(0,r._)(()=>{l.current&&t[0]()}),n.add(()=>{l.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.includes(e)||t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}});var r=n(7856)},47769:(e,t,n)=>{n.d(t,{P:()=>a,a:()=>i});var r=n(12115),o=n(30797);let l=Symbol();function i(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return Object.assign(e,{[l]:t})}function a(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let i=(0,r.useRef)(t);(0,r.useEffect)(()=>{i.current=t},[t]);let a=(0,o._)(e=>{for(let t of i.current)null!=t&&("function"==typeof t?t(e):t.current=e)});return t.every(e=>null==e||(null==e?void 0:e[l]))?void 0:a}},48014:(e,t,n)=>{n.d(t,{L:()=>l});var r=n(12115),o=n(45261);function l(){let[e]=(0,r.useState)(o.e);return(0,r.useEffect)(()=>()=>e.dispose(),[e]),e}},55424:(e,t,n)=>{n.d(t,{m_:()=>_});var r=n(12115),o=n(41093),l=n(29300),i=n(87358);let a={core:!1,base:!1};function s({css:e,id:t="react-tooltip-base-styles",type:n="base",ref:r}){var o,l;if(!e||"undefined"==typeof document||a[n]||"core"===n&&void 0!==i&&(null==(o=null==i?void 0:i.env)?void 0:o.REACT_TOOLTIP_DISABLE_CORE_STYLES)||"base"!==n&&void 0!==i&&(null==(l=null==i?void 0:i.env)?void 0:l.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;"core"===n&&(t="react-tooltip-core-styles"),r||(r={});let{insertAt:s}=r;if(document.getElementById(t))return;let u=document.head||document.getElementsByTagName("head")[0],c=document.createElement("style");c.id=t,c.type="text/css","top"===s&&u.firstChild?u.insertBefore(c,u.firstChild):u.appendChild(c),c.styleSheet?c.styleSheet.cssText=e:c.appendChild(document.createTextNode(e)),a[n]=!0}let u=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:n=null,place:r="top",offset:l=10,strategy:i="absolute",middlewares:a=[(0,o.cY)(Number(l)),(0,o.UU)({fallbackAxisSideDirection:"start"}),(0,o.BN)({padding:5})],border:s,arrowSize:u=8})=>e&&null!==t?n?(a.push((0,o.UE)({element:n,padding:5})),(0,o.rD)(e,t,{placement:r,strategy:i,middleware:a}).then(({x:e,y:t,placement:n,middlewareData:r})=>{var o,l;let i={left:`${e}px`,top:`${t}px`,border:s},{x:a,y:c}=null!=(o=r.arrow)?o:{x:0,y:0},d=null!=(l=({top:"bottom",right:"left",bottom:"top",left:"right"})[n.split("-")[0]])?l:"bottom",f=0;if(s){let e=`${s}`.match(/(\d+)px/);f=(null==e?void 0:e[1])?Number(e[1]):1}return{tooltipStyles:i,tooltipArrowStyles:{left:null!=a?`${a}px`:"",top:null!=c?`${c}px`:"",right:"",bottom:"",...s&&{borderBottom:s,borderRight:s},[d]:`-${u/2+f}px`},place:n}})):(0,o.rD)(e,t,{placement:"bottom",strategy:i,middleware:a}).then(({x:e,y:t,placement:n})=>({tooltipStyles:{left:`${e}px`,top:`${t}px`},tooltipArrowStyles:{},place:n})):{tooltipStyles:{},tooltipArrowStyles:{},place:r},c=(e,t)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(e,t),d=(e,t,n)=>{let r=null,o=function(...o){let l=()=>{r=null,n||e.apply(this,o)};n&&!r&&(e.apply(this,o),r=setTimeout(l,t)),n||(r&&clearTimeout(r),r=setTimeout(l,t))};return o.cancel=()=>{r&&(clearTimeout(r),r=null)},o},f=e=>null!==e&&!Array.isArray(e)&&"object"==typeof e,p=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((e,n)=>p(e,t[n]));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!f(e)||!f(t))return e===t;let n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&n.every(n=>p(e[n],t[n]))},v=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;let t=getComputedStyle(e);return["overflow","overflow-x","overflow-y"].some(e=>{let n=t.getPropertyValue(e);return"auto"===n||"scroll"===n})},m=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(v(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},h="undefined"!=typeof window?r.useLayoutEffect:r.useEffect,g=e=>{e.current&&(clearTimeout(e.current),e.current=null)},b={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},y=(0,r.createContext)({getTooltipData:()=>b});function w(e="DEFAULT_TOOLTIP_ID"){return(0,r.useContext)(y).getTooltipData(e)}var E={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},S={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};let x=({forwardRef:e,id:t,className:n,classNameArrow:i,variant:a="dark",anchorId:s,anchorSelect:c,place:f="top",offset:v=10,events:b=["hover"],openOnClick:y=!1,positionStrategy:x="absolute",middlewares:O,wrapper:_,delayShow:C=0,delayHide:A=0,float:k=!1,hidden:T=!1,noArrow:R=!1,clickable:P=!1,closeOnEsc:L=!1,closeOnScroll:F=!1,closeOnResize:N=!1,openEvents:I,closeEvents:M,globalCloseEvents:D,imperativeModeOnly:j,style:B,position:U,afterShow:H,afterHide:Y,disableTooltip:W,content:z,contentWrapperRef:K,isOpen:X,defaultIsOpen:q=!1,setIsOpen:V,activeAnchor:$,setActiveAnchor:G,border:Q,opacity:Z,arrowColor:J,arrowSize:ee=8,role:et="tooltip"})=>{var en;let er=(0,r.useRef)(null),eo=(0,r.useRef)(null),el=(0,r.useRef)(null),ei=(0,r.useRef)(null),ea=(0,r.useRef)(null),[es,eu]=(0,r.useState)({tooltipStyles:{},tooltipArrowStyles:{},place:f}),[ec,ed]=(0,r.useState)(!1),[ef,ep]=(0,r.useState)(!1),[ev,em]=(0,r.useState)(null),eh=(0,r.useRef)(!1),eg=(0,r.useRef)(null),{anchorRefs:eb,setActiveAnchor:ey}=w(t),ew=(0,r.useRef)(!1),[eE,eS]=(0,r.useState)([]),ex=(0,r.useRef)(!1),eO=y||b.includes("click"),e_=eO||(null==I?void 0:I.click)||(null==I?void 0:I.dblclick)||(null==I?void 0:I.mousedown),eC=I?{...I}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!I&&eO&&Object.assign(eC,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});let eA=M?{...M}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!M&&eO&&Object.assign(eA,{mouseleave:!1,blur:!1,mouseout:!1});let ek=D?{...D}:{escape:L||!1,scroll:F||!1,resize:N||!1,clickOutsideAnchor:e_||!1};j&&(Object.assign(eC,{mouseover:!1,focus:!1,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(eA,{mouseout:!1,blur:!1,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(ek,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),h(()=>(ex.current=!0,()=>{ex.current=!1}),[]);let eT=e=>{ex.current&&(e&&ep(!0),setTimeout(()=>{ex.current&&(null==V||V(e),void 0===X&&ed(e))},10))};(0,r.useEffect)(()=>{if(void 0===X)return()=>null;X&&ep(!0);let e=setTimeout(()=>{ed(X)},10);return()=>{clearTimeout(e)}},[X]),(0,r.useEffect)(()=>{ec!==eh.current&&((g(ea),eh.current=ec,ec)?null==H||H():ea.current=setTimeout(()=>{ep(!1),em(null),null==Y||Y()},(e=>{let t=e.match(/^([\d.]+)(ms|s)$/);if(!t)return 0;let[,n,r]=t;return Number(n)*("ms"===r?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"))+25))},[ec]);let eR=e=>{eu(t=>p(t,e)?t:e)},eP=(e=C)=>{g(el),ef?eT(!0):el.current=setTimeout(()=>{eT(!0)},e)},eL=(e=A)=>{g(ei),ei.current=setTimeout(()=>{ew.current||eT(!1)},e)},eF=e=>{var t;if(!e)return;let n=null!=(t=e.currentTarget)?t:e.target;if(!(null==n?void 0:n.isConnected))return G(null),void ey({current:null});C?eP():eT(!0),G(n),ey({current:n}),g(ei)},eN=()=>{P?eL(A||100):A?eL():eT(!1),g(el)},eI=({x:e,y:t})=>{var n;u({place:null!=(n=null==ev?void 0:ev.place)?n:f,offset:v,elementReference:{getBoundingClientRect:()=>({x:e,y:t,width:0,height:0,top:t,left:e,right:e,bottom:t})},tooltipReference:er.current,tooltipArrowReference:eo.current,strategy:x,middlewares:O,border:Q,arrowSize:ee}).then(e=>{eR(e)})},eM=e=>{if(!e)return;let t={x:e.clientX,y:e.clientY};eI(t),eg.current=t},eD=e=>{var t;if(!ec)return;let n=e.target;n.isConnected&&(null==(t=er.current)||!t.contains(n))&&([document.querySelector(`[id='${s}']`),...eE].some(e=>null==e?void 0:e.contains(n))||(eT(!1),g(el)))},ej=d(eF,50,!0),eB=d(eN,50,!0),eU=e=>{eB.cancel(),ej(e)},eH=()=>{ej.cancel(),eB()},eY=(0,r.useCallback)(()=>{var e,t;let n=null!=(e=null==ev?void 0:ev.position)?e:U;n?eI(n):k?eg.current&&eI(eg.current):(null==$?void 0:$.isConnected)&&u({place:null!=(t=null==ev?void 0:ev.place)?t:f,offset:v,elementReference:$,tooltipReference:er.current,tooltipArrowReference:eo.current,strategy:x,middlewares:O,border:Q,arrowSize:ee}).then(e=>{ex.current&&eR(e)})},[ec,$,z,B,f,null==ev?void 0:ev.place,v,x,U,null==ev?void 0:ev.position,k,ee]);(0,r.useEffect)(()=>{var e,t;let n=new Set(eb);eE.forEach(e=>{(null==W?void 0:W(e))||n.add({current:e})});let r=document.querySelector(`[id='${s}']`);!r||(null==W?void 0:W(r))||n.add({current:r});let l=()=>{eT(!1)},i=m($),a=m(er.current);ek.scroll&&(window.addEventListener("scroll",l),null==i||i.addEventListener("scroll",l),null==a||a.addEventListener("scroll",l));let u=null;ek.resize?window.addEventListener("resize",l):$&&er.current&&(u=(0,o.ll)($,er.current,eY,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));let c=e=>{"Escape"===e.key&&eT(!1)};ek.escape&&window.addEventListener("keydown",c),ek.clickOutsideAnchor&&window.addEventListener("click",eD);let d=[],f=e=>!!((null==e?void 0:e.target)&&(null==$?void 0:$.contains(e.target))),p=e=>{ec&&f(e)||eF(e)},v=e=>{ec&&f(e)&&eN()},h=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],g=["click","dblclick","mousedown","mouseup"];Object.entries(eC).forEach(([e,t])=>{t&&(h.includes(e)?d.push({event:e,listener:eU}):g.includes(e)&&d.push({event:e,listener:p}))}),Object.entries(eA).forEach(([e,t])=>{t&&(h.includes(e)?d.push({event:e,listener:eH}):g.includes(e)&&d.push({event:e,listener:v}))}),k&&d.push({event:"pointermove",listener:eM});let b=()=>{ew.current=!0},y=()=>{ew.current=!1,eN()},w=P&&(eA.mouseout||eA.mouseleave);return w&&(null==(e=er.current)||e.addEventListener("mouseover",b),null==(t=er.current)||t.addEventListener("mouseout",y)),d.forEach(({event:e,listener:t})=>{n.forEach(n=>{var r;null==(r=n.current)||r.addEventListener(e,t)})}),()=>{var e,t;ek.scroll&&(window.removeEventListener("scroll",l),null==i||i.removeEventListener("scroll",l),null==a||a.removeEventListener("scroll",l)),ek.resize?window.removeEventListener("resize",l):null==u||u(),ek.clickOutsideAnchor&&window.removeEventListener("click",eD),ek.escape&&window.removeEventListener("keydown",c),w&&(null==(e=er.current)||e.removeEventListener("mouseover",b),null==(t=er.current)||t.removeEventListener("mouseout",y)),d.forEach(({event:e,listener:t})=>{n.forEach(n=>{var r;null==(r=n.current)||r.removeEventListener(e,t)})})}},[$,eY,ef,eb,eE,I,M,D,eO,C,A]),(0,r.useEffect)(()=>{var e,n;let r=null!=(n=null!=(e=null==ev?void 0:ev.anchorSelect)?e:c)?n:"";!r&&t&&(r=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`);let o=new MutationObserver(e=>{let n=[],o=[];e.forEach(e=>{if("attributes"===e.type&&"data-tooltip-id"===e.attributeName&&(e.target.getAttribute("data-tooltip-id")===t?n.push(e.target):e.oldValue===t&&o.push(e.target)),"childList"===e.type){if($){let t=[...e.removedNodes].filter(e=>1===e.nodeType);if(r)try{o.push(...t.filter(e=>e.matches(r))),o.push(...t.flatMap(e=>[...e.querySelectorAll(r)]))}catch(e){}t.some(e=>{var t;return!!(null==(t=null==e?void 0:e.contains)?void 0:t.call(e,$))&&(ep(!1),eT(!1),G(null),g(el),g(ei),!0)})}if(r)try{let t=[...e.addedNodes].filter(e=>1===e.nodeType);n.push(...t.filter(e=>e.matches(r))),n.push(...t.flatMap(e=>[...e.querySelectorAll(r)]))}catch(e){}}}),(n.length||o.length)&&eS(e=>[...e.filter(e=>!o.includes(e)),...n])});return o.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{o.disconnect()}},[t,c,null==ev?void 0:ev.anchorSelect,$]),(0,r.useEffect)(()=>{eY()},[eY]),(0,r.useEffect)(()=>{if(!(null==K?void 0:K.current))return()=>null;let e=new ResizeObserver(()=>{setTimeout(()=>eY())});return e.observe(K.current),()=>{e.disconnect()}},[z,null==K?void 0:K.current]),(0,r.useEffect)(()=>{var e;let t=document.querySelector(`[id='${s}']`),n=[...eE,t];$&&n.includes($)||G(null!=(e=eE[0])?e:t)},[s,eE,$]),(0,r.useEffect)(()=>(q&&eT(!0),()=>{g(el),g(ei)}),[]),(0,r.useEffect)(()=>{var e;let n=null!=(e=null==ev?void 0:ev.anchorSelect)?e:c;if(!n&&t&&(n=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`),n)try{let e=Array.from(document.querySelectorAll(n));eS(e)}catch(e){eS([])}},[t,c,null==ev?void 0:ev.anchorSelect]),(0,r.useEffect)(()=>{el.current&&(g(el),eP(C))},[C]);let eW=null!=(en=null==ev?void 0:ev.content)?en:z,ez=ec&&Object.keys(es.tooltipStyles).length>0;return(0,r.useImperativeHandle)(e,()=>({open:e=>{if(null==e?void 0:e.anchorSelect)try{document.querySelector(e.anchorSelect)}catch(t){return void console.warn(`[react-tooltip] "${e.anchorSelect}" is not a valid CSS selector`)}em(null!=e?e:null),(null==e?void 0:e.delay)?eP(e.delay):eT(!0)},close:e=>{(null==e?void 0:e.delay)?eL(e.delay):eT(!1)},activeAnchor:$,place:es.place,isOpen:!!(ef&&!T&&eW&&ez)})),ef&&!T&&eW?r.createElement(_,{id:t,role:et,className:l("react-tooltip",E.tooltip,S.tooltip,S[a],n,`react-tooltip__place-${es.place}`,E[ez?"show":"closing"],ez?"react-tooltip__show":"react-tooltip__closing","fixed"===x&&E.fixed,P&&E.clickable),onTransitionEnd:e=>{g(ea),ec||"opacity"!==e.propertyName||(ep(!1),em(null),null==Y||Y())},style:{...B,...es.tooltipStyles,opacity:void 0!==Z&&ez?Z:void 0},ref:er},eW,r.createElement(_,{className:l("react-tooltip-arrow",E.arrow,S.arrow,i,R&&E.noArrow),style:{...es.tooltipArrowStyles,background:J?`linear-gradient(to right bottom, transparent 50%, ${J} 50%)`:void 0,"--rt-arrow-size":`${ee}px`},ref:eo})):null},O=({content:e})=>r.createElement("span",{dangerouslySetInnerHTML:{__html:e}}),_=r.forwardRef(({id:e,anchorId:t,anchorSelect:n,content:o,html:i,render:a,className:s,classNameArrow:u,variant:d="dark",place:f="top",offset:p=10,wrapper:v="div",children:m=null,events:h=["hover"],openOnClick:g=!1,positionStrategy:b="absolute",middlewares:y,delayShow:E=0,delayHide:S=0,float:_=!1,hidden:C=!1,noArrow:A=!1,clickable:k=!1,closeOnEsc:T=!1,closeOnScroll:R=!1,closeOnResize:P=!1,openEvents:L,closeEvents:F,globalCloseEvents:N,imperativeModeOnly:I=!1,style:M,position:D,isOpen:j,defaultIsOpen:B=!1,disableStyleInjection:U=!1,border:H,opacity:Y,arrowColor:W,arrowSize:z,setIsOpen:K,afterShow:X,afterHide:q,disableTooltip:V,role:$="tooltip"},G)=>{let[Q,Z]=(0,r.useState)(o),[J,ee]=(0,r.useState)(i),[et,en]=(0,r.useState)(f),[er,eo]=(0,r.useState)(d),[el,ei]=(0,r.useState)(p),[ea,es]=(0,r.useState)(E),[eu,ec]=(0,r.useState)(S),[ed,ef]=(0,r.useState)(_),[ep,ev]=(0,r.useState)(C),[em,eh]=(0,r.useState)(v),[eg,eb]=(0,r.useState)(h),[ey,ew]=(0,r.useState)(b),[eE,eS]=(0,r.useState)(null),[ex,eO]=(0,r.useState)(null),e_=(0,r.useRef)(U),{anchorRefs:eC,activeAnchor:eA}=w(e),ek=e=>null==e?void 0:e.getAttributeNames().reduce((t,n)=>{var r;return n.startsWith("data-tooltip-")&&(t[n.replace(/^data-tooltip-/,"")]=null!=(r=null==e?void 0:e.getAttribute(n))?r:null),t},{}),eT=e=>{let t={place:e=>{en(null!=e?e:f)},content:e=>{Z(null!=e?e:o)},html:e=>{ee(null!=e?e:i)},variant:e=>{eo(null!=e?e:d)},offset:e=>{ei(null===e?p:Number(e))},wrapper:e=>{eh(null!=e?e:v)},events:e=>{let t=null==e?void 0:e.split(" ");eb(null!=t?t:h)},"position-strategy":e=>{ew(null!=e?e:b)},"delay-show":e=>{es(null===e?E:Number(e))},"delay-hide":e=>{ec(null===e?S:Number(e))},float:e=>{ef(null===e?_:"true"===e)},hidden:e=>{ev(null===e?C:"true"===e)},"class-name":e=>{eS(e)}};Object.values(t).forEach(e=>e(null)),Object.entries(e).forEach(([e,n])=>{var r;null==(r=t[e])||r.call(t,n)})};(0,r.useEffect)(()=>{Z(o)},[o]),(0,r.useEffect)(()=>{ee(i)},[i]),(0,r.useEffect)(()=>{en(f)},[f]),(0,r.useEffect)(()=>{eo(d)},[d]),(0,r.useEffect)(()=>{ei(p)},[p]),(0,r.useEffect)(()=>{es(E)},[E]),(0,r.useEffect)(()=>{ec(S)},[S]),(0,r.useEffect)(()=>{ef(_)},[_]),(0,r.useEffect)(()=>{ev(C)},[C]),(0,r.useEffect)(()=>{ew(b)},[b]),(0,r.useEffect)(()=>{e_.current!==U&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[U]),(0,r.useEffect)(()=>{"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:"core"===U,disableBase:U}}))},[]),(0,r.useEffect)(()=>{var r;let o=new Set(eC),l=n;if(!l&&e&&(l=`[data-tooltip-id='${e.replace(/'/g,"\\'")}']`),l)try{document.querySelectorAll(l).forEach(e=>{o.add({current:e})})}catch(e){console.warn(`[react-tooltip] "${l}" is not a valid CSS selector`)}let i=document.querySelector(`[id='${t}']`);if(i&&o.add({current:i}),!o.size)return()=>null;let a=null!=(r=null!=ex?ex:i)?r:eA.current,s=new MutationObserver(e=>{e.forEach(e=>{var t;a&&"attributes"===e.type&&(null==(t=e.attributeName)?void 0:t.startsWith("data-tooltip-"))&&eT(ek(a))})});return a&&(eT(ek(a)),s.observe(a,{attributes:!0,childList:!1,subtree:!1})),()=>{s.disconnect()}},[eC,eA,ex,t,n]),(0,r.useEffect)(()=>{(null==M?void 0:M.border)&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),H&&!c("border",`${H}`)&&console.warn(`[react-tooltip] "${H}" is not a valid \`border\`.`),(null==M?void 0:M.opacity)&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),Y&&!c("opacity",`${Y}`)&&console.warn(`[react-tooltip] "${Y}" is not a valid \`opacity\`.`)},[]);let eR=m,eP=(0,r.useRef)(null);if(a){let e=a({content:(null==ex?void 0:ex.getAttribute("data-tooltip-content"))||Q||null,activeAnchor:ex});eR=e?r.createElement("div",{ref:eP,className:"react-tooltip-content-wrapper"},e):null}else Q&&(eR=Q);J&&(eR=r.createElement(O,{content:J}));let eL={forwardRef:G,id:e,anchorId:t,anchorSelect:n,className:l(s,eE),classNameArrow:u,content:eR,contentWrapperRef:eP,place:et,variant:er,offset:el,wrapper:em,events:eg,openOnClick:g,positionStrategy:ey,middlewares:y,delayShow:ea,delayHide:eu,float:ed,hidden:ep,noArrow:A,clickable:k,closeOnEsc:T,closeOnScroll:R,closeOnResize:P,openEvents:L,closeEvents:F,globalCloseEvents:N,imperativeModeOnly:I,style:M,position:D,isOpen:j,defaultIsOpen:B,border:H,opacity:Y,arrowColor:W,arrowSize:z,setIsOpen:K,afterShow:X,afterHide:q,disableTooltip:V,activeAnchor:ex,setActiveAnchor:e=>eO(e),role:$};return r.createElement(x,{...eL})});"undefined"!=typeof window&&window.addEventListener("react-tooltip-inject-styles",e=>{e.detail.disableCore||s({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s;--rt-arrow-size:8px}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit;z-index:-1}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),e.detail.disableBase||s({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:var(--rt-arrow-size);height:var(--rt-arrow-size)}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})})},83357:(e,t,n)=>{n.d(t,{WF:()=>tf});var r,o,l,i=n(49061),a=n(33871),s=n(12115),u=n(47650),c=n(87657);function d(e){var t,n;return c._.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}var f=n(48014),p=n(30797);function v(e,t){return null!==e&&null!==t&&"object"==typeof e&&"object"==typeof t&&"id"in e&&"id"in t?e.id===t.id:e===t}var m=n(21231),h=n(45261);class g extends Map{get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}constructor(e){super(),this.factory=e}}var b=Object.defineProperty,y=(e,t,n)=>t in e?b(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,w=(e,t,n)=>(y(e,"symbol"!=typeof t?t+"":t,n),n),E=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},S=(e,t,n)=>(E(e,t,"read from private field"),n?n.call(e):t.get(e)),x=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},O=(e,t,n,r)=>(E(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);class _{dispose(){this.disposables.dispose()}get state(){return S(this,r)}subscribe(e,t){let n={selector:e,callback:t,current:e(S(this,r))};return S(this,l).add(n),this.disposables.add(()=>{S(this,l).delete(n)})}on(e,t){return S(this,o).get(e).add(t),this.disposables.add(()=>{S(this,o).get(e).delete(t)})}send(e){let t=this.reduce(S(this,r),e);if(t!==S(this,r)){for(let e of(O(this,r,t),S(this,l))){let t=e.selector(S(this,r));C(e.current,t)||(e.current=t,e.callback(t))}for(let t of S(this,o).get(e.type))t(S(this,r),e)}}constructor(e){x(this,r,{}),x(this,o,new g(()=>new Set)),x(this,l,new Set),w(this,"disposables",(0,h.e)()),O(this,r,e)}}function C(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&A(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&A(e.entries(),t.entries()):!!(k(e)&&k(t))&&A(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function A(e,t){for(;;){let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}}function k(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}function T(e){let[t,n]=e(),r=(0,h.e)();return function(){for(var e=arguments.length,o=Array(e),l=0;l<e;l++)o[l]=arguments[l];t(...o),r.dispose(),r.microTask(n)}}r=new WeakMap,o=new WeakMap,l=new WeakMap;var R=n(27279),P=Object.defineProperty,L=(e,t,n)=>t in e?P(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,F=(e,t,n)=>(L(e,"symbol"!=typeof t?t+"":t,n),n),N=(e=>(e[e.Push=0]="Push",e[e.Pop=1]="Pop",e))(N||{});let I={0(e,t){let n=t.id,r=e.stack,o=e.stack.indexOf(n);if(-1!==o){let t=e.stack.slice();return t.splice(o,1),t.push(n),r=t,{...e,stack:r}}return{...e,stack:[...e.stack,n]}},1(e,t){let n=t.id,r=e.stack.indexOf(n);if(-1===r)return e;let o=e.stack.slice();return o.splice(r,1),{...e,stack:o}}};class M extends _{static new(){return new M({stack:[]})}reduce(e,t){return(0,R.Y)(t.type,I,e,t)}constructor(){super(...arguments),F(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),F(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}}let D=new g(()=>M.new());var j=n(39611);function B(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:C;return(0,j.useSyncExternalStoreWithSelector)((0,p._)(t=>e.subscribe(U,t)),(0,p._)(()=>e.state),(0,p._)(()=>e.state),(0,p._)(t),n)}function U(e){return e}function H(e,t){let n=(0,s.useId)(),r=D.get(t),[o,l]=B(r,(0,s.useCallback)(e=>[r.selectors.isTop(e,n),r.selectors.inStack(e,n)],[r,n]));return(0,m.s)(()=>{if(e)return r.actions.push(n),()=>r.actions.pop(n)},[r,e,n]),!!e&&(!l||o)}let Y=new Map,W=new Map;function z(e){var t;let n=null!=(t=W.get(e))?t:0;return W.set(e,n+1),0!==n||(Y.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let n=null!=(t=W.get(e))?t:1;if(1===n?W.delete(e):W.set(e,n-1),1!==n)return;let r=Y.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,Y.delete(e))})(e)}var K=n(6232);function X(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function q(e){return X(e)&&"tagName"in e}function V(e){return q(e)&&"accessKey"in e}function $(e){return q(e)&&"tabIndex"in e}function G(e){return V(e)&&"INPUT"===e.nodeName}function Q(e){return V(e)&&"LABEL"===e.nodeName}function Z(e){return V(e)&&"LEGEND"===e.nodeName}let J=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(","),ee=["[data-autofocus]"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(",");var et=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(et||{}),en=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(en||{}),er=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(er||{});function eo(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(J)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var el=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(el||{});function ei(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e!==(null==(t=d(e))?void 0:t.body)&&(0,R.Y)(n,{0:()=>e.matches(J),1(){let t=e;for(;null!==t;){if(t.matches(J))return!0;t=t.parentElement}return!1}})}var ea=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(ea||{});function es(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return e.slice().sort((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let l=r.compareDocumentPosition(o);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function eu(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function ec(){return eu()||/Android/gi.test(window.navigator.userAgent)}function ed(e,t,n,r){let o=(0,K.Y)(n);(0,s.useEffect)(()=>{if(e)return document.addEventListener(t,n,r),()=>document.removeEventListener(t,n,r);function n(e){o.current(e)}},[e,t,r])}function ef(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,s.useMemo)(()=>d(...t),[...t])}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));var ep=(e=>(e[e.Ignore=0]="Ignore",e[e.Select=1]="Select",e[e.Close=2]="Close",e))(ep||{});let ev={Ignore:{kind:0},Select:e=>({kind:1,target:e}),Close:{kind:2}},em=function(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e){for(var o=arguments.length,l=Array(o>1?o-1:0),i=1;i<o;i++)l[i-1]=arguments[i];let a=t[e].call(n,...l);a&&(n=a,r.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,h.e)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT(e){let t,{doc:n,d:r,meta:o}=e,l={doc:n,d:r,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(o)},i=[eu()?{before(e){let{doc:t,d:n,meta:r}=e;function o(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}n.microTask(()=>{var e;if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=(0,h.e)();e.style(t.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>e.dispose()))}let r=null!=(e=window.scrollY)?e:window.pageYOffset,l=null;n.addEventListener(t,"click",e=>{if($(e.target))try{let n=e.target.closest("a");if(!n)return;let{hash:r}=new URL(n.href),i=t.querySelector(r);$(i)&&!o(i)&&(l=i)}catch(e){}},!0),n.addEventListener(t,"touchstart",e=>{var t;if($(e.target)&&q(t=e.target)&&"style"in t)if(o(e.target)){let t=e.target;for(;t.parentElement&&o(t.parentElement);)t=t.parentElement;n.style(t,"overscrollBehavior","contain")}else n.style(e.target,"touchAction","none")}),n.addEventListener(t,"touchmove",e=>{if($(e.target)&&!G(e.target))if(o(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()},{passive:!1}),n.add(()=>{var e;r!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,r),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)})})}}:{},{before(e){var n;let{doc:r}=e,o=r.documentElement;t=Math.max(0,(null!=(n=r.defaultView)?n:window).innerWidth-o.clientWidth)},after(e){let{doc:n,d:r}=e,o=n.documentElement,l=Math.max(0,o.clientWidth-o.offsetWidth),i=Math.max(0,t-l);r.style(o,"paddingRight","".concat(i,"px"))}},{before(e){let{doc:t,d:n}=e;n.style(t.documentElement,"overflow","hidden")}}];i.forEach(e=>{let{before:t}=e;return null==t?void 0:t(l)}),i.forEach(e=>{let{after:t}=e;return null==t?void 0:t(l)})},SCROLL_ALLOW(e){let{d:t}=e;t.dispose()},TEARDOWN(e){let{doc:t}=e;this.delete(t)}});em.subscribe(()=>{let e=em.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&em.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&em.dispatch("TEARDOWN",n)}});var eh=n(47769);let eg=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function eb(e){var t,n;let r=null!=(t=e.innerText)?t:"",o=e.cloneNode(!0);if(!V(o))return r;let l=!1;for(let e of o.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))e.remove(),l=!0;let i=l?null!=(n=o.innerText)?n:"":r;return eg.test(i)&&(i=i.replace(eg,"")),i}function ey(e){return[e.screenX,e.screenY]}var ew=n(88048);let eE=(0,s.createContext)(void 0);function eS(){return(0,s.useContext)(eE)}var ex=n(15782);let eO=(0,s.createContext)({styles:void 0,setReference:()=>{},setFloating:()=>{},getReferenceProps:()=>({}),getFloatingProps:()=>({}),slot:{}});eO.displayName="FloatingContext";let e_=(0,s.createContext)(null);function eC(e){var t,n,r,o,l,i;let a,u,c,{children:d,enabled:f=!0}=e,[v,h]=(0,s.useState)(null),[g,b]=(0,s.useState)(0),y=(0,s.useRef)(null),[w,E]=(0,s.useState)(null);t=w,(0,m.s)(()=>{if(!t)return;let e=new MutationObserver(()=>{let e=window.getComputedStyle(t).maxHeight,n=parseFloat(e);if(isNaN(n))return;let r=parseInt(e);isNaN(r)||n!==r&&(t.style.maxHeight="".concat(Math.ceil(n),"px"))});return e.observe(t,{attributes:!0,attributeFilter:["style"]}),()=>{e.disconnect()}},[t]);let S=f&&null!==v&&null!==w,{to:x="bottom",gap:O=0,offset:_=0,padding:C=0,inner:A}=(n=v,r=w,a=eA(null!=(o=null==n?void 0:n.gap)?o:"var(--anchor-gap, 0)",r),u=eA(null!=(l=null==n?void 0:n.offset)?l:"var(--anchor-offset, 0)",r),c=eA(null!=(i=null==n?void 0:n.padding)?i:"var(--anchor-padding, 0)",r),{...n,gap:a,offset:u,padding:c}),[k,T="center"]=x.split(" ");(0,m.s)(()=>{S&&b(0)},[S]);let{refs:R,floatingStyles:P,context:L}=(0,ex.we)({open:S,placement:"selection"===k?"center"===T?"bottom":"bottom-".concat(T):"center"===T?"".concat(k):"".concat(k,"-").concat(T),strategy:"absolute",transform:!1,middleware:[(0,ex.cY)({mainAxis:"selection"===k?0:O,crossAxis:_}),(0,ex.BN)({padding:C}),"selection"!==k&&(0,ex.UU)({padding:C}),"selection"===k&&A?(0,ex.vW)({...A,padding:C,overflowRef:y,offset:g,minItemsVisible:4,referenceOverflowThreshold:C,onFallbackChange(e){var t,n;if(!e)return;let r=L.elements.floating;if(!r)return;let o=parseFloat(getComputedStyle(r).scrollPaddingBottom)||0,l=Math.min(4,r.childElementCount),i=0,a=0;for(let e of null!=(n=null==(t=L.elements.floating)?void 0:t.childNodes)?n:[])if(V(e)){let t=e.offsetTop,n=t+e.clientHeight+o,s=r.scrollTop,u=s+r.clientHeight;if(t>=s&&n<=u)l--;else{a=Math.max(0,Math.min(n,u)-Math.max(t,s)),i=e.clientHeight;break}}l>=1&&b(e=>{let t=i*l-a+o;return e>=t?e:t})}}):null,(0,ex.Ej)({padding:C,apply(e){let{availableWidth:t,availableHeight:n,elements:r}=e;Object.assign(r.floating.style,{overflow:"auto",maxWidth:"".concat(t,"px"),maxHeight:"min(var(--anchor-max-height, 100vh), ".concat(n,"px)")})}})].filter(Boolean),whileElementsMounted:ex.ll}),[F=k,N=T]=L.placement.split("-");"selection"===k&&(F="selection");let I=(0,s.useMemo)(()=>({anchor:[F,N].filter(Boolean).join(" ")}),[F,N]),M=(0,ex.Zx)(L,{overflowRef:y,onChange:b}),{getReferenceProps:D,getFloatingProps:j}=(0,ex.bv)([M]),B=(0,p._)(e=>{E(e),R.setFloating(e)});return s.createElement(e_.Provider,{value:h},s.createElement(eO.Provider,{value:{setFloating:B,setReference:R.setReference,styles:P,getReferenceProps:D,getFloatingProps:j,slot:I}},d))}function eA(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,r=(0,f.L)(),o=(0,p._)((e,t)=>{if(null==e)return[n,null];if("number"==typeof e)return[e,null];if("string"==typeof e){if(!t)return[n,null];let o=ek(e,t);return[o,n=>{let l=function e(t){let n=/var\((.*)\)/.exec(t);if(n){let t=n[1].indexOf(",");if(-1===t)return[n[1]];let r=n[1].slice(0,t).trim(),o=n[1].slice(t+1).trim();return o?[r,...e(o)]:[r]}return[]}(e);{let i=l.map(e=>window.getComputedStyle(t).getPropertyValue(e));r.requestAnimationFrame(function a(){r.nextFrame(a);let s=!1;for(let[e,n]of l.entries()){let r=window.getComputedStyle(t).getPropertyValue(n);if(i[e]!==r){i[e]=r,s=!0;break}}if(!s)return;let u=ek(e,t);o!==u&&(n(u),o=u)})}return r.dispose}]}return[n,null]}),l=(0,s.useMemo)(()=>o(e,t)[0],[e,t]),[i=l,a]=(0,s.useState)();return(0,m.s)(()=>{let[n,r]=o(e,t);if(a(n),r)return r(a)},[e,t]),i}function ek(e,t){let n=document.createElement("div");t.appendChild(n),n.style.setProperty("margin-top","0px","important"),n.style.setProperty("margin-top",e,"important");let r=parseFloat(window.getComputedStyle(n).marginTop)||0;return t.removeChild(n),r}e_.displayName="PlacementContext";function eT(e,t){return e?e+"["+t+"]":t}var eR=n(84554),eP=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(eP||{});let eL=(0,eR.FX)(function(e,t){var n;let{features:r=1,...o}=e,l={ref:t,"aria-hidden":(2&r)==2||(null!=(n=o["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,eR.Ci)()({ourProps:l,theirProps:o,slot:{},defaultTag:"span",name:"Hidden"})}),eF=(0,s.createContext)(null);function eN(e){let{children:t}=e,n=(0,s.useContext)(eF);if(!n)return s.createElement(s.Fragment,null,t);let{target:r}=n;return r?(0,u.createPortal)(s.createElement(s.Fragment,null,t),r):null}function eI(e){let{data:t,form:n,disabled:r,onReset:o,overrides:l}=e,[i,a]=(0,s.useState)(null),u=(0,f.L)();return(0,s.useEffect)(()=>{if(o&&i)return u.addEventListener(i,"reset",o)},[i,n,o]),s.createElement(eN,null,s.createElement(eM,{setForm:a,formId:n}),(function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];for(let[o,l]of Object.entries(t))!function t(n,r,o){if(Array.isArray(o))for(let[e,l]of o.entries())t(n,eT(r,e.toString()),l);else o instanceof Date?n.push([r,o.toISOString()]):"boolean"==typeof o?n.push([r,o?"1":"0"]):"string"==typeof o?n.push([r,o]):"number"==typeof o?n.push([r,"".concat(o)]):null==o?n.push([r,""]):e(o,r,n)}(r,eT(n,o),l);return r})(t).map(e=>{let[t,o]=e;return s.createElement(eL,{features:eP.Hidden,...(0,eR.oE)({key:t,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:n,disabled:r,name:t,value:o,...l})})}))}function eM(e){let{setForm:t,formId:n}=e;return(0,s.useEffect)(()=>{if(n){let e=document.getElementById(n);e&&t(e)}},[t,n]),n?null:s.createElement(eL,{features:eP.Hidden,as:"input",type:"hidden",hidden:!0,readOnly:!0,ref:e=>{if(!e)return;let n=e.closest("form");n&&t(n)}})}let eD=(0,s.createContext)(void 0);function ej(){return(0,s.useContext)(eD)}var eB=n(91525),eU=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(eU||{});function eH(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),o=null!=r?r:-1;switch(e.focus){case 0:for(let e=0;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 1:-1===o&&(o=n.length);for(let e=o-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 2:for(let e=o+1;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 3:for(let e=n.length-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 4:for(let r=0;r<n.length;++r)if(t.resolveId(n[r],r,n)===e.id)return r;return r;case 5:return null;default:throw Error("Unexpected object: "+e)}}let eY=(0,s.createContext)(null);eY.displayName="DescriptionContext",Object.assign((0,eR.FX)(function(e,t){let n=(0,s.useId)(),r=eS(),{id:o="headlessui-description-".concat(n),...l}=e,i=function e(){let t=(0,s.useContext)(eY);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),a=(0,eh.P)(t);(0,m.s)(()=>i.register(o),[o,i.register]);let u=r||!1,c=(0,s.useMemo)(()=>({...i.slot,disabled:u}),[i.slot,u]),d={ref:a,...i.props,id:o};return(0,eR.Ci)()({ourProps:d,theirProps:l,slot:c,defaultTag:"p",name:i.name||"Description"})}),{});var eW=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(eW||{});let ez=(0,s.createContext)(null);function eK(e){var t,n,r;let o=null!=(n=null==(t=(0,s.useContext)(ez))?void 0:t.value)?n:void 0;return(null!=(r=null==e?void 0:e.length)?r:0)>0?[o,...e].filter(Boolean).join(" "):o}ez.displayName="LabelContext";let eX=Object.assign((0,eR.FX)(function(e,t){var n;let r=(0,s.useId)(),o=function e(){let t=(0,s.useContext)(ez);if(null===t){let t=Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),l=ej(),i=eS(),{id:a="headlessui-label-".concat(r),htmlFor:u=null!=l?l:null==(n=o.props)?void 0:n.htmlFor,passive:c=!1,...d}=e,f=(0,eh.P)(t);(0,m.s)(()=>o.register(a),[a,o.register]);let v=(0,p._)(e=>{var t;let n=e.currentTarget;if(!(e.target!==e.currentTarget&&q(t=e.target)&&t.matches('a[href],audio[controls],button,details,embed,iframe,img[usemap],input:not([type="hidden"]),label,select,textarea,video[controls]'))&&(Q(n)&&e.preventDefault(),o.props&&"onClick"in o.props&&"function"==typeof o.props.onClick&&o.props.onClick(e),Q(n))){let e=document.getElementById(n.htmlFor);if(e){let t=e.getAttribute("disabled");if("true"===t||""===t)return;let n=e.getAttribute("aria-disabled");if("true"===n||""===n)return;(G(e)&&("file"===e.type||"radio"===e.type||"checkbox"===e.type)||"radio"===e.role||"checkbox"===e.role||"switch"===e.role)&&e.click(),e.focus({preventScroll:!0})}}}),h=i||!1,g=(0,s.useMemo)(()=>({...o.slot,disabled:h}),[o.slot,h]),b={ref:f,...o.props,id:a,htmlFor:u,onClick:v};return c&&("onClick"in b&&(delete b.htmlFor,delete b.onClick),"onClick"in d&&delete d.onClick),(0,eR.Ci)()({ourProps:b,theirProps:d,slot:g,defaultTag:u?"label":"div",name:o.name||"Label"})}),{});var eq=n(7856);function eV(e){let t=(0,p._)(e),n=(0,s.useRef)(!1);(0,s.useEffect)(()=>(n.current=!1,()=>{n.current=!0,(0,eq._)(()=>{n.current&&t()})}),[t])}var e$=n(89925);let eG=(0,s.createContext)(!1),eQ=s.Fragment,eZ=(0,eR.FX)(function(e,t){let{ownerDocument:n=null,...r}=e,o=(0,s.useRef)(null),l=(0,eh.P)((0,eh.a)(e=>{o.current=e}),t),i=ef(o),a=null!=n?n:i,d=function(e){let t=(0,s.useContext)(eG),n=(0,s.useContext)(e0),[r,o]=(0,s.useState)(()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(c._.isServer)return null;let o=null==e?void 0:e.getElementById("headlessui-portal-root");if(o)return o;if(null===e)return null;let l=e.createElement("div");return l.setAttribute("id","headlessui-portal-root"),e.body.appendChild(l)});return(0,s.useEffect)(()=>{null!==r&&(null!=e&&e.body.contains(r)||null==e||e.body.appendChild(r))},[r,e]),(0,s.useEffect)(()=>{t||null!==n&&o(n.current)},[n,o,t]),r}(a),[f]=(0,s.useState)(()=>{var e;return c._.isServer?null:null!=(e=null==a?void 0:a.createElement("div"))?e:null}),p=(0,s.useContext)(e1),v=(0,e$.g)();(0,m.s)(()=>{!d||!f||d.contains(f)||(f.setAttribute("data-headlessui-portal",""),d.appendChild(f))},[d,f]),(0,m.s)(()=>{if(f&&p)return p.register(f)},[p,f]),eV(()=>{var e;d&&f&&(X(f)&&d.contains(f)&&d.removeChild(f),d.childNodes.length<=0&&(null==(e=d.parentElement)||e.removeChild(d)))});let h=(0,eR.Ci)();return v&&d&&f?(0,u.createPortal)(h({ourProps:{ref:l},theirProps:r,slot:{},defaultTag:eQ,name:"Portal"}),f):null}),eJ=s.Fragment,e0=(0,s.createContext)(null),e1=(0,s.createContext)(null),e2=Object.assign((0,eR.FX)(function(e,t){let n=(0,eh.P)(t),{enabled:r=!0,ownerDocument:o,...l}=e,i=(0,eR.Ci)();return r?s.createElement(eZ,{...l,ownerDocument:o,ref:n}):i({ourProps:{ref:n},theirProps:l,slot:{},defaultTag:eQ,name:"Portal"})}),{Group:(0,eR.FX)(function(e,t){let{target:n,...r}=e,o={ref:(0,eh.P)(t)},l=(0,eR.Ci)();return s.createElement(e0.Provider,{value:n},l({ourProps:o,theirProps:r,defaultTag:eJ,name:"Popover.Group"}))})});var e5=Object.defineProperty,e3=(e,t,n)=>t in e?e5(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,e4=(e,t,n)=>(e3(e,"symbol"!=typeof t?t+"":t,n),n),e7=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(e7||{}),e8=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(e8||{}),e6=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(e6||{}),e9=(e=>(e[e.OpenListbox=0]="OpenListbox",e[e.CloseListbox=1]="CloseListbox",e[e.GoToOption=2]="GoToOption",e[e.Search=3]="Search",e[e.ClearSearch=4]="ClearSearch",e[e.RegisterOptions=5]="RegisterOptions",e[e.UnregisterOptions=6]="UnregisterOptions",e[e.SetButtonElement=7]="SetButtonElement",e[e.SetOptionsElement=8]="SetOptionsElement",e[e.SortOptions=9]="SortOptions",e))(e9||{});function te(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e,n=null!==e.activeOptionIndex?e.options[e.activeOptionIndex]:null,r=es(t(e.options.slice()),e=>e.dataRef.current.domRef.current),o=n?r.indexOf(n):null;return -1===o&&(o=null),{options:r,activeOptionIndex:o}}let tt={1:e=>e.dataRef.current.disabled||1===e.listboxState?e:{...e,activeOptionIndex:null,pendingFocus:{focus:eU.Nothing},listboxState:1,__demoMode:!1},0(e,t){if(e.dataRef.current.disabled||0===e.listboxState)return e;let n=e.activeOptionIndex,{isSelected:r}=e.dataRef.current,o=e.options.findIndex(e=>r(e.dataRef.current.value));return -1!==o&&(n=o),{...e,pendingFocus:t.focus,listboxState:0,activeOptionIndex:n,__demoMode:!1}},2(e,t){var n,r,o,l,i;if(e.dataRef.current.disabled||1===e.listboxState)return e;let a={...e,searchQuery:"",activationTrigger:null!=(n=t.trigger)?n:1,__demoMode:!1};if(t.focus===eU.Nothing)return{...a,activeOptionIndex:null};if(t.focus===eU.Specific)return{...a,activeOptionIndex:e.options.findIndex(e=>e.id===t.id)};if(t.focus===eU.Previous){let n=e.activeOptionIndex;if(null!==n){let l=e.options[n].dataRef.current.domRef,i=eH(t,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});if(null!==i){let t=e.options[i].dataRef.current.domRef;if((null==(r=l.current)?void 0:r.previousElementSibling)===t.current||(null==(o=t.current)?void 0:o.previousElementSibling)===null)return{...a,activeOptionIndex:i}}}}else if(t.focus===eU.Next){let n=e.activeOptionIndex;if(null!==n){let r=e.options[n].dataRef.current.domRef,o=eH(t,{resolveItems:()=>e.options,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});if(null!==o){let t=e.options[o].dataRef.current.domRef;if((null==(l=r.current)?void 0:l.nextElementSibling)===t.current||(null==(i=t.current)?void 0:i.nextElementSibling)===null)return{...a,activeOptionIndex:o}}}}let s=te(e),u=eH(t,{resolveItems:()=>s.options,resolveActiveIndex:()=>s.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return{...a,...s,activeOptionIndex:u}},3:(e,t)=>{if(e.dataRef.current.disabled||1===e.listboxState)return e;let n=+(""===e.searchQuery),r=e.searchQuery+t.value.toLowerCase(),o=(null!==e.activeOptionIndex?e.options.slice(e.activeOptionIndex+n).concat(e.options.slice(0,e.activeOptionIndex+n)):e.options).find(e=>{var t;return!e.dataRef.current.disabled&&(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(r))}),l=o?e.options.indexOf(o):-1;return -1===l||l===e.activeOptionIndex?{...e,searchQuery:r}:{...e,searchQuery:r,activeOptionIndex:l,activationTrigger:1}},4:e=>e.dataRef.current.disabled||1===e.listboxState||""===e.searchQuery?e:{...e,searchQuery:""},5:(e,t)=>{let n=e.options.concat(t.options),r=e.activeOptionIndex;if(e.pendingFocus.focus!==eU.Nothing&&(r=eH(e.pendingFocus,{resolveItems:()=>n,resolveActiveIndex:()=>e.activeOptionIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled})),null===e.activeOptionIndex){let{isSelected:t}=e.dataRef.current;if(t){let e=n.findIndex(e=>null==t?void 0:t(e.dataRef.current.value));-1!==e&&(r=e)}}return{...e,options:n,activeOptionIndex:r,pendingFocus:{focus:eU.Nothing},pendingShouldSort:!0}},6:(e,t)=>{let n=e.options,r=[],o=new Set(t.options);for(let[e,t]of n.entries())if(o.has(t.id)&&(r.push(e),o.delete(t.id),0===o.size))break;if(r.length>0)for(let e of(n=n.slice(),r.reverse()))n.splice(e,1);return{...e,options:n,activationTrigger:1}},7:(e,t)=>e.buttonElement===t.element?e:{...e,buttonElement:t.element},8:(e,t)=>e.optionsElement===t.element?e:{...e,optionsElement:t.element},9:e=>e.pendingShouldSort?{...e,...te(e),pendingShouldSort:!1}:e};class tn extends _{static new(e){let{id:t,__demoMode:n=!1}=e;return new tn({id:t,dataRef:{current:{}},listboxState:+!n,options:[],searchQuery:"",activeOptionIndex:null,activationTrigger:1,buttonElement:null,optionsElement:null,pendingShouldSort:!1,pendingFocus:{focus:eU.Nothing},__demoMode:n})}reduce(e,t){return(0,R.Y)(t.type,tt,e,t)}constructor(e){super(e),e4(this,"actions",{onChange:e=>{let{onChange:t,compare:n,mode:r,value:o}=this.state.dataRef.current;return(0,R.Y)(r,{0:()=>null==t?void 0:t(e),1:()=>{let r=o.slice(),l=r.findIndex(t=>n(t,e));return -1===l?r.push(e):r.splice(l,1),null==t?void 0:t(r)}})},registerOption:T(()=>{let e=[],t=new Set;return[(n,r)=>{t.has(r)||(t.add(r),e.push({id:n,dataRef:r}))},()=>(t.clear(),this.send({type:5,options:e.splice(0)}))]}),unregisterOption:T(()=>{let e=[];return[t=>e.push(t),()=>{this.send({type:6,options:e.splice(0)})}]}),goToOption:T(()=>{let e=null;return[(t,n)=>{e={type:2,...t,trigger:n}},()=>e&&this.send(e)]}),closeListbox:()=>{this.send({type:1})},openListbox:e=>{this.send({type:0,focus:e})},selectActiveOption:()=>{if(null!==this.state.activeOptionIndex){let{dataRef:e,id:t}=this.state.options[this.state.activeOptionIndex];this.actions.onChange(e.current.value),this.send({type:2,focus:eU.Specific,id:t})}},selectOption:e=>{let t=this.state.options.find(t=>t.id===e);t&&this.actions.onChange(t.dataRef.current.value)},search:e=>{this.send({type:3,value:e})},clearSearch:()=>{this.send({type:4})},setButtonElement:e=>{this.send({type:7,element:e})},setOptionsElement:e=>{this.send({type:8,element:e})}}),e4(this,"selectors",{activeDescendantId(e){var t;let n=e.activeOptionIndex,r=e.options;return null===n||null==(t=r[n])?void 0:t.id},isActive(e,t){var n;let r=e.activeOptionIndex,o=e.options;return null!==r&&(null==(n=o[r])?void 0:n.id)===t},shouldScrollIntoView(e,t){return!e.__demoMode&&0===e.listboxState&&0!==e.activationTrigger&&this.isActive(e,t)}}),this.on(5,()=>{requestAnimationFrame(()=>{this.send({type:9})})});{let e=this.state.id,t=D.get(null);this.disposables.add(t.on(N.Push,n=>{t.selectors.isTop(n,e)||0!==this.state.listboxState||this.actions.closeListbox()})),this.on(0,()=>t.actions.push(e)),this.on(1,()=>t.actions.pop(e))}}}let tr=(0,s.createContext)(null);function to(e){let t=(0,s.useContext)(tr);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Listbox /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,tl),t}return t}function tl(e){let{id:t,__demoMode:n=!1}=e,r=(0,s.useMemo)(()=>tn.new({id:t,__demoMode:n}),[]);return eV(()=>r.dispose()),r}let ti=(0,s.createContext)(null);function ta(e){let t=(0,s.useContext)(ti);if(null===t){let t=Error("<".concat(e," /> is missing a parent <Listbox /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,ta),t}return t}ti.displayName="ListboxDataContext";let ts=s.Fragment,tu=(0,s.createContext)(!1),tc=eR.Ac.RenderStrategy|eR.Ac.Static,td=s.Fragment,tf=Object.assign((0,eR.FX)(function(e,t){var n,r;let o,l,i,a,u,c=(0,s.useId)(),d=eS(),{value:f,defaultValue:h,form:g,name:b,onChange:y,by:w,invalid:E=!1,disabled:S=d||!1,horizontal:x=!1,multiple:O=!1,__demoMode:_=!1,...C}=e,A=x?"horizontal":"vertical",k=(0,eh.P)(t),T=function(e){let[t]=(0,s.useState)(e);return t}(h),[P=O?[]:void 0,L]=function(e,t,n){let[r,o]=(0,s.useState)(n),l=void 0!==e,i=(0,s.useRef)(l),a=(0,s.useRef)(!1),u=(0,s.useRef)(!1);return!l||i.current||a.current?l||!i.current||u.current||(u.current=!0,i.current=l,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")):(a.current=!0,i.current=l,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")),[l?e:r,(0,p._)(e=>(l||o(e),null==t?void 0:t(e)))]}(f,y,T),F=tl({id:c,__demoMode:_}),N=(0,s.useRef)({static:!1,hold:!1}),I=(0,s.useRef)(new Map),M=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:v;return(0,s.useCallback)((t,n)=>"string"==typeof e?(null==t?void 0:t[e])===(null==n?void 0:n[e]):e(t,n),[e])}(w),j=(0,s.useCallback)(e=>(0,R.Y)(U.mode,{[e8.Multi]:()=>P.some(t=>M(t,e)),[e8.Single]:()=>M(P,e)}),[P]),U=(0,s.useMemo)(()=>({value:P,disabled:S,invalid:E,mode:O?e8.Multi:e8.Single,orientation:A,onChange:L,compare:M,isSelected:j,optionsPropsRef:N,listRef:I}),[P,S,E,O,A,L,M,j,N,I]);(0,m.s)(()=>{F.state.dataRef.current=U},[U]);let H=B(F,e=>e.listboxState),Y=D.get(null),W=B(Y,(0,s.useCallback)(e=>Y.selectors.isTop(e,c),[Y,c])),[z,X]=B(F,e=>[e.buttonElement,e.optionsElement]);n=[z,X],o=(0,K.Y)((e,t)=>{F.send({type:e9.CloseListbox}),ei(t,el.Loose)||(e.preventDefault(),null==z||z.focus())}),l=(0,s.useCallback)(function(e,t){if(e.defaultPrevented)return;let r=t(e);if(null!==r&&r.getRootNode().contains(r)&&r.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(n))if(null!==t&&(t.contains(r)||e.composed&&e.composedPath().includes(t)))return;return ei(r,el.Loose)||-1===r.tabIndex||e.preventDefault(),o.current(e,r)}},[o,n]),i=(0,s.useRef)(null),ed(W,"pointerdown",e=>{var t,n;ec()||(i.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),ed(W,"pointerup",e=>{if(ec()||!i.current)return;let t=i.current;return i.current=null,l(e,()=>t)},!0),a=(0,s.useRef)({x:0,y:0}),ed(W,"touchstart",e=>{a.current.x=e.touches[0].clientX,a.current.y=e.touches[0].clientY},!0),ed(W,"touchend",e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-a.current.x)>=30||Math.abs(t.y-a.current.y)>=30))return l(e,()=>$(e.target)?e.target:null)},!0),r="blur",u=(0,K.Y)(e=>l(e,()=>{var e;return V(e=window.document.activeElement)&&"IFRAME"===e.nodeName?window.document.activeElement:null})),(0,s.useEffect)(()=>{if(W)return window.addEventListener(r,e,!0),()=>window.removeEventListener(r,e,!0);function e(e){u.current(e)}},[W,r,!0]);let q=(0,s.useMemo)(()=>({open:H===e7.Open,disabled:S,invalid:E,value:P}),[H,S,E,P]),[G,Q]=function(){let{inherit:e=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=eK(),[n,r]=(0,s.useState)([]),o=e?[t,...n].filter(Boolean):n;return[o.length>0?o.join(" "):void 0,(0,s.useMemo)(()=>function(e){let t=(0,p._)(e=>(r(t=>[...t,e]),()=>r(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),n=(0,s.useMemo)(()=>({register:t,slot:e.slot,name:e.name,props:e.props,value:e.value}),[t,e.slot,e.name,e.props,e.value]);return s.createElement(ez.Provider,{value:n},e.children)},[r])]}({inherit:!0}),Z=(0,s.useCallback)(()=>{if(void 0!==T)return null==L?void 0:L(T)},[L,T]),J=(0,eR.Ci)();return s.createElement(Q,{value:G,props:{htmlFor:null==z?void 0:z.id},slot:{open:H===e7.Open,disabled:S}},s.createElement(eC,null,s.createElement(tr.Provider,{value:F},s.createElement(ti.Provider,{value:U},s.createElement(eB.El,{value:(0,R.Y)(H,{[e7.Open]:eB.Uw.Open,[e7.Closed]:eB.Uw.Closed})},null!=b&&null!=P&&s.createElement(eI,{disabled:S,data:{[b]:P},form:g,onReset:Z}),J({ourProps:{ref:k},theirProps:C,slot:q,defaultTag:ts,name:"Listbox"}))))))}),{Button:(0,eR.FX)(function(e,t){var n,r;let o=(0,s.useId)(),l=ej(),c=ta("Listbox.Button"),v=to("Listbox.Button"),{id:m=l||"headlessui-listbox-button-".concat(o),disabled:h=c.disabled||!1,autoFocus:g=!1,...b}=e,y=(0,eh.P)(t,(0,s.useContext)(eO).setReference,v.actions.setButtonElement),w=(0,s.useContext)(eO).getReferenceProps,[E,S,x]=B(v,e=>[e.listboxState,e.buttonElement,e.optionsElement]);!function(e,t){let{trigger:n,action:r,close:o,select:l}=t,i=(0,s.useRef)(null);ed(e&&null!==n,"pointerdown",e=>{X(null==e?void 0:e.target)&&null!=n&&n.contains(e.target)&&(i.current=new Date)}),ed(e&&null!==n,"pointerup",e=>{if(null===i.current||!$(e.target))return;let t=r(e),n=new Date().getTime()-i.current.getTime();switch(i.current=null,t.kind){case 0:return;case 1:n>200&&(l(t.target),o());break;case 2:o()}},{capture:!0})}(E===e7.Open,{trigger:S,action:(0,s.useCallback)(e=>{if(null!=S&&S.contains(e.target))return ev.Ignore;let t=e.target.closest('[role="option"]:not([data-disabled])');return V(t)?ev.Select(t):null!=x&&x.contains(e.target)?ev.Ignore:ev.Close},[S,x]),close:v.actions.closeListbox,select:v.actions.selectActiveOption});let O=(0,p._)(e=>{switch(e.key){case eW.Enter:!function(e){var t,n;let r=null!=(t=null==e?void 0:e.form)?t:e.closest("form");if(r){for(let t of r.elements)if(t!==e&&("INPUT"===t.tagName&&"submit"===t.type||"BUTTON"===t.tagName&&"submit"===t.type||"INPUT"===t.nodeName&&"image"===t.type))return void t.click();null==(n=r.requestSubmit)||n.call(r)}}(e.currentTarget);break;case eW.Space:case eW.ArrowDown:e.preventDefault(),v.actions.openListbox({focus:c.value?eU.Nothing:eU.First});break;case eW.ArrowUp:e.preventDefault(),v.actions.openListbox({focus:c.value?eU.Nothing:eU.Last})}}),_=(0,p._)(e=>{e.key===eW.Space&&e.preventDefault()}),C=(0,p._)(e=>{var t;if(0===e.button){if(function(e){var t;let n=e.parentElement,r=null;for(;n&&!(V(t=n)&&"FIELDSET"===t.nodeName);)Z(n)&&(r=n),n=n.parentElement;let o=(null==n?void 0:n.getAttribute("disabled"))==="";return!(o&&function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(Z(t))return!1;t=t.previousElementSibling}return!0}(r))&&o}(e.currentTarget))return e.preventDefault();v.state.listboxState===e7.Open?((0,u.flushSync)(()=>v.actions.closeListbox()),null==(t=v.state.buttonElement)||t.focus({preventScroll:!0})):(e.preventDefault(),v.actions.openListbox({focus:eU.Nothing}))}}),A=(0,p._)(e=>e.preventDefault()),k=eK([m]),T=null!=(r=null==(n=(0,s.useContext)(eY))?void 0:n.value)?r:void 0,{isFocusVisible:R,focusProps:P}=(0,i.og)({autoFocus:g}),{isHovered:L,hoverProps:F}=(0,a.Mk)({isDisabled:h}),{pressed:N,pressProps:I}=function(){let{disabled:e=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,s.useRef)(null),[n,r]=(0,s.useState)(!1),o=(0,f.L)(),l=(0,p._)(()=>{t.current=null,r(!1),o.dispose()}),i=(0,p._)(e=>{if(o.dispose(),null===t.current){t.current=e.currentTarget,r(!0);{let n=d(e.currentTarget);o.addEventListener(n,"pointerup",l,!1),o.addEventListener(n,"pointermove",e=>{if(t.current){var n,o;let l,i;r((l=e.width/2,i=e.height/2,n={top:e.clientY-i,right:e.clientX+l,bottom:e.clientY+i,left:e.clientX-l},o=t.current.getBoundingClientRect(),!(!n||!o||n.right<o.left||n.left>o.right||n.bottom<o.top||n.top>o.bottom)))}},!1),o.addEventListener(n,"pointercancel",l,!1)}}});return{pressed:n,pressProps:e?{}:{onPointerDown:i,onPointerUp:l,onClick:l}}}({disabled:h}),M=(0,s.useMemo)(()=>({open:E===e7.Open,active:N||E===e7.Open,disabled:h,invalid:c.invalid,value:c.value,hover:L,focus:R,autofocus:g}),[E,c.value,h,L,R,N,c.invalid,g]),D=B(v,e=>e.listboxState===e7.Open),j=(0,eR.v6)(w(),{ref:y,id:m,type:(0,s.useMemo)(()=>{var t;if(e.type)return e.type;let n=null!=(t=e.as)?t:"button";if("string"==typeof n&&"button"===n.toLowerCase()||(null==S?void 0:S.tagName)==="BUTTON"&&!S.hasAttribute("type"))return"button"},[e.type,e.as,S]),"aria-haspopup":"listbox","aria-controls":null==x?void 0:x.id,"aria-expanded":D,"aria-labelledby":k,"aria-describedby":T,disabled:h||void 0,autoFocus:g,onKeyDown:O,onKeyUp:_,onKeyPress:A,onPointerDown:C},P,F,I);return(0,eR.Ci)()({ourProps:j,theirProps:b,slot:M,defaultTag:"button",name:"Listbox.Button"})}),Label:eX,Options:(0,eR.FX)(function(e,t){var n;let r,o=(0,s.useId)(),{id:l="headlessui-listbox-options-".concat(o),anchor:i,portal:a=!1,modal:c=!0,transition:v=!1,...g}=e,b=(0,s.useMemo)(()=>i?"string"==typeof i?{to:i}:i:null,[i]),[y,w]=(0,s.useState)(null);b&&(a=!0);let E=ta("Listbox.Options"),S=to("Listbox.Options"),[x,O,_,C]=B(S,e=>[e.listboxState,e.buttonElement,e.optionsElement,e.__demoMode]),A=ef(O),k=ef(_),T=(0,eB.O_)(),[P,L]=(0,ew.p)(v,y,null!==T?(T&eB.Uw.Open)===eB.Uw.Open:x===e7.Open);n=S.actions.closeListbox,r=(0,K.Y)(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&n()}),(0,s.useEffect)(()=>{if(!P)return;let e=null===O?null:V(O)?O:O.current;if(!e)return;let t=(0,h.e)();if("undefined"!=typeof ResizeObserver){let n=new ResizeObserver(()=>r.current(e));n.observe(e),t.add(()=>n.disconnect())}if("undefined"!=typeof IntersectionObserver){let n=new IntersectionObserver(()=>r.current(e));n.observe(e),t.add(()=>n.disconnect())}return()=>t.dispose()},[O,r,P]),function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>[document.body];!function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>({containers:[]}),r=(0,s.useSyncExternalStore)(em.subscribe,em.getSnapshot,em.getSnapshot),o=t?r.get(t):void 0;o&&o.count,(0,m.s)(()=>{if(!(!t||!e))return em.dispatch("PUSH",t,n),()=>em.dispatch("POP",t,n)},[e,t])}(H(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}(!C&&c&&x===e7.Open,k),function(e){let{allowed:t,disallowed:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=H(e,"inert-others");(0,m.s)(()=>{var e,o;if(!r)return;let l=(0,h.e)();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&l.add(z(t));let i=null!=(o=null==t?void 0:t())?o:[];for(let e of i){if(!e)continue;let t=d(e);if(!t)continue;let n=e.parentElement;for(;n&&n!==t.body;){for(let e of n.children)i.some(t=>e.contains(t))||l.add(z(e));n=n.parentElement}}return l.dispose},[r,t,n])}(!C&&c&&x===e7.Open,{allowed:(0,s.useCallback)(()=>[O,_],[O,_])});let F=!function(e,t){let n=(0,s.useRef)({left:0,top:0});if((0,m.s)(()=>{if(!t)return;let e=t.getBoundingClientRect();e&&(n.current=e)},[e,t]),null==t||!e||t===document.activeElement)return!1;let r=t.getBoundingClientRect();return r.top!==n.current.top||r.left!==n.current.left}(x!==e7.Open,O)&&P,N=function(e,t){let[n,r]=(0,s.useState)(t);return e||n===t||r(t),e?n:t}(P&&x===e7.Closed,E.value),I=(0,p._)(e=>E.compare(N,e)),M=B(S,e=>{var t;if(null==b||!(null!=(t=null==b?void 0:b.to)&&t.includes("selection")))return null;let n=e.options.findIndex(e=>I(e.dataRef.current.value));return -1===n&&(n=0),n}),[D,j]=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;!1===e&&(e=null),"string"==typeof e&&(e={to:e});let t=(0,s.useContext)(e_),n=(0,s.useMemo)(()=>e,[JSON.stringify(e,(e,t)=>{var n;return null!=(n=null==t?void 0:t.outerHTML)?n:t})]);(0,m.s)(()=>{null==t||t(null!=n?n:null)},[t,n]);let r=(0,s.useContext)(eO);return(0,s.useMemo)(()=>[r.setFloating,e?r.styles:{}],[r.setFloating,e,r.styles])}((()=>{if(null==b)return;if(null===M)return{...b,inner:void 0};let e=Array.from(E.listRef.current.values());return{...b,inner:{listRef:{current:e},index:M}}})()),U=function(){let{getFloatingProps:e,slot:t}=(0,s.useContext)(eO);return(0,s.useCallback)(function(){for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];return Object.assign({},e(...r),{"data-anchor":t.anchor})},[e,t])}(),Y=(0,eh.P)(t,b?D:null,S.actions.setOptionsElement,w),W=(0,f.L)();(0,s.useEffect)(()=>{var e;_&&x===e7.Open&&_!==(null==(e=d(_))?void 0:e.activeElement)&&(null==_||_.focus({preventScroll:!0}))},[x,_]);let X=(0,p._)(e=>{var t,n,r,o;switch(W.dispose(),e.key){case eW.Space:if(""!==S.state.searchQuery)return e.preventDefault(),e.stopPropagation(),S.actions.search(e.key);case eW.Enter:if(e.preventDefault(),e.stopPropagation(),null!==S.state.activeOptionIndex){let{dataRef:e}=S.state.options[S.state.activeOptionIndex];S.actions.onChange(e.current.value)}E.mode===e8.Single&&((0,u.flushSync)(()=>S.actions.closeListbox()),null==(t=S.state.buttonElement)||t.focus({preventScroll:!0}));break;case(0,R.Y)(E.orientation,{vertical:eW.ArrowDown,horizontal:eW.ArrowRight}):return e.preventDefault(),e.stopPropagation(),S.actions.goToOption({focus:eU.Next});case(0,R.Y)(E.orientation,{vertical:eW.ArrowUp,horizontal:eW.ArrowLeft}):return e.preventDefault(),e.stopPropagation(),S.actions.goToOption({focus:eU.Previous});case eW.Home:case eW.PageUp:return e.preventDefault(),e.stopPropagation(),S.actions.goToOption({focus:eU.First});case eW.End:case eW.PageDown:return e.preventDefault(),e.stopPropagation(),S.actions.goToOption({focus:eU.Last});case eW.Escape:e.preventDefault(),e.stopPropagation(),(0,u.flushSync)(()=>S.actions.closeListbox()),null==(n=S.state.buttonElement)||n.focus({preventScroll:!0});return;case eW.Tab:e.preventDefault(),e.stopPropagation(),(0,u.flushSync)(()=>S.actions.closeListbox()),r=S.state.buttonElement,o=e.shiftKey?et.Previous:et.Next,function(e,t){var n,r,o;let{sorted:l=!0,relativeTo:i=null,skipElements:a=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,u=Array.isArray(e)?l?es(e):e:64&t?function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(ee)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):eo(e);a.length>0&&u.length>1&&(u=u.filter(e=>!a.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),i=null!=i?i:s.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,u.indexOf(i))-1;if(4&t)return Math.max(0,u.indexOf(i))+1;if(8&t)return u.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},p=0,v=u.length,m;do{if(p>=v||p+v<=0)return 0;let e=d+p;if(16&t)e=(e+v)%v;else{if(e<0)return 3;if(e>=v)return 1}null==(m=u[e])||m.focus(f),p+=c}while(m!==s.activeElement);6&t&&null!=(o=null==(r=null==(n=m)?void 0:n.matches)?void 0:r.call(n,"textarea,input"))&&o&&m.select()}(eo(),o,{relativeTo:r});break;default:1===e.key.length&&(S.actions.search(e.key),W.setTimeout(()=>S.actions.clearSearch(),350))}}),q=B(S,e=>{var t;return null==(t=e.buttonElement)?void 0:t.id}),$=(0,s.useMemo)(()=>({open:x===e7.Open}),[x]),G=(0,eR.v6)(b?U():{},{id:l,ref:Y,"aria-activedescendant":B(S,S.selectors.activeDescendantId),"aria-multiselectable":E.mode===e8.Multi||void 0,"aria-labelledby":q,"aria-orientation":E.orientation,onKeyDown:X,role:"listbox",tabIndex:x===e7.Open?0:void 0,style:{...g.style,...j,"--button-width":function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],[n,r]=(0,s.useReducer)(()=>({}),{}),o=(0,s.useMemo)(()=>(function(e){if(null===e)return{width:0,height:0};let{width:t,height:n}=e.getBoundingClientRect();return{width:t,height:n}})(e),[e,n]);return(0,m.s)(()=>{if(!e)return;let t=new ResizeObserver(r);return t.observe(e),()=>{t.disconnect()}},[e]),t?{width:"".concat(o.width,"px"),height:"".concat(o.height,"px")}:o}(O,!0).width},...(0,ew.B)(L)}),Q=(0,eR.Ci)(),Z=(0,s.useMemo)(()=>E.mode===e8.Multi?E:{...E,isSelected:I},[E,I]);return s.createElement(e2,{enabled:!!a&&(e.static||P),ownerDocument:A},s.createElement(ti.Provider,{value:Z},Q({ourProps:G,theirProps:g,slot:$,defaultTag:"div",features:tc,visible:F,name:"Listbox.Options"})))}),Option:(0,eR.FX)(function(e,t){let n,r,o,l=(0,s.useId)(),{id:i="headlessui-listbox-option-".concat(l),disabled:a=!1,value:c,...d}=e,f=!0===(0,s.useContext)(tu),v=ta("Listbox.Option"),g=to("Listbox.Option"),b=B(g,e=>g.selectors.isActive(e,i)),y=v.isSelected(c),w=(0,s.useRef)(null),E=(n=(0,s.useRef)(""),r=(0,s.useRef)(""),(0,p._)(()=>{let e=w.current;if(!e)return"";let t=e.innerText;if(n.current===t)return r.current;let o=(function(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let n=e.getAttribute("aria-labelledby");if(n){let e=n.split(" ").map(e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():eb(t).trim()}return null}).filter(Boolean);if(e.length>0)return e.join(", ")}return eb(e).trim()})(e).trim().toLowerCase();return n.current=t,r.current=o,o})),S=(0,K.Y)({disabled:a,value:c,domRef:w,get textValue(){return E()}}),x=(0,eh.P)(t,w,e=>{e?v.listRef.current.set(i,e):v.listRef.current.delete(i)}),O=B(g,e=>g.selectors.shouldScrollIntoView(e,i));(0,m.s)(()=>{if(O)return(0,h.e)().requestAnimationFrame(()=>{var e,t;null==(t=null==(e=w.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})},[O,w]),(0,m.s)(()=>{if(!f)return g.actions.registerOption(i,S),()=>g.actions.unregisterOption(i)},[S,i,f]);let _=(0,p._)(e=>{var t;if(a)return e.preventDefault();g.actions.onChange(c),v.mode===e8.Single&&((0,u.flushSync)(()=>g.actions.closeListbox()),null==(t=g.state.buttonElement)||t.focus({preventScroll:!0}))}),C=(0,p._)(()=>{if(a)return g.actions.goToOption({focus:eU.Nothing});g.actions.goToOption({focus:eU.Specific,id:i})}),A=(o=(0,s.useRef)([-1,-1]),{wasMoved(e){let t=ey(e);return(o.current[0]!==t[0]||o.current[1]!==t[1])&&(o.current=t,!0)},update(e){o.current=ey(e)}}),k=(0,p._)(e=>{A.update(e),!a&&(b||g.actions.goToOption({focus:eU.Specific,id:i},e6.Pointer))}),T=(0,p._)(e=>{A.wasMoved(e)&&(a||b||g.actions.goToOption({focus:eU.Specific,id:i},e6.Pointer))}),R=(0,p._)(e=>{A.wasMoved(e)&&(a||b&&g.actions.goToOption({focus:eU.Nothing}))}),P=(0,s.useMemo)(()=>({active:b,focus:b,selected:y,disabled:a,selectedOption:y&&f}),[b,y,a,f]),L=f?{}:{id:i,ref:x,role:"option",tabIndex:!0===a?void 0:-1,"aria-disabled":!0===a||void 0,"aria-selected":y,disabled:void 0,onClick:_,onFocus:C,onPointerEnter:k,onMouseEnter:k,onPointerMove:T,onMouseMove:T,onPointerLeave:R,onMouseLeave:R},F=(0,eR.Ci)();return!y&&f?null:F({ourProps:L,theirProps:d,slot:P,defaultTag:"div",name:"Listbox.Option"})}),SelectedOption:(0,eR.FX)(function(e,t){let{options:n,placeholder:r,...o}=e,l={ref:(0,eh.P)(t)},i=ta("ListboxSelectedOption"),a=(0,s.useMemo)(()=>({}),[]),u=void 0===i.value||null===i.value||i.mode===e8.Multi&&Array.isArray(i.value)&&0===i.value.length,c=(0,eR.Ci)();return s.createElement(tu.Provider,{value:!0},c({ourProps:l,theirProps:{...o,children:s.createElement(s.Fragment,null,r&&u?r:n)},slot:a,defaultTag:td,name:"ListboxSelectedOption"}))})})},84554:(e,t,n)=>{n.d(t,{Ac:()=>i,Ci:()=>s,FX:()=>p,mK:()=>a,oE:()=>v,v6:()=>f});var r=n(12115),o=n(20379),l=n(27279),i=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(i||{}),a=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(a||{});function s(){let e,t,n=(e=(0,r.useRef)([]),t=(0,r.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]),function(){for(var n=arguments.length,r=Array(n),o=0;o<n;o++)r[o]=arguments[o];if(!r.every(e=>null==e))return e.current=r,t});return(0,r.useCallback)(e=>(function(e){let{ourProps:t,theirProps:n,slot:r,defaultTag:o,features:i,visible:a=!0,name:s,mergeRefs:f}=e;f=null!=f?f:c;let p=d(n,t);if(a)return u(p,r,o,s,f);let v=null!=i?i:0;if(2&v){let{static:e=!1,...t}=p;if(e)return u(t,r,o,s,f)}if(1&v){let{unmount:e=!0,...t}=p;return(0,l.Y)(+!e,{0:()=>null,1:()=>u({...t,hidden:!0,style:{display:"none"}},r,o,s,f)})}return u(p,r,o,s,f)})({mergeRefs:n,...e}),[n])}function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,l=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0,{as:a=n,children:s,refName:u="ref",...c}=m(e,["unmount","static"]),f=void 0!==e.ref?{[u]:e.ref}:{},p="function"==typeof s?s(t):s;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t)),c["aria-labelledby"]&&c["aria-labelledby"]===c.id&&(c["aria-labelledby"]=void 0);let h={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())));if(e)for(let e of(h["data-headlessui-state"]=n.join(" "),n))h["data-".concat(e)]=""}if(a===r.Fragment&&(Object.keys(v(c)).length>0||Object.keys(v(h)).length>0))if(!(0,r.isValidElement)(p)||Array.isArray(p)&&p.length>1){if(Object.keys(v(c)).length>0)throw Error(['Passing props on "Fragment"!',"","The current component <".concat(l,' /> is rendering a "Fragment".'),"However we need to passthrough the following props:",Object.keys(v(c)).concat(Object.keys(v(h))).map(e=>"  - ".concat(e)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>"  - ".concat(e)).join("\n")].join("\n"))}else{var g;let e=p.props,t=null==e?void 0:e.className,n="function"==typeof t?function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0,o.x)(t(...n),c.className)}:(0,o.x)(t,c.className),l=d(p.props,v(m(c,["ref"])));for(let e in h)e in l&&delete h[e];return(0,r.cloneElement)(p,Object.assign({},l,h,f,{ref:i((g=p,r.version.split(".")[0]>="19"?g.props.ref:g.ref),f.ref)},n?{className:n}:{}))}return(0,r.createElement)(a,Object.assign({},m(c,["ref"]),a!==r.Fragment&&f,a!==r.Fragment&&h),p)}function c(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.every(e=>null==e)?void 0:e=>{for(let n of t)null!=n&&("function"==typeof n?n(e):n.current=e)}}function d(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},o={};for(let e of t)for(let t in e)t.startsWith("on")&&"function"==typeof e[t]?(null!=o[t]||(o[t]=[]),o[t].push(e[t])):r[t]=e[t];if(r.disabled||r["aria-disabled"])for(let e in o)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(o[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in o)Object.assign(r,{[e](t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];for(let n of o[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;n(t,...r)}}});return r}function f(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},o={};for(let e of t)for(let t in e)t.startsWith("on")&&"function"==typeof e[t]?(null!=o[t]||(o[t]=[]),o[t].push(e[t])):r[t]=e[t];for(let e in o)Object.assign(r,{[e](){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];for(let t of o[e])null==t||t(...n)}});return r}function p(e){var t;return Object.assign((0,r.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function v(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}},87657:(e,t,n)=>{n.d(t,{_:()=>a});var r=Object.defineProperty,o=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,l=(e,t,n)=>(o(e,"symbol"!=typeof t?t+"":t,n),n);class i{set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}constructor(){l(this,"current",this.detect()),l(this,"handoffState","pending"),l(this,"currentId",0)}}let a=new i},88048:(e,t,n)=>{n.d(t,{B:()=>d,p:()=>f});var r,o,l=n(12115),i=n(45261),a=n(48014),s=n(21231),u=n(87358);void 0!==u&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(r=null==u?void 0:u.env)?void 0:r.NODE_ENV)==="test"&&void 0===(null==(o=null==Element?void 0:Element.prototype)?void 0:o.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn("Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.\nPlease install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.\n\nExample usage:\n```js\nimport { mockAnimationsApi } from 'jsdom-testing-mocks'\nmockAnimationsApi()\n```"),[]});var c=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(c||{});function d(e){let t={};for(let n in e)!0===e[n]&&(t["data-".concat(n)]="");return t}function f(e,t,n,r){let[o,u]=(0,l.useState)(n),{hasFlag:c,addFlag:d,removeFlag:f}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,[t,n]=(0,l.useState)(e),r=(0,l.useCallback)(e=>n(e),[t]),o=(0,l.useCallback)(e=>n(t=>t|e),[t]),i=(0,l.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:r,addFlag:o,hasFlag:i,removeFlag:(0,l.useCallback)(e=>n(t=>t&~e),[n]),toggleFlag:(0,l.useCallback)(e=>n(t=>t^e),[n])}}(e&&o?3:0),p=(0,l.useRef)(!1),v=(0,l.useRef)(!1),m=(0,a.L)();return(0,s.s)(()=>{var o;if(e){if(n&&u(!0),!t){n&&d(3);return}return null==(o=null==r?void 0:r.start)||o.call(r,n),function(e,t){let{prepare:n,run:r,done:o,inFlight:l}=t,a=(0,i.e)();return function(e,t){let{inFlight:n,prepare:r}=t;if(null!=n&&n.current)return r();let o=e.style.transition;e.style.transition="none",r(),e.offsetHeight,e.style.transition=o}(e,{prepare:n,inFlight:l}),a.nextFrame(()=>{r(),a.requestAnimationFrame(()=>{a.add(function(e,t){var n,r;let o=(0,i.e)();if(!e)return o.dispose;let l=!1;o.add(()=>{l=!0});let a=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===a.length?t():Promise.allSettled(a.map(e=>e.finished)).then(()=>{l||t()}),o.dispose}(e,o))})}),a.dispose}(t,{inFlight:p,prepare(){v.current?v.current=!1:v.current=p.current,p.current=!0,v.current||(n?(d(3),f(4)):(d(4),f(2)))},run(){v.current?n?(f(3),d(4)):(f(4),d(3)):n?f(1):d(1)},done(){var e;v.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(p.current=!1,f(7),n||u(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})}},[e,n,t,m]),e?[o,{closed:c(1),enter:c(2),leave:c(4),transition:c(2)||c(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}},89925:(e,t,n)=>{n.d(t,{g:()=>i});var r,o=n(12115),l=n(87657);function i(){let e,t=(e="undefined"==typeof document,(0,(r||(r=n.t(o,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[i,a]=o.useState(l._.isHandoffComplete);return i&&!1===l._.isHandoffComplete&&a(!1),o.useEffect(()=>{!0!==i&&a(!0)},[i]),o.useEffect(()=>l._.handoff(),[]),!t&&i}},91525:(e,t,n)=>{n.d(t,{El:()=>a,O_:()=>i,Uw:()=>l});var r=n(12115);let o=(0,r.createContext)(null);o.displayName="OpenClosedContext";var l=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(l||{});function i(){return(0,r.useContext)(o)}function a(e){let{value:t,children:n}=e;return r.createElement(o.Provider,{value:t},n)}},91659:(e,t,n)=>{n.d(t,{e:()=>C});var r=n(12115),o=n(48014),l=n(30797),i=n(21231),a=n(6232),s=n(89925),u=n(47769),c=n(88048),d=n(91525),f=n(20379),p=n(27279),v=n(84554);function m(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:E)!==r.Fragment||1===r.Children.count(e.children)}let h=(0,r.createContext)(null);h.displayName="TransitionContext";var g=(e=>(e.Visible="visible",e.Hidden="hidden",e))(g||{});let b=(0,r.createContext)(null);function y(e){return"children"in e?y(e.children):e.current.filter(e=>{let{el:t}=e;return null!==t.current}).filter(e=>{let{state:t}=e;return"visible"===t}).length>0}function w(e,t){let n,s=(0,a.Y)(e),u=(0,r.useRef)([]),c=(n=(0,r.useRef)(!1),(0,i.s)(()=>(n.current=!0,()=>{n.current=!1}),[]),n),d=(0,o.L)(),f=(0,l._)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:v.mK.Hidden,n=u.current.findIndex(t=>{let{el:n}=t;return n===e});-1!==n&&((0,p.Y)(t,{[v.mK.Unmount](){u.current.splice(n,1)},[v.mK.Hidden](){u.current[n].state="hidden"}}),d.microTask(()=>{var e;!y(u)&&c.current&&(null==(e=s.current)||e.call(s))}))}),m=(0,l._)(e=>{let t=u.current.find(t=>{let{el:n}=t;return n===e});return t?"visible"!==t.state&&(t.state="visible"):u.current.push({el:e,state:"visible"}),()=>f(e,v.mK.Unmount)}),h=(0,r.useRef)([]),g=(0,r.useRef)(Promise.resolve()),b=(0,r.useRef)({enter:[],leave:[]}),w=(0,l._)((e,n,r)=>{h.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(t=>{let[n]=t;return n!==e})),null==t||t.chains.current[n].push([e,new Promise(e=>{h.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(b.current[n].map(e=>{let[t,n]=e;return n})).then(()=>e())})]),"enter"===n?g.current=g.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),E=(0,l._)((e,t,n)=>{Promise.all(b.current[t].splice(0).map(e=>{let[t,n]=e;return n})).then(()=>{var e;null==(e=h.current.shift())||e()}).then(()=>n(t))});return(0,r.useMemo)(()=>({children:u,register:m,unregister:f,onStart:w,onStop:E,wait:g,chains:b}),[m,f,u,w,E,b,g])}b.displayName="NestingContext";let E=r.Fragment,S=v.Ac.RenderStrategy,x=(0,v.FX)(function(e,t){let{show:n,appear:o=!1,unmount:a=!0,...c}=e,f=(0,r.useRef)(null),p=m(e),g=(0,u.P)(...p?[f,t]:null===t?[]:[t]);(0,s.g)();let E=(0,d.O_)();if(void 0===n&&null!==E&&(n=(E&d.Uw.Open)===d.Uw.Open),void 0===n)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[x,_]=(0,r.useState)(n?"visible":"hidden"),C=w(()=>{n||_("hidden")}),[A,k]=(0,r.useState)(!0),T=(0,r.useRef)([n]);(0,i.s)(()=>{!1!==A&&T.current[T.current.length-1]!==n&&(T.current.push(n),k(!1))},[T,n]);let R=(0,r.useMemo)(()=>({show:n,appear:o,initial:A}),[n,o,A]);(0,i.s)(()=>{n?_("visible"):y(C)||null===f.current||_("hidden")},[n,C]);let P={unmount:a},L=(0,l._)(()=>{var t;A&&k(!1),null==(t=e.beforeEnter)||t.call(e)}),F=(0,l._)(()=>{var t;A&&k(!1),null==(t=e.beforeLeave)||t.call(e)}),N=(0,v.Ci)();return r.createElement(b.Provider,{value:C},r.createElement(h.Provider,{value:R},N({ourProps:{...P,as:r.Fragment,children:r.createElement(O,{ref:g,...P,...c,beforeEnter:L,beforeLeave:F})},theirProps:{},defaultTag:r.Fragment,features:S,visible:"visible"===x,name:"Transition"})))}),O=(0,v.FX)(function(e,t){var n,o;let{transition:a=!0,beforeEnter:g,afterEnter:x,beforeLeave:O,afterLeave:_,enter:C,enterFrom:A,enterTo:k,entered:T,leave:R,leaveFrom:P,leaveTo:L,...F}=e,[N,I]=(0,r.useState)(null),M=(0,r.useRef)(null),D=m(e),j=(0,u.P)(...D?[M,t,I]:null===t?[]:[t]),B=null==(n=F.unmount)||n?v.mK.Unmount:v.mK.Hidden,{show:U,appear:H,initial:Y}=function(){let e=(0,r.useContext)(h);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[W,z]=(0,r.useState)(U?"visible":"hidden"),K=function(){let e=(0,r.useContext)(b);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:X,unregister:q}=K;(0,i.s)(()=>X(M),[X,M]),(0,i.s)(()=>{if(B===v.mK.Hidden&&M.current)return U&&"visible"!==W?void z("visible"):(0,p.Y)(W,{hidden:()=>q(M),visible:()=>X(M)})},[W,M,X,q,U,B]);let V=(0,s.g)();(0,i.s)(()=>{if(D&&V&&"visible"===W&&null===M.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[M,W,V,D]);let $=Y&&!H,G=H&&U&&Y,Q=(0,r.useRef)(!1),Z=w(()=>{Q.current||(z("hidden"),q(M))},K),J=(0,l._)(e=>{Q.current=!0,Z.onStart(M,e?"enter":"leave",e=>{"enter"===e?null==g||g():"leave"===e&&(null==O||O())})}),ee=(0,l._)(e=>{let t=e?"enter":"leave";Q.current=!1,Z.onStop(M,t,e=>{"enter"===e?null==x||x():"leave"===e&&(null==_||_())}),"leave"!==t||y(Z)||(z("hidden"),q(M))});(0,r.useEffect)(()=>{D&&a||(J(U),ee(U))},[U,D,a]);let et=!(!a||!D||!V||$),[,en]=(0,c.p)(et,N,U,{start:J,end:ee}),er=(0,v.oE)({ref:j,className:(null==(o=(0,f.x)(F.className,G&&C,G&&A,en.enter&&C,en.enter&&en.closed&&A,en.enter&&!en.closed&&k,en.leave&&R,en.leave&&!en.closed&&P,en.leave&&en.closed&&L,!en.transition&&U&&T))?void 0:o.trim())||void 0,...(0,c.B)(en)}),eo=0;"visible"===W&&(eo|=d.Uw.Open),"hidden"===W&&(eo|=d.Uw.Closed),U&&"hidden"===W&&(eo|=d.Uw.Opening),U||"visible"!==W||(eo|=d.Uw.Closing);let el=(0,v.Ci)();return r.createElement(b.Provider,{value:Z},r.createElement(d.El,{value:eo},el({ourProps:er,theirProps:F,defaultTag:E,features:S,visible:"visible"===W,name:"Transition.Child"})))}),_=(0,v.FX)(function(e,t){let n=null!==(0,r.useContext)(h),o=null!==(0,d.O_)();return r.createElement(r.Fragment,null,!n&&o?r.createElement(x,{ref:t,...e}):r.createElement(O,{ref:t,...e}))}),C=Object.assign(x,{Child:_,Root:x})}}]);