(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3077],{45667:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(95155),l=a(12115),r=a(35695),n=a(61772);a(66419);var o=a(55764),c=a(25052),i=a(99695),d=a(55628),x=a(10184),h=a(61316),m=a(63782);function b(e){let{params:s}=e,a=(0,r.useParams)(),b=(0,r.useRouter)(),u=null==a?void 0:a.shareToken,[f,p]=(0,l.useState)(null),[g,w]=(0,l.useState)(null),[j,y,N]=(0,n.ck)([]),[v,k,E]=(0,n.fM)([]),[C,S]=(0,l.useState)(!0),[_,A]=(0,l.useState)(null),[O,L]=(0,l.useState)(null);(0,l.useEffect)(()=>{u&&R()},[u]);let R=async()=>{try{let e=await fetch("/api/manual-build/shared/".concat(u));if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to load shared workflow")}let s=await e.json();p(s.workflow),w(s.permissions),L(s.share),y(s.workflow.nodes||[]),k(s.workflow.edges||[])}catch(e){A(e instanceof Error?e.message:"Failed to load workflow")}finally{S(!1)}},D=async()=>{if(f&&(null==g?void 0:g.canExport))try{let e=await fetch("/api/manual-build/shared/".concat(u,"/copy"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:"".concat(f.name," (Copy)")})});if(e.ok){let s=await e.json();b.push("/manual-build/".concat(s.workflow.id))}}catch(e){}},P=async()=>{if(f&&(null==g?void 0:g.canExport))try{let e=await fetch("/api/manual-build/shared/".concat(u,"/export"));if(e.ok){let s=await e.blob(),a=window.URL.createObjectURL(s),t=document.createElement("a");t.style.display="none",t.href=a,t.download="".concat(f.name,".json"),document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(a),document.body.removeChild(t)}}catch(e){}};return C?(0,t.jsx)("div",{className:"h-screen bg-[#040716] flex items-center justify-center",children:(0,t.jsx)("div",{className:"text-white",children:"Loading shared workflow..."})}):_?(0,t.jsx)("div",{className:"h-screen bg-[#040716] flex items-center justify-center",children:(0,t.jsxs)("div",{className:"max-w-md w-full text-center",children:[(0,t.jsx)(d.A,{className:"w-16 h-16 text-red-400 mx-auto mb-4"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-white mb-2",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-400 mb-6",children:_}),(0,t.jsx)("button",{onClick:()=>b.push("/manual-build"),className:"px-6 py-3 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors",children:"Go to Manual Build"})]})}):f&&g?(0,t.jsxs)("div",{className:"h-screen bg-[#040716] flex flex-col",children:[(0,t.jsx)("div",{className:"bg-gray-900/50 border-b border-gray-800 backdrop-blur-sm",children:(0,t.jsxs)("div",{className:"flex items-center justify-between px-6 py-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsx)(m.A,{className:"w-6 h-6 text-[#ff6b35]"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-white",children:f.name}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-400",children:[(0,t.jsx)("span",{children:"Shared workflow"}),(null==O?void 0:O.is_public)&&(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(x.A,{className:"w-4 h-4"}),"Public"]}),(null==O?void 0:O.expires_at)&&(0,t.jsxs)("span",{children:["Expires ",new Date(O.expires_at).toLocaleDateString()]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[g.canExport&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("button",{onClick:P,className:"flex items-center gap-2 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:[(0,t.jsx)(c.A,{className:"w-4 h-4"}),"Export"]}),(0,t.jsxs)("button",{onClick:D,className:"flex items-center gap-2 px-4 py-2 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors",children:[(0,t.jsx)(i.A,{className:"w-4 h-4"}),"Copy to My Workflows"]})]}),g.canEdit&&(0,t.jsxs)("button",{onClick:()=>b.push("/manual-build/".concat(f.id)),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-500 transition-colors",children:[(0,t.jsx)(h.A,{className:"w-4 h-4"}),"Edit"]})]})]})}),(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsxs)(n.Gc,{nodes:j,edges:v,onNodesChange:g.canEdit?N:void 0,onEdgesChange:g.canEdit?E:void 0,nodeTypes:o.c_,fitView:!0,className:"bg-[#040716]",nodesDraggable:g.canEdit,nodesConnectable:g.canEdit,elementsSelectable:g.canEdit,proOptions:{hideAttribution:!0},children:[(0,t.jsx)(n.VS,{color:"#1f2937",gap:20,size:1}),(0,t.jsx)(n.H2,{className:"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm",showInteractive:!1}),(0,t.jsx)(n.of,{className:"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm",nodeColor:"#ff6b35",maskColor:"rgba(0, 0, 0, 0.2)"})]}),!g.canEdit&&(0,t.jsx)("div",{className:"absolute top-4 left-4 bg-yellow-900/20 border border-yellow-700/50 rounded-lg px-4 py-2",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-yellow-300",children:[(0,t.jsx)(x.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"text-sm",children:"Read-only view"})]})})]}),f.description&&(0,t.jsx)("div",{className:"bg-gray-900/50 border-t border-gray-800 px-6 py-4",children:(0,t.jsx)("p",{className:"text-gray-300 text-sm",children:f.description})})]}):(0,t.jsx)("div",{className:"h-screen bg-[#040716] flex items-center justify-center",children:(0,t.jsx)("div",{className:"text-white",children:"Workflow not found"})})}},77211:(e,s,a)=>{Promise.resolve().then(a.bind(a,45667))}},e=>{var s=s=>e(e.s=s);e.O(0,[8946,5738,2396,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(77211)),_N_E=e.O()}]);