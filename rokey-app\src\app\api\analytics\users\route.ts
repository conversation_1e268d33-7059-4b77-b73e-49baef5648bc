import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get user profile data
    const { data: userProfile } = await supabase
      .from('user_profiles')
      .select('subscription_tier, subscription_status, created_at')
      .eq('id', user.id)
      .single();

    // Get total active users (users with API activity in the period)
    const { data: activeUsersData } = await supabase
      .from('request_logs')
      .select('user_id')
      .eq('user_id', user.id)
      .gte('request_timestamp', startDate.toISOString())
      .limit(1);

    const activeUsers = activeUsersData && activeUsersData.length > 0 ? 1 : 0;

    // Get user's API keys count
    const { data: apiKeysData, count: apiKeysCount } = await supabase
      .from('user_generated_api_keys')
      .select('id', { count: 'exact' })
      .eq('user_id', user.id);

    // Get user's custom API configs count
    const { data: configsData, count: configsCount } = await supabase
      .from('custom_api_configs')
      .select('id', { count: 'exact' })
      .eq('user_id', user.id);

    // Get user activity over time (requests per day)
    const { data: activityData } = await supabase
      .from('request_logs')
      .select('request_timestamp')
      .eq('user_id', user.id)
      .gte('request_timestamp', startDate.toISOString())
      .order('request_timestamp', { ascending: true });

    // Process activity data into daily counts
    const dailyActivity: Record<string, number> = {};
    if (activityData) {
      activityData.forEach((log: any) => {
        const date = new Date(log.request_timestamp).toISOString().split('T')[0];
        dailyActivity[date] = (dailyActivity[date] || 0) + 1;
      });
    }

    // Convert to time series format
    const timeSeriesData: Array<{
      date: string;
      requests: number;
    }> = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      timeSeriesData.push({
        date: dateStr,
        requests: dailyActivity[dateStr] || 0
      });
    }

    // Get user's total requests
    const { data: totalRequestsData, count: totalRequests } = await supabase
      .from('request_logs')
      .select('id', { count: 'exact' })
      .eq('user_id', user.id)
      .gte('request_timestamp', startDate.toISOString());

    // Get user's successful requests
    const { data: successfulRequestsData, count: successfulRequests } = await supabase
      .from('request_logs')
      .select('id', { count: 'exact' })
      .eq('user_id', user.id)
      .gte('request_timestamp', startDate.toISOString())
      .gte('status_code', 200)
      .lt('status_code', 300);

    // Calculate success rate
    const totalRequestsCount = totalRequests || 0;
    const successfulRequestsCount = successfulRequests || 0;
    const successRate = totalRequestsCount > 0 ? (successfulRequestsCount / totalRequestsCount) * 100 : 0;

    // Get subscription tier breakdown (for this user)
    const subscriptionBreakdown = {
      free: userProfile?.subscription_tier === 'free' ? 1 : 0,
      starter: userProfile?.subscription_tier === 'starter' ? 1 : 0,
      professional: userProfile?.subscription_tier === 'professional' ? 1 : 0,
      enterprise: userProfile?.subscription_tier === 'enterprise' ? 1 : 0
    };

    // Get user's most used models
    const { data: modelUsageData } = await supabase
      .from('request_logs')
      .select('llm_model_name')
      .eq('user_id', user.id)
      .gte('request_timestamp', startDate.toISOString())
      .not('llm_model_name', 'is', null);

    const modelUsage: Record<string, number> = {};
    if (modelUsageData) {
      modelUsageData.forEach((log: any) => {
        const model = log.llm_model_name;
        modelUsage[model] = (modelUsage[model] || 0) + 1;
      });
    }

    const topModels = Object.entries(modelUsage)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 5)
      .map(([model, count]) => ({ model, count }));

    return NextResponse.json({
      success: true,
      data: {
        // Summary metrics
        activeUsers,
        totalApiKeys: apiKeysCount || 0,
        totalConfigs: configsCount || 0,
        totalRequests: totalRequests || 0,
        successfulRequests: successfulRequests || 0,
        successRate,
        
        // User profile info
        userProfile: {
          tier: userProfile?.subscription_tier || 'free',
          status: userProfile?.subscription_status || 'active',
          createdAt: userProfile?.created_at
        },
        
        // Subscription breakdown
        subscriptionBreakdown,
        
        // Activity data
        timeSeriesData,
        
        // Model usage
        topModels,
        
        // Period info
        period: `${days} days`,
        startDate: startDate.toISOString(),
        endDate: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in users analytics:', error);
    return NextResponse.json({
      error: 'Failed to fetch users analytics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
