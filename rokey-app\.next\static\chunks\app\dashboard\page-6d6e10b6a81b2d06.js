(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{23079:(e,s,a)=>{Promise.resolve().then(a.bind(a,66521))},66521:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>p});var t=a(95155),r=a(12115),l=a(35695),n=a(27572),i=a(14615),c=a(5500),d=a(6865),o=a(82771),m=a(58397),u=a(28960),h=a(92975),x=a(55628),g=a(57765),y=a(83298);function p(){var e,s,a;let p=(0,l.useRouter)(),{user:b}=(0,y.R)(),[v,j]=(0,r.useState)(null),[w,N]=(0,r.useState)(!1),[f,k]=(0,r.useState)(!0),[_,A]=(0,r.useState)(null),[C,D]=(0,r.useState)([]),[S,T]=(0,r.useState)([{name:"API Gateway",status:"operational"},{name:"Routing Engine",status:"operational"},{name:"Analytics",status:"degraded"}]);(0,r.useEffect)(()=>{(async()=>{await new Promise(e=>setTimeout(e,50));let e=[L(),R(),F()];await Promise.allSettled(e),k(!1)})();let e=setInterval(R,3e4),s=setInterval(F,6e4);return()=>{clearInterval(e),clearInterval(s)}},[]);let L=(0,r.useCallback)(async()=>{try{f&&N(!0);let e=new Date;e.setDate(e.getDate()-30);let s=await fetch("/api/analytics/summary?startDate=".concat(e.toISOString(),"&groupBy=day"));if(!s.ok)throw Error("Failed to fetch analytics data");let a=await s.json();j(a)}catch(e){A(e.message)}finally{f&&N(!1)}},[f]),E=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:6}).format(e),I=e=>new Intl.NumberFormat("en-US").format(e),R=async()=>{try{let e=await fetch("/api/activity?limit=10");if(!e.ok)throw Error("Failed to fetch recent activity");let s=(await e.json()).activities.map(e=>({id:e.id,action:e.action,model:e.model,time:e.time,status:e.status,details:e.details}));D(s)}catch(e){D([{id:"1",action:"System initialized",model:"RoKey",time:"Just now",status:"info"}])}},F=async()=>{try{let e=await fetch("/api/system-status");if(!e.ok)throw Error("Failed to fetch system status");let s=(await e.json()).checks.map(e=>({name:e.name,status:e.status,lastChecked:new Date(e.lastChecked).toLocaleTimeString()}));T(s)}catch(e){T([{name:"API Gateway",status:"down",lastChecked:new Date().toLocaleTimeString()},{name:"Routing Engine",status:"down",lastChecked:new Date().toLocaleTimeString()},{name:"Analytics",status:"down",lastChecked:new Date().toLocaleTimeString()}])}},P=(null==b||null==(e=b.user_metadata)?void 0:e.first_name)||(null==b||null==(a=b.user_metadata)||null==(s=a.full_name)?void 0:s.split(" ")[0])||"there",q=v?[{name:"Total Requests",value:I(v.summary.total_requests),change:"Last 30 days",changeType:"neutral",icon:c.A},{name:"Total Cost",value:E(v.summary.total_cost),change:"".concat(E(v.summary.average_cost_per_request)," avg/request"),changeType:"neutral",icon:u.A},{name:"Success Rate",value:"".concat(v.summary.success_rate.toFixed(1),"%"),change:"".concat(I(v.summary.successful_requests)," successful"),changeType:v.summary.success_rate>=95?"positive":"negative",icon:d.A},{name:"Total Tokens",value:I(v.summary.total_tokens),change:"".concat(I(v.summary.total_input_tokens)," in, ").concat(I(v.summary.total_output_tokens)," out"),changeType:"neutral",icon:m.A}]:[];return w&&f?(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"card p-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-3/4 mb-2"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3"})]},s))})]}):_?(0,t.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:(0,t.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"animate-slide-in",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold mb-2",children:(0,t.jsxs)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400",children:["Welcome ",P,"! \uD83D\uDC4B"]})}),(0,t.jsx)("p",{className:"text-gray-400 text-lg",children:"Here's what's happening with your LLM infrastructure today."})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 text-center mt-8",children:[(0,t.jsxs)("p",{className:"text-red-400 mb-4",children:["Error loading analytics data: ",_]}),(0,t.jsx)("button",{onClick:L,className:"btn-primary",children:"Retry"})]})]})}):(0,t.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:(0,t.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8",children:[(0,t.jsxs)("div",{className:"animate-slide-in",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold mb-2",children:(0,t.jsxs)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400",children:["Welcome ",P,"! \uD83D\uDC4B"]})}),(0,t.jsx)("p",{className:"text-gray-400 text-lg",children:"Here's what's happening with your LLM infrastructure today."})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-slide-in",children:q.map((e,s)=>(0,t.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200",style:{animationDelay:"".concat(100*s,"ms")},children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-400 mb-1",children:e.name}),(0,t.jsx)("p",{className:"text-3xl font-bold text-white mt-2",children:e.value}),(0,t.jsxs)("p",{className:"text-sm mt-2 flex items-center ".concat("positive"===e.changeType?"text-green-400":"negative"===e.changeType?"text-red-400":"text-gray-500"),children:["neutral"!==e.changeType&&(0,t.jsx)(n.A,{className:"h-4 w-4 mr-1 ".concat("negative"===e.changeType?"rotate-180":"")}),e.change]})]}),(0,t.jsx)("div",{className:"text-gray-500",children:(0,t.jsx)(e.icon,{className:"h-6 w-6"})})]})},e.name))}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 animate-slide-in",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"Quick Actions"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("button",{onClick:()=>{p.push("/my-models")},className:"btn-primary w-full justify-center hover:scale-105 transition-transform duration-200",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 mr-3"}),"Add New Model"]}),(0,t.jsxs)("button",{onClick:()=>{p.push("/playground")},className:"btn-secondary-dark w-full justify-center hover:scale-105 transition-transform duration-200",children:[(0,t.jsx)(i.A,{className:"h-5 w-5 mr-3"}),"Test in Playground"]}),(0,t.jsxs)("button",{onClick:()=>{p.push("/logs")},className:"btn-secondary-dark w-full justify-center hover:scale-105 transition-transform duration-200",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 mr-3"}),"View Logs"]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 animate-slide-in",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"System Status"}),(0,t.jsx)("div",{className:"space-y-4",children:S.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full mr-3 ".concat("operational"===e.status?"bg-green-500":"degraded"===e.status?"bg-yellow-500":"bg-red-500")}),(0,t.jsx)("span",{className:"text-gray-300",children:e.name})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("span",{className:"text-sm font-medium ".concat("operational"===e.status?"text-green-400":"degraded"===e.status?"text-yellow-400":"text-red-400"),children:"operational"===e.status?"Operational":"degraded"===e.status?"Degraded":"Down"}),e.lastChecked&&(0,t.jsx)("p",{className:"text-xs text-gray-500",children:e.lastChecked})]})]},e.name))})]})]}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 animate-slide-in",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Recent Activity"}),(0,t.jsx)("button",{onClick:R,className:"text-orange-400 hover:text-orange-300 text-sm font-medium",children:"Refresh"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[0===C.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(o.A,{className:"h-12 w-12 text-gray-500 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-400",children:"No recent activity"}),(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"Activity will appear here as you use the API"})]}):C.slice(-4).map(e=>(0,t.jsxs)("div",{className:"flex items-start p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-colors duration-200 group overflow-hidden",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full mr-4 ".concat("success"===e.status?"bg-green-500":"warning"===e.status?"bg-yellow-500":"error"===e.status?"bg-red-500":"bg-blue-500")}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-white font-medium break-words",children:e.action}),(0,t.jsxs)("p",{className:"text-gray-400 text-sm break-words",children:[e.model," • ",e.time]}),e.details&&(0,t.jsx)("p",{className:"text-gray-500 text-xs mt-1 line-clamp-2 leading-relaxed",title:e.details,children:e.details})]}),(0,t.jsx)("div",{className:"text-gray-400 group-hover:text-gray-300",children:"error"===e.status?(0,t.jsx)(x.A,{className:"h-5 w-5 text-red-400"}):(0,t.jsx)(m.A,{className:"h-5 w-5"})})]},e.id)),C.length>4&&(0,t.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,t.jsxs)("button",{onClick:()=>{window.location.href="/logs"},className:"text-sm text-orange-600 hover:text-orange-700 font-medium flex items-center justify-center w-full py-2 hover:bg-orange-50 rounded-lg transition-colors",children:["View All Activity (",C.length,")",(0,t.jsx)("svg",{className:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})]})})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,5738,9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(23079)),_N_E=e.O()}]);