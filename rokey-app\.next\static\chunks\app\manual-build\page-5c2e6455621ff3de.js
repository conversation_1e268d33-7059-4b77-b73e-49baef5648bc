(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9280],{12368:(e,t,s)=>{Promise.resolve().then(s.bind(s,40193))},40193:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var a=s(95155),r=s(12115),l=s(35695),i=s(5279),n=s(29337),o=s(45754),c=s(37186),d=s(99695);let x=r.forwardRef(function(e,t){let{title:s,titleId:a,...l}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),s?r.createElement("title",{id:a},s):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"}))});var m=s(94038),u=s(78046),h=s(63603),f=s(57765),w=s(55020);function g(e){let{workflow:t,onEdit:s,onDuplicate:l,onDelete:x,onRefreshApiKey:u,onTestInPlayground:f}=e,[g,p]=(0,r.useState)(null),[b,y]=(0,r.useState)(!1),[j,N]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/workflows?workflowId=".concat(t.id));if(e.ok){let t=await e.json();p(t.api_key_info)}}catch(e){}})()},[t.id]);let v=async()=>{if(null==g?void 0:g.key_prefix)try{let e="".concat(g.key_prefix).concat("*".repeat(32));await navigator.clipboard.writeText(e),N(!0),setTimeout(()=>N(!1),2e3)}catch(e){}},k=async()=>{if(u){y(!0);try{await u(t.id);let e=await fetch("/api/workflows?workflowId=".concat(t.id));if(e.ok){let t=await e.json();p(t.api_key_info)}}catch(e){}finally{y(!1)}}};return(0,a.jsxs)(w.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-2},className:"bg-gradient-to-br from-gray-900/60 to-gray-800/40 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 hover:border-[#ff6b35]/30 transition-all duration-300 group",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-2 group-hover:text-[#ff6b35] transition-colors",children:t.name}),(0,a.jsx)("p",{className:"text-gray-400 text-sm line-clamp-2",children:t.description||"No description provided"})]}),(0,a.jsx)("div",{className:"flex items-center gap-2 ml-4",children:(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(t.is_active?"bg-green-500/20 text-green-400 border border-green-500/30":"bg-gray-500/20 text-gray-400 border border-gray-500/30"),children:t.is_active?"Active":"Inactive"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[(0,a.jsxs)("span",{children:[t.node_count||0," nodes"]}),(0,a.jsxs)("span",{children:["Updated ",new Date(t.updated_at).toLocaleDateString()]})]}),g&&(0,a.jsxs)("div",{className:"bg-gray-900/30 rounded-lg p-3 mb-4 border border-gray-700/30",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 text-[#ff6b35]"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-300",children:"API Key"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("button",{onClick:v,className:"text-xs text-[#ff6b35] hover:text-[#e55a2b] transition-colors flex items-center gap-1",title:"Copy API Key Prefix",children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A,{className:"w-3 h-3"}),"Copied!"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"w-3 h-3"}),"Copy"]})}),u&&(0,a.jsxs)("button",{onClick:k,disabled:b,className:"text-xs text-gray-400 hover:text-gray-300 transition-colors flex items-center gap-1 ml-2",title:"Generate New API Key",children:[(0,a.jsx)(i.A,{className:"w-3 h-3 ".concat(b?"animate-spin":"")}),"New"]})]})]}),(0,a.jsxs)("div",{className:"font-mono text-xs text-green-400 bg-gray-800/50 rounded px-2 py-1",children:[g.key_prefix,"*".repeat(32)]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2 text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:["Requests: ",g.total_requests||0]}),(0,a.jsx)("span",{children:g.last_used_at?"Last used: ".concat(new Date(g.last_used_at).toLocaleDateString()):"Never used"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("button",{onClick:()=>s(t.id),className:"flex-1 bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2",children:[(0,a.jsx)(c.A,{className:"w-4 h-4"}),"Edit"]}),(0,a.jsx)("button",{onClick:()=>l(t.id),className:"bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors duration-200",title:"Duplicate",children:(0,a.jsx)(d.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:()=>f(t.id),className:"bg-green-600 hover:bg-green-500 text-white px-3 py-2 rounded-lg transition-colors duration-200",title:"Test in Playground",children:(0,a.jsx)(h.A,{className:"w-4 h-4"})})]})]})}function p(e){let{template:t,onUse:s}=e;return(0,a.jsxs)(w.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-2},className:"bg-gradient-to-br from-blue-900/40 to-blue-800/20 backdrop-blur-sm border border-blue-700/50 rounded-xl p-6 hover:border-blue-500/50 transition-all duration-300 group",children:[(0,a.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white group-hover:text-blue-400 transition-colors",children:t.name}),t.is_official&&(0,a.jsx)("span",{className:"bg-blue-500/20 text-blue-400 border border-blue-500/30 px-2 py-0.5 rounded-full text-xs font-medium",children:"Official"})]}),(0,a.jsx)("p",{className:"text-gray-400 text-sm line-clamp-2",children:t.description})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[(0,a.jsx)("span",{className:"bg-gray-700/50 px-2 py-1 rounded text-xs",children:t.category}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("span",{children:["⭐ ",t.rating.toFixed(1)]}),(0,a.jsxs)("span",{children:["↓ ",t.download_count]})]})]}),(0,a.jsx)("button",{onClick:()=>s(t.id),className:"w-full bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200",children:"Use Template"})]})}function b(){let e=(0,l.useRouter)(),[t,s]=(0,r.useState)([]),[i,n]=(0,r.useState)([]),[o,c]=(0,r.useState)(!0),[m,h]=(0,r.useState)(""),[w,b]=(0,r.useState)("workflows");(0,r.useEffect)(()=>{y(),j()},[]);let y=async()=>{try{let e=await fetch("/api/workflows");if(e.ok){let t=await e.json();s(t.workflows||[])}}catch(e){}},j=async()=>{try{n([]),c(!1)}catch(e){c(!1)}},N=()=>{e.push("/manual-build/new")},v=t=>{e.push("/manual-build/".concat(t))},k=e=>{window.open("/playground?workflow=".concat(e),"_blank")},C=async e=>{},A=async e=>{if(confirm("Are you sure you want to delete this workflow? This action cannot be undone."))try{let t=await fetch("/api/workflows?id=".concat(e),{method:"DELETE"});if(t.ok)y(),alert("Workflow deleted successfully");else{let e=await t.json();throw Error(e.details||"Failed to delete workflow")}}catch(e){alert("Failed to delete workflow: ".concat(e instanceof Error?e.message:"Unknown error"))}},_=t=>{e.push("/manual-build/new?template=".concat(t))},L=async e=>{try{alert("API key regeneration will be implemented in a future update.")}catch(e){alert("Failed to refresh API key. Please try again.")}},E=t.filter(e=>{var t;return e.name.toLowerCase().includes(m.toLowerCase())||(null==(t=e.description)?void 0:t.toLowerCase().includes(m.toLowerCase()))}),P=i.filter(e=>e.name.toLowerCase().includes(m.toLowerCase())||e.description.toLowerCase().includes(m.toLowerCase())||e.category.toLowerCase().includes(m.toLowerCase()));return o?(0,a.jsx)("div",{className:"min-h-screen bg-[#040716] flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white",children:"Loading..."})}):(0,a.jsx)("div",{className:"min-h-screen bg-[#040716] p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Manual Build"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Create custom AI workflows with visual node-based editor"})]}),(0,a.jsxs)("button",{onClick:N,className:"bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"w-5 h-5"}),"Create New Workflow"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("div",{className:"flex items-center gap-4",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search workflows and templates...",value:m,onChange:e=>h(e.target.value),className:"bg-gray-800/50 border border-gray-700/50 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-500 focus:outline-none focus:border-[#ff6b35]/50 transition-colors w-80"})]})}),(0,a.jsxs)("div",{className:"flex bg-gray-800/50 rounded-lg p-1",children:[(0,a.jsxs)("button",{onClick:()=>b("workflows"),className:"px-4 py-2 rounded-md font-medium transition-colors ".concat("workflows"===w?"bg-[#ff6b35] text-white":"text-gray-400 hover:text-white"),children:[(0,a.jsx)(x,{className:"w-4 h-4 inline mr-2"}),"My Workflows"]}),(0,a.jsxs)("button",{onClick:()=>b("templates"),className:"px-4 py-2 rounded-md font-medium transition-colors ".concat("templates"===w?"bg-[#ff6b35] text-white":"text-gray-400 hover:text-white"),children:[(0,a.jsx)(d.A,{className:"w-4 h-4 inline mr-2"}),"Templates"]})]})]}),"workflows"===w?(0,a.jsx)("div",{children:0===E.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(x,{className:"w-16 h-16 text-gray-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No workflows yet"}),(0,a.jsx)("p",{className:"text-gray-400 mb-6",children:"Create your first visual workflow to get started"}),(0,a.jsxs)("button",{onClick:N,className:"bg-[#ff6b35] hover:bg-[#e55a2b] text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 inline-flex items-center gap-2",children:[(0,a.jsx)(f.A,{className:"w-5 h-5"}),"Create New Workflow"]})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:E.map(e=>(0,a.jsx)(g,{workflow:e,onEdit:v,onDuplicate:C,onDelete:A,onRefreshApiKey:L,onTestInPlayground:k},e.id))})}):(0,a.jsx)("div",{children:0===P.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(d.A,{className:"w-16 h-16 text-gray-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No templates found"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Try adjusting your search or check back later"})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:P.map(e=>(0,a.jsx)(p,{template:e,onUse:_},e.id))})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(12368)),_N_E=e.O()}]);