(()=>{var e={};e.id=7545,e.ids=[7545],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},18536:(e,s,t)=>{Promise.resolve().then(t.bind(t,35291))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25333:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=t(65239),a=t(48088),n=t(88170),i=t.n(n),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d={children:["",{children:["routing-setup",{children:["[configId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,26697)),"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\[configId]\\page.tsx"]}]},{}]},{loading:[()=>Promise.resolve().then(t.bind(t,71238)),"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\[configId]\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/routing-setup/[configId]/page",pathname:"/routing-setup/[configId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},26697:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\[configId]\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31082:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(43210);let a=r.forwardRef(function({title:e,titleId:s,...t},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?r.createElement("title",{id:s},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},32216:(e,s,t)=>{Promise.resolve().then(t.bind(t,26697))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44725:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(43210);let a=r.forwardRef(function({title:e,titleId:s,...t},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?r.createElement("title",{id:s},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"}))})},47417:(e,s,t)=>{"use strict";t.d(s,{AnalyticsSkeleton:()=>o,ConfigSelectorSkeleton:()=>n,MessageSkeleton:()=>a,MyModelsSkeleton:()=>i,RoutingSetupSkeleton:()=>l});var r=t(12907);(0,r.registerClientReference)(function(){throw Error("Attempted to call LoadingSkeleton() from the server but LoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LoadingSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call ChatHistorySkeleton() from the server but ChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ChatHistorySkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call EnhancedChatHistorySkeleton() from the server but EnhancedChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","EnhancedChatHistorySkeleton");let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call MessageSkeleton() from the server but MessageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MessageSkeleton"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call ConfigSelectorSkeleton() from the server but ConfigSelectorSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ConfigSelectorSkeleton");(0,r.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","DashboardSkeleton");let i=(0,r.registerClientReference)(function(){throw Error("Attempted to call MyModelsSkeleton() from the server but MyModelsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MyModelsSkeleton"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call RoutingSetupSkeleton() from the server but RoutingSetupSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","RoutingSetupSkeleton");(0,r.registerClientReference)(function(){throw Error("Attempted to call TrainingSkeleton() from the server but TrainingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","TrainingSkeleton");let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call AnalyticsSkeleton() from the server but AnalyticsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","AnalyticsSkeleton");(0,r.registerClientReference)(function(){throw Error("Attempted to call PlaygroundSkeleton() from the server but PlaygroundSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","PlaygroundSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call LogsSkeleton() from the server but LogsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LogsSkeleton"),(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LoadingSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","default")},54984:(e,s,t)=>{Promise.resolve().then(t.bind(t,47417))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60925:(e,s,t)=>{"use strict";t.d(s,{c:()=>n});var r=t(43210);let a={};function n(){let[e,s]=(0,r.useState)({}),t=(0,r.useRef)({}),n=(0,r.useCallback)(e=>{let s=a[e];return!!s&&!(Date.now()-s.timestamp>3e5)&&!s.isLoading},[]),i=(0,r.useCallback)(e=>{let s=a[e];return!s||s.isLoading?null:Date.now()-s.timestamp>3e5?(delete a[e],null):s.data},[]),l=(0,r.useCallback)(async(e,r="medium")=>{if(n(e))return i(e);if(a[e]?.isLoading)return null;t.current[e]&&t.current[e].abort();let l=new AbortController;t.current[e]=l,a[e]={data:{},timestamp:Date.now(),isLoading:!0},s(s=>({...s,[e]:"loading"}));try{"low"===r?await new Promise(e=>setTimeout(e,200)):"medium"===r&&await new Promise(e=>setTimeout(e,50));let[t,n,i]=await Promise.allSettled([fetch(`/api/custom-configs/${e}`,{signal:l.signal}),fetch(`/api/keys?custom_config_id=${e}`,{signal:l.signal}),fetch(`/api/complexity-assignments?custom_config_id=${e}`,{signal:l.signal})]),o=null,d=[],c="none",m={},u=[];"fulfilled"===t.status&&t.value.ok&&(c=(o=await t.value.json()).routing_strategy||"none",m=o.routing_strategy_params||{}),"fulfilled"===n.status&&n.value.ok&&(d=await n.value.json()),"fulfilled"===i.status&&i.value.ok&&(u=await i.value.json());let x={configDetails:o,apiKeys:d,routingStrategy:c,routingParams:m,complexityAssignments:u};return a[e]={data:x,timestamp:Date.now(),isLoading:!1},s(s=>({...s,[e]:"success"})),x}catch(t){if("AbortError"===t.name)return null;return delete a[e],s(s=>({...s,[e]:"error"})),null}finally{delete t.current[e]}},[n,i]),o=(0,r.useCallback)(e=>({onMouseEnter:()=>{n(e)||l(e,"high")}}),[l,n]),d=(0,r.useCallback)(e=>{delete a[e],s(s=>{let t={...s};return delete t[e],t})},[]),c=(0,r.useCallback)(()=>{Object.keys(a).forEach(e=>{delete a[e]}),s({})},[]);return{prefetchRoutingSetupData:l,getCachedData:i,isCached:n,createHoverPrefetch:o,clearCache:d,clearAllCache:c,getStatus:(0,r.useCallback)(s=>e[s]||"idle",[e]),getCacheInfo:(0,r.useCallback)(()=>({cachedConfigs:Object.keys(a),cacheSize:Object.keys(a).length,totalCacheAge:Object.values(a).reduce((e,s)=>e+(Date.now()-s.timestamp),0)/Object.keys(a).length}),[]),prefetchStatus:e}}},62392:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(43210);let a=r.forwardRef(function({title:e,titleId:s,...t},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?r.createElement("title",{id:s},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65443:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(43210);let a=r.forwardRef(function({title:e,titleId:s,...t},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?r.createElement("title",{id:s},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"}))})},67064:(e,s,t)=>{Promise.resolve().then(t.bind(t,85445))},70149:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(43210);let a=r.forwardRef(function({title:e,titleId:s,...t},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?r.createElement("title",{id:s},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))})},71238:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(37413),a=t(47417);function n(){return(0,r.jsx)(a.RoutingSetupSkeleton,{})}},74075:e=>{"use strict";e.exports=require("zlib")},74461:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(43210);let a=r.forwardRef(function({title:e,titleId:s,...t},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":s},t),e?r.createElement("title",{id:s},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"}))})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},85445:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>T});var r=t(60687),a=t(43210),n=t(16189),i=t(85814),l=t.n(i),o=t(51426),d=t(62392),c=t(74461),m=t(65443),u=t(31082),x=t(86297),p=t(6510),h=t(58089),g=t(44725),b=t(5927),f=t(66244);let y=(0,a.createContext)(null);var j=t(52535),v=t(72789),N=t(24342),k=t(32582);function w(e){let s=(0,v.M)(()=>(0,N.OQ)(e)),{isStatic:t}=(0,a.useContext)(k.Q);if(t){let[,t]=(0,a.useState)(e);(0,a.useEffect)(()=>s.on("change",t),[])}return s}var C=t(19331),S=t(23671),A=t(15124);function R(e,s){let t=w(s()),r=()=>t.set(s());return r(),(0,A.E)(()=>{let s=()=>S.Gt.preRender(r,!1,!0),t=e.map(e=>e.on("change",s));return()=>{t.forEach(e=>e()),(0,S.WG)(r)}}),t}function _(e,s){let t=(0,v.M)(()=>[]);return R(e,()=>{t.length=0;let r=e.length;for(let s=0;s<r;s++)t[s]=e[s].get();return s(t)})}function L(e,s=0){return(0,b.S)(e)?e:w(s)}let E=(0,a.forwardRef)(function({children:e,style:s={},value:t,as:n="li",onDrag:i,layout:l=!0,...o},d){let c=(0,v.M)(()=>j.P[n]),m=(0,a.useContext)(y),u={x:L(s.x),y:L(s.y)},x=function(e,s,t,r){if("function"==typeof e){N.bt.current=[],e();let s=R(N.bt.current,e);return N.bt.current=void 0,s}let a="function"==typeof s?s:function(...e){let s=!Array.isArray(e[0]),t=s?0:-1,r=e[0+t],a=e[1+t],n=e[2+t],i=e[3+t],l=(0,C.G)(a,n,i);return s?l(r):l}(s,void 0,void 0);return Array.isArray(e)?_(e,a):_([e],([e])=>a(e))}([u.x,u.y],([e,s])=>e||s?1:"unset");(0,f.V)(!!m,"Reorder.Item must be a child of Reorder.Group");let{axis:p,registerItem:h,updateOrder:g}=m;return(0,r.jsx)(c,{drag:p,...o,dragSnapToOrigin:!0,style:{...s,x:u.x,y:u.y,zIndex:x},layout:l,onDrag:(e,s)=>{let{velocity:r}=s;r[p]&&g(t,u[p].get(),r[p]),i&&i(e,s)},onLayoutMeasure:e=>h(t,e),ref:d,ignoreStrict:!0,children:e})});var P=t(68028),M=t(87556);let I=(0,a.forwardRef)(function({children:e,as:s="ul",axis:t="y",onReorder:n,values:i,...l},o){let d=(0,v.M)(()=>j.P[s]),c=[],m=(0,a.useRef)(!1);return(0,f.V)(!!i,"Reorder.Group must be provided a values prop"),(0,r.jsx)(d,{...l,ref:o,ignoreStrict:!0,children:(0,r.jsx)(y.Provider,{value:{axis:t,registerItem:(e,s)=>{let r=c.findIndex(s=>e===s.value);-1!==r?c[r].layout=s[t]:c.push({value:e,layout:s[t]}),c.sort(q)},updateOrder:(e,s,t)=>{if(m.current)return;let r=function(e,s,t,r){if(!r)return e;let a=e.findIndex(e=>e.value===s);if(-1===a)return e;let n=r>0?1:-1,i=e[a+n];if(!i)return e;let l=e[a],o=i.layout,d=(0,P.k)(o.min,o.max,.5);return 1===n&&l.layout.max+t>d||-1===n&&l.layout.min+t<d?(0,M.Pe)(e,a,a+n):e}(c,e,s,t);c!==r&&(m.current=!0,n(r.map(K).filter(e=>-1!==i.indexOf(e))))}},children:e})})});function K(e){return e.value}function q(e,s){return e.layout.min-s.layout.min}var B=t(60925);function z(){return(0,r.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"})}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]})}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-xl p-4 animate-pulse",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full animate-pulse"})]})]})},e))})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"card p-8 min-h-[600px]",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("div",{className:"h-7 bg-gray-200 rounded w-48 mb-3 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full animate-pulse"})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16 animate-pulse"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 animate-pulse"})]})]},e))})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-48 mb-4 animate-pulse"}),(0,r.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-4",children:[1,2,3,4,5].map(e=>(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mx-auto mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse"})]},e))}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-40 animate-pulse"})})]})]})})]})]})})}function D(){return(0,r.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"})]})]})},e))}),(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-20 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]})]})]})})}var F=t(4847);let H=[{id:"none",name:"Default Behavior",shortDescription:"Automatic load balancing",description:"RouKey will automatically load balance across all keys assigned to this configuration with intra-request retries. No extra setup needed.",icon:o.A},{id:"intelligent_role",name:"Intelligent Role Routing",shortDescription:"AI-powered role classification",description:"RouKey uses an LLM to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.",icon:d.A},{id:"complexity_round_robin",name:"Complexity-Based Round-Robin",shortDescription:"Route by prompt complexity",description:"RouKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.",icon:c.A},{id:"strict_fallback",name:"Strict Fallback",shortDescription:"Ordered failover sequence",description:"Define an ordered list of API keys. RouKey will try them in sequence until one succeeds.",icon:m.A},{id:"cost_optimized",name:"Cost-Optimized Routing",shortDescription:"Smart cost-performance balance",description:"RouKey intelligently balances cost and performance by routing simple tasks to cheaper models and complex tasks to premium models. Maximizes savings while ensuring quality.",icon:u.A},{id:"ab_routing",name:"A/B Routing",shortDescription:"Continuous model optimization",description:"RouKey continuously tests different models with 15% of requests to find the best performing models for your specific use cases. Automatically optimizes routing based on quality and cost metrics.",icon:x.A}];function O({apiKey:e,index:s}){return(0,r.jsx)(E,{value:e,className:"bg-gray-800/50 border border-gray-700/50 rounded-lg p-4 shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-600/50 cursor-grab active:cursor-grabbing backdrop-blur-sm",whileDrag:{scale:1.02,boxShadow:"0 10px 25px rgba(0, 0, 0, 0.3)",zIndex:1e3},dragListener:!0,dragControls:void 0,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-orange-500/20 border border-orange-500/30 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-semibold text-orange-400",children:s+1})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:e.label}),(0,r.jsxs)("p",{className:"text-xs text-gray-400",children:[e.provider," - ",e.predefined_model_id]})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("div",{className:"p-2 text-gray-400 hover:text-gray-300 transition-colors cursor-grab active:cursor-grabbing",children:(0,r.jsx)(p.A,{className:"w-4 h-4"})})})]})})}function T(){let e=(0,n.useParams)(),s=(0,n.useRouter)(),t=(0,n.useSearchParams)(),i=e.configId,{getCachedData:p,isCached:b}=(0,B.c)(),f=()=>{let e=t.get("from");return"routing-setup"===e?"/routing-setup":`/my-models/${i}`},[y,j]=(0,a.useState)(null),[v,N]=(0,a.useState)(!0),[k,w]=(0,a.useState)(null),[C,S]=(0,a.useState)(null),[A,R]=(0,a.useState)(!1),[_,L]=(0,a.useState)("none"),[E,P]=(0,a.useState)({}),[M,K]=(0,a.useState)([]),[q,T]=(0,a.useState)(!1),[$,W]=(0,a.useState)([]),[G,Z]=(0,a.useState)(null),[V,U]=(0,a.useState)({}),[Q,J]=(0,a.useState)([]),[X,Y]=(0,a.useState)(!1),[ee,es]=(0,a.useState)(!1),[et,er]=(0,a.useState)(null),[ea,en]=(0,a.useState)(null);(0,a.useCallback)(async()=>{if(!i){w("Configuration ID is missing."),N(!1);return}let e=p(i);if(e&&e.configDetails&&e.apiKeys){j(e.configDetails),K(e.apiKeys);let s=e.routingStrategy||"none";if(L(s),P(e.routingParams||{}),"strict_fallback"===s&&e.routingParams?.ordered_api_key_ids){let s=e.routingParams.ordered_api_key_ids;W([...s.map(s=>e.apiKeys.find(e=>e.id===s)).filter(Boolean),...e.apiKeys.filter(e=>!s.includes(e.id))])}else W([...e.apiKeys]);N(!1),T(!1);return}b(i)||R(!0),N(!0),T(!0),w(null),S(null);try{let e=await fetch(`/api/custom-configs/${i}`);if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to fetch configuration")}let s=await e.json();j(s);let t=s.routing_strategy||"none";L(t);let r=s.routing_strategy_params||{};P(r);let a=await fetch(`/api/keys?custom_config_id=${i}`);if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to fetch API keys for this configuration")}let n=await a.json();if(K(n),"strict_fallback"===t&&r.ordered_api_key_ids){let e=r.ordered_api_key_ids,s=e.map(e=>n.find(s=>s.id===e)).filter(Boolean),t=n.filter(s=>!e.includes(s.id));W([...s,...t])}else W([...n])}catch(e){w(`Error loading data: ${e.message}`),j(null),K([])}finally{N(!1),T(!1),R(!1)}},[i,p,b]),(0,a.useCallback)(async e=>{if(i&&e){Y(!0),er(null),en(null);try{let s=await fetch(`/api/custom-configs/${i}/keys/${e}/complexity-assignments`);if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to fetch complexity assignments")}let t=await s.json();U(s=>({...s,[e]:t})),J(t)}catch(e){er(`Error fetching assignments for key: ${e.message}`),J([])}finally{Y(!1)}}},[i]);let ei=(e,s)=>{J(t=>s?[...t,e].sort((e,s)=>e-s):t.filter(s=>s!==e))},el=(0,a.useCallback)(async()=>{if(!i||!G)return void er("No API key selected to save assignments for.");es(!0),er(null),en(null);try{let e=await fetch(`/api/custom-configs/${i}/keys/${G}/complexity-assignments`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({complexity_levels:Q})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to save complexity assignments")}let s=await e.json();U(e=>({...e,[G]:[...Q]})),en(s.message||"Complexity assignments saved successfully!")}catch(e){er(`Error saving assignments: ${e.message}`)}finally{es(!1)}},[i,G,Q]),eo=e=>{W(e),P({ordered_api_key_ids:e.map(e=>e.id)})},ed=async e=>{if(e.preventDefault(),!i||!y)return void w("Configuration details not loaded.");N(!0),w(null),S(null);let s=E;"strict_fallback"===_&&(s={ordered_api_key_ids:$.map(e=>e.id)});try{let e=await fetch(`/api/custom-configs/${i}/routing`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({routing_strategy:_,routing_strategy_params:s})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to save routing settings")}let t=await e.json();S(t.message||"Routing settings saved successfully!"),j(e=>e?{...e,routing_strategy:_,routing_strategy_params:s}:null),P(s)}catch(e){w(`Error saving settings: ${e.message}`)}finally{N(!1)}},ec=()=>"complexity_round_robin"!==_?null:(0,r.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-700/50",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-white mb-4",children:"Complexity-Based Key Assignments"}),(0,r.jsx)("p",{className:"text-sm text-gray-400 mb-6",children:"Assign API keys to handle prompts of specific complexity levels (1-5). The system will classify incoming prompts and round-robin requests among keys assigned to that complexity."}),q&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-400"}),(0,r.jsx)("p",{className:"text-sm text-gray-400 ml-2",children:"Loading API keys..."})]}),!q&&0===M.length&&(0,r.jsx)("div",{className:"bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4 backdrop-blur-sm",children:(0,r.jsx)("p",{className:"text-sm text-yellow-200",children:"No API keys found for this configuration. Please add API keys first on the model configuration page."})}),M.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{htmlFor:"apiKeyForComplexity",className:"block text-sm font-medium text-gray-300 mb-2",children:"Select API Key to Assign Complexities:"}),(0,r.jsxs)("select",{id:"apiKeyForComplexity",value:G||"",onChange:e=>Z(e.target.value||null),className:"bg-gray-800/50 border border-gray-700/50 text-white rounded-lg px-3 py-2 max-w-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500",children:[(0,r.jsx)("option",{value:"",disabled:!0,children:"-- Select an API Key --"}),M.map(e=>(0,r.jsxs)("option",{value:e.id,children:[e.label," (",e.provider," - ",e.predefined_model_id,")"]},e.id))]})]}),G&&(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-xl p-6",children:[(0,r.jsxs)("h4",{className:"text-md font-medium text-white mb-4",children:["Assign Complexity Levels for: ",(0,r.jsx)("span",{className:"text-orange-400",children:M.find(e=>e.id===G)?.label})]}),X&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-400"}),(0,r.jsx)("p",{className:"text-sm text-gray-400 ml-2",children:"Loading current assignments..."})]}),et&&(0,r.jsx)("div",{className:"bg-red-900/20 border border-red-500/30 rounded-lg p-3 mb-4 backdrop-blur-sm",children:(0,r.jsx)("p",{className:"text-red-300 text-sm",children:et})}),ea&&(0,r.jsx)("div",{className:"bg-green-900/20 border border-green-500/30 rounded-lg p-3 mb-4 backdrop-blur-sm",children:(0,r.jsx)("p",{className:"text-green-300 text-sm",children:ea})}),!X&&(0,r.jsx)("div",{className:"space-y-3 mb-6",children:[1,2,3,4,5].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-3 p-3 bg-gray-800/30 border border-gray-700/50 rounded-lg hover:border-gray-600/50 cursor-pointer transition-colors duration-200",children:[(0,r.jsx)("input",{type:"checkbox",checked:Q.includes(e),onChange:s=>ei(e,s.target.checked),className:"h-4 w-4 text-orange-500 bg-gray-800 border-gray-600 rounded focus:ring-orange-500 focus:ring-2"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-white",children:["Complexity Level ",e]})]},e))}),(0,r.jsx)("button",{onClick:el,disabled:ee||X,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:ee?"Saving Assignments...":"Save Assignments for this Key"})]})]});return A&&!b(i)?(0,r.jsx)(z,{}):v&&!y?(0,r.jsx)(D,{}):!k||y||v?(0,r.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,r.jsx)("div",{className:"border-b border-gray-800/50",children:(0,r.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("button",{onClick:()=>{s.push(f())},className:"inline-flex items-center text-sm px-4 py-2 bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 hover:text-white rounded-lg border border-gray-700/50 hover:border-gray-600/50 transition-all duration-200",children:[(0,r.jsx)(g.A,{className:"w-4 h-4 mr-2"}),(()=>{let e=t.get("from");return"routing-setup"===e?"Back to Routing Setup":"Back to Configuration"})()]})}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Advanced Routing Setup"}),y&&(0,r.jsxs)("p",{className:"text-sm text-gray-400 mt-1",children:["Configuration: ",(0,r.jsx)("span",{className:"text-orange-400 font-semibold",children:y.name})]})]})]})})}),(0,r.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[k&&!C&&(0,r.jsx)("div",{className:"bg-red-900/20 border border-red-500/30 rounded-xl p-6 mb-8 max-w-4xl mx-auto backdrop-blur-sm",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-red-300",children:"Configuration Error"}),(0,r.jsx)("p",{className:"text-red-400 mt-1",children:k})]})]})}),C&&(0,r.jsx)("div",{className:"bg-green-900/20 border border-green-500/30 rounded-xl p-6 mb-8 max-w-4xl mx-auto backdrop-blur-sm",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-green-300",children:"Settings Saved"}),(0,r.jsx)("p",{className:"text-green-400 mt-1",children:C})]})]})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-xl p-6 sticky top-8",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white mb-6",children:"Routing Strategy"}),(0,r.jsx)("div",{className:"space-y-3",children:H.map(e=>{let s=e.icon,t=_===e.id,a=["intelligent_role","complexity_round_robin","auto_optimal","cost_optimized","ab_routing"].includes(e.id),n=(0,r.jsx)("button",{onClick:()=>{if(L(e.id),"strict_fallback"===e.id){let e=E.ordered_api_key_ids;e&&Array.isArray(e)?W([...e.map(e=>M.find(s=>s.id===e)).filter(Boolean),...M.filter(s=>!e.includes(s.id))]):W([...M]),P({ordered_api_key_ids:$.map(e=>e.id)})}else P({}),W([...M]);Z(null),J([]),er(null),en(null)},disabled:v,className:`w-full text-left p-4 rounded-xl border-2 transition-all duration-300 group ${t?"border-orange-500 bg-orange-500/10 shadow-lg transform scale-[1.02]":"border-gray-700/50 bg-gray-800/30 hover:border-orange-400/50 hover:bg-orange-500/5 hover:shadow-md"}`,children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:`p-3 rounded-lg transition-colors duration-300 ${t?"bg-orange-500/20 text-orange-400":"bg-gray-700/50 text-gray-400 group-hover:bg-orange-500/10 group-hover:text-orange-400"}`,children:(0,r.jsx)(s,{className:"w-6 h-6"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)("h3",{className:`font-semibold text-sm transition-colors duration-300 ${t?"text-orange-300":"text-white"}`,children:e.name}),t&&(0,r.jsx)(h.A,{className:"w-4 h-4 text-orange-400 animate-in fade-in duration-300"})]}),(0,r.jsx)("p",{className:`text-xs leading-relaxed transition-colors duration-300 ${t?"text-orange-400/80":"text-gray-400"}`,children:e.shortDescription})]})]})},e.id);return a?(0,r.jsx)(F.sU,{feature:"advanced_routing",customMessage:`${e.name} is available starting with the Starter plan. Upgrade to access advanced routing strategies that optimize performance and cost.`,fallback:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"opacity-50 pointer-events-none",children:n}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"bg-gray-900/90 backdrop-blur-sm border border-orange-500/30 rounded-lg px-3 py-1",children:(0,r.jsx)("span",{className:"text-xs font-medium text-orange-400",children:"Starter Plan Required"})})})]}),children:n},e.id):n})})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsx)("form",{onSubmit:ed,children:(0,r.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-xl p-8 min-h-[600px]",children:(0,r.jsx)("div",{className:"animate-in fade-in slide-in-from-right-4 duration-500",children:(()=>{let e=H.find(e=>e.id===_);return"none"===_?(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("div",{className:"w-20 h-20 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(o.A,{className:"w-10 h-10 text-orange-400"})}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-white mb-4",children:"Default Behavior"}),(0,r.jsx)("p",{className:"text-gray-400 max-w-md mx-auto leading-relaxed",children:e?.description}),(0,r.jsx)("div",{className:"mt-8 p-4 bg-green-900/20 border border-green-500/30 rounded-xl backdrop-blur-sm",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)(h.A,{className:"w-5 h-5 text-green-400"}),(0,r.jsx)("span",{className:"text-green-300 font-medium",children:"No additional setup required"})]})}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-700/50",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:v,children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"intelligent_role"===_?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-white mb-4 flex items-center",children:[(0,r.jsx)(d.A,{className:"w-7 h-7 mr-3 text-orange-400"}),"Intelligent Role Routing"]}),(0,r.jsx)("p",{className:"text-gray-400 leading-relaxed",children:e?.description})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-900/20 border border-blue-500/30 rounded-xl p-6 backdrop-blur-sm",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-300 mb-3",children:"How it works:"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm text-blue-200",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-400",children:"1"})}),(0,r.jsx)("p",{children:"System analyzes your prompt to understand the main task"})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-400",children:"2"})}),(0,r.jsx)("p",{children:"Matches task to relevant RoKey operational roles (e.g., 'Coding - Frontend', 'Copywriting')"})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-400",children:"3"})}),(0,r.jsx)("p",{children:"Routes to assigned API key or falls back to 'Default General Chat Model'"})]})]})]}),(0,r.jsx)("div",{className:"bg-green-900/20 border border-green-500/30 rounded-xl p-6 backdrop-blur-sm",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(h.A,{className:"w-6 h-6 text-green-400 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-green-300 mb-2",children:"Ready to use!"}),(0,r.jsx)("p",{className:"text-sm text-green-200 leading-relaxed",children:"No additional setup required. Future enhancements may allow further customization."})]})]})})]}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-700/50 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:v,children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"strict_fallback"===_?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-white mb-4 flex items-center",children:[(0,r.jsx)(m.A,{className:"w-7 h-7 mr-3 text-orange-400"}),"Strict Fallback Configuration"]}),(0,r.jsx)("p",{className:"text-gray-400 leading-relaxed",children:e?.description})]}),q&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,r.jsx)("div",{className:"w-8 h-8 border-2 border-orange-400/20 border-t-orange-400 rounded-full animate-spin"}),(0,r.jsx)("p",{className:"text-gray-400 ml-3",children:"Loading API keys..."})]}),!q&&0===M.length&&(0,r.jsxs)("div",{className:"bg-yellow-900/20 border border-yellow-500/30 rounded-xl p-8 text-center backdrop-blur-sm",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("h4",{className:"text-lg font-semibold text-yellow-300 mb-2",children:"No API Keys Found"}),(0,r.jsx)("p",{className:"text-yellow-200 leading-relaxed",children:"Please add API keys on the main configuration page to set up fallback order."})]}),!q&&M.length>0&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-blue-900/20 border border-blue-500/30 rounded-xl p-6 backdrop-blur-sm",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("p",{className:"text-sm text-blue-200 leading-relaxed",children:"Drag and drop to arrange the API keys in the desired order of execution. The router will try the first key, then the second if the first fails, and so on."})]})}),(0,r.jsx)(I,{axis:"y",values:$,onReorder:eo,className:"space-y-3",children:$.map((e,s)=>(0,r.jsx)(O,{apiKey:e,index:s},e.id))})]}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-700/50 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:v||0===M.length,children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"complexity_round_robin"===_?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-white mb-4 flex items-center",children:[(0,r.jsx)(c.A,{className:"w-7 h-7 mr-3 text-orange-400"}),"Complexity-Based Round-Robin"]}),(0,r.jsx)("p",{className:"text-gray-400 leading-relaxed",children:e?.description})]}),ec(),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-700/50 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:v,children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"cost_optimized"===_?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-white mb-4 flex items-center",children:[(0,r.jsx)(u.A,{className:"w-7 h-7 mr-3 text-orange-400"}),"Smart Cost-Optimized Routing"]}),(0,r.jsx)("p",{className:"text-gray-400 leading-relaxed",children:e?.description})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-900/20 border border-blue-500/30 rounded-xl p-6 backdrop-blur-sm",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-300 mb-3",children:"How it works:"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm text-blue-200",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-400",children:"1"})}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"RouKey Classifier"})," analyzes your prompt to determine task complexity (Simple, Moderate, Complex)"]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-400",children:"2"})}),(0,r.jsxs)("p",{children:["Routes to appropriate cost tier: ",(0,r.jsx)("strong",{children:"Simple tasks"})," → Cheapest models, ",(0,r.jsx)("strong",{children:"Complex tasks"})," → Premium models"]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-400",children:"3"})}),(0,r.jsx)("p",{children:"Prioritizes cost savings by using cheaper models whenever possible while ensuring quality for complex tasks"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"bg-green-900/20 border border-green-500/30 rounded-xl p-4 backdrop-blur-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-green-400 rounded-full"}),(0,r.jsx)("h5",{className:"font-semibold text-green-300 text-sm",children:"Simple Tasks"})]}),(0,r.jsxs)("p",{className:"text-xs text-green-200 leading-relaxed",children:["Basic questions, simple conversations, straightforward requests → ",(0,r.jsx)("strong",{children:"Cheapest models"})]})]}),(0,r.jsxs)("div",{className:"bg-yellow-900/20 border border-yellow-500/30 rounded-xl p-4 backdrop-blur-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-yellow-400 rounded-full"}),(0,r.jsx)("h5",{className:"font-semibold text-yellow-300 text-sm",children:"Moderate Tasks"})]}),(0,r.jsxs)("p",{className:"text-xs text-yellow-200 leading-relaxed",children:["Analysis, explanations, moderate complexity → ",(0,r.jsx)("strong",{children:"Balanced pricing models"})]})]}),(0,r.jsxs)("div",{className:"bg-purple-900/20 border border-purple-500/30 rounded-xl p-4 backdrop-blur-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-purple-400 rounded-full"}),(0,r.jsx)("h5",{className:"font-semibold text-purple-300 text-sm",children:"Complex Tasks"})]}),(0,r.jsxs)("p",{className:"text-xs text-purple-200 leading-relaxed",children:["Advanced reasoning, coding, research → ",(0,r.jsx)("strong",{children:"Premium models"})]})]})]}),(0,r.jsx)("div",{className:"bg-orange-900/20 border border-orange-500/30 rounded-xl p-6 backdrop-blur-sm",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(u.A,{className:"w-6 h-6 text-orange-400 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-orange-300 mb-2",children:"Intelligent Cost Optimization"}),(0,r.jsx)("p",{className:"text-sm text-orange-200 leading-relaxed",children:"This strategy maximizes cost savings by routing most requests to cheaper models, while automatically upgrading to premium models only when task complexity truly requires it. Perfect for balancing budget constraints with quality requirements."})]})]})}),(0,r.jsx)("div",{className:"bg-green-900/20 border border-green-500/30 rounded-xl p-6 backdrop-blur-sm",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(h.A,{className:"w-6 h-6 text-green-400 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-green-300 mb-2",children:"Ready to use!"}),(0,r.jsx)("p",{className:"text-sm text-green-200 leading-relaxed",children:"No additional setup required. RouKey will automatically classify task complexity and route to the most cost-effective model tier for optimal savings and performance."})]})]})})]}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-700/50 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:v,children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):"ab_routing"===_?(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-white mb-4 flex items-center",children:[(0,r.jsx)(x.A,{className:"w-7 h-7 mr-3 text-orange-400"}),"A/B Routing Optimization"]}),(0,r.jsx)("p",{className:"text-gray-400 leading-relaxed",children:e?.description})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-900/20 border border-blue-500/30 rounded-xl p-6 backdrop-blur-sm",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-300 mb-3",children:"How it works:"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm text-blue-200",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-400",children:"1"})}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"85% Control Group:"})," Routes to your best-performing models based on historical data"]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-400",children:"2"})}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"15% Test Group:"})," Experiments with different models to discover better options"]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs font-bold text-blue-400",children:"3"})}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Continuous Learning:"})," Automatically updates routing based on quality metrics and user feedback"]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-green-900/20 border border-green-500/30 rounded-xl p-4 backdrop-blur-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-green-400 rounded-full"}),(0,r.jsx)("h5",{className:"font-semibold text-green-300 text-sm",children:"Quality Optimization"})]}),(0,r.jsx)("p",{className:"text-xs text-green-200 leading-relaxed",children:"Tracks response quality, user satisfaction, and task completion rates to identify the best models for your specific use cases."})]}),(0,r.jsxs)("div",{className:"bg-purple-900/20 border border-purple-500/30 rounded-xl p-4 backdrop-blur-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-purple-400 rounded-full"}),(0,r.jsx)("h5",{className:"font-semibold text-purple-300 text-sm",children:"Cost Efficiency"})]}),(0,r.jsx)("p",{className:"text-xs text-purple-200 leading-relaxed",children:"Balances quality with cost to find models that deliver the best value for your specific requirements and budget."})]})]}),(0,r.jsx)("div",{className:"bg-orange-900/20 border border-orange-500/30 rounded-xl p-6 backdrop-blur-sm",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(x.A,{className:"w-6 h-6 text-orange-400 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-orange-300 mb-2",children:"Intelligent Experimentation"}),(0,r.jsx)("p",{className:"text-sm text-orange-200 leading-relaxed",children:"This strategy continuously learns from your usage patterns to optimize routing decisions. The more you use it, the better it becomes at selecting the perfect model for each request."})]})]})}),(0,r.jsx)("div",{className:"bg-green-900/20 border border-green-500/30 rounded-xl p-6 backdrop-blur-sm",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(h.A,{className:"w-6 h-6 text-green-400 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-green-300 mb-2",children:"Ready to use!"}),(0,r.jsx)("p",{className:"text-sm text-green-200 leading-relaxed",children:"No additional setup required. RouKey will automatically start A/B testing and learning from your requests to optimize routing performance."})]})]})})]}),(0,r.jsx)("div",{className:"mt-12 pt-6 border-t border-gray-700/50 flex justify-end",children:(0,r.jsx)("button",{type:"submit",className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",disabled:v,children:v?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("svg",{className:"w-4 h-4 mr-2 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Save Routing Settings"]})})})]}):null})()})})})})]})})]})]}):(0,r.jsx)("div",{className:"min-h-screen bg-[#040716] text-white",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:"Routing Setup Error"}),(0,r.jsxs)("div",{className:"bg-red-900/20 border border-red-500/30 rounded-xl p-6 backdrop-blur-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-red-400 rounded-full"}),(0,r.jsx)("p",{className:"text-red-300",children:k})]}),(0,r.jsx)(l(),{href:"/my-models",className:"mt-4 btn-primary inline-block",children:"Back to My Models"})]})]})})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,5449,2535,4912,4847],()=>t(25333));module.exports=r})();