(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2076],{1571:(e,t,i)=>{"use strict";i.d(t,{$:()=>n.A,f:()=>r.A});var n=i(78046),r=i(74500)},5187:(e,t,i)=>{"use strict";i.d(t,{CT:()=>n.A,O4:()=>r.A,ny:()=>a.A});var n=i(72227),r=i(82771),a=i(14170)},8652:(e,t,i)=>{"use strict";i.d(t,{AQ:()=>c.A,RY:()=>o.A,cu:()=>r.A,fA:()=>s.A,r9:()=>a.A,sR:()=>u.A,tl:()=>n.A});var n=i(94648),r=i(14615),a=i(5500),c=i(92975),s=i(69994),o=i(94038),u=i(39883)},11845:(e,t,i)=>{"use strict";i.d(t,{b:()=>n.A});var n=i(10184)},17974:(e,t,i)=>{"use strict";i.d(t,{BZ:()=>r.A,Gg:()=>a.A,OR:()=>c.A,Zu:()=>n.A});var n=i(78039),r=i(90345),a=i(62486),c=i(67508)},18685:(e,t,i)=>{"use strict";i.d(t,{AQ:()=>c.A,RY:()=>o.A,cu:()=>r.A,fA:()=>s.A,r9:()=>a.A,sR:()=>u.A,tl:()=>n.A});var n=i(51297),r=i(48612),a=i(10187),c=i(55616),s=i(34853),o=i(11595),u=i(22670)},19393:()=>{},19681:(e,t,i)=>{"use strict";i.d(t,{D3:()=>r.A,fK:()=>a.A,tK:()=>n.A});var n=i(69598),r=i(63418),a=i(74500)},21884:(e,t,i)=>{"use strict";i.d(t,{C1:()=>n.A,KS:()=>a.A,Pi:()=>r.A,fK:()=>s.A,qh:()=>c.A});var n=i(6865),r=i(55628),a=i(67695),c=i(52589),s=i(74500)},22261:(e,t,i)=>{"use strict";i.d(t,{G:()=>c,c:()=>s});var n=i(95155),r=i(12115);let a=(0,r.createContext)(void 0);function c(e){let{children:t}=e,[i,c]=(0,r.useState)(!0),[s,o]=(0,r.useState)(!1),[u,d]=(0,r.useState)(!1);return(0,n.jsx)(a.Provider,{value:{isCollapsed:i,isHovered:s,isHoverDisabled:u,toggleSidebar:()=>c(!i),collapseSidebar:()=>c(!0),expandSidebar:()=>c(!1),setHovered:e=>{u||o(e)},setHoverDisabled:e=>{d(e),e&&o(!1)}},children:t})}function s(){let e=(0,r.useContext)(a);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}},27016:(e,t,i)=>{"use strict";i.d(t,{$p:()=>d.A,AQ:()=>u.A,BF:()=>o.A,D3:()=>c.A,Rz:()=>n.A,Vy:()=>s.A,XF:()=>a.A,tK:()=>r.A});var n=i(55596),r=i(69598),a=i(58828),c=i(63418),s=i(37186),o=i(40710),u=i(92975),d=i(78046)},27850:(e,t,i)=>{"use strict";i.d(t,{O:()=>n.A});var n=i(64353)},28578:(e,t,i)=>{"use strict";i.d(t,{C1:()=>n.A,Pi:()=>r.A,jO:()=>a.A});var n=i(6865),r=i(55628),a=i(58688)},31430:(e,t,i)=>{"use strict";i.d(t,{AQ:()=>c.A,DP:()=>a.A,RY:()=>s.A,cu:()=>n.A,r9:()=>r.A,sR:()=>o.A});var n=i(14615),r=i(5500),a=i(5246),c=i(92975),s=i(94038),o=i(39883)},38152:(e,t,i)=>{"use strict";i.d(t,{Pi:()=>n.A,fK:()=>a.A,uc:()=>r.A});var n=i(55628),r=i(31151),a=i(74500)},39499:(e,t,i)=>{"use strict";i.d(t,{Gg:()=>a.A,JD:()=>r.A,Kp:()=>n.A});var n=i(15713),r=i(15442),a=i(27305)},40705:(e,t,i)=>{"use strict";i.d(t,{h:()=>n.A});var n=i(18276)},41448:(e,t,i)=>{"use strict";i.d(t,{B:()=>r.A,K:()=>n.A});var n=i(15713),r=i(86474)},43194:(e,t,i)=>{"use strict";i.d(t,{Y:()=>n.A});var n=i(58397)},43914:(e,t,i)=>{"use strict";i.d(t,{A:()=>n.A});var n=i(92975)},46780:(e,t,i)=>{"use strict";i.d(t,{m:()=>n.A});var n=i(65529)},47225:(e,t,i)=>{"use strict";i.d(t,{Dc:()=>r,p2:()=>n});let n=[{id:"general_chat",name:"General Chat",description:"Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives."},{id:"coding_frontend",name:"Coding - Frontend",description:"Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development."},{id:"coding_backend",name:"Coding - Backend",description:"Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture."},{id:"research_synthesis",name:"Research & Synthesis",description:"Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information."},{id:"translation_localization",name:"Translation & Localization",description:"Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance."},{id:"image_generation",name:"Image Generation",description:"Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing."},{id:"data_extractor",name:"Data Extractor",description:"Extracting specific data from web pages, scraping content, and gathering information from websites."},{id:"form_filler",name:"Form Filler",description:"Filling out web forms, submitting data, and handling form-based interactions on websites."},{id:"verification_agent",name:"Verification Agent",description:"Verifying information on websites, fact-checking, and validating data accuracy."},{id:"research_assistant",name:"Research Assistant",description:"Conducting web-based research, gathering information from multiple sources, and compiling research findings."},{id:"shopping_assistant",name:"Shopping Assistant",description:"Helping with online shopping, price comparisons, product research, and e-commerce tasks."},{id:"price_comparison",name:"Price Comparison",description:"Comparing prices across different websites, finding deals, and analyzing product pricing."},{id:"fact_checker",name:"Fact Checker",description:"Verifying facts and information across multiple web sources, cross-referencing data for accuracy."},{id:"task_executor",name:"Task Executor",description:"General task execution and automation, handling various web-based tasks and workflows."}],r=e=>n.find(t=>t.id===e)},47321:(e,t,i)=>{"use strict";i.d(t,{C1:()=>n.A,Pi:()=>r.A,qh:()=>a.A});var n=i(6865),r=i(55628),a=i(52589)},75922:(e,t,i)=>{"use strict";i.d(t,{MG:()=>n});let n=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},76653:(e,t,i)=>{"use strict";i.d(t,{n:()=>n.A});var n=i(14170)},84441:()=>{},89464:(e,t,i)=>{"use strict";i.d(t,{K:()=>n.A});var n=i(64219)},89732:(e,t,i)=>{"use strict";i.d(t,{AQ:()=>c.A,RY:()=>o.A,cu:()=>r.A,fA:()=>s.A,fK:()=>d.A,r9:()=>a.A,sR:()=>u.A,tl:()=>n.A});var n=i(94648),r=i(14615),a=i(5500),c=i(92975),s=i(69994),o=i(94038),u=i(39883),d=i(74500)},99323:(e,t,i)=>{"use strict";i.d(t,{bu:()=>o,i9:()=>s});var n=i(95155),r=i(12115),a=i(35695);let c=(0,r.createContext)(void 0);function s(e){let{children:t}=e,[i,s]=(0,r.useState)(!1),[o,u]=(0,r.useState)(null),[d,l]=(0,r.useState)([]),[g,p]=(0,r.useState)(new Set),[m,A]=(0,r.useState)(!1),f=(0,a.usePathname)(),h=(0,a.useRouter)(),v=(0,r.useRef)(null),b=(0,r.useRef)([]),I=(0,r.useRef)(null),C=(0,r.useRef)(0),T=(0,r.useRef)({}),D=(0,r.useRef)({});(0,r.useEffect)(()=>{A(!0)},[]);let S=(0,r.useCallback)(e=>{},[m]);(0,r.useEffect)(()=>{f&&!d.includes(f)&&(l(e=>[...e,f]),p(e=>new Set([...e,f])))},[f,d]),(0,r.useEffect)(()=>{S("\uD83D\uDD0D [OPTIMISTIC NAV] Route check: target=".concat(o,", current=").concat(f,", navigationId=").concat(I.current)),o&&I.current&&f===o&&(S("✅ [OPTIMISTIC NAV] Navigation completed: ".concat(o," -> ").concat(f)),v.current&&(clearTimeout(v.current),v.current=null),s(!1),u(null),I.current=null,b.current=b.current.filter(e=>e.route!==o))},[f,o,S]),(0,r.useEffect)(()=>{i&&o&&f===o&&(S("\uD83D\uDE80 [OPTIMISTIC NAV] Immediate route match detected, clearing navigation state"),s(!1),u(null),v.current&&(clearTimeout(v.current),v.current=null))},[f,o,i,S]);let k=(0,r.useCallback)(e=>g.has(e),[g]),w=(0,r.useCallback)(()=>{if(0===b.current.length)return;let e=b.current[b.current.length-1];b.current=[e];let{route:t,id:i}=e;S("\uD83D\uDE80 [OPTIMISTIC NAV] Processing navigation to: ".concat(t," (id: ").concat(i,")")),v.current&&(clearTimeout(v.current),v.current=null),I.current=i;let n=k(t);n&&(S("⚡ [OPTIMISTIC NAV] Using cached navigation for: ".concat(t)),setTimeout(()=>{I.current===i&&s(!1)},100));try{h.push(t)}catch(e){S("❌ [OPTIMISTIC NAV] Router.push failed for: ".concat(t,", using fallback")),window.location.href=t;return}v.current=setTimeout(()=>{if(S("⚠️ [OPTIMISTIC NAV] Timeout reached for: ".concat(t," (id: ").concat(i,"), current path: ").concat(f)),I.current===i){S("\uD83D\uDD04 [OPTIMISTIC NAV] Attempting fallback navigation to: ".concat(t));try{window.location.href=t}catch(e){S("❌ [OPTIMISTIC NAV] Fallback navigation failed: ".concat(e))}s(!1),u(null),I.current=null}v.current=null},n?800:3e3)},[h,f,k,S]),x=(0,r.useCallback)(e=>{if(f===e||!m)return;let t=Date.now();if(t-C.current<100&&o===e)return void S("\uD83D\uDD04 [OPTIMISTIC NAV] Debouncing duplicate navigation to: ".concat(e));if(C.current=t,T.current[e]||(T.current[e]=0),T.current[e]++,D.current[e]&&clearTimeout(D.current[e]),D.current[e]=setTimeout(()=>{T.current[e]=0},2e3),T.current[e]>=3){S("\uD83D\uDEA8 [OPTIMISTIC NAV] Force navigation escape hatch for: ".concat(e)),T.current[e]=0,window.location.href=e;return}v.current&&(clearTimeout(v.current),v.current=null),s(!0),u(e);let i="nav_".concat(t,"_").concat(Math.random().toString(36).substr(2,9));b.current=[{route:e,timestamp:t,id:i}],w()},[f,o,w,S,m]),P=(0,r.useCallback)(()=>{v.current&&(clearTimeout(v.current),v.current=null),s(!1),u(null),I.current=null,b.current=[]},[]);return(0,r.useEffect)(()=>{if(!m)return;let e=()=>{!document.hidden&&i&&(S("\uD83D\uDC41️ [OPTIMISTIC NAV] Document visible, checking if navigation should clear"),setTimeout(()=>{o&&f===o&&(S("\uD83D\uDD27 [OPTIMISTIC NAV] Force clearing navigation state"),s(!1),u(null),v.current&&(clearTimeout(v.current),v.current=null))},100))};return document.addEventListener("visibilitychange",e),()=>document.removeEventListener("visibilitychange",e)},[i,o,f,S,m]),(0,r.useEffect)(()=>()=>{v.current&&clearTimeout(v.current)},[]),(0,n.jsx)(c.Provider,{value:{isNavigating:i,targetRoute:o,navigateOptimistically:x,clearNavigation:P,isPageCached:k,navigationHistory:d},children:t})}function o(){return(0,r.useContext)(c)||null}}}]);