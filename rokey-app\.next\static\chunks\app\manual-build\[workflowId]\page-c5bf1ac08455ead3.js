(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4833],{4351:(e,t,o)=>{"use strict";o.d(t,{AQ:()=>r.A,EF:()=>a.A,N7:()=>s.A,Pi:()=>i.A});var a=o(5279),s=o(69454),r=o(92975),i=o(55628)},9266:(e,t,o)=>{"use strict";o.d(t,{A6:()=>a.A,O4:()=>s.A,Vy:()=>r.A,li:()=>l.A,py:()=>i.A,ud:()=>n.A});var a=o(32461),s=o(82771),r=o(37186),i=o(74684),n=o(63603),l=o(63782)},26592:(e,t,o)=>{"use strict";o.d(t,{JM:()=>r.A,Pi:()=>i.A,RY:()=>n.A,Sr:()=>a.A,Xx:()=>s.A,fK:()=>l.A});var a=o(29337),s=o(45754),r=o(42278),i=o(55628),n=o(94038),l=o(74500)},28707:(e,t,o)=>{"use strict";o.d(t,{C1:()=>s.A,D3:()=>r.A,EF:()=>a.A,KS:()=>d.A,Mt:()=>i.A,O4:()=>n.A,Pi:()=>l.A,qh:()=>c.A});var a=o(5279),s=o(6865),r=o(63418),i=o(40975),n=o(82771),l=o(55628),d=o(67695),c=o(52589)},44726:(e,t,o)=>{"use strict";o.d(t,{C1:()=>a.A,Pi:()=>l.A,Sr:()=>s.A,Vy:()=>n.A,fK:()=>c.A,go:()=>i.A,qY:()=>d.A,yW:()=>r.A});var a=o(6865),s=o(29337),r=o(87027),i=o(89959),n=o(37186),l=o(55628),d=o(68673),c=o(74500)},46813:(e,t,o)=>{"use strict";o.d(t,{CT:()=>a.A,K6:()=>u.A,R2:()=>n.A,Xx:()=>s.A,Zu:()=>d.A,bM:()=>r.A,fK:()=>p.A,li:()=>l.A,mS:()=>i.A,uc:()=>c.A});var a=o(72227),s=o(45754),r=o(10184),i=o(65529),n=o(61316),l=o(63782),d=o(8246),c=o(31151),u=o(64219),p=o(74500)},63071:(e,t,o)=>{"use strict";o.d(t,{Vy:()=>a.A,gF:()=>s.A,uc:()=>i.A,vk:()=>r.A});var a=o(37186),s=o(99695),r=o(3332),i=o(31151)},72396:(e,t,o)=>{Promise.resolve().then(o.bind(o,86239))},76351:(e,t,o)=>{"use strict";o.d(t,{S:()=>a.A});var a=o(46172)},76514:(e,t,o)=>{"use strict";o.d(t,{AQ:()=>d.A,D3:()=>a.A,K6:()=>p.A,OL:()=>r.A,Pp:()=>i.A,YE:()=>l.A,bM:()=>c.A,hp:()=>n.A,jO:()=>y.A,mS:()=>u.A,ny:()=>f.A,vK:()=>s.A});var a=o(63418),s=o(1442),r=o(64353),i=o(94830),n=o(18276),l=o(58397),d=o(92975),c=o(10184),u=o(65529),p=o(64219),f=o(14170),y=o(58688)},82347:(e,t,o)=>{"use strict";o.d(t,{W:()=>a.WF,e:()=>s.e});var a=o(83357),s=o(91659)},86239:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>m});var a=o(95155),s=o(12115),r=o(35695),i=o(61772);o(66419);var n=o(63768),l=o(75521),d=o(41324),c=o(82416),u=o(55764),p=o(10009),f=o(69200),y=o(27223),w=o(35020),A=o(46091);function m(e){let{params:t}=e,o=(0,r.useParams)(),m=(0,r.useRouter)(),g=null==o?void 0:o.workflowId,[k,v]=(0,s.useState)(null),[h,b,x]=(0,i.ck)([]),[C,S,j]=(0,i.fM)([]),[N,O]=(0,s.useState)(null),[E,P]=(0,s.useState)(!0),[D,M]=(0,s.useState)(!1),[_,K]=(0,s.useState)(!1),[R,T]=(0,s.useState)(null),[q,F]=(0,s.useState)({isOpen:!1,mode:"save"}),[I,V]=(0,s.useState)(null),[W,U]=(0,p.S)("new"!==g?g:null,{autoConnect:!0,onEvent:e=>{"workflow_started"===e.type||"node_started"===e.type||"node_completed"===e.type||"workflow_completed"===e.type||e.type},onConnect:()=>{},onDisconnect:()=>{},onError:e=>{}}),[Y,z]=(0,s.useState)([]),[L,X]=(0,s.useState)(!1),[J,Z]=(0,s.useState)(!1);(0,s.useEffect)(()=>{"new"===g?Q():B(g)},[g]);let Q=async()=>{try{b([{id:"user-request",type:"userRequest",position:{x:50,y:200},data:{label:"User Request",config:{},isConfigured:!0,description:"Starting point for user input"}},{id:"classifier",type:"classifier",position:{x:350,y:200},data:{label:"Classifier",config:{},isConfigured:!0,description:"Analyzes and categorizes the request"}},{id:"output",type:"output",position:{x:950,y:200},data:{label:"Output",config:{},isConfigured:!0,description:"Final response to the user"}}]),S([{id:"e1",source:"user-request",target:"classifier",type:"smoothstep",animated:!0}]),P(!1)}catch(e){P(!1)}},B=async e=>{try{P(!0);let t=await fetch("/api/workflows?id=".concat(e));if(!t.ok)throw await t.text(),Error("Failed to load workflow: ".concat(t.status," ").concat(t.statusText));let o=(await t.json()).workflow;if(o)v(o),o.nodes&&Array.isArray(o.nodes)?b(o.nodes):b([]),o.edges&&Array.isArray(o.edges)?S(o.edges):S([]);else throw Error("No workflow data found")}catch(e){}finally{P(!1)}},G=(0,s.useCallback)(e=>{let t={...e,id:"e".concat(C.length+1),type:"smoothstep",animated:!0};S(e=>(0,i.rN)(t,e)),K(!0)},[C.length,S]),H=(0,s.useCallback)((e,t)=>{O(t)},[]),$=(0,s.useCallback)(()=>{O(null),T(null)},[]),ee=(0,s.useCallback)((e,t)=>{e.preventDefault(),T({id:t.id,type:"node",nodeType:t.type,x:e.clientX,y:e.clientY})},[]),et=(0,s.useCallback)((e,t)=>{e.preventDefault(),T({id:t.id,type:"edge",x:e.clientX,y:e.clientY})},[]),eo=(0,s.useCallback)(e=>{["user-request","classifier","output"].includes(e)||(b(t=>t.filter(t=>t.id!==e)),S(t=>t.filter(t=>t.source!==e&&t.target!==e)),K(!0),(null==N?void 0:N.id)===e&&O(null))},[N,b,S]),ea=(0,s.useCallback)(e=>{S(t=>t.filter(t=>t.id!==e)),K(!0)},[S]),es=(0,s.useCallback)(e=>{let t=h.find(t=>t.id===e);if(!t)return;let o={...t,id:"".concat(t.type,"-").concat(Date.now()),position:{x:t.position.x+50,y:t.position.y+50},data:{...t.data,label:"".concat(t.data.label," Copy")}};b(e=>[...e,o]),K(!0)},[h,b]),er=(0,s.useCallback)(e=>{let t=h.find(t=>t.id===e);t&&O(t)},[h]),ei=async()=>{k||"new"!==g?await el():F({isOpen:!0,mode:"save"})},en=async(e,t)=>{M(!0);try{let o=await fetch("/api/workflows",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e,description:t,nodes:h,edges:C,settings:{}})});if(!o.ok){let e=await o.json();throw Error(e.details||"Failed to save workflow")}let a=await o.json();F({isOpen:!0,mode:"success",apiKey:a.api_key,workflowName:e}),V(a.workflow.id)}catch(e){F({isOpen:!0,mode:"error",errorMessage:e instanceof Error?e.message:"Unknown error"})}finally{M(!1)}},el=async()=>{if(k){M(!0);try{let e=await fetch("/api/workflows",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:g,name:null==k?void 0:k.name,description:null==k?void 0:k.description,nodes:h,edges:C,settings:(null==k?void 0:k.settings)||{}})});if(!e.ok){let t=await e.json();throw Error(t.details||"Failed to update workflow")}K(!1),F({isOpen:!0,mode:"success",workflowName:(null==k?void 0:k.name)||"Workflow"})}catch(e){F({isOpen:!0,mode:"error",errorMessage:e instanceof Error?e.message:"Unknown error"})}finally{M(!1)}}},ed=(e,t)=>{b(o=>o.map(o=>o.id===e?{...o,data:{...o.data,...t}}:o)),K(!0)};return E?(0,a.jsx)("div",{className:"h-screen bg-[#040716] flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-white",children:"Loading workflow..."})}):(0,a.jsx)(f.Ay,{showDetails:!1,onError:(e,t)=>{let o={id:"error-".concat(Date.now()),nodeId:"editor",nodeType:"editor",nodeLabel:"Workflow Editor",message:e.message,timestamp:new Date().toISOString(),attempt:1,maxRetries:3,status:"pending",recoveryStrategies:[{type:"retry",description:"Reload the editor",available:!0,recommended:!0}]};z(e=>[...e,o]),X(!0)},children:(0,a.jsxs)("div",{className:"h-screen bg-[#040716] flex flex-col",children:[(0,a.jsx)(n.A,{workflow:k,isDirty:_,isSaving:D,onSave:ei,onExecute:()=>{(null==k?void 0:k.id)?window.open("/playground?workflow=".concat(k.id),"_blank"):F({isOpen:!0,mode:"error",errorMessage:"Please save the workflow first to test it in the playground"})},onBack:()=>m.push("/manual-build"),onShare:()=>Z(!0)}),(0,a.jsxs)("div",{className:"flex-1 flex",children:[(0,a.jsx)(l.A,{onAddNode:(e,t)=>{let o={},a=!0;"provider"===e?(o={providerId:"",modelId:"",apiKey:"",parameters:{temperature:1,maxTokens:void 0,topP:void 0,frequencyPenalty:void 0,presencePenalty:void 0}},a=!1):"centralRouter"===e&&(o={routingStrategy:"smart",fallbackProvider:"",maxRetries:3,timeout:3e4,enableCaching:!0,debugMode:!1},a=!0);let s={id:"".concat(e,"-").concat(Date.now()),type:e,position:t,data:{label:"centralRouter"===e?"Central Router":e.charAt(0).toUpperCase()+e.slice(1),config:o,isConfigured:a,description:"".concat(e," node")}};b(e=>[...e,s]),K(!0)}}),(0,a.jsxs)("div",{className:"flex-1 relative manual-build-canvas",children:[(0,a.jsxs)(i.Gc,{nodes:h,edges:C,onNodesChange:x,onEdgesChange:j,onConnect:G,onNodeClick:H,onNodeContextMenu:ee,onEdgeContextMenu:et,onPaneClick:$,nodeTypes:u.c_,fitView:!0,className:"bg-[#040716]",defaultViewport:{x:0,y:0,zoom:.8},minZoom:.1,maxZoom:2,connectionLineStyle:{stroke:"#ff6b35",strokeWidth:2},defaultEdgeOptions:{style:{stroke:"#ff6b35",strokeWidth:2},type:"smoothstep",animated:!0},proOptions:{hideAttribution:!0},children:[(0,a.jsx)(i.VS,{color:"#1f2937",gap:20,size:1}),(0,a.jsx)(i.H2,{className:"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm",showInteractive:!1}),(0,a.jsx)(i.of,{className:"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm",nodeColor:"#ff6b35",maskColor:"rgba(0, 0, 0, 0.2)"})]}),R&&(0,a.jsx)(c.A,{id:R.id,type:R.type,nodeType:R.nodeType,top:R.y,left:R.x,onClose:()=>T(null),onDelete:"node"===R.type?eo:ea,onDuplicate:"node"===R.type?es:void 0,onConfigure:"node"===R.type?er:void 0,onDisconnect:"edge"===R.type?ea:void 0})]}),N&&(0,a.jsx)(d.A,{node:N,onUpdate:e=>ed(N.id,e),onClose:()=>O(null)})]}),(0,a.jsx)(y.A,{errors:Y,onRetry:e=>{},onSkip:e=>{},onManualFix:e=>{},isVisible:L,onClose:()=>X(!1)}),k&&(0,a.jsx)(w.A,{workflowId:k.id,workflowName:k.name,isOpen:J,onClose:()=>Z(!1)}),(0,a.jsx)(A.A,{isOpen:q.isOpen,mode:q.mode,apiKey:q.apiKey,workflowName:q.workflowName,errorMessage:q.errorMessage,isSaving:D,onClose:()=>{let e="success"===q.mode&&q.apiKey;F({isOpen:!1,mode:"save"}),e&&"new"===g&&I&&m.push("/manual-build/".concat(I))},onSave:en})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8946,7874,5738,2396,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6050,9061,8530,6213,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(72396)),_N_E=e.O()}]);