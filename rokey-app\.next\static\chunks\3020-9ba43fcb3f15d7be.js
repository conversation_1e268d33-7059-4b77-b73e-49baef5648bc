(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3020],{1243:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},24357:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},29300:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=a(e,function(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=a(t,n));return t}(n)))}return e}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=(function(){return o}).apply(t,[]))||(e.exports=n)}()},30463:(e,t,n)=>{"use strict";n.d(t,{bm:()=>td,UC:()=>tl,VY:()=>tc,hJ:()=>tu,ZL:()=>ti,bL:()=>to,hE:()=>ts,l9:()=>ta});var r,o,a,i=n(12115),u=n.t(i,2);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var s=n(6101),c=n(95155),d=globalThis?.document?i.useLayoutEffect:()=>{},f=u[" useId ".trim().toString()]||(()=>void 0),m=0;function h(e){let[t,n]=i.useState(f());return d(()=>{e||n(e=>e??String(m++))},[e]),e||(t?`radix-${t}`:"")}u[" useEffectEvent ".trim().toString()],u[" useInsertionEffect ".trim().toString()];var v=u[" useInsertionEffect ".trim().toString()]||d,p=(Symbol("RADIX:SYNC_STATE"),n(63655));function g(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}var y="dismissableLayer.update",b=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=i.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:m,onDismiss:h,...v}=e,w=i.useContext(b),[k,C]=i.useState(null),S=null!=(r=null==k?void 0:k.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,x]=i.useState({}),D=(0,s.s)(t,e=>C(e)),A=Array.from(w.layers),[N]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),P=A.indexOf(N),T=k?A.indexOf(k):-1,O=w.layersWithOutsidePointerEventsDisabled.size>0,R=T>=P,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=g(e),o=i.useRef(!1),a=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){M("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...w.branches].some(e=>e.contains(t));R&&!n&&(null==d||d(e),null==m||m(e),e.defaultPrevented||null==h||h())},S),W=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=g(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&M("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...w.branches].some(e=>e.contains(t))&&(null==f||f(e),null==m||m(e),e.defaultPrevented||null==h||h())},S);return!function(e,t=globalThis?.document){let n=g(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{T===w.layers.size-1&&(null==u||u(e),!e.defaultPrevented&&h&&(e.preventDefault(),h()))},S),i.useEffect(()=>{if(k)return a&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(o=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(k)),w.layers.add(k),E(),()=>{a&&1===w.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=o)}},[k,S,a,w]),i.useEffect(()=>()=>{k&&(w.layers.delete(k),w.layersWithOutsidePointerEventsDisabled.delete(k),E())},[k,w]),i.useEffect(()=>{let e=()=>x({});return document.addEventListener(y,e),()=>document.removeEventListener(y,e)},[]),(0,c.jsx)(p.sG.div,{...v,ref:D,style:{pointerEvents:O?R?"auto":"none":void 0,...e.style},onFocusCapture:l(e.onFocusCapture,W.onFocusCapture),onBlurCapture:l(e.onBlurCapture,W.onBlurCapture),onPointerDownCapture:l(e.onPointerDownCapture,j.onPointerDownCapture)})});function E(){let e=new CustomEvent(y);document.dispatchEvent(e)}function M(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,p.hO)(a,i):a.dispatchEvent(i)}w.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(b),r=i.useRef(null),o=(0,s.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(p.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var k="focusScope.autoFocusOnMount",C="focusScope.autoFocusOnUnmount",S={bubbles:!1,cancelable:!0},x=i.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...u}=e,[l,d]=i.useState(null),f=g(o),m=g(a),h=i.useRef(null),v=(0,s.s)(t,e=>d(e)),y=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let e=function(e){if(y.paused||!l)return;let t=e.target;l.contains(t)?h.current=t:N(h.current,{select:!0})},t=function(e){if(y.paused||!l)return;let t=e.relatedTarget;null!==t&&(l.contains(t)||N(h.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&N(l)});return l&&n.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,l,y.paused]),i.useEffect(()=>{if(l){P.add(y);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(k,S);l.addEventListener(k,f),l.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(N(r,{select:t}),document.activeElement!==n)return}(D(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&N(l))}return()=>{l.removeEventListener(k,f),setTimeout(()=>{let t=new CustomEvent(C,S);l.addEventListener(C,m),l.dispatchEvent(t),t.defaultPrevented||N(null!=e?e:document.body,{select:!0}),l.removeEventListener(C,m),P.remove(y)},0)}}},[l,f,m,y]);let b=i.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=D(e);return[A(t,e),A(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&N(a,{select:!0})):(e.preventDefault(),n&&N(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,c.jsx)(p.sG.div,{tabIndex:-1,...u,ref:v,onKeyDown:b})});function D(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function A(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function N(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}x.displayName="FocusScope";var P=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=T(e,t)).unshift(t)},remove(t){var n;null==(n=(e=T(e,t))[0])||n.resume()}}}();function T(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var O=n(47650),R=i.forwardRef((e,t)=>{var n,r;let{container:o,...a}=e,[u,l]=i.useState(!1);d(()=>l(!0),[]);let s=o||u&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return s?O.createPortal((0,c.jsx)(p.sG.div,{...a,ref:t}),s):null});R.displayName="Portal";var j=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=i.useState(),a=i.useRef(null),u=i.useRef(e),l=i.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return i.useEffect(()=>{let e=W(a.current);l.current="mounted"===s?e:"none"},[s]),d(()=>{let t=a.current,n=u.current;if(n!==e){let r=l.current,o=W(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),u.current=e}},[e,c]),d(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=W(a.current).includes(e.animationName);if(e.target===r&&o&&(c("ANIMATION_END"),!u.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(l.current=W(a.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}c("ANIMATION_END")},[r,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:i.useCallback(e=>{a.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):i.Children.only(n),a=(0,s.s)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?i.cloneElement(o,{ref:a}):null};function W(e){return(null==e?void 0:e.animationName)||"none"}j.displayName="Presence";var L=0;function F(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var I=function(){return(I=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function _(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var X=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),Y="width-before-scroll-bar";function U(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var B="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,z=new WeakMap,q=function(){return null},H=new WeakMap;function V(e){return e}new WeakMap;var $=function(e){var t=e.sideCar,n=_(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,I({},n))};$.isSideCarExport=!0;var G=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=V),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return a.options=I({async:!0,ssr:!1},e),a}(),J=function(){},K=i.forwardRef(function(e,t){var n,r,o,a,u=i.useRef(null),l=i.useState({onScrollCapture:J,onWheelCapture:J,onTouchMoveCapture:J}),s=l[0],c=l[1],d=e.forwardProps,f=e.children,m=e.className,h=e.removeScrollBar,v=e.enabled,p=e.shards,g=e.sideCar,y=e.noRelative,b=e.noIsolation,w=e.inert,E=e.allowPinchZoom,M=e.as,k=e.gapMode,C=_(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(n=[u,t],r=function(e){return n.forEach(function(t){return U(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,a=o.facade,B(function(){var e=z.get(a);if(e){var t=new Set(e),r=new Set(n),o=a.current;t.forEach(function(e){r.has(e)||U(e,null)}),r.forEach(function(e){t.has(e)||U(e,o)})}z.set(a,n)},[n]),a),x=I(I({},C),s);return i.createElement(i.Fragment,null,v&&i.createElement(g,{sideCar:G,removeScrollBar:h,shards:p,noRelative:y,noIsolation:b,inert:w,setCallbacks:c,allowPinchZoom:!!E,lockRef:u,gapMode:k}),d?i.cloneElement(i.Children.only(f),I(I({},x),{ref:S})):i.createElement(void 0===M?"div":M,I({},x,{className:m,ref:S}),f))});K.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},K.classNames={fullWidth:Y,zeroRight:X};var Z=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=a||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Q=function(){var e=Z();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},ee=function(){var e=Q();return function(t){return e(t.styles,t.dynamic),null}},et={left:0,top:0,right:0,gap:0},en=function(e){return parseInt(e||"",10)||0},er=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[en(n),en(r),en(o)]},eo=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return et;var t=er(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},ea=ee(),ei="data-scroll-locked",eu=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(ei,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(X," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(Y," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(X," .").concat(X," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(Y," .").concat(Y," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(ei,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},el=function(){var e=parseInt(document.body.getAttribute(ei)||"0",10);return isFinite(e)?e:0},es=function(){i.useEffect(function(){return document.body.setAttribute(ei,(el()+1).toString()),function(){var e=el()-1;e<=0?document.body.removeAttribute(ei):document.body.setAttribute(ei,e.toString())}},[])},ec=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;es();var a=i.useMemo(function(){return eo(o)},[o]);return i.createElement(ea,{styles:eu(a,!t,o,n?"":"!important")})},ed=!1;if("undefined"!=typeof window)try{var ef=Object.defineProperty({},"passive",{get:function(){return ed=!0,!0}});window.addEventListener("test",ef,ef),window.removeEventListener("test",ef,ef)}catch(e){ed=!1}var em=!!ed&&{passive:!1},eh=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},ev=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),ep(e,r)){var o=eg(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ep=function(e,t){return"v"===e?eh(t,"overflowY"):eh(t,"overflowX")},eg=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ey=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=i*r,l=n.target,s=t.contains(l),c=!1,d=u>0,f=0,m=0;do{if(!l)break;var h=eg(e,l),v=h[0],p=h[1]-h[2]-i*v;(v||p)&&ep(e,l)&&(f+=p,m+=v);var g=l.parentNode;l=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!s&&l!==document.body||s&&(t.contains(l)||t===l));return d&&(o&&1>Math.abs(f)||!o&&u>f)?c=!0:!d&&(o&&1>Math.abs(m)||!o&&-u>m)&&(c=!0),c},eb=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ew=function(e){return[e.deltaX,e.deltaY]},eE=function(e){return e&&"current"in e?e.current:e},eM=0,ek=[];let eC=(r=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(eM++)[0],a=i.useState(ee)[0],u=i.useRef(e);i.useEffect(function(){u.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eE),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,a=eb(e),i=n.current,l="deltaX"in e?e.deltaX:i[0]-a[0],s="deltaY"in e?e.deltaY:i[1]-a[1],c=e.target,d=Math.abs(l)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=ev(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=ev(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||s)&&(r.current=o),!o)return!0;var m=r.current||o;return ey(m,t,e,"h"===m?l:s,!0)},[]),s=i.useCallback(function(e){if(ek.length&&ek[ek.length-1]===a){var n="deltaY"in e?ew(e):eb(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(eE).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=i.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=i.useCallback(function(e){n.current=eb(e),r.current=void 0},[]),f=i.useCallback(function(t){c(t.type,ew(t),t.target,l(t,e.lockRef.current))},[]),m=i.useCallback(function(t){c(t.type,eb(t),t.target,l(t,e.lockRef.current))},[]);i.useEffect(function(){return ek.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:m}),document.addEventListener("wheel",s,em),document.addEventListener("touchmove",s,em),document.addEventListener("touchstart",d,em),function(){ek=ek.filter(function(e){return e!==a}),document.removeEventListener("wheel",s,em),document.removeEventListener("touchmove",s,em),document.removeEventListener("touchstart",d,em)}},[]);var h=e.removeScrollBar,v=e.inert;return i.createElement(i.Fragment,null,v?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?i.createElement(ec,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},G.useMedium(r),$);var eS=i.forwardRef(function(e,t){return i.createElement(K,I({},e,{ref:t,sideCar:eC}))});eS.classNames=K.classNames;var ex=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},eD=new WeakMap,eA=new WeakMap,eN={},eP=0,eT=function(e){return e&&(e.host||eT(e.parentNode))},eO=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=eT(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eN[n]||(eN[n]=new WeakMap);var a=eN[n],i=[],u=new Set,l=new Set(o),s=function(e){!e||u.has(e)||(u.add(e),s(e.parentNode))};o.forEach(s);var c=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(u.has(e))c(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,l=(eD.get(e)||0)+1,s=(a.get(e)||0)+1;eD.set(e,l),a.set(e,s),i.push(e),1===l&&o&&eA.set(e,!0),1===s&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return c(t),u.clear(),eP++,function(){i.forEach(function(e){var t=eD.get(e)-1,o=a.get(e)-1;eD.set(e,t),a.set(e,o),t||(eA.has(e)||e.removeAttribute(r),eA.delete(e)),o||e.removeAttribute(n)}),--eP||(eD=new WeakMap,eD=new WeakMap,eA=new WeakMap,eN={})}},eR=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||ex(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),eO(r,o,n,"aria-hidden")):function(){return null}},ej=n(99708),eW="Dialog",[eL,eF]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>i.createContext(e));return function(n){let r=n?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=i.createContext(r),a=n.length;n=[...n,r];let u=t=>{let{scope:n,children:r,...u}=t,l=n?.[e]?.[a]||o,s=i.useMemo(()=>u,Object.values(u));return(0,c.jsx)(l.Provider,{value:s,children:r})};return u.displayName=t+"Provider",[u,function(n,u){let l=u?.[e]?.[a]||o,s=i.useContext(l);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(eW),[eI,e_]=eL(eW),eX=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:u=!0}=e,l=i.useRef(null),s=i.useRef(null),[d,f]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=i.useState(e),o=i.useRef(n),a=i.useRef(t);return v(()=>{a.current=t},[t]),i.useEffect(()=>{o.current!==n&&(a.current?.(n),o.current=n)},[n,o]),[n,r,a]}({defaultProp:t,onChange:n}),l=void 0!==e,s=l?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[s,i.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[l,e,a,u])]}({prop:r,defaultProp:null!=o&&o,onChange:a,caller:eW});return(0,c.jsx)(eI,{scope:t,triggerRef:l,contentRef:s,contentId:h(),titleId:h(),descriptionId:h(),open:d,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(e=>!e),[f]),modal:u,children:n})};eX.displayName=eW;var eY="DialogTrigger",eU=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=e_(eY,n),a=(0,s.s)(t,o.triggerRef);return(0,c.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":e3(o.open),...r,ref:a,onClick:l(e.onClick,o.onOpenToggle)})});eU.displayName=eY;var eB="DialogPortal",[ez,eq]=eL(eB,{forceMount:void 0}),eH=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=e_(eB,t);return(0,c.jsx)(ez,{scope:t,forceMount:n,children:i.Children.map(r,e=>(0,c.jsx)(j,{present:n||a.open,children:(0,c.jsx)(R,{asChild:!0,container:o,children:e})}))})};eH.displayName=eB;var eV="DialogOverlay",e$=i.forwardRef((e,t)=>{let n=eq(eV,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=e_(eV,e.__scopeDialog);return a.modal?(0,c.jsx)(j,{present:r||a.open,children:(0,c.jsx)(eJ,{...o,ref:t})}):null});e$.displayName=eV;var eG=(0,ej.TL)("DialogOverlay.RemoveScroll"),eJ=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=e_(eV,n);return(0,c.jsx)(eS,{as:eG,allowPinchZoom:!0,shards:[o.contentRef],children:(0,c.jsx)(p.sG.div,{"data-state":e3(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eK="DialogContent",eZ=i.forwardRef((e,t)=>{let n=eq(eK,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=e_(eK,e.__scopeDialog);return(0,c.jsx)(j,{present:r||a.open,children:a.modal?(0,c.jsx)(eQ,{...o,ref:t}):(0,c.jsx)(e0,{...o,ref:t})})});eZ.displayName=eK;var eQ=i.forwardRef((e,t)=>{let n=e_(eK,e.__scopeDialog),r=i.useRef(null),o=(0,s.s)(t,n.contentRef,r);return i.useEffect(()=>{let e=r.current;if(e)return eR(e)},[]),(0,c.jsx)(e1,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:l(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:l(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:l(e.onFocusOutside,e=>e.preventDefault())})}),e0=i.forwardRef((e,t)=>{let n=e_(eK,e.__scopeDialog),r=i.useRef(!1),o=i.useRef(!1);return(0,c.jsx)(e1,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,i;null==(a=e.onCloseAutoFocus)||a.call(e,t),t.defaultPrevented||(r.current||null==(i=n.triggerRef.current)||i.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var a,i;null==(a=e.onInteractOutside)||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let u=t.target;(null==(i=n.triggerRef.current)?void 0:i.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),e1=i.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...u}=e,l=e_(eK,n),d=i.useRef(null),f=(0,s.s)(t,d);return i.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:F()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:F()),L++,()=>{1===L&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),L--}},[]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(x,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,c.jsx)(w,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":e3(l.open),...u,ref:f,onDismiss:()=>l.onOpenChange(!1)})}),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(tn,{titleId:l.titleId}),(0,c.jsx)(tr,{contentRef:d,descriptionId:l.descriptionId})]})]})}),e2="DialogTitle",e4=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=e_(e2,n);return(0,c.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});e4.displayName=e2;var e9="DialogDescription",e5=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=e_(e9,n);return(0,c.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});e5.displayName=e9;var e6="DialogClose",e7=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=e_(e6,n);return(0,c.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:l(e.onClick,()=>o.onOpenChange(!1))})});function e3(e){return e?"open":"closed"}e7.displayName=e6;var e8="DialogTitleWarning",[te,tt]=function(e,t){let n=i.createContext(t),r=e=>{let{children:t,...r}=e,o=i.useMemo(()=>r,Object.values(r));return(0,c.jsx)(n.Provider,{value:o,children:t})};return r.displayName=e+"Provider",[r,function(r){let o=i.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}(e8,{contentName:eK,titleName:e2,docsSlug:"dialog"}),tn=e=>{let{titleId:t}=e,n=tt(e8),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return i.useEffect(()=>{t&&document.getElementById(t)},[r,t]),null},tr=e=>{let{contentRef:t,descriptionId:n}=e,r=tt("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return i.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&document.getElementById(n)},[o,t,n]),null},to=eX,ta=eU,ti=eH,tu=e$,tl=eZ,ts=e4,tc=e5,td=e7},34869:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},53904:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},62525:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},69074:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},69803:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},75525:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},76804:(e,t,n)=>{"use strict";n.d(t,{m:()=>g});let r=Symbol.for("constructDateFrom");function o(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&r in e?e[r](t):e instanceof Date?new e.constructor(t):new Date(t)}let a={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function i(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}let u={date:i({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:i({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:i({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},l={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function s(e){return(t,n)=>{let r;if("formatting"===((null==n?void 0:n.context)?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,o=(null==n?void 0:n.width)?String(n.width):t;r=e.formattingValues[o]||e.formattingValues[t]}else{let t=e.defaultWidth,o=(null==n?void 0:n.width)?String(n.width):e.defaultWidth;r=e.values[o]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function c(e){return function(t){let n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=r.width,a=o&&e.matchPatterns[o]||e.matchPatterns[e.defaultMatchWidth],i=t.match(a);if(!i)return null;let u=i[0],l=o&&e.parsePatterns[o]||e.parsePatterns[e.defaultParseWidth],s=Array.isArray(l)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(l,e=>e.test(u)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(l,e=>e.test(u));return n=e.valueCallback?e.valueCallback(s):s,{value:n=r.valueCallback?r.valueCallback(n):n,rest:t.slice(u.length)}}}let d={code:"en-US",formatDistance:(e,t,n)=>{let r,o=a[e];if(r="string"==typeof o?o:1===t?o.one:o.other.replace("{{count}}",t.toString()),null==n?void 0:n.addSuffix)if(n.comparison&&n.comparison>0)return"in "+r;else return r+" ago";return r},formatLong:u,formatRelative:(e,t,n,r)=>l[e],localize:{ordinalNumber:(e,t)=>{let n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:s({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:s({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:s({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:s({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:s({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.match(e.matchPattern);if(!r)return null;let o=r[0],a=t.match(e.parsePattern);if(!a)return null;let i=e.valueCallback?e.valueCallback(a[0]):a[0];return{value:i=n.valueCallback?n.valueCallback(i):i,rest:t.slice(o.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:c({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:c({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:c({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:c({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:c({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},f={};function m(e,t){return o(t||e,e)}function h(e){let t=m(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),e-n}function v(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];let a=o.bind(null,e||n.find(e=>"object"==typeof e));return n.map(a)}function p(e,t){let n=m(e)-m(t);return n<0?-1:n>0?1:n}function g(e,t){return function(e,t,n){var r,o;let a,i=null!=(o=null!=(r=null==n?void 0:n.locale)?r:f.locale)?o:d,u=p(e,t);if(isNaN(u))throw RangeError("Invalid time value");let l=Object.assign({},n,{addSuffix:null==n?void 0:n.addSuffix,comparison:u}),[s,c]=v(null==n?void 0:n.in,...u>0?[t,e]:[e,t]),g=function(e,t,n){var r;return(r=void 0,e=>{let t=(r?Math[r]:Math.trunc)(e);return 0===t?0:t})((m(e)-m(t))/1e3)}(c,s),y=Math.round((g-(h(c)-h(s))/1e3)/60);if(y<2)if(null==n?void 0:n.includeSeconds)if(g<5)return i.formatDistance("lessThanXSeconds",5,l);else if(g<10)return i.formatDistance("lessThanXSeconds",10,l);else if(g<20)return i.formatDistance("lessThanXSeconds",20,l);else if(g<40)return i.formatDistance("halfAMinute",0,l);else if(g<60)return i.formatDistance("lessThanXMinutes",1,l);else return i.formatDistance("xMinutes",1,l);else if(0===y)return i.formatDistance("lessThanXMinutes",1,l);else return i.formatDistance("xMinutes",y,l);if(y<45)return i.formatDistance("xMinutes",y,l);if(y<90)return i.formatDistance("aboutXHours",1,l);if(y<1440){let e=Math.round(y/60);return i.formatDistance("aboutXHours",e,l)}if(y<2520)return i.formatDistance("xDays",1,l);else if(y<43200){let e=Math.round(y/1440);return i.formatDistance("xDays",e,l)}else if(y<86400)return a=Math.round(y/43200),i.formatDistance("aboutXMonths",a,l);if((a=function(e,t,n){let[r,o,a]=v(void 0,e,e,t),i=p(o,a),u=Math.abs(function(e,t,n){let[r,o]=v(void 0,e,t);return 12*(r.getFullYear()-o.getFullYear())+(r.getMonth()-o.getMonth())}(o,a));if(u<1)return 0;1===o.getMonth()&&o.getDate()>27&&o.setDate(30),o.setMonth(o.getMonth()-i*u);let l=p(o,a)===-i;(function(e,t){let n=m(e,void 0);return+function(e,t){let n=m(e,null==t?void 0:t.in);return n.setHours(23,59,59,999),n}(n,void 0)==+function(e,t){let n=m(e,null==t?void 0:t.in),r=n.getMonth();return n.setFullYear(n.getFullYear(),r+1,0),n.setHours(23,59,59,999),n}(n,t)})(r)&&1===u&&1===p(r,a)&&(l=!1);let s=i*(u-l);return 0===s?0:s}(c,s))<12){let e=Math.round(y/43200);return i.formatDistance("xMonths",e,l)}{let e=a%12,t=Math.trunc(a/12);return e<3?i.formatDistance("aboutXYears",t,l):e<9?i.formatDistance("overXYears",t,l):i.formatDistance("almostXYears",t+1,l)}}(e,o(e,Date.now()),t)}},78749:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},79397:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},84616:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},92657:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);