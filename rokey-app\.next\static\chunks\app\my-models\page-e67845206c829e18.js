(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5690],{68492:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>j});var t=s(95155),r=s(12115),l=s(6874),n=s.n(l),i=s(72227),o=s(94038),c=s(61316),d=s(85037),m=s(31151),x=s(80377),u=s(87162),g=s(74338),h=s(28003),f=s(60993),b=s(6875),p=s(83298);function j(){let[e,a]=(0,r.useState)([]),[s,l]=(0,r.useState)(!0),[j,y]=(0,r.useState)(null),[N,w]=(0,r.useState)(""),[v,C]=(0,r.useState)(!1),k=(0,u.Z)(),[A,M]=(0,r.useState)(!1),{createHoverPrefetch:S,prefetchManageKeysData:T}=(0,h._)(),{subscriptionStatus:E,user:P}=(0,p.R)(),D=async()=>{l(!0),y(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let a=await e.json();throw Error(a.error||"Failed to fetch configurations")}let s=await e.json();a(s)}catch(e){y(e.message)}finally{l(!1)}};(0,r.useEffect)(()=>{P?D():null===P&&l(!1)},[P]);let I=async e=>{if(e.preventDefault(),!N.trim())return void y("Configuration name cannot be empty.");C(!0),y(null);try{let e=await fetch("/api/custom-configs",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:N})}),a=await e.json();if(!e.ok)throw Error(a.details||a.error||"Failed to create configuration");w(""),M(!1),await D()}catch(e){y(e.message)}finally{C(!1)}},F=(e,a)=>{k.showConfirmation({title:"Delete Configuration",message:'Are you sure you want to delete "'.concat(a,'"? This will permanently remove the configuration and all associated API keys. This action cannot be undone.'),confirmText:"Delete Configuration",cancelText:"Cancel",type:"danger"},async()=>{y(null);try{let a=await fetch("/api/custom-configs/".concat(e),{method:"DELETE"}),s=await a.json();if(!a.ok)throw Error(s.details||s.error||"Failed to delete configuration");await D()}catch(e){throw y("Failed to delete: ".concat(e.message)),e}})};return(0,t.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:(0,t.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-4xl font-bold mb-2",children:(0,t.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400",children:"My API Models"})}),(0,t.jsx)("p",{className:"text-gray-400 mt-2",children:"Manage your custom API configurations and keys"})]}),(0,t.jsx)(f.sU,{feature:"configurations",currentCount:e.length,customMessage:"You've reached your configuration limit. Upgrade to create more API configurations and organize your models better.",fallback:(0,t.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,t.jsxs)("button",{disabled:!0,className:"btn-primary opacity-50 cursor-not-allowed",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Create New Model"]}),(0,t.jsx)("p",{className:"text-xs text-orange-600 font-medium",children:(null==E?void 0:E.tier)==="free"?"Upgrade to Starter for more configurations":"Configuration limit reached - upgrade for more"})]}),children:(0,t.jsxs)("button",{onClick:()=>M(!A),className:A?"btn-secondary-dark":"btn-primary",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),A?"Cancel":"Create New Model"]})})]}),(null==E?void 0:E.tier)==="free"&&(0,t.jsx)(b.L,{message:"Unlock intelligent routing and 15 configurations",variant:"compact",className:"mb-4"}),(0,t.jsx)(b.p,{className:"mb-4"}),j&&(0,t.jsx)("div",{className:"bg-red-900/50 backdrop-blur-sm border border-red-800/50 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,t.jsx)("p",{className:"text-red-300",children:j})]})}),A&&(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg max-w-md animate-scale-in p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Create New Model"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Set up a new API configuration"})]}),(0,t.jsxs)("form",{onSubmit:I,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"configName",className:"block text-sm font-medium text-gray-300 mb-2",children:"Model Name"}),(0,t.jsx)("input",{type:"text",id:"configName",value:N,onChange:e=>w(e.target.value),required:!0,className:"w-full px-3 py-2 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",placeholder:"e.g., My Main Chat Assistant"})]}),(0,t.jsx)("button",{type:"submit",disabled:v,className:"btn-primary w-full",children:v?"Creating...":"Create Model"})]})]}),s&&(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,a)=>(0,t.jsx)(g.B0,{},a))}),!s&&!e.length&&!j&&!A&&(0,t.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg text-center py-12",children:(0,t.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-orange-500/20 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-orange-500/30",children:(0,t.jsx)(o.A,{className:"h-8 w-8 text-orange-400"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No API Models Yet"}),(0,t.jsx)("p",{className:"text-gray-400 mb-6",children:"Create your first API model configuration to get started with RoKey."}),(0,t.jsx)(f.sU,{feature:"configurations",currentCount:e.length,customMessage:"Create your first API configuration to get started with RouKey. Free tier includes 1 configuration.",fallback:(0,t.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,t.jsxs)("button",{disabled:!0,className:"btn-primary opacity-50 cursor-not-allowed",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Create Your First Model"]}),(0,t.jsx)("p",{className:"text-xs text-orange-600 font-medium",children:"Upgrade to create configurations"})]}),children:(0,t.jsxs)("button",{onClick:()=>M(!0),className:"btn-primary",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Create Your First Model"]})})]})}),!s&&e.length>0&&(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map((e,a)=>(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 hover:border-gray-700/50 transition-all duration-200 animate-slide-in",style:{animationDelay:"".concat(100*a,"ms")},children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-white mb-2 truncate",children:e.name}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-400",children:[(0,t.jsx)(o.A,{className:"h-3 w-3 mr-1"}),"ID: ",e.id.slice(0,8),"..."]}),(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-400",children:[(0,t.jsx)(i.A,{className:"h-3 w-3 mr-1"}),"Created: ",new Date(e.created_at).toLocaleDateString()]})]})]}),(0,t.jsx)("div",{className:"w-12 h-12 bg-orange-500/20 rounded-xl flex items-center justify-center shrink-0 border border-orange-500/30",children:(0,t.jsx)(o.A,{className:"h-6 w-6 text-orange-400"})})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 mt-6",children:[(0,t.jsx)(n(),{href:"/my-models/".concat(e.id),className:"flex-1",...S(e.id),children:(0,t.jsxs)("button",{className:"btn-primary w-full",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Manage Keys"]})}),(0,t.jsxs)("button",{onClick:()=>F(e.id,e.name),className:"btn-secondary-dark text-red-400 hover:text-red-300 hover:bg-red-900/20",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]},e.id))}),(0,t.jsx)(x.A,{isOpen:k.isOpen,onClose:k.hideConfirmation,onConfirm:k.onConfirm,title:k.title,message:k.message,confirmText:k.confirmText,cancelText:k.cancelText,type:k.type,isLoading:k.isLoading})]})})}},69528:(e,a,s)=>{Promise.resolve().then(s.bind(s,68492))}},e=>{var a=a=>e(e.s=a);e.O(0,[8888,1459,5738,2396,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>a(69528)),_N_E=e.O()}]);