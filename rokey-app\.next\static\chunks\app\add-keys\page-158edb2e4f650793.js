(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5833],{76805:(e,l,a)=>{Promise.resolve().then(a.bind(a,81767))},81767:(e,l,a)=>{"use strict";a.r(l),a.d(l,{default:()=>c});var t=a(95155),s=a(12115),r=a(75922),d=a(80377),n=a(87162);let o=r.MG.map(e=>({value:e.id,label:e.name})),i=e=>{let l=r.MG.find(l=>l.id===e);return l?l.models.map(e=>({value:e.id,label:e.name})):[]};function c(){var e;let l=(0,n.Z)(),[a,r]=(0,s.useState)((null==(e=o[0])?void 0:e.value)||"openai"),[c,u]=(0,s.useState)(""),[x,m]=(0,s.useState)(""),[h,p]=(0,s.useState)(""),[y,b]=(0,s.useState)(!1),[g,f]=(0,s.useState)(null),[v,j]=(0,s.useState)(null),[N,w]=(0,s.useState)([]),[k,S]=(0,s.useState)(!0),[_,A]=(0,s.useState)(null),P=async()=>{S(!0);let e=null;try{let e=await fetch("/api/keys");if(!e.ok){let l=await e.json();throw Error(l.error||"Failed to fetch keys")}let l=await e.json();w(l),f(null)}catch(e){f("Error fetching keys: ".concat(e.message))}S(!1)};(0,s.useEffect)(()=>{P()},[]),(0,s.useEffect)(()=>{var e;u((null==(e=i(a)[0])?void 0:e.value)||"")},[a]);let I=async e=>{e.preventDefault(),b(!0),f(null),j(null);try{var l;let e=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:a,predefined_model_id:c,api_key_raw:x,label:h,custom_api_config_id:""})}),t=await e.json();if(!e.ok)throw Error(t.details||t.error||"Failed to save API key");j('API key "'.concat(h,'" saved successfully!')),r((null==(l=o[0])?void 0:l.value)||"openai"),u(""),m(""),p(""),await P()}catch(e){f(e.message),j(null)}b(!1)},C=(e,a)=>{l.showConfirmation({title:"Delete API Key",message:'Are you sure you want to delete the API key "'.concat(a,'"? This action cannot be undone.'),confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{A(e),f(null),j(null);try{let l=await fetch("/api/keys/".concat(e),{method:"DELETE"}),t=await l.json();if(!l.ok)throw Error(t.details||t.error||"Failed to delete API key");j('API key "'.concat(a,'" deleted successfully!')),w(l=>l.filter(l=>l.id!==e))}catch(e){throw f(e.message),j(null),e}finally{A(null)}})},E=i(a);return(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Add API Key"}),(0,t.jsxs)("form",{onSubmit:I,className:"space-y-6 bg-gray-800 p-6 rounded-lg shadow-xl max-w-lg",children:[g&&(0,t.jsxs)("p",{className:"text-red-400 bg-red-900/50 p-3 rounded-md",children:["Error: ",g]}),v&&(0,t.jsx)("p",{className:"text-green-400 bg-green-900/50 p-3 rounded-md",children:v}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-300 mb-1",children:"Provider"}),(0,t.jsx)("select",{id:"provider",value:a,onChange:e=>r(e.target.value),className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500",children:o.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"modelId",className:"block text-sm font-medium text-gray-300 mb-1",children:"Model ID"}),(0,t.jsx)("select",{id:"modelId",value:c,onChange:e=>u(e.target.value),disabled:!E.length,className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50",children:E.length>0?E.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value)):(0,t.jsx)("option",{value:"",disabled:!0,children:"Select a provider first or no models configured"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"apiKey",className:"block text-sm font-medium text-gray-300 mb-1",children:"API Key"}),(0,t.jsx)("input",{type:"password",id:"apiKey",value:x,onChange:e=>m(e.target.value),required:!0,className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter your API key"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-300 mb-1",children:"Label"}),(0,t.jsx)("input",{type:"text",id:"label",value:h,onChange:e=>p(e.target.value),required:!0,className:"w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., My Personal OpenAI Key"})]}),(0,t.jsx)("button",{type:"submit",disabled:y,className:"w-full px-4 py-2.5 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:ring-4 focus:ring-blue-800 disabled:opacity-50 font-medium",children:y?"Saving...":"Save API Key"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Saved API Keys"}),k?(0,t.jsx)("p",{className:"text-gray-400",children:"Loading keys..."}):g&&0===N.length?(0,t.jsxs)("p",{className:"text-red-400 bg-red-900/50 p-3 rounded-md",children:["Could not load keys: ",g.replace("Error fetching keys: ","")]}):0===N.length?(0,t.jsx)("p",{className:"text-gray-400",children:"No API keys saved yet."}):(0,t.jsx)("div",{className:"overflow-x-auto bg-gray-800 p-4 rounded-lg shadow-xl",children:(0,t.jsxs)("table",{className:"min-w-full text-sm text-left text-gray-300",children:[(0,t.jsx)("thead",{className:"text-xs text-gray-400 uppercase bg-gray-700",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Label"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Provider"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Predefined Model ID"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Status"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Created At"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3",children:"Last Used"}),(0,t.jsx)("th",{scope:"col",className:"px-6 py-3",children:(0,t.jsx)("span",{className:"sr-only",children:"Actions"})})]})}),(0,t.jsx)("tbody",{children:N.map(e=>(0,t.jsxs)("tr",{className:"border-b border-gray-700 hover:bg-gray-700/50",children:[(0,t.jsx)("td",{className:"px-6 py-4 font-medium whitespace-nowrap text-white",children:e.label}),(0,t.jsx)("td",{className:"px-6 py-4",children:e.provider}),(0,t.jsx)("td",{className:"px-6 py-4",children:e.predefined_model_id}),(0,t.jsx)("td",{className:"px-6 py-4",children:(0,t.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full \n                        ".concat("active"===e.status?"bg-green-900 text-green-300":"inactive"===e.status?"bg-yellow-900 text-yellow-300":"bg-red-900 text-red-300"),children:e.status})}),(0,t.jsx)("td",{className:"px-6 py-4",children:new Date(e.created_at).toLocaleDateString()}),(0,t.jsx)("td",{className:"px-6 py-4",children:e.last_used_at?new Date(e.last_used_at).toLocaleDateString():"Never"}),(0,t.jsx)("td",{className:"px-6 py-4 text-right",children:(0,t.jsx)("button",{onClick:()=>C(e.id,e.label),disabled:_===e.id,className:"font-medium text-red-500 hover:text-red-400 disabled:opacity-50 disabled:cursor-not-allowed",children:_===e.id?"Deleting...":"Delete"})})]},e.id))})]})})]}),(0,t.jsx)(d.A,{isOpen:l.isOpen,onClose:l.hideConfirmation,onConfirm:l.onConfirm,title:l.title,message:l.message,confirmText:l.confirmText,cancelText:l.cancelText,type:l.type,isLoading:l.isLoading})]})}}},e=>{var l=l=>e(e.s=l);e.O(0,[5738,2396,4842,6419,6210,1826,721,7069,5313,6585,9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>l(76805)),_N_E=e.O()}]);