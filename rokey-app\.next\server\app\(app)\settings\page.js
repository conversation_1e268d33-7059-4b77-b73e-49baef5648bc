(()=>{var e={};e.id=109,e.ids=[109],e.modules={2643:(e,r,t)=>{"use strict";t.d(r,{$:()=>i});var s=t(60687),a=t(43210),n=t(9776);let i=(0,a.forwardRef)(({className:e="",variant:r="default",size:t="default",loading:a=!1,icon:i,iconPosition:l="left",children:o,disabled:d,...c},u)=>{let m={default:"h-5 w-5",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",icon:"h-5 w-5"},p=d||a;return(0,s.jsxs)("button",{ref:u,className:`inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed ${{default:"bg-orange-600 text-white hover:bg-orange-700",primary:"bg-orange-600 text-white hover:bg-orange-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500",destructive:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500",danger:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500"}[r]} ${{default:"px-4 py-2.5 text-sm",sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-6 py-3 text-base",icon:"h-10 w-10"}[t]} ${e}`,disabled:p,...c,children:[a&&(0,s.jsx)(n.Ay,{size:"lg"===t?"md":"sm",className:"mr-2"}),!a&&i&&"left"===l&&(0,s.jsx)("span",{className:`${m[t]} mr-2`,children:i}),o,!a&&i&&"right"===l&&(0,s.jsx)("span",{className:`${m[t]} ml-2`,children:i})]})});i.displayName="Button"},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8730:(e,r,t)=>{"use strict";t.d(r,{TL:()=>i});var s=t(43210),a=t(98599),n=t(60687);function i(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...n}=e;if(s.isValidElement(t)){var i;let e,l,o=(i=t,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,r){let t={...r};for(let s in r){let a=e[s],n=r[s];/^on[A-Z]/.test(s)?a&&n?t[s]=(...e)=>{let r=n(...e);return a(...e),r}:a&&(t[s]=a):"style"===s?t[s]={...a,...n}:"className"===s&&(t[s]=[a,n].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==s.Fragment&&(d.ref=r?(0,a.t)(r,o):o),s.cloneElement(t,d)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:a,...i}=e,l=s.Children.toArray(a),d=l.find(o);if(d){let e=d.props.children,a=l.map(r=>r!==d?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...i,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(r,{...i,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}var l=Symbol("radix.slottable");function o(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},9776:(e,r,t)=>{"use strict";t.d(r,{Ay:()=>a,B0:()=>n});var s=t(60687);function a({size:e="md",className:r=""}){return(0,s.jsx)("div",{className:`animate-spin rounded-full border-2 border-gray-600 border-t-indigo-500 ${{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[e]} ${r}`})}function n({className:e=""}){return(0,s.jsx)("div",{className:`glass rounded-2xl p-6 animate-pulse ${e}`,children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl"}),(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),(0,s.jsx)("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14163:(e,r,t)=>{"use strict";t.d(r,{hO:()=>o,sG:()=>l});var s=t(43210),a=t(51215),n=t(8730),i=t(60687),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,n.TL)(`Primitive.${r}`),a=s.forwardRef((e,s)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?t:r,{...n,ref:s})});return a.displayName=`Primitive.${r}`,{...e,[r]:a}},{});function o(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},16796:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(60687),a=t(50549),n=t(36721),i=t(24868);function l({children:e}){return(0,s.jsx)("div",{className:"flex h-screen bg-[#040716] w-full",children:(0,s.jsx)(a.G,{children:(0,s.jsx)(n.i9,{children:(0,s.jsx)(i.A,{children:e})})})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26403:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(43210);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43985:(e,r,t)=>{"use strict";t.d(r,{F:()=>n});let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=function(){for(var e,r,t=0,s="",a=arguments.length;t<a;t++)(e=arguments[t])&&(r=function e(r){var t,s,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r)if(Array.isArray(r)){var n=r.length;for(t=0;t<n;t++)r[t]&&(s=e(r[t]))&&(a&&(a+=" "),a+=s)}else for(s in r)r[s]&&(a&&(a+=" "),a+=s);return a}(e))&&(s&&(s+=" "),s+=r);return s},n=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:l}=r,o=Object.keys(i).map(e=>{let r=null==t?void 0:t[e],a=null==l?void 0:l[e];if(null===r)return null;let n=s(r)||s(a);return i[e][n]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return a(e,o,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...d}[r]):({...l,...d})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},50942:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(43210);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},52992:(e,r,t)=>{Promise.resolve().then(t.bind(t,70718))},54300:(e,r,t)=>{"use strict";t.d(r,{J:()=>o});var s=t(60687),a=t(43210),n=t(14163),i=a.forwardRef((e,r)=>(0,s.jsx)(n.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";let l=(0,t(43985).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(i,{ref:t,className:`${l()} ${e||""}`,...r}));o.displayName=i.displayName},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60578:(e,r,t)=>{Promise.resolve().then(t.bind(t,68389))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64859:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(43210);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))})},64908:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(43210);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))})},66524:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(43210);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},68389:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\(app)\\settings\\page.tsx","default")},70718:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\(app)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\(app)\\layout.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},78730:(e,r,t)=>{Promise.resolve().then(t.bind(t,80147))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80147:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>w});var s=t(60687),a=t(43210),n=t(16189),i=t(64908),l=t(52238),o=t(50942),d=t(26403),c=t(64859),u=t(97450),m=t(94257),p=t(66524),x=t(2643),h=t(54300),g=t(34374),f=t(79481),b=t(11016);function w(){(0,n.useRouter)();let{user:e}=(0,b.R)(),r=(0,f.createSupabaseBrowserClient)(),{success:t,error:w}=(0,g.dj)(),[y,v]=(0,a.useState)("account"),[j,N]=(0,a.useState)(!1),[k,C]=(0,a.useState)(!1),[P,A]=(0,a.useState)(!1),[E,S]=(0,a.useState)(!1),[R,_]=(0,a.useState)(!1),[L,$]=(0,a.useState)(!1),[q,F]=(0,a.useState)(!1),[D,O]=(0,a.useState)(!1),[M,T]=(0,a.useState)(!1),[K,W]=(0,a.useState)({newEmail:"",currentPassword:""}),[I,z]=(0,a.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[B,G]=(0,a.useState)({emailNotifications:!0,securityAlerts:!0,usageAlerts:!0,marketingEmails:!1}),[U,J]=(0,a.useState)({configCount:0,apiKeyCount:0,userApiKeyCount:0}),V=[{id:"account",label:"Account settings",icon:i.A},{id:"notifications",label:"Notifications",icon:l.A},{id:"security",label:"Security",icon:o.A},{id:"danger",label:"Danger zone",icon:d.A}],Z=async s=>{if(s.preventDefault(),!I.currentPassword.trim())return void w("Current password is required");if(!I.newPassword.trim())return void w("New password is required");if(I.newPassword!==I.confirmPassword)return void w("New passwords do not match");if(I.newPassword.length<8)return void w("Password must be at least 8 characters long");if(I.newPassword===I.currentPassword)return void w("New password must be different from current password");N(!0);try{let{error:s}=await r.auth.signInWithPassword({email:e?.email||"",password:I.currentPassword});if(s){w("Current password is incorrect"),N(!1);return}let{error:a}=await r.auth.updateUser({password:I.newPassword});if(a)throw a;t("Password updated successfully","Your password has been changed successfully."),z({currentPassword:"",newPassword:"",confirmPassword:""})}catch(e){w("Failed to update password",e.message||"Please try again")}finally{N(!1)}},Y=async()=>{N(!0);try{localStorage.setItem("notification_settings",JSON.stringify(B));let{error:e}=await r.auth.updateUser({data:{notification_preferences:B}});if(e)throw e;t("Notification preferences saved successfully")}catch(e){w("Failed to save notification preferences")}finally{N(!1)}},H=async()=>{if(!e?.email)return void w("No email address found");F(!0);try{let{error:s}=await r.auth.resetPasswordForEmail(e.email,{redirectTo:`${window.location.origin}/auth/reset-password`});if(s)throw s;t("Password reset email sent!","Check your inbox for instructions to reset your password."),$(!1)}catch(e){w("Failed to send reset email",e.message||"Please try again.")}finally{F(!1)}},X=async()=>{if(!K.newEmail.trim())return void w("Please enter a new email address");if(!K.currentPassword.trim())return void w("Current password is required to change email");if(K.newEmail===e?.email)return void w("New email must be different from current email");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(K.newEmail))return void w("Please enter a valid email address");T(!0);try{let{error:s}=await r.auth.signInWithPassword({email:e?.email||"",password:K.currentPassword});if(s){w("Current password is incorrect"),T(!1);return}let{error:a}=await r.auth.updateUser({email:K.newEmail});if(a)throw a;t("Email change initiated!","Check both your old and new email addresses for confirmation instructions."),O(!1),W({newEmail:"",currentPassword:""})}catch(e){w("Failed to change email",e.message||"Please try again.")}finally{T(!1)}},Q=async()=>{if(e){N(!0);try{let e=await fetch("/api/user/delete-account",{method:"DELETE",headers:{"Content-Type":"application/json"}});if(!e.ok){let r=await e.json();throw Error(r.error||"Failed to delete account")}t("Account deleted successfully. You will be redirected to the homepage."),await r.auth.signOut(),setTimeout(()=>{window.location.href="/"},2e3)}catch(e){w("Failed to delete account",e.message||"Please contact support.")}finally{N(!1)}}};return(0,s.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,s.jsx)("div",{className:"border-b border-gray-800/50",children:(0,s.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Account Settings"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Manage your account settings and preferences"})]})})})}),(0,s.jsx)("div",{className:"border-b border-gray-800/50",children:(0,s.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("nav",{className:"-mb-px flex space-x-8",children:V.map(e=>{let r=e.icon;return(0,s.jsxs)("button",{onClick:()=>v(e.id),className:`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${y===e.id?"border-orange-500 text-orange-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600"}`,children:[(0,s.jsx)(r,{className:"h-4 w-4"}),e.label]},e.id)})})})}),(0,s.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8 space-y-8",children:["account"===y&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)("div",{className:"p-2 bg-orange-500/10 rounded-lg",children:(0,s.jsx)(c.A,{className:"h-5 w-5 text-orange-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Email address"})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("p",{className:"text-gray-300",children:["Your email address is ",(0,s.jsx)("span",{className:"font-medium text-white",children:e?.email})]}),(0,s.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"This is used for login and important notifications"})]}),(0,s.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-orange-400 hover:text-orange-300 hover:bg-orange-500/10 rounded-md transition-colors border border-orange-500/20 hover:border-orange-500/40 shrink-0",onClick:()=>O(!0),children:"Change"})]})]}),(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-500/10 rounded-lg",children:(0,s.jsx)(i.A,{className:"h-5 w-5 text-blue-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Account Information"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-white mb-1",children:"Full Name"}),(0,s.jsx)("p",{className:"text-gray-300",children:e?.user_metadata?.full_name||e?.user_metadata?.first_name+" "+e?.user_metadata?.last_name||"Not set"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-white mb-1",children:"Account Created"}),(0,s.jsx)("p",{className:"text-gray-300",children:e?.created_at?new Date(e.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"Unknown"})]})]}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-white mb-1",children:"Account Usage"}),(0,s.jsxs)("div",{className:"space-y-1 text-sm text-gray-300",children:[(0,s.jsxs)("p",{children:[U.configCount," configurations"]}),(0,s.jsxs)("p",{children:[U.apiKeyCount," API keys"]}),(0,s.jsxs)("p",{children:[U.userApiKeyCount," user-generated keys"]})]})]})})]})]}),(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,s.jsx)("div",{className:"p-2 bg-green-500/10 rounded-lg",children:(0,s.jsx)(u.A,{className:"h-5 w-5 text-green-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Password"})]}),(0,s.jsxs)("form",{onSubmit:Z,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(h.J,{htmlFor:"currentPassword",className:"text-sm font-medium text-white",children:"Current password"}),(0,s.jsxs)("div",{className:"relative mt-2",children:[(0,s.jsx)("input",{id:"currentPassword",type:k?"text":"password",value:I.currentPassword,onChange:e=>z(r=>({...r,currentPassword:e.target.value})),placeholder:"••••••••••",className:"w-full pr-10 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>C(!k),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300",children:k?(0,s.jsx)(m.A,{className:"h-4 w-4"}):(0,s.jsx)(p.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(h.J,{htmlFor:"newPassword",className:"text-sm font-medium text-white",children:"New password"}),(0,s.jsxs)("div",{className:"relative mt-2",children:[(0,s.jsx)("input",{id:"newPassword",type:P?"text":"password",value:I.newPassword,onChange:e=>z(r=>({...r,newPassword:e.target.value})),placeholder:"••••••••••",className:"w-full pr-10 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>A(!P),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300",children:P?(0,s.jsx)(m.A,{className:"h-4 w-4"}):(0,s.jsx)(p.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(h.J,{htmlFor:"confirmPassword",className:"text-sm font-medium text-white",children:"Confirm new password"}),(0,s.jsxs)("div",{className:"relative mt-2",children:[(0,s.jsx)("input",{id:"confirmPassword",type:E?"text":"password",value:I.confirmPassword,onChange:e=>z(r=>({...r,confirmPassword:e.target.value})),placeholder:"••••••••••",className:"w-full pr-10 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>S(!E),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300",children:E?(0,s.jsx)(m.A,{className:"h-4 w-4"}):(0,s.jsx)(p.A,{className:"h-4 w-4"})})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-300",children:["Can't remember your current password? ",(0,s.jsx)("button",{type:"button",className:"text-orange-400 hover:text-orange-300 font-medium",onClick:()=>$(!0),children:"Reset your password"})]}),(0,s.jsx)(x.$,{type:"submit",disabled:j,className:"bg-orange-600 hover:bg-orange-700 text-white",children:j?"Saving...":"Save password"})]})]})]})]}),"notifications"===y&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,s.jsx)("div",{className:"p-2 bg-blue-500/10 rounded-lg",children:(0,s.jsx)(l.A,{className:"h-5 w-5 text-blue-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Notification Preferences"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-white",children:"Email Notifications"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:"Receive important updates via email"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:B.emailNotifications,onChange:e=>G(r=>({...r,emailNotifications:e.target.checked})),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-white",children:"Security Alerts"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:"Get notified about security events"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:B.securityAlerts,onChange:e=>G(r=>({...r,securityAlerts:e.target.checked})),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-white",children:"Usage Alerts"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:"Notifications about API usage and limits"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:B.usageAlerts,onChange:e=>G(r=>({...r,usageAlerts:e.target.checked})),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-white",children:"Marketing Emails"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:"Product updates and feature announcements"})]}),(0,s.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,s.jsx)("input",{type:"checkbox",checked:B.marketingEmails,onChange:e=>G(r=>({...r,marketingEmails:e.target.checked})),className:"sr-only peer"}),(0,s.jsx)("div",{className:"w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"})]})]})]}),(0,s.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-700",children:(0,s.jsx)(x.$,{onClick:Y,disabled:j,className:"bg-orange-600 hover:bg-orange-700 text-white",children:j?"Saving...":"Save Preferences"})})]})}),"security"===y&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,s.jsx)("div",{className:"p-2 bg-green-500/10 rounded-lg",children:(0,s.jsx)(o.A,{className:"h-5 w-5 text-green-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Security Settings"})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-green-500/10 border border-green-500/20 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(o.A,{className:"h-5 w-5 text-green-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-green-400",children:"Account Security Status"}),(0,s.jsx)("p",{className:"text-sm text-green-300",children:"Your account is secure and protected"})]})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-white mb-2",children:"Last Login"}),(0,s.jsx)("p",{className:"text-sm text-gray-300",children:e?.last_sign_in_at?new Date(e.last_sign_in_at).toLocaleString():"Unknown"})]}),(0,s.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-white mb-2",children:"Account Type"}),(0,s.jsx)("p",{className:"text-sm text-gray-300",children:"Email & Password"})]})]})]})]})}),"danger"===y&&(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"bg-red-900/20 backdrop-blur-sm rounded-lg border border-red-800/50 p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[(0,s.jsx)("div",{className:"p-2 bg-red-500/10 rounded-lg",children:(0,s.jsx)(d.A,{className:"h-5 w-5 text-red-400"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Danger Zone"})]}),(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-red-400 mb-2",children:"⚠️ Account Deletion"}),(0,s.jsx)("p",{className:"text-sm text-red-300 mb-4",children:"Once you delete your account, there is no going back. This will permanently delete your account, configurations, API keys, and all associated data."}),(0,s.jsx)(x.$,{onClick:()=>_(!0),className:"bg-red-600 hover:bg-red-700 text-white",children:"Delete Account"})]})})]})})]}),D&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,s.jsxs)("div",{className:"bg-gray-900 border border-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)(c.A,{className:"h-6 w-6 text-orange-400"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Change Email Address"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(h.J,{htmlFor:"newEmail",className:"text-sm font-medium text-white",children:"New Email Address"}),(0,s.jsx)("input",{id:"newEmail",type:"email",value:K.newEmail,onChange:e=>W(r=>({...r,newEmail:e.target.value})),placeholder:"Enter new email address",className:"w-full mt-2 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(h.J,{htmlFor:"currentPasswordEmail",className:"text-sm font-medium text-white",children:"Current Password"}),(0,s.jsx)("input",{id:"currentPasswordEmail",type:"password",value:K.currentPassword,onChange:e=>W(r=>({...r,currentPassword:e.target.value})),placeholder:"Enter current password",className:"w-full mt-2 px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-orange-500 focus:border-transparent"})]})]}),(0,s.jsxs)("div",{className:"flex gap-3 mt-6",children:[(0,s.jsx)(x.$,{variant:"outline",onClick:()=>O(!1),className:"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50",children:"Cancel"}),(0,s.jsx)(x.$,{onClick:X,disabled:M,className:"flex-1 bg-orange-600 hover:bg-orange-700 text-white",children:M?"Changing...":"Change Email"})]})]})}),L&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,s.jsxs)("div",{className:"bg-gray-900 border border-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)(u.A,{className:"h-6 w-6 text-blue-400"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Reset Password"})]}),(0,s.jsx)("p",{className:"text-gray-300 mb-4",children:"We'll send a password reset link to your email address."}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)(x.$,{variant:"outline",onClick:()=>$(!1),className:"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50",children:"Cancel"}),(0,s.jsx)(x.$,{onClick:H,disabled:q,className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white",children:q?"Sending...":"Send Reset Link"})]})]})}),R&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,s.jsxs)("div",{className:"bg-gray-900 border border-red-800/50 rounded-xl shadow-2xl max-w-md w-full p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsx)(d.A,{className:"h-6 w-6 text-red-400"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Delete Account"})]}),(0,s.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-4",children:(0,s.jsxs)("p",{className:"text-red-300 text-sm",children:["⚠️ ",(0,s.jsx)("strong",{children:"This action cannot be undone."})," This will permanently delete your account, all configurations, API keys, and associated data."]})}),(0,s.jsx)("p",{className:"text-gray-300 mb-6",children:'Are you sure you want to delete your account? Type "DELETE" to confirm.'}),(0,s.jsx)("input",{type:"text",placeholder:"Type DELETE to confirm",className:"w-full px-3 py-2 border border-gray-600 rounded-lg text-white placeholder-gray-400 bg-gray-800/50 focus:ring-2 focus:ring-red-500 focus:border-transparent mb-4",onChange:e=>{}}),(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)(x.$,{variant:"outline",onClick:()=>_(!1),className:"flex-1 text-gray-300 border-gray-600 hover:bg-gray-800/50",children:"Cancel"}),(0,s.jsx)(x.$,{onClick:Q,disabled:j,className:"flex-1 bg-red-600 hover:bg-red-700 text-white",children:j?"Deleting...":"Delete Account"})]})]})})]})}},81630:e=>{"use strict";e.exports=require("http")},82368:(e,r,t)=>{Promise.resolve().then(t.bind(t,16796))},82489:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d={children:["",{children:["(app)",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,68389)),"C:\\RoKey App\\rokey-app\\src\\app\\(app)\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,70718)),"C:\\RoKey App\\rokey-app\\src\\app\\(app)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\(app)\\settings\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(app)/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},94257:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(43210);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})},94735:e=>{"use strict";e.exports=require("events")},98599:(e,r,t)=>{"use strict";t.d(r,{s:()=>i,t:()=>n});var s=t(43210);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function n(...e){return r=>{let t=!1,s=e.map(e=>{let s=a(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():a(e[r],null)}}}}function i(...e){return s.useCallback(n(...e),e)}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,5449,4912],()=>t(82489));module.exports=s})();