(()=>{var e={};e.id=3930,e.ids=[1489,3930],e.modules={2507:(e,r,t)=>{"use strict";t.d(r,{H:()=>n,Q:()=>a,createSupabaseServerClientOnRequest:()=>o});var s=t(34386),i=t(39398),u=t(44999);async function o(){let e=await (0,u.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:r=>e.get(r)?.value,set(r,t,s){try{e.set({name:r,value:t,...s})}catch(e){}},remove(r,t){try{e.set({name:r,value:"",...t})}catch(e){}}}})}function a(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:r=>e.cookies.get(r)?.value,set(e,r,t){},remove(e,r){}}})}function n(){return(0,i.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8932:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var i=t(96559),u=t(48088),o=t(37719),a=t(32190),n=t(2507);async function p(e){let r=(0,n.Q)(e);try{let{data:{user:e},error:t}=await r.auth.getUser();if(t||!e)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{data:s}=await r.from("subscriptions").select("tier").eq("user_id",e.id).eq("status","active").single(),i=s?.tier||"free";return a.NextResponse.json({tier:i})}catch(e){return a.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/user/subscription-tier/route",pathname:"/api/user/subscription-tier",filename:"route",bundlePath:"app/api/user/subscription-tier/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\user\\subscription-tier\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:d,serverHooks:x}=c;function l(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:d})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398,3410],()=>t(8932));module.exports=s})();