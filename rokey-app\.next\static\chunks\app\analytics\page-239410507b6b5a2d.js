(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1745],{18959:(e,s,a)=>{Promise.resolve().then(a.bind(a,26661))},26661:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>q});var t=a(95155),l=a(12115),r=a(35695),i=a(83298);let c=l.forwardRef(function(e,s){let{title:a,titleId:t,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),a?l.createElement("title",{id:t},a):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181"}))});var d=a(27572),n=a(5500),o=a(30192),x=a(64353),m=a(82771),h=a(5246),g=a(58397),u=a(28960),j=a(92975),b=a(55628),p=a(10184),v=a(65529),y=a(94038),N=a(78046);let f=l.forwardRef(function(e,s){let{title:a,titleId:t,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),a?l.createElement("title",{id:t},a):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.602H7.923a3.375 3.375 0 0 0-3.285 2.602l-2.268 9.64a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3m16.5 0h.008v.008h-.008v-.008Zm-3 0h.008v.008h-.008v-.008Z"}))}),w=l.forwardRef(function(e,s){let{title:a,titleId:t,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),a?l.createElement("title",{id:t},a):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});var k=a(52589);let A=e=>e>=1e6?"".concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString(),S=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:4}).format(e),C=e=>{if(0===e)return"0 Bytes";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["Bytes","KB","MB","GB"][s]},M=(e,s)=>{if(0===s)return{percentage:0,isPositive:e>0};let a=(e-s)/s*100;return{percentage:Math.abs(a),isPositive:a>=0}},R=e=>{let{title:s,value:a,trend:l,icon:r,subtitle:i}=e;return(0,t.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200",children:(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-400 mb-1",children:s}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:"text-3xl font-bold text-white",children:"number"==typeof a?A(a):a}),l&&(0,t.jsxs)("span",{className:"text-sm px-2 py-1 rounded-md flex items-center space-x-1 ".concat(l.isPositive?"text-green-400 bg-green-400/10":"text-red-400 bg-red-400/10"),children:[l.isPositive?(0,t.jsx)(d.A,{className:"w-3 h-3"}):(0,t.jsx)(c,{className:"w-3 h-3"}),(0,t.jsxs)("span",{children:[l.percentage.toFixed(1),"%"]})]})]}),i&&(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:i})]}),(0,t.jsx)("div",{className:"text-gray-500",children:r})]})})};function T(){var e,s,a,T,q,P,_,E,F,D,L,B,I,U;(0,r.useRouter)();let O=(0,i.R)(),[G,H]=(0,l.useState)(null),[W,z]=(0,l.useState)(null),[K,Z]=(0,l.useState)([]),[Q,Y]=(0,l.useState)([]),[J,V]=(0,l.useState)(!0),[X,$]=(0,l.useState)(null),[ee,es]=(0,l.useState)(null),[ea,et]=(0,l.useState)(null),[el,er]=(0,l.useState)(null),[ei,ec]=(0,l.useState)(null),[ed,en]=(0,l.useState)({}),[eo,ex]=(0,l.useState)("30"),[em,eh]=(0,l.useState)(""),[eg,eu]=(0,l.useState)("overview");(0,l.useEffect)(()=>{ey()},[]);let ej=(0,l.useCallback)(async()=>{if(!ed.users){en(e=>({...e,users:!0}));try{let e=await fetch("/api/analytics/users?days=".concat(eo));if(e.ok){let s=await e.json();es(s.data)}}catch(e){}finally{en(e=>({...e,users:!1}))}}},[eo,ed.users]),eb=(0,l.useCallback)(async()=>{if(!ed.errors){en(e=>({...e,errors:!0}));try{let e=await fetch("/api/analytics/errors?days=".concat(eo));if(e.ok){let s=await e.json();et(s.data)}}catch(e){}finally{en(e=>({...e,errors:!1}))}}},[eo,ed.errors]),ep=(0,l.useCallback)(async()=>{if(!ed.cache){en(e=>({...e,cache:!0}));try{let e=await fetch("/api/analytics/cache?days=".concat(eo));if(e.ok){let s=await e.json();er(s.data)}}catch(e){}finally{en(e=>({...e,cache:!1}))}}},[eo,ed.cache]),ev=(0,l.useCallback)(async()=>{if(!ed.metadata){en(e=>({...e,metadata:!0}));try{let e=await fetch("/api/analytics/metadata?days=".concat(eo));if(e.ok){let s=await e.json();ec(s.data)}}catch(e){}finally{en(e=>({...e,metadata:!1}))}}},[eo,ed.metadata]);(0,l.useEffect)(()=>{switch(eg){case"users":ee||ej();break;case"errors":ea||eb();break;case"cache":el||ep();break;case"metadata":ei||ev()}},[eg,ee,ea,el,ei,ej,eb,ep,ev]),(0,l.useEffect)(()=>{if("overview"!==eg)switch(eg){case"users":es(null),ej();break;case"errors":et(null),eb();break;case"cache":er(null),ep();break;case"metadata":ec(null),ev()}},[eo]),(0,l.useEffect)(()=>{var e;(null==O?void 0:O.loading)!==!0&&((null==O||null==(e=O.user)?void 0:e.id)?($(null),eN()):(null==O?void 0:O.loading)===!1&&($("Authentication required. Please log in to view analytics."),V(!1)))},[null==O||null==(e=O.user)?void 0:e.id,null==O?void 0:O.loading,eo,em]);let ey=async()=>{try{let e=await fetch("/api/custom-configs");if(e.ok){let s=await e.json();Y(s)}}catch(e){}},eN=(0,l.useCallback)(async()=>{try{var e;V(!0),$(null);try{let e=await fetch("/api/analytics/summary?groupBy=provider");if(401===e.status)throw Error("Authentication required. Please log in to view analytics.")}catch(e){}let s=new URLSearchParams,a=new Date;a.setDate(a.getDate()-parseInt(eo)),s.append("startDate",a.toISOString()),em&&s.append("customApiConfigId",em);let t=await fetch("/api/analytics/summary?".concat(s.toString(),"&groupBy=provider"));if(!t.ok){let e=await t.text();if(401===t.status)throw Error("Authentication required. Please log in to view analytics.");throw Error("Failed to fetch analytics data: ".concat(t.status," ").concat(e))}let l=await t.json();if(H(l),(null==(e=l.summary)?void 0:e.total_requests)>0)try{let e=new URLSearchParams,a=new Date;a.setDate(a.getDate()-2*parseInt(eo));let t=new Date;t.setDate(t.getDate()-parseInt(eo)),e.append("startDate",a.toISOString()),e.append("endDate",t.toISOString()),em&&e.append("customApiConfigId",em);let[l,r]=await Promise.all([fetch("/api/analytics/summary?".concat(e.toString(),"&groupBy=provider")),fetch("/api/analytics/summary?".concat(s.toString(),"&groupBy=day"))]),i=l.ok?await l.json():null,c=r.ok?await r.json():null;if(z(i),null==c?void 0:c.grouped_data){let e=c.grouped_data.map(e=>({date:e.period||e.name,cost:e.cost||0,requests:e.requests||0,tokens:(e.input_tokens||0)+(e.output_tokens||0),latency:e.avg_latency||0}));Z(e)}}catch(e){}}catch(e){$(e.message)}finally{V(!1)}},[eo,em]);if((null==O?void 0:O.loading)!==!1||J)return(0,t.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white",children:(0,t.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"animate-pulse space-y-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-800 rounded w-1/3"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"bg-gray-900 rounded-lg p-6 border border-gray-800",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-800 rounded w-1/2 mb-4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-800 rounded w-3/4 mb-2"}),(0,t.jsx)("div",{className:"h-3 bg-gray-800 rounded w-1/3"})]},s))})]}),(0,t.jsxs)("div",{className:"fixed bottom-4 right-4 bg-gray-800 rounded-lg p-3 flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-500"}),(0,t.jsx)("span",{className:"text-sm text-gray-300",children:(null==O?void 0:O.loading)!==!1?"Authenticating...":"Loading analytics..."})]})]})});if(X)return(0,t.jsx)("div",{className:"min-h-screen w-full bg-[#040716] text-white",children:(0,t.jsx)("div",{className:"w-full px-6 py-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold mb-4",children:"Analytics"}),(0,t.jsxs)("div",{className:"bg-red-900/20 border border-red-800 rounded-lg p-6 max-w-md mx-auto",children:[(0,t.jsxs)("p",{className:"text-red-400 mb-4",children:["Error loading analytics: ",X]}),(0,t.jsx)("button",{onClick:eN,className:"px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 transition-colors",children:"Retry"})]})]})})});let ef=null==G?void 0:G.summary,ew=null==W?void 0:W.summary,ek=ew?M((null==ef?void 0:ef.total_cost)||0,ew.total_cost):void 0,eA=ew?M((null==ef?void 0:ef.total_requests)||0,ew.total_requests):void 0,eS=ew?M((null==ef?void 0:ef.average_latency)||0,ew.average_latency||0):void 0,eC=ew?M((null==ef?void 0:ef.success_rate)||0,ew.success_rate):void 0;return(0,t.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,t.jsx)("div",{className:"border-b border-gray-800/50",children:(0,t.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center space-x-8",children:(0,t.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Analytics"})}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,t.jsx)(N.A,{className:"w-4 h-4"}),(0,t.jsx)("input",{type:"text",placeholder:"Search Filter",className:"bg-transparent border-none outline-none text-gray-400 placeholder-gray-500 w-32"})]}),(0,t.jsxs)("select",{value:eo,onChange:e=>ex(e.target.value),className:"px-3 py-1 bg-gray-800 border border-gray-700 rounded text-sm text-white focus:outline-none focus:border-cyan-500",children:[(0,t.jsx)("option",{value:"7",children:"Last 7 days"}),(0,t.jsx)("option",{value:"30",children:"Last 30 days"}),(0,t.jsx)("option",{value:"90",children:"Last 90 days"})]})]})]})})}),(0,t.jsx)("div",{className:"border-b border-gray-800/50",children:(0,t.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,t.jsxs)("button",{onClick:()=>eu("overview"),className:"flex items-center space-x-2 py-4 border-b-2 transition-colors ".concat("overview"===eg?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"),children:[(0,t.jsx)(p.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Overview"})]}),(0,t.jsxs)("button",{onClick:()=>eu("users"),className:"flex items-center space-x-2 py-4 border-b-2 transition-colors ".concat("users"===eg?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"),children:[(0,t.jsx)(w,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"text-sm",children:"Users"})]}),(0,t.jsxs)("button",{onClick:()=>eu("errors"),className:"flex items-center space-x-2 py-4 border-b-2 transition-colors ".concat("errors"===eg?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"),children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"text-sm",children:"Errors"})]}),(0,t.jsxs)("button",{onClick:()=>eu("cache"),className:"flex items-center space-x-2 py-4 border-b-2 transition-colors ".concat("cache"===eg?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"),children:[(0,t.jsx)(f,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"text-sm",children:"Cache"})]}),(0,t.jsxs)("button",{onClick:()=>eu("metadata"),className:"flex items-center space-x-2 py-4 border-b-2 transition-colors ".concat("metadata"===eg?"border-cyan-500 text-cyan-500":"border-transparent text-gray-400 hover:text-white"),children:[(0,t.jsx)(j.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"text-sm",children:"Metadata"})]})]})})}),(0,t.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:["overview"===eg&&(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(R,{title:"Total Request Made",value:(null==ef?void 0:ef.total_requests)||0,trend:eA,icon:(0,t.jsx)(n.A,{className:"w-6 h-6"})}),(0,t.jsx)(R,{title:"Average Latency",value:"".concat(Math.round((null==ef?void 0:ef.average_latency)||0),"ms"),trend:eS,icon:(0,t.jsx)(m.A,{className:"w-6 h-6"})}),(0,t.jsx)(R,{title:"User Feedback",value:"".concat(((null==ef?void 0:ef.success_rate)||0).toFixed(1),"%"),trend:eC,icon:(0,t.jsx)(o.A,{className:"w-6 h-6"})}),(0,t.jsx)(R,{title:"Total Cost",value:S((null==ef?void 0:ef.total_cost)||0),trend:ek,icon:(0,t.jsx)(u.A,{className:"w-6 h-6"})})]}),(!ef||0===ef.total_requests)&&!J&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(n.A,{className:"h-16 w-16 mx-auto mb-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Analytics Data Yet"}),(0,t.jsx)("p",{className:"text-gray-400 mb-4",children:"Start making API requests to see your analytics data here."}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mb-6",children:"Analytics will appear once you begin using your API configurations."}),!1]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Cost"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,t.jsx)("span",{className:"text-3xl font-bold text-white",children:S((null==ef?void 0:ef.total_cost)||0)}),ek&&(0,t.jsxs)("span",{className:"text-sm px-2 py-1 rounded flex items-center space-x-1 ".concat(ek.isPositive?"text-red-400 bg-red-400/10":"text-green-400 bg-green-400/10"),children:[ek.isPositive?(0,t.jsx)(d.A,{className:"w-3 h-3"}):(0,t.jsx)(c,{className:"w-3 h-3"}),(0,t.jsxs)("span",{children:[ek.percentage.toFixed(1),"%"]})]})]})]}),(0,t.jsx)("div",{className:"text-gray-500",children:(0,t.jsx)(u.A,{className:"w-6 h-6"})})]}),(0,t.jsx)("div",{className:"h-48 relative bg-gray-800/50 rounded",children:K.length>0?(0,t.jsx)("div",{className:"absolute inset-4",children:(0,t.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 400 120",children:[(0,t.jsx)("defs",{children:(0,t.jsxs)("linearGradient",{id:"costGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,t.jsx)("stop",{offset:"0%",stopColor:"#10b981",stopOpacity:"0.3"}),(0,t.jsx)("stop",{offset:"100%",stopColor:"#10b981",stopOpacity:"0"})]})}),[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsx)("line",{x1:"0",y1:24*s,x2:"400",y2:24*s,stroke:"#374151",strokeWidth:"0.5",opacity:"0.3"},s)),(0,t.jsx)("polyline",{fill:"none",stroke:"#10b981",strokeWidth:"2",points:K.map((e,s)=>{let a=s/Math.max(K.length-1,1)*400,t=Math.max(...K.map(e=>e.cost)),l=120-e.cost/t*100;return"".concat(a,",").concat(l)}).join(" ")}),(0,t.jsx)("polygon",{fill:"url(#costGradient)",points:"0,120 ".concat(K.map((e,s)=>{let a=s/Math.max(K.length-1,1)*400,t=Math.max(...K.map(e=>e.cost)),l=120-e.cost/t*100;return"".concat(a,",").concat(l)}).join(" ")," 400,120")})]})}):(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(n.A,{className:"h-8 w-8 mx-auto mb-2 text-gray-500"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"No data available"})]})})})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Latency"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,t.jsxs)("span",{className:"text-3xl font-bold text-white",children:[Math.round((null==ef?void 0:ef.average_latency)||0),"ms"]}),eS&&(0,t.jsxs)("span",{className:"text-sm px-2 py-1 rounded flex items-center space-x-1 ".concat(eS.isPositive?"text-red-400 bg-red-400/10":"text-green-400 bg-green-400/10"),children:[eS.isPositive?(0,t.jsx)(d.A,{className:"w-3 h-3"}):(0,t.jsx)(c,{className:"w-3 h-3"}),(0,t.jsxs)("span",{children:[eS.percentage.toFixed(1),"%"]})]})]})]}),(0,t.jsx)("div",{className:"text-gray-500",children:(0,t.jsx)(m.A,{className:"w-6 h-6"})})]}),(0,t.jsx)("div",{className:"h-48 relative bg-gray-800/50 rounded",children:K.length>0?(0,t.jsx)("div",{className:"absolute inset-4",children:(0,t.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 400 120",children:[(0,t.jsx)("defs",{children:(0,t.jsxs)("linearGradient",{id:"latencyGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,t.jsx)("stop",{offset:"0%",stopColor:"#f59e0b",stopOpacity:"0.3"}),(0,t.jsx)("stop",{offset:"100%",stopColor:"#f59e0b",stopOpacity:"0"})]})}),[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsx)("line",{x1:"0",y1:24*s,x2:"400",y2:24*s,stroke:"#374151",strokeWidth:"0.5",opacity:"0.3"},s)),(0,t.jsx)("polyline",{fill:"none",stroke:"#f59e0b",strokeWidth:"2",points:K.map((e,s)=>{let a=s/Math.max(K.length-1,1)*400,t=Math.max(...K.map(e=>e.latency||0)),l=120-(e.latency||0)/Math.max(t,1)*100;return"".concat(a,",").concat(l)}).join(" ")}),(0,t.jsx)("polygon",{fill:"url(#latencyGradient)",points:"0,120 ".concat(K.map((e,s)=>{let a=s/Math.max(K.length-1,1)*400,t=Math.max(...K.map(e=>e.latency||0)),l=120-(e.latency||0)/Math.max(t,1)*100;return"".concat(a,",").concat(l)}).join(" ")," 400,120")})]})}):(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(m.A,{className:"h-8 w-8 mx-auto mb-2 text-gray-500"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"No data available"})]})})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Tokens Used"}),(0,t.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"March 28"})]}),(0,t.jsx)("div",{className:"text-gray-500",children:(0,t.jsx)(g.A,{className:"w-6 h-6"})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,t.jsx)("span",{className:"text-3xl font-bold text-white",children:A((null==ef?void 0:ef.total_tokens)||0)}),(0,t.jsxs)("span",{className:"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1",children:[(0,t.jsx)(d.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:"8.39%"})]})]}),(0,t.jsx)("div",{className:"h-32 relative bg-gray-800/50 rounded",children:(0,t.jsxs)("div",{className:"absolute inset-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4 mb-4 text-xs",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full mr-2"}),(0,t.jsx)("span",{className:"text-gray-400",children:"Input Token"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),(0,t.jsx)("span",{className:"text-gray-400",children:"Output Token"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full mr-2"}),(0,t.jsx)("span",{className:"text-gray-400",children:"Total Token"})]})]}),(0,t.jsx)("div",{className:"relative h-16",children:[...Array(20)].map((e,s)=>(0,t.jsx)("div",{className:"absolute w-1 h-1 rounded-full ".concat(s%3==0?"bg-yellow-500":s%3==1?"bg-green-500":"bg-blue-500"),style:{left:"".concat(90*Math.random(),"%"),top:"".concat(80*Math.random(),"%")}},s))})]})})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("div",{children:(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Requests"})}),(0,t.jsx)("div",{className:"text-gray-500",children:(0,t.jsx)(v.A,{className:"w-6 h-6"})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,t.jsx)("span",{className:"text-3xl font-bold text-white",children:A((null==ef?void 0:ef.total_requests)||0)}),(0,t.jsxs)("span",{className:"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1",children:[(0,t.jsx)(d.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:"3.39%"})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:null==G?void 0:G.grouped_data.slice(0,5).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-20 text-sm text-gray-400 truncate",children:e.name}),(0,t.jsx)("div",{className:"flex-1 mx-3",children:(0,t.jsx)("div",{className:"h-2 bg-gray-800 rounded-full overflow-hidden",children:(0,t.jsx)("div",{className:"h-full rounded-full ".concat(0===s?"bg-pink-500":1===s?"bg-purple-500":2===s?"bg-cyan-500":3===s?"bg-green-500":"bg-yellow-500"),style:{width:"".concat(e.requests/((null==ef?void 0:ef.total_requests)||1)*100,"%")}})})}),(0,t.jsx)("div",{className:"text-sm text-gray-400 w-12 text-right",children:A(e.requests)})]},e.name))})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("div",{children:(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Unique Users"})}),(0,t.jsx)("div",{className:"text-gray-500",children:(0,t.jsx)(w,{className:"w-6 h-6"})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,t.jsx)("span",{className:"text-3xl font-bold text-white",children:(null==ef?void 0:ef.successful_requests)||0}),(0,t.jsxs)("span",{className:"text-sm text-green-400 bg-green-400/10 px-2 py-1 rounded flex items-center space-x-1",children:[(0,t.jsx)(d.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:"3.39%"})]})]}),(0,t.jsx)("div",{className:"h-32 relative bg-gray-800/50 rounded",children:(0,t.jsx)("div",{className:"absolute inset-4",children:(0,t.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 200 80",children:[(0,t.jsx)("defs",{children:(0,t.jsxs)("linearGradient",{id:"waveGradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[(0,t.jsx)("stop",{offset:"0%",stopColor:"#8b5cf6",stopOpacity:"0.3"}),(0,t.jsx)("stop",{offset:"100%",stopColor:"#8b5cf6",stopOpacity:"0"})]})}),(0,t.jsx)("path",{d:"M0,40 Q50,20 100,40 T200,40",fill:"none",stroke:"#8b5cf6",strokeWidth:"2"}),(0,t.jsx)("path",{d:"M0,40 Q50,20 100,40 T200,40 L200,80 L0,80 Z",fill:"url(#waveGradient)"})]})})})]})]})]}),"users"===eg&&(0,t.jsx)("div",{className:"space-y-6",children:ed.users?(0,t.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"}),(0,t.jsx)("span",{className:"ml-3 text-gray-400",children:"Loading users analytics..."})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Active Users"}),(0,t.jsx)(w,{className:"w-6 h-6 text-gray-400"})]}),(0,t.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:(null==ee?void 0:ee.activeUsers)||0}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Users with API activity"})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"API Keys Generated"}),(0,t.jsx)(y.A,{className:"w-6 h-6 text-gray-400"})]}),(0,t.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:A((null==ee?void 0:ee.totalApiKeys)||0)}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"User-generated API keys"})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Total Requests"}),(0,t.jsx)(n.A,{className:"w-6 h-6 text-gray-400"})]}),(0,t.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:A((null==ee?void 0:ee.totalRequests)||0)}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"API requests made"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"User Profile"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Subscription Tier"}),(0,t.jsx)("span",{className:"text-white capitalize",children:(null==ee||null==(s=ee.userProfile)?void 0:s.tier)||"Free"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Account Status"}),(0,t.jsx)("span",{className:"text-green-400 capitalize",children:(null==ee||null==(a=ee.userProfile)?void 0:a.status)||"Active"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Success Rate"}),(0,t.jsxs)("span",{className:"text-white",children:[(null==ee||null==(T=ee.successRate)?void 0:T.toFixed(1))||0,"%"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Total Configs"}),(0,t.jsx)("span",{className:"text-white",children:(null==ee?void 0:ee.totalConfigs)||0})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Top Models Used"}),(0,t.jsx)("div",{className:"space-y-3",children:(null==ee||null==(q=ee.topModels)?void 0:q.slice(0,5).map((e,s)=>(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-400 truncate",children:e.model}),(0,t.jsx)("span",{className:"text-white",children:e.count})]},e.model)))||(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No model usage data available"})})]})]}),(null==ee?void 0:ee.timeSeriesData)&&ee.timeSeriesData.length>0&&(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Activity Timeline"}),(0,t.jsx)("div",{className:"h-64 flex items-end space-x-2",children:ee.timeSeriesData.map((e,s)=>(0,t.jsxs)("div",{className:"flex-1 flex flex-col items-center",children:[(0,t.jsx)("div",{className:"w-full bg-cyan-500 rounded-t",style:{height:"".concat(Math.max(e.requests/Math.max(...ee.timeSeriesData.map(e=>e.requests))*200,2),"px")}}),(0,t.jsx)("span",{className:"text-xs text-gray-500 mt-2 transform -rotate-45",children:new Date(e.date).toLocaleDateString("en-US",{month:"short",day:"numeric"})})]},e.date))})]})]})}),"errors"===eg&&(0,t.jsx)("div",{className:"space-y-6",children:ed.errors?(0,t.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"}),(0,t.jsx)("span",{className:"ml-3 text-gray-400",children:"Loading error analytics..."})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Error Rate"}),(0,t.jsx)(b.A,{className:"w-6 h-6 text-red-400"})]}),(0,t.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:(null==ea?void 0:ea.errorRate)?"".concat(ea.errorRate.toFixed(2),"%"):"0%"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Failed requests percentage"}),(null==ea?void 0:ea.errorRateTrend)!==void 0&&(0,t.jsxs)("span",{className:"text-xs px-2 py-1 rounded ".concat(ea.errorRateTrend>0?"bg-red-500/20 text-red-400":"bg-green-500/20 text-green-400"),children:[ea.errorRateTrend>0?"+":"",ea.errorRateTrend.toFixed(2),"%"]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Total Errors"}),(0,t.jsx)(k.A,{className:"w-6 h-6 text-red-400"})]}),(0,t.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:A((null==ea?void 0:ea.failedRequests)||0)}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Failed requests count"})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Total Requests"}),(0,t.jsx)(n.A,{className:"w-6 h-6 text-gray-400"})]}),(0,t.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:A((null==ea?void 0:ea.totalRequests)||0)}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"All requests processed"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Top Error Status Codes"}),(0,t.jsx)("div",{className:"space-y-3",children:(null==ea||null==(P=ea.statusCodeBreakdown)?void 0:P.slice(0,5).map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-white",children:e.statusCode}),(0,t.jsx)("span",{className:"text-gray-400 text-sm ml-2",children:e.description})]}),(0,t.jsx)("span",{className:"text-red-400",children:e.count})]},e.statusCode)))||(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No error data available"})})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Error Sources"}),(0,t.jsx)("div",{className:"space-y-3",children:(null==ea||null==(_=ea.errorSourceBreakdown)?void 0:_.slice(0,5).map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-400 capitalize",children:e.source}),(0,t.jsx)("span",{className:"text-red-400",children:e.count})]},e.source)))||(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No error source data available"})})]})]}),(null==ea?void 0:ea.recentErrors)&&ea.recentErrors.length>0&&(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Errors"}),(0,t.jsx)("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:ea.recentErrors.slice(0,10).map((e,s)=>(0,t.jsx)("div",{className:"border-l-2 border-red-500 pl-4 py-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"text-white text-sm font-medium",children:e.message}),(0,t.jsxs)("p",{className:"text-gray-400 text-xs mt-1",children:["Source: ",e.source," | Status: ",e.statusCode]})]}),(0,t.jsx)("span",{className:"text-gray-500 text-xs",children:new Date(e.timestamp).toLocaleString()})]})},s))})]})]})}),"cache"===eg&&(0,t.jsx)("div",{className:"space-y-6",children:ed.cache?(0,t.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"}),(0,t.jsx)("span",{className:"ml-3 text-gray-400",children:"Loading cache analytics..."})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Cache Hit Rate"}),(0,t.jsx)(f,{className:"w-6 h-6 text-green-400"})]}),(0,t.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:(null==el?void 0:el.cacheHitRate)?"".concat(el.cacheHitRate.toFixed(1),"%"):"0%"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Semantic cache efficiency"}),(null==el?void 0:el.hitRateTrend)!==void 0&&(0,t.jsxs)("span",{className:"text-xs px-2 py-1 rounded ".concat(el.hitRateTrend>0?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"),children:[el.hitRateTrend>0?"+":"",el.hitRateTrend.toFixed(1),"%"]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Cache Entries"}),(0,t.jsx)(x.A,{className:"w-6 h-6 text-blue-400"})]}),(0,t.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:A((null==el?void 0:el.totalCacheEntries)||0)}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Stored cache responses"})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Cost Savings"}),(0,t.jsx)(u.A,{className:"w-6 h-6 text-green-400"})]}),(0,t.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:S((null==el?void 0:el.totalCostSaved)||0)}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Saved through caching"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Cache Performance"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Total Hits"}),(0,t.jsx)("span",{className:"text-green-400",children:A((null==el?void 0:el.totalHits)||0)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Total Misses"}),(0,t.jsx)("span",{className:"text-red-400",children:A((null==el?void 0:el.totalMisses)||0)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Avg Response Time Saved"}),(0,t.jsxs)("span",{className:"text-white",children:[(null==el||null==(E=el.averageResponseTimeSaved)?void 0:E.toFixed(0))||0,"ms"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Storage Used"}),(0,t.jsx)("span",{className:"text-white",children:C((null==el?void 0:el.totalStorageBytes)||0)})]})]})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Cache by Provider"}),(0,t.jsx)("div",{className:"space-y-3",children:(null==el||null==(F=el.cacheByProvider)?void 0:F.slice(0,5).map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-400",children:e.provider}),(0,t.jsx)("span",{className:"text-white",children:e.count})]},e.provider)))||(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No cache data available"})})]})]}),(null==el?void 0:el.topCachedModels)&&el.topCachedModels.length>0&&(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Most Cached Models"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:el.topCachedModels.slice(0,6).map(e=>(0,t.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4",children:[(0,t.jsx)("div",{className:"text-white font-medium truncate",children:e.model}),(0,t.jsxs)("div",{className:"text-cyan-400 text-sm",children:[e.count," cached responses"]})]},e.model))})]})]})}),"metadata"===eg&&(0,t.jsx)("div",{className:"space-y-6",children:ed.metadata?(0,t.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"}),(0,t.jsx)("span",{className:"ml-3 text-gray-400",children:"Loading metadata analytics..."})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Total Configurations"}),(0,t.jsx)(h.A,{className:"w-6 h-6 text-orange-400"})]}),(0,t.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:(null==ei?void 0:ei.totalConfigs)||0}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Active API configurations"})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Unique Models"}),(0,t.jsx)(g.A,{className:"w-6 h-6 text-green-400"})]}),(0,t.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:(null==ei?void 0:ei.uniqueModels)||0}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Different models used"})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"API Keys"}),(0,t.jsx)(y.A,{className:"w-6 h-6 text-cyan-400"})]}),(0,t.jsx)("div",{className:"text-3xl font-bold text-white mb-2",children:((null==ei?void 0:ei.totalApiKeys)||0)+((null==ei?void 0:ei.totalUserApiKeys)||0)}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:"Total API keys configured"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Routing Strategies"}),(0,t.jsx)("div",{className:"space-y-3",children:(null==ei||null==(D=ei.routingStrategyBreakdown)?void 0:D.map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-400",children:e.strategy}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-white",children:e.count}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["(",e.percentage.toFixed(1),"%)"]})]})]},e.strategy)))||(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No routing strategy data available"})})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Performance Metrics"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Avg Processing Time"}),(0,t.jsxs)("span",{className:"text-white",children:[(null==ei||null==(L=ei.averageProcessingTime)?void 0:L.toFixed(0))||0,"ms"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Avg Request Size"}),(0,t.jsx)("span",{className:"text-white",children:C((null==ei?void 0:ei.averageRequestSize)||0)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Avg Response Size"}),(0,t.jsx)("span",{className:"text-white",children:C((null==ei?void 0:ei.averageResponseSize)||0)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Config Age"}),(0,t.jsxs)("span",{className:"text-white",children:[(null==ei||null==(B=ei.averageConfigAge)?void 0:B.toFixed(0))||0," days"]})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Top Models"}),(0,t.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:(null==ei||null==(I=ei.topModels)?void 0:I.slice(0,10).map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center py-1",children:[(0,t.jsx)("span",{className:"text-gray-400 text-sm truncate",children:e.model}),(0,t.jsx)("span",{className:"text-white text-sm",children:e.count})]},e.model)))||(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No model usage data available"})})]}),(0,t.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Temperature Distribution"}),(0,t.jsx)("div",{className:"space-y-3",children:(null==ei||null==(U=ei.temperatureDistribution)?void 0:U.map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:e.range}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-white",children:e.count}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["(",e.percentage.toFixed(1),"%)"]})]})]},e.range)))||(0,t.jsx)("p",{className:"text-gray-500 text-sm",children:"No temperature data available"})})]})]})]})})]})]})}function q(){return(0,t.jsx)(l.Suspense,{fallback:(0,t.jsx)("div",{className:"min-h-screen bg-[#040716] text-white",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,t.jsxs)("div",{className:"animate-pulse space-y-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-800 rounded w-1/3"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsxs)("div",{className:"bg-gray-900 rounded-lg p-6 border border-gray-800",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-800 rounded w-1/2 mb-4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-800 rounded w-3/4 mb-2"}),(0,t.jsx)("div",{className:"h-3 bg-gray-800 rounded w-1/3"})]},s))})]})})}),children:(0,t.jsx)(T,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,5738,9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>s(18959)),_N_E=e.O()}]);