(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4586],{8443:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(95155),r=s(12115),n=s(55020),i=s(6874),l=s.n(i),c=s(32461);function o(){let[e,t]=(0,r.useState)(""),[s,i]=(0,r.useState)(!1),[o,d]=(0,r.useState)(null),[m,x]=(0,r.useState)(""),u=async t=>{t.preventDefault(),i(!0),x(""),d(null);try{let t=await fetch("/api/auth/check-pending-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})}),s=await t.json();if(!t.ok)throw Error(s.error||"Failed to check account status");d(s)}catch(e){x(e instanceof Error?e.message:"Failed to check account status")}finally{i(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)(l(),{href:"/auth/signin",className:"inline-flex items-center text-gray-600 hover:text-gray-800 mb-8 transition-colors",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Back to Sign In"]}),(0,a.jsxs)(n.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Account Recovery"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Can't remember if you have an account or need to complete a payment? Enter your email to check your account status."})]}),(0,a.jsxs)("form",{onSubmit:u,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,a.jsx)("input",{id:"email",type:"email",value:e,onChange:e=>t(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent transition-all",placeholder:"Enter your email address"})]}),(0,a.jsx)("button",{type:"submit",disabled:s,className:"w-full bg-[#ff6b35] text-white py-3 px-4 rounded-xl font-medium hover:bg-[#e55a2b] focus:ring-2 focus:ring-[#ff6b35] focus:ring-offset-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Checking...":"Check Account Status"})]}),m&&(0,a.jsx)(n.PY1.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-6 p-4 bg-red-50 border border-red-200 rounded-xl",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:m})}),o&&(0,a.jsx)(n.PY1.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-6 space-y-4",children:o.exists?o.hasPendingPayment?(0,a.jsxs)("div",{className:"p-4 bg-amber-50 border border-amber-200 rounded-xl",children:[(0,a.jsx)("p",{className:"text-amber-700 text-sm mb-3",children:o.message}),(0,a.jsx)(l(),{href:o.signInUrl,className:"inline-block bg-amber-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-amber-700 transition-colors",children:"Complete Payment"})]}):o.hasActiveSubscription?(0,a.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-xl",children:[(0,a.jsx)("p",{className:"text-green-700 text-sm mb-3",children:o.message}),(0,a.jsx)(l(),{href:o.signInUrl,className:"inline-block bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors",children:"Sign In"})]}):(0,a.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-xl",children:[(0,a.jsx)("p",{className:"text-blue-700 text-sm mb-3",children:o.message}),(0,a.jsx)(l(),{href:o.signInUrl,className:"inline-block bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors",children:"Sign In"})]}):(0,a.jsxs)("div",{className:"p-4 bg-gray-50 border border-gray-200 rounded-xl",children:[(0,a.jsx)("p",{className:"text-gray-700 text-sm mb-3",children:o.message}),(0,a.jsx)(l(),{href:"/auth/signup",className:"inline-block bg-[#ff6b35] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#e55a2b] transition-colors",children:"Create Account"})]})}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Need help? Contact us at"," ",(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-[#ff6b35] hover:underline",children:"<EMAIL>"})]})})]})]})})}},47383:(e,t,s)=>{Promise.resolve().then(s.bind(s,8443))}},e=>{var t=t=>e(e.s=t);e.O(0,[9299,9420,3362,2662,8669,4703,622,2432,408,8925,7068,2076,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(47383)),_N_E=e.O()}]);