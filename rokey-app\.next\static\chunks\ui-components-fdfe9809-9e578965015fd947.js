"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2396],{4654:(e,r,t)=>{t.d(r,{f:()=>o});var a=t(95155);t(12115);var s=t(11485);let o=e=>{let{orchestrationComplete:r,onMaximize:t,isCanvasOpen:o,isCanvasMinimized:i}=e;return(0,a.jsxs)("div",{className:"flex justify-start group mb-16 mt-8 ".concat(o&&!i?"-ml-96":""," ").concat(o&&!i?"ml-8":""),children:[(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,a.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,a.jsx)("div",{className:"".concat(o&&!i?"max-w-[80%]":"max-w-[65%]"," relative"),children:(0,a.jsx)("div",{onClick:t,className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-2xl rounded-bl-lg shadow-lg cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-[1.02] min-w-[320px] ring-2 ring-blue-300/60 hover:ring-blue-300/80 shadow-blue-500/20",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(s.v,{className:"w-6 h-6"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h3",{className:"font-semibold text-sm",children:"AI Team Collaboration"}),(0,a.jsx)("p",{className:"text-xs opacity-90",children:r?"Completed - Click to view results":"Multi-Role Orchestration in progress"})]}),(0,a.jsxs)("div",{className:"flex-shrink-0",children:[!r&&(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),r&&(0,a.jsx)(s.B,{className:"w-5 h-5"})]})]})})})]})}},35462:(e,r,t)=>{t.d(r,{default:()=>d});var a=t(95155),s=t(35695),o=t(22261),i=t(99323),n=t(90433),l=t(64198);function d(e){let{children:r}=e,t=(0,s.usePathname)(),{toasts:d,removeToast:c}=(0,l.dj)();return"/"===t||t.startsWith("/pricing")||t.startsWith("/features")||t.startsWith("/about")||t.startsWith("/routing-strategies")||t.startsWith("/contact")||t.startsWith("/docs")||t.startsWith("/blog")||t.startsWith("/auth/")||t.startsWith("/privacy")||t.startsWith("/terms")||t.startsWith("/cookies")||t.startsWith("/security")?(0,a.jsxs)(a.Fragment,{children:[r,(0,a.jsx)(l.N9,{toasts:d,onRemove:c})]}):(0,a.jsx)(o.G,{children:(0,a.jsx)(i.i9,{children:(0,a.jsx)(n.A,{children:r})})})}},43456:(e,r,t)=>{t.d(r,{A:()=>c});var a=t(95155),s=t(11518),o=t.n(s),i=t(12115),n=t(34359),l=t(64134);let d={initializing:{icon:n.P,text:"Initializing",description:"Starting up systems",bgColor:"bg-gradient-to-r from-slate-50 to-gray-50",iconColor:"text-slate-600",borderColor:"border-slate-200/60",glowColor:"shadow-slate-200/50",gradientFrom:"from-slate-400",gradientTo:"to-gray-400",duration:200},analyzing:{icon:n.$p,text:"Analyzing",description:"Understanding your request",bgColor:"bg-gradient-to-r from-cyan-50 to-blue-50",iconColor:"text-cyan-600",borderColor:"border-cyan-200/60",glowColor:"shadow-cyan-200/50",gradientFrom:"from-cyan-400",gradientTo:"to-blue-400",duration:300},routing:{icon:n.EF,text:"Smart routing",description:"Finding optimal path",bgColor:"bg-gradient-to-r from-indigo-50 to-purple-50",iconColor:"text-indigo-600",borderColor:"border-indigo-200/60",glowColor:"shadow-indigo-200/50",gradientFrom:"from-indigo-400",gradientTo:"to-purple-400",duration:400},complexity_analysis:{icon:n.XL,text:"Analyzing complexity",description:"Evaluating request depth",bgColor:"bg-gradient-to-r from-amber-50 to-yellow-50",iconColor:"text-amber-600",borderColor:"border-amber-200/60",glowColor:"shadow-amber-200/50",gradientFrom:"from-amber-400",gradientTo:"to-yellow-400",duration:500},role_classification:{icon:n.Gg,text:"Assembling specialists",description:"Building expert team",bgColor:"bg-gradient-to-r from-violet-50 to-purple-50",iconColor:"text-violet-600",borderColor:"border-violet-200/60",glowColor:"shadow-violet-200/50",gradientFrom:"from-violet-400",gradientTo:"to-purple-400",duration:600},preparing:{icon:n.DQ,text:"Preparing",description:"Setting up processing",bgColor:"bg-gradient-to-r from-orange-50 to-amber-50",iconColor:"text-orange-600",borderColor:"border-orange-200/60",glowColor:"shadow-orange-200/50",gradientFrom:"from-orange-400",gradientTo:"to-amber-400",duration:300},connecting:{icon:n.nr,text:"Connecting",description:"Establishing AI link",bgColor:"bg-gradient-to-r from-rose-50 to-pink-50",iconColor:"text-rose-600",borderColor:"border-rose-200/60",glowColor:"shadow-rose-200/50",gradientFrom:"from-rose-400",gradientTo:"to-pink-400",duration:400},generating:{icon:n.Y3,text:"Thinking deeply",description:"AI processing in progress",bgColor:"bg-gradient-to-r from-emerald-50 to-teal-50",iconColor:"text-emerald-600",borderColor:"border-emerald-200/60",glowColor:"shadow-emerald-200/50",gradientFrom:"from-emerald-400",gradientTo:"to-teal-400",duration:800},typing:{icon:n.R2,text:"Streaming response",description:"Delivering your answer",bgColor:"bg-gradient-to-r from-green-50 to-emerald-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-emerald-400"},finalizing:{icon:n.BZ,text:"Finalizing",description:"Adding finishing touches",bgColor:"bg-gradient-to-r from-teal-50 to-cyan-50",iconColor:"text-teal-600",borderColor:"border-teal-200/60",glowColor:"shadow-teal-200/50",gradientFrom:"from-teal-400",gradientTo:"to-cyan-400",duration:200},complete:{icon:n.Zu,text:"Complete",description:"Response delivered",bgColor:"bg-gradient-to-r from-green-50 to-lime-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-lime-400",duration:100}};function c(e){let{currentStage:r,isStreaming:t=!1,className:s="",onStageChange:c,orchestrationStatus:m}=e,[g,x]=(0,i.useState)(r),[h,u]=(0,i.useState)(!1),[p,b]=(0,i.useState)(0),[f,y]=(0,i.useState)(""),[j,w]=(0,i.useState)(1),v=[{bgColor:"bg-gradient-to-r from-blue-50 to-indigo-50",iconColor:"text-blue-600",borderColor:"border-blue-200/60",glowColor:"shadow-blue-200/50",gradientFrom:"from-blue-400",gradientTo:"to-indigo-400"},{bgColor:"bg-gradient-to-r from-purple-50 to-violet-50",iconColor:"text-purple-600",borderColor:"border-purple-200/60",glowColor:"shadow-purple-200/50",gradientFrom:"from-purple-400",gradientTo:"to-violet-400"},{bgColor:"bg-gradient-to-r from-indigo-50 to-blue-50",iconColor:"text-indigo-600",borderColor:"border-indigo-200/60",glowColor:"shadow-indigo-200/50",gradientFrom:"from-indigo-400",gradientTo:"to-blue-400"},{bgColor:"bg-gradient-to-r from-cyan-50 to-teal-50",iconColor:"text-cyan-600",borderColor:"border-cyan-200/60",glowColor:"shadow-cyan-200/50",gradientFrom:"from-cyan-400",gradientTo:"to-teal-400"},{bgColor:"bg-gradient-to-r from-teal-50 to-emerald-50",iconColor:"text-teal-600",borderColor:"border-teal-200/60",glowColor:"shadow-teal-200/50",gradientFrom:"from-teal-400",gradientTo:"to-emerald-400"},{bgColor:"bg-gradient-to-r from-green-50 to-lime-50",iconColor:"text-green-600",borderColor:"border-green-200/60",glowColor:"shadow-green-200/50",gradientFrom:"from-green-400",gradientTo:"to-lime-400"},{bgColor:"bg-gradient-to-r from-yellow-50 to-amber-50",iconColor:"text-yellow-600",borderColor:"border-yellow-200/60",glowColor:"shadow-yellow-200/50",gradientFrom:"from-yellow-400",gradientTo:"to-amber-400"},{bgColor:"bg-gradient-to-r from-orange-50 to-red-50",iconColor:"text-orange-600",borderColor:"border-orange-200/60",glowColor:"shadow-orange-200/50",gradientFrom:"from-orange-400",gradientTo:"to-red-400"},{bgColor:"bg-gradient-to-r from-rose-50 to-pink-50",iconColor:"text-rose-600",borderColor:"border-rose-200/60",glowColor:"shadow-rose-200/50",gradientFrom:"from-rose-400",gradientTo:"to-pink-400"},{bgColor:"bg-gradient-to-r from-emerald-50 to-teal-50",iconColor:"text-emerald-600",borderColor:"border-emerald-200/60",glowColor:"shadow-emerald-200/50",gradientFrom:"from-emerald-400",gradientTo:"to-teal-400"}],N=v[p%v.length],k=m?{...d[g],...N,icon:m.includes("\uD83D\uDD0D")||m.includes("detected")?n.$p:m.includes("✅")||m.includes("complete")?n.C1:m.includes("\uD83C\uDFAF")||m.includes("Selected")?l.g:m.includes("\uD83C\uDFD7️")||m.includes("workflow")?n.DP:m.includes("\uD83E\uDD16")||m.includes("agent")?n.K6:m.includes("\uD83D\uDC51")||m.includes("supervisor")?n.Gg:m.includes("\uD83D\uDCCB")||m.includes("Planning")?n.Pp:m.includes("\uD83D\uDE80")||m.includes("starting")?n.P:m.includes("\uD83D\uDD04")||m.includes("synthesizing")?n.EF:d[g].icon}:d[g],C=k.icon;return(0,i.useEffect)(()=>{r!==g&&(u(!0),setTimeout(()=>{x(r),u(!1),null==c||c(r)},200))},[r,g,c]),(0,i.useEffect)(()=>{m&&m!==f&&(y(m),b(e=>e+1),u(!0),w(2.5),setTimeout(()=>{u(!1)},300),setTimeout(()=>{w(1)},1e3))},[m,f]),(0,a.jsxs)("div",{className:"jsx-f56d70faa8a01b64 "+"flex justify-start ".concat(s),children:[(0,a.jsxs)("div",{className:"jsx-f56d70faa8a01b64 relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0",children:[(0,a.jsx)("div",{style:{animation:"spin ".concat(1.2/j,"s linear infinite"),borderTopColor:k.iconColor.replace("text-",""),filter:"drop-shadow(0 0 6px rgba(59, 130, 246, 0.4))"},className:"jsx-f56d70faa8a01b64 absolute -inset-2 w-10 h-10 rounded-full border-[3px] border-t-blue-500 border-r-transparent border-b-transparent border-l-transparent"}),(0,a.jsx)("div",{style:{animation:"spin ".concat(1.8/j,"s linear infinite reverse"),borderTopColor:k.iconColor.replace("text-","").replace("600","400"),opacity:.7},className:"jsx-f56d70faa8a01b64 absolute -inset-1.5 w-9 h-9 rounded-full border-[2px] border-t-purple-400 border-r-transparent border-b-transparent border-l-transparent"}),(0,a.jsx)("div",{style:{animation:"spin ".concat(.8/j,"s linear infinite"),borderTopColor:k.iconColor.replace("text-","").replace("600","300"),opacity:.5},className:"jsx-f56d70faa8a01b64 absolute -inset-1 w-8 h-8 rounded-full border-[1px] border-t-cyan-300 border-r-transparent border-b-transparent border-l-transparent"}),(0,a.jsx)("div",{style:{borderColor:k.iconColor.replace("text-","").replace("600","200"),opacity:.3,animation:"pulse 2s ease-in-out infinite"},className:"jsx-f56d70faa8a01b64 absolute -inset-0.5 w-7 h-7 rounded-full border animate-pulse"}),(0,a.jsx)("div",{style:{boxShadow:"0 0 12px ".concat(k.iconColor.replace("text-",""),"40, 0 0 24px ").concat(k.iconColor.replace("text-",""),"20")},className:"jsx-f56d70faa8a01b64 "+"relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 ".concat(k.bgColor," border-2 ").concat(k.borderColor," shadow-lg backdrop-blur-sm"),children:(0,a.jsx)(C,{className:"jsx-f56d70faa8a01b64 "+"w-3.5 h-3.5 transition-all duration-500 ".concat(k.iconColor," ").concat(h?"scale-125 rotate-12":"scale-100"," drop-shadow-lg")})})]}),(0,a.jsxs)("div",{className:"jsx-f56d70faa8a01b64 "+"max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 ".concat(k.bgColor," ").concat(k.borderColor," border ").concat(k.glowColor," shadow-sm backdrop-blur-sm"),children:[(0,a.jsx)("div",{className:"jsx-f56d70faa8a01b64 flex items-center space-x-1.5",children:(0,a.jsxs)("div",{className:"jsx-f56d70faa8a01b64 transition-all duration-500",children:[(0,a.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"text-xs font-semibold transition-colors duration-500 ".concat(k.iconColor," tracking-wide"),children:m||k.text}),t&&"typing"===g&&!m&&(0,a.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"ml-1.5 text-[10px] opacity-80 ".concat(k.iconColor," font-medium"),children:"• Live"}),m&&(0,a.jsx)("span",{className:"jsx-f56d70faa8a01b64 "+"ml-1.5 text-[10px] opacity-80 ".concat(k.iconColor," font-medium"),children:"• Orchestrating"})]})}),("generating"===g||"typing"===g)&&(0,a.jsx)("div",{className:"jsx-f56d70faa8a01b64 mt-2",children:(0,a.jsx)("div",{className:"jsx-f56d70faa8a01b64 h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20",children:(0,a.jsx)("div",{style:{width:"typing"===g?"100%":"60%",animation:"typing"===g?"progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite":"progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite"},className:"jsx-f56d70faa8a01b64 "+"h-full rounded-full transition-all duration-1000 bg-gradient-to-r ".concat(k.gradientFrom," ").concat(k.gradientTo," relative overflow-hidden"),children:(0,a.jsx)("div",{style:{animation:"progressShine 2s linear infinite",transform:"skewX(-20deg)"},className:"jsx-f56d70faa8a01b64 absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"})})})})]}),(0,a.jsx)(o(),{id:"f56d70faa8a01b64",children:"@-webkit-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-webkit-keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-moz-keyframes progressShine{0%{-moz-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-moz-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-o-keyframes progressShine{0%{-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);-moz-transform:translatex(-100%)skewx(-20deg);-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);-moz-transform:translatex(300%)skewx(-20deg);-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-webkit-keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);transform:scaley(1.1)}}@-moz-keyframes progressPulse{0%,100%{opacity:1;-moz-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-moz-transform:scaley(1.1);transform:scaley(1.1)}}@-o-keyframes progressPulse{0%,100%{opacity:1;-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-o-transform:scaley(1.1);transform:scaley(1.1)}}@keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);-moz-transform:scaley(1);-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);-moz-transform:scaley(1.1);-o-transform:scaley(1.1);transform:scaley(1.1)}}@-webkit-keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-moz-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-o-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}"})]})}},46197:(e,r,t)=>{t(95155),t(12115),t(26203)},52399:(e,r,t)=>{t.d(r,{c:()=>i});var a=t(95155),s=t(12115),o=t(60875);let i=e=>{var r,t;let{message:i}=e,n=e=>{switch(e){case"assignment":return(0,a.jsx)(o.fl,{className:"w-3 h-3 text-blue-500"});case"completion":return(0,a.jsx)(o.C1,{className:"w-3 h-3 text-green-500"});case"handoff":return(0,a.jsx)(o.fl,{className:"w-3 h-3 text-purple-500"});default:return null}},l="moderator"===i.sender,d=(e=>{if(!e)return"from-blue-500 to-blue-600";let r=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return r[e.split("").reduce((e,r)=>e+r.charCodeAt(0),0)%r.length]})(i.roleId);return(0,a.jsx)("div",{className:"flex ".concat("justify-start"," mb-4"),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ".concat(d," flex items-center justify-center text-white shadow-sm"),children:(r=i.sender,i.roleId,"moderator"===r?(0,a.jsx)(o.BZ,{className:"w-4 h-4"}):(0,a.jsx)(o.YE,{className:"w-4 h-4"}))}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("span",{className:"text-sm font-semibold ".concat(l?"text-blue-700":"text-gray-700"),children:i.senderName}),n(i.type)&&(0,a.jsx)("div",{className:"flex items-center",children:n(i.type)}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:i.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),(0,a.jsx)("div",{className:"inline-block px-4 py-3 rounded-2xl shadow-sm ".concat(l?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"," ").concat("completion"===i.type?"border-green-200 bg-green-50":"assignment"===i.type?"border-blue-200 bg-blue-50":"handoff"===i.type?"border-purple-200 bg-purple-50":""),children:(0,a.jsx)("div",{className:"text-sm leading-relaxed ".concat(l?"text-blue-900":"text-gray-800"," ").concat("completion"===i.type?"text-green-900":"assignment"===i.type?"text-blue-900":"handoff"===i.type?"text-purple-900":""),children:i.content.split("\n").map((e,r)=>(0,a.jsxs)(s.Fragment,{children:[e,r<i.content.split("\n").length-1&&(0,a.jsx)("br",{})]},r))})}),"message"!==i.type&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat("assignment"===i.type?"bg-blue-100 text-blue-800":"completion"===i.type?"bg-green-100 text-green-800":"handoff"===i.type?"bg-purple-100 text-purple-800":"clarification"===i.type?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:["assignment"===i.type&&"\uD83D\uDCCB Task Assignment","completion"===i.type&&"✅ Task Complete","handoff"===i.type&&"\uD83D\uDD04 Handoff","clarification"===i.type&&"❓ Clarification"]})})]})]})})}},52469:(e,r,t)=>{t.d(r,{default:()=>i});var a=t(12115),s=t(35695);let o=["/features","/pricing","/about","/auth/signin","/auth/signup"];function i(){let e=(0,s.useRouter)();return(0,a.useEffect)(()=>{let r=()=>{o.forEach(r=>{e.prefetch(r)})};"requestIdleCallback"in window?window.requestIdleCallback(r,{timeout:2e3}):setTimeout(r,100)},[e]),null}},73360:(e,r,t)=>{t.d(r,{A:()=>l});var a=t(95155),s=t(12115),o=t(91480),i=t(87162),n=t(80377);function l(e){let{configId:r,onDocumentUploaded:t,onDocumentDeleted:l,theme:d="light"}=e,[c,m]=(0,s.useState)([]),[g,x]=(0,s.useState)(!1),[h,u]=(0,s.useState)(!1),[p,b]=(0,s.useState)(0),[f,y]=(0,s.useState)(!1),[j,w]=(0,s.useState)(null),[v,N]=(0,s.useState)(null),k=(0,s.useRef)(null),C=(0,i.Z)(),T=(0,s.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0;if(r){0===e&&u(!0);try{let a=await fetch("/api/documents/list?configId=".concat(r));if(a.ok){let r=(await a.json()).documents||[];if(t&&e<3&&!r.find(e=>e.id===t))return void setTimeout(()=>{T(e+1,t)},(e+1)*500);m(e=>{if(!t||r.find(e=>e.id===t))return r.sort((e,r)=>new Date(r.created_at).getTime()-new Date(e.created_at).getTime());{let t=new Set(r.map(e=>e.id)),a=e.filter(e=>!t.has(e.id));return[...r,...a].sort((e,r)=>new Date(r.created_at).getTime()-new Date(e.created_at).getTime())}})}}catch(r){e<2&&setTimeout(()=>{T(e+1,t)},1e3)}finally{(0===e||t)&&u(!1)}}},[r]);s.useEffect(()=>{T()},[T]);let S=async e=>{if(!r)return void w("Please select an API configuration first");let a=e[0];if(a){if(!["application/pdf","text/plain","text/markdown"].includes(a.type))return void w("Please upload PDF, TXT, or MD files only");if(a.size>0xa00000)return void w("File size must be less than 10MB");x(!0),w(null),N(null),b(0);try{let e=new FormData;e.append("file",a),e.append("configId",r);let s=setInterval(()=>{b(e=>Math.min(e+10,90))},200),o=await fetch("/api/documents/upload",{method:"POST",body:e});if(clearInterval(s),b(100),!o.ok){let e=await o.json();throw Error(e.error||"Upload failed")}let i=await o.json();N("✨ ".concat(a.name," uploaded successfully! Processing ").concat(i.document.chunks_total," chunks."));let n={id:i.document.id,filename:i.document.filename||a.name,file_type:a.type,file_size:a.size,status:i.document.status||"processing",chunks_count:i.document.chunks_processed||0,created_at:new Date().toISOString()};m(e=>e.find(e=>e.id===n.id)?e.map(e=>e.id===n.id?n:e):[n,...e]),setTimeout(async()=>{await T(0,i.document.id)},200),null==t||t()}catch(e){w("Upload failed: ".concat(e.message)),setTimeout(()=>w(null),8e3)}finally{x(!1),b(0),k.current&&(k.current.value=""),v&&setTimeout(()=>N(null),5e3)}}},A=(0,s.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?y(!0):"dragleave"===e.type&&y(!1)},[]),D=(0,s.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),y(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&S(e.dataTransfer.files)},[r]),z=(e,r)=>{C.showConfirmation({title:"Delete Document",message:'Are you sure you want to delete "'.concat(r,'"? This will permanently remove the document and all its processed chunks from your knowledge base. This action cannot be undone.'),confirmText:"Delete Document",cancelText:"Cancel",type:"danger"},async()=>{m(r=>r.filter(r=>r.id!==e));try{let r=await fetch("/api/documents/".concat(e),{method:"DELETE"});if(!r.ok){let e=await r.json().catch(()=>({error:"Unknown error"}));throw m(c),Error(e.error||"HTTP ".concat(r.status,": Failed to delete document"))}await r.json(),N("Document deleted successfully"),setTimeout(async()=>{await T()},200),null==l||l(),setTimeout(()=>N(null),3e3)}catch(e){m(c),w("Delete failed: ".concat(e.message)),setTimeout(()=>w(null),8e3)}})},F=e=>{if(0===e)return"0 Bytes";let r=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,r)).toFixed(2))+" "+["Bytes","KB","MB","GB"][r]},R=e=>e.includes("pdf")?(0,a.jsx)(o.iU,{className:"w-5 h-5 text-red-500"}):e.includes("word")?(0,a.jsx)(o.iU,{className:"w-5 h-5 text-blue-500"}):(0,a.jsx)(o.ZH,{className:"w-5 h-5 text-gray-500"});return(0,a.jsxs)("div",{className:"space-y-6",children:[j&&(0,a.jsx)("div",{className:"rounded-xl p-4 ".concat("dark"===d?"bg-red-900/20 border border-red-500/30":"bg-red-50 border border-red-200"),children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.RI,{className:"w-5 h-5 ".concat("dark"===d?"text-red-400":"text-red-600")}),(0,a.jsx)("p",{className:"text-sm font-medium ".concat("dark"===d?"text-red-200":"text-red-800"),children:j})]})}),v&&(0,a.jsx)("div",{className:"rounded-xl p-4 ".concat("dark"===d?"bg-green-900/20 border border-green-500/30":"bg-green-50 border border-green-200"),children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.rA,{className:"w-5 h-5 ".concat("dark"===d?"text-green-400":"text-green-600")}),(0,a.jsx)("p",{className:"text-sm font-medium ".concat("dark"===d?"text-green-200":"text-green-800"),children:v})]})}),(0,a.jsxs)("div",{className:"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform ".concat(f?"dark"===d?"border-orange-400 bg-orange-900/20 scale-105 shadow-lg":"border-orange-400 bg-orange-50 scale-105 shadow-lg":"dark"===d?"border-gray-600 hover:border-orange-400 hover:bg-orange-900/10 hover:scale-102 hover:shadow-md":"border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md"," ").concat(r?"cursor-pointer":"opacity-50 cursor-not-allowed"),onDragEnter:A,onDragLeave:A,onDragOver:A,onDrop:D,onClick:()=>{var e;return r&&(null==(e=k.current)?void 0:e.click())},children:[(0,a.jsx)("input",{ref:k,type:"file",className:"hidden",accept:".pdf,.txt,.md",onChange:e=>{e.target.files&&e.target.files[0]&&S(e.target.files)},disabled:!r||g}),g?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(o.kr,{className:"w-12 h-12 text-orange-500 mx-auto animate-spin"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-lg font-medium ".concat("dark"===d?"text-white":"text-gray-900"),children:"Processing Document..."}),(0,a.jsx)("div",{className:"w-full rounded-full h-2 ".concat("dark"===d?"bg-gray-700":"bg-gray-200"),children:(0,a.jsx)("div",{className:"bg-orange-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(p,"%")}})}),(0,a.jsxs)("p",{className:"text-sm ".concat("dark"===d?"text-gray-300":"text-gray-600"),children:[p,"% complete"]})]})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(o._O,{className:"w-12 h-12 mx-auto ".concat("dark"===d?"text-gray-500":"text-gray-400")}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-medium ".concat("dark"===d?"text-white":"text-gray-900"),children:r?"Upload Knowledge Documents":"Select a configuration first"}),(0,a.jsx)("p",{className:"text-sm mt-1 ".concat("dark"===d?"text-gray-300":"text-gray-600"),children:"Drag and drop files here, or click to browse"}),(0,a.jsx)("p",{className:"text-xs mt-2 ".concat("dark"===d?"text-gray-400":"text-gray-500"),children:"Supports PDF, TXT, MD files up to 10MB"})]})]})]}),c.length>0&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold ".concat("dark"===d?"text-white":"text-gray-900"),children:"Uploaded Documents"}),h&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm ".concat("dark"===d?"text-gray-300":"text-gray-600"),children:[(0,a.jsx)(o.kr,{className:"w-4 h-4 animate-spin"}),(0,a.jsx)("span",{children:"Refreshing..."})]})]}),(0,a.jsx)("div",{className:"grid gap-4",children:c.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-xl transition-shadow ".concat("dark"===d?"bg-gray-800/50 border border-gray-700/50 hover:shadow-lg hover:shadow-gray-900/20":"bg-white border border-gray-200 hover:shadow-md"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[R(e.file_type),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium ".concat("dark"===d?"text-white":"text-gray-900"),children:e.filename}),(0,a.jsxs)("p",{className:"text-sm ".concat("dark"===d?"text-gray-300":"text-gray-600"),children:[F(e.file_size)," • ",e.chunks_count," chunks"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:["completed"===e.status&&(0,a.jsx)(o.rA,{className:"w-5 h-5 text-green-500"}),"processing"===e.status&&(0,a.jsx)(o.kr,{className:"w-5 h-5 text-orange-500 animate-spin"}),"failed"===e.status&&(0,a.jsx)(o.RI,{className:"w-5 h-5 text-red-500"}),(0,a.jsx)("span",{className:"text-sm font-medium ".concat("completed"===e.status?"text-green-600":"processing"===e.status?"text-orange-600":"text-red-600"),children:"completed"===e.status?"Ready":"processing"===e.status?"Processing":"Failed"})]}),(0,a.jsx)("button",{onClick:()=>z(e.id,e.filename),className:"p-1 transition-colors ".concat("dark"===d?"text-gray-500 hover:text-red-400":"text-gray-400 hover:text-red-500"),title:"Delete document",children:(0,a.jsx)(o.X,{className:"w-4 h-4"})})]})]},e.id))})]}),(0,a.jsx)(n.A,{isOpen:C.isOpen,onClose:C.hideConfirmation,onConfirm:C.onConfirm,title:C.title,message:C.message,confirmText:C.confirmText,cancelText:C.cancelText,type:C.type,isLoading:C.isLoading})]})}},79112:(e,r,t)=>{t.d(r,{A:()=>n});var a=t(95155),s=t(12115);let o=(0,s.lazy)(()=>Promise.all([t.e(5006),t.e(5928),t.e(4726),t.e(4280),t.e(2548),t.e(8960),t.e(8961),t.e(703),t.e(3285),t.e(2396),t.e(4842),t.e(6419),t.e(6210),t.e(1826),t.e(721),t.e(7069),t.e(5313),t.e(6585),t.e(3613),t.e(5260),t.e(7525),t.e(3310),t.e(7096),t.e(7455),t.e(678),t.e(8730)]).then(t.bind(t,90882))),i=()=>(0,a.jsxs)("div",{className:"space-y-2 animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]});function n(e){let{content:r,className:t=""}=e;return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)(i,{}),children:(0,a.jsx)(o,{content:r,className:t})})}},79958:(e,r,t)=>{t.d(r,{A:()=>s,_:()=>o});var a=t(95155);function s(){return(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-700 rounded animate-pulse"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-8 bg-gray-700 rounded w-48 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-64 animate-pulse"})]})]})}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-700 rounded w-32 mb-6 animate-pulse"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-20 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-10 bg-gray-700 rounded animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-16 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-10 bg-gray-700 rounded animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-20 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-10 bg-gray-700 rounded animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-12 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-10 bg-gray-700 rounded animate-pulse"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-24 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-6 bg-gray-700 rounded animate-pulse"})]})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsx)("div",{className:"h-10 bg-gray-700 rounded w-32 animate-pulse"})})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-700 rounded w-40 animate-pulse"}),(0,a.jsx)("div",{className:"h-8 bg-gray-700 rounded w-24 animate-pulse"})]}),(0,a.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,a.jsxs)("div",{className:"border border-gray-700/50 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gray-700 rounded-lg animate-pulse"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-5 bg-gray-700 rounded w-32 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-48 animate-pulse"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-700 rounded w-20 animate-pulse"}),(0,a.jsx)("div",{className:"h-8 bg-gray-700 rounded w-16 animate-pulse"}),(0,a.jsx)("div",{className:"h-8 bg-gray-700 rounded w-16 animate-pulse"})]})]}),(0,a.jsxs)("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-3 bg-gray-700 rounded w-16 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-20 animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-3 bg-gray-700 rounded w-12 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-16 animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-3 bg-gray-700 rounded w-20 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-24 animate-pulse"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-3 bg-gray-700 rounded w-14 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-18 animate-pulse"})]})]}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-28 mb-2 animate-pulse"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:[1,2,3].map(e=>(0,a.jsx)("div",{className:"h-6 bg-gray-700 rounded-full w-20 animate-pulse"},e))})]})]},e))})]}),(0,a.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("div",{className:"h-6 bg-gray-700 rounded w-36 animate-pulse"}),(0,a.jsx)("div",{className:"h-8 bg-gray-700 rounded w-28 animate-pulse"})]}),(0,a.jsx)("div",{className:"space-y-3",children:[1,2].map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-700/50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-32 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-3 bg-gray-700 rounded w-48 animate-pulse"})]}),(0,a.jsx)("div",{className:"h-8 bg-gray-700 rounded w-16 animate-pulse"})]},e))})]})]})}function o(){return(0,a.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-700 rounded animate-pulse"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-6 bg-gray-700 rounded w-40 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-56 animate-pulse"})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[1,2,3,4].map(e=>(0,a.jsx)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-700 rounded animate-pulse"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-700 rounded w-24 mb-1 animate-pulse"}),(0,a.jsx)("div",{className:"h-3 bg-gray-700 rounded w-32 animate-pulse"})]})]})},e))})]})}t(12115)},90433:(e,r,t)=>{t.d(r,{A:()=>h});var a=t(95155),s=t(12115),o=t(95494),i=t(95060),n=t(22261),l=t(99323),d=t(78817),c=t(69903),m=t(42126),g=t(64198);function x(e){let{children:r}=e,{isCollapsed:t,isHovered:d,collapseSidebar:x}=(0,n.c)(),{isNavigating:h,targetRoute:u,isPageCached:p}=(0,l.bu)()||{isNavigating:!1,targetRoute:null,isPageCached:()=>!1},{toasts:b,removeToast:f}=(0,g.dj)(),[y,j]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=()=>{j(window.innerWidth>=1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let w=y?!t||d?256:64:0;return(0,m.v)({maxConcurrent:2,idleTimeout:1500,backgroundDelay:3e3}),(0,a.jsxs)("div",{className:"flex h-screen overflow-hidden",children:[(0,a.jsx)("div",{className:"hidden lg:block fixed left-0 top-0 h-full z-40",children:(0,a.jsx)(i.A,{})}),(0,a.jsxs)("div",{className:"lg:hidden fixed inset-0 z-50 ".concat(t?"pointer-events-none":""),children:[(0,a.jsx)("div",{onClick:x,className:"absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer ".concat(t?"opacity-0":"opacity-50")}),(0,a.jsx)("div",{className:"absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out ".concat(t?"-translate-x-full":"translate-x-0"),children:(0,a.jsx)(i.A,{})})]}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col min-w-0 transition-all duration-200 ease-out",style:{marginLeft:"".concat(w,"px")},children:[(0,a.jsx)("div",{className:"fixed top-0 right-0 z-30 transition-all duration-200 ease-out",style:{left:"".concat(w,"px")},children:(0,a.jsx)(o.A,{})}),(0,a.jsx)("main",{className:"flex-1 overflow-y-auto content-area mt-16 bg-[#040716] w-full",children:(0,a.jsx)("div",{className:"w-full h-full min-h-screen",children:(0,a.jsx)("div",{className:"page-transition",children:h&&u?(0,a.jsx)(c.A,{targetRoute:u,children:r}):r})})})]}),(0,a.jsx)(g.N9,{toasts:b,onRemove:f})]})}function h(e){let{children:r}=e;return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)(d.A,{targetRoute:null}),children:(0,a.jsx)(x,{children:r})})}},90441:(e,r,t)=>{t.d(r,{K:()=>l});var a=t(95155),s=t(12115),o=t(35695),i=t(1571),n=t(31430);function l(e){let{isOpen:r,onClose:t}=e,[l,d]=(0,s.useState)(""),[c,m]=(0,s.useState)([]),[g,x]=(0,s.useState)(!1),[h,u]=(0,s.useState)(0),p=(0,o.useRouter)(),b=(0,s.useRef)(null),f=[{id:"dashboard",title:"Dashboard",subtitle:"Overview & analytics",type:"page",href:"/dashboard",icon:n.r9},{id:"my-models",title:"My Models",subtitle:"API key management",type:"page",href:"/my-models",icon:n.RY},{id:"playground",title:"Playground",subtitle:"Test your models",type:"page",href:"/playground",icon:n.cu},{id:"routing-setup",title:"Routing Setup",subtitle:"Configure routing",type:"page",href:"/routing-setup",icon:n.sR},{id:"logs",title:"Logs",subtitle:"Request history",type:"page",href:"/logs",icon:n.AQ}];(0,s.useEffect)(()=>{r&&b.current&&b.current.focus()},[r]);let y=async e=>{if(!e.trim())return void m([]);x(!0);let r=[];try{let t=f.filter(r=>r.title.toLowerCase().includes(e.toLowerCase())||r.subtitle.toLowerCase().includes(e.toLowerCase()));r.push(...t);let a=[];try{let r=await fetch("/api/custom-configs");if(r.ok){let t=await r.json();a=(Array.isArray(t)?t:[]).filter(r=>{var t;return null==r||null==(t=r.name)?void 0:t.toLowerCase().includes(e.toLowerCase())}).map(e=>({id:"config-".concat(e.id),title:e.name,subtitle:"Configuration • ".concat(e.routing_strategy||"Default"),type:"config",href:"/my-models/".concat(e.id),icon:n.DP,metadata:new Date(e.created_at).toLocaleDateString()}))}}catch(e){}let s=[];try{let r=await fetch("/api/user-api-keys");if(r.ok){let t=await r.json(),a=t.api_keys||t||[];Array.isArray(a)&&(s=a.filter(r=>{var t;return null==r||null==(t=r.key_name)?void 0:t.toLowerCase().includes(e.toLowerCase())}).map(e=>({id:"user-key-".concat(e.id),title:e.key_name,subtitle:"User API Key • ".concat(e.status||"Unknown"),type:"user-api-key",href:"/my-models/".concat(e.custom_api_config_id,"?tab=user-api-keys"),icon:n.RY,metadata:"".concat(e.total_requests||0," requests")})))}}catch(e){}r.push(...a,...s);let o=r.sort((r,t)=>{let a=r.title.toLowerCase()===e.toLowerCase(),s=t.title.toLowerCase()===e.toLowerCase();return a&&!s?-1:!a&&s?1:0});m(o.slice(0,10)),u(0)}catch(e){m([])}finally{x(!1)}};(0,s.useEffect)(()=>{let e=setTimeout(()=>{y(l)},300);return()=>clearTimeout(e)},[l]);let j=e=>{p.push(e.href),t(),d("")};return r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-sm z-50",onClick:t}),(0,a.jsx)("div",{className:"fixed top-20 left-1/2 transform -translate-x-1/2 w-full max-w-2xl mx-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center px-4 py-3 border-b border-gray-200",children:[(0,a.jsx)(i.$,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,a.jsx)("input",{ref:b,type:"text",placeholder:"Search configurations, API keys, pages...",value:l,onChange:e=>d(e.target.value),onKeyDown:e=>{switch(e.key){case"ArrowDown":e.preventDefault(),u(e=>Math.min(e+1,c.length-1));break;case"ArrowUp":e.preventDefault(),u(e=>Math.max(e-1,0));break;case"Enter":e.preventDefault(),c[h]&&j(c[h]);break;case"Escape":e.preventDefault(),t()}},className:"flex-1 text-gray-900 placeholder-gray-500 bg-transparent border-none outline-none text-sm"}),(0,a.jsx)("button",{onClick:t,className:"p-1 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(i.f,{className:"h-4 w-4 text-gray-400"})})]}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:g?(0,a.jsxs)("div",{className:"px-4 py-8 text-center text-gray-500",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-sm",children:"Searching..."})]}):c.length>0?(0,a.jsx)("div",{className:"py-2",children:c.map((e,r)=>{let t=e.icon;return(0,a.jsxs)("button",{onClick:()=>j(e),className:"w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center space-x-3 ".concat(r===h?"bg-orange-50 border-r-2 border-orange-500":""),children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(t,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),(0,a.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.subtitle})]}),e.metadata&&(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("span",{className:"text-xs text-gray-400",children:e.metadata})})]},e.id)})}):l.trim()?(0,a.jsxs)("div",{className:"px-4 py-8 text-center text-gray-500",children:[(0,a.jsx)(i.$,{className:"h-8 w-8 mx-auto text-gray-300 mb-2"}),(0,a.jsxs)("p",{className:"text-sm",children:['No results found for "',l,'"']}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:'Try searching for pages like "dashboard", "playground", or "settings"'})]}):(0,a.jsxs)("div",{className:"px-4 py-8 text-center text-gray-500",children:[(0,a.jsx)(i.$,{className:"h-8 w-8 mx-auto text-gray-300 mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"Start typing to search..."}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Search across configurations, API keys, and pages"})]})}),(0,a.jsxs)("div",{className:"px-4 py-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{children:"↑↓ Navigate"}),(0,a.jsx)("span",{children:"↵ Select"}),(0,a.jsx)("span",{children:"Esc Close"})]}),(0,a.jsxs)("span",{children:[c.length," results"]})]})]})})]}):null}},90882:(e,r,t)=>{t.r(r),t.d(r,{default:()=>x});var a=t(95155),s=t(28831),o=t(70765),i=t(18730),n=t(15478),l=t(86212),d=t(12115),c=t(95803);class m extends d.Component{static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(){this.props.onError()}render(){return this.state.hasError?null:this.props.children}constructor(e){super(e),this.state={hasError:!1}}}function g(){return e=>{(0,l.YR)(e,"paragraph",(e,r,t)=>{1===e.children.length&&"code"===e.children[0].type&&"number"==typeof r&&t&&t.children&&(t.children[r]=e.children[0])})}}function x(e){let{content:r,className:t=""}=e,[l,x]=(0,d.useState)(!1);(0,d.useEffect)(()=>{x(!1)},[r]);let h=r.includes("# ")||r.includes("## ")||r.includes("* ")||r.includes("- ")||r.includes("**")||r.includes("```")?r:r.split(/\n\s*\n/).map(e=>(e=e.trim())?e.replace(/\n(?!\n)/g," "):"").filter(e=>e).join("\n\n");return l?(0,a.jsx)("div",{className:"markdown-content ".concat(t),children:(0,a.jsx)("pre",{className:"whitespace-pre-wrap text-sm text-white leading-relaxed",children:r})}):(0,a.jsx)("div",{className:"markdown-content ".concat(t),children:(0,a.jsx)(d.Suspense,{fallback:(0,a.jsxs)("div",{className:"animate-pulse bg-gray-100 rounded p-4",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-2"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-2 w-3/4"}),(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded w-1/2"})]}),children:(0,a.jsx)(m,{onError:()=>x(!0),children:(0,a.jsx)(s.Ay,{remarkPlugins:[o.A,g],components:{h1:e=>{let{children:r}=e;return(0,a.jsx)("h1",{className:"text-xl font-bold mb-3 mt-4 first:mt-0 text-white",children:r})},h2:e=>{let{children:r}=e;return(0,a.jsx)("h2",{className:"text-lg font-bold mb-2 mt-3 first:mt-0 text-white",children:r})},h3:e=>{let{children:r}=e;return(0,a.jsx)("h3",{className:"text-base font-bold mb-2 mt-3 first:mt-0 text-white",children:r})},h4:e=>{let{children:r}=e;return(0,a.jsx)("h4",{className:"text-sm font-bold mb-1 mt-2 first:mt-0 text-white",children:r})},p:e=>{let{children:r}=e;return(0,a.jsx)("p",{className:"mb-3 last:mb-0 leading-relaxed text-white break-words",children:r})},strong:e=>{let{children:r}=e;return(0,a.jsx)("strong",{className:"font-bold text-white",children:r})},em:e=>{let{children:r}=e;return(0,a.jsx)("em",{className:"italic text-white",children:r})},ul:e=>{let{children:r}=e;return(0,a.jsx)("ul",{className:"list-disc list-inside mb-3 space-y-1 text-white",children:r})},ol:e=>{let{children:r}=e;return(0,a.jsx)("ol",{className:"list-decimal list-inside mb-3 space-y-1 text-white",children:r})},li:e=>{let{children:r}=e;return(0,a.jsx)("li",{className:"leading-relaxed text-white",children:r})},code:e=>{let{node:r,inline:t,className:s,children:o,...l}=e,d=/language-(\w+)/.exec(s||""),m=d?d[1]:"",g=String(o).replace(/\n$/,"");return t?(0,a.jsx)("code",{className:"bg-gray-800 text-gray-100 px-1.5 py-0.5 rounded text-sm font-mono",...l,children:o}):!(g.length<=60)||g.includes("\n")||m?m?(0,a.jsxs)("div",{className:"my-3 rounded-lg overflow-hidden relative group",children:[(0,a.jsx)("div",{className:"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200",children:(0,a.jsx)(c.A,{text:g,variant:"code",size:"sm",title:"Copy code",className:"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50"})}),(0,a.jsx)(i.M,{style:n.bM,language:m,PreTag:"div",className:"text-sm",...l,children:g})]}):(0,a.jsxs)("div",{className:"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100",children:[(0,a.jsx)("div",{className:"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200",children:(0,a.jsx)(c.A,{text:g,variant:"code",size:"sm",title:"Copy code",className:"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50"})}),(0,a.jsx)("pre",{className:"p-4 text-sm font-mono overflow-x-auto",children:(0,a.jsx)("code",{children:g})})]}):(0,a.jsx)("code",{className:"bg-orange-50 text-orange-700 px-1.5 py-0.5 rounded text-sm font-mono border border-orange-200",children:g})},blockquote:e=>{let{children:r}=e;return(0,a.jsx)("blockquote",{className:"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-300",children:r})},a:e=>{let{children:r,href:t}=e;return(0,a.jsx)("a",{href:t,target:"_blank",rel:"noopener noreferrer",className:"text-orange-600 hover:text-orange-700 underline transition-colors duration-200",children:r})},table:e=>{let{children:r}=e;return(0,a.jsx)("div",{className:"overflow-x-auto my-3",children:(0,a.jsx)("table",{className:"min-w-full border border-gray-600 rounded-lg",children:r})})},thead:e=>{let{children:r}=e;return(0,a.jsx)("thead",{className:"bg-gray-800",children:r})},tbody:e=>{let{children:r}=e;return(0,a.jsx)("tbody",{className:"divide-y divide-gray-600 bg-gray-900",children:r})},tr:e=>{let{children:r}=e;return(0,a.jsx)("tr",{className:"hover:bg-gray-800",children:r})},th:e=>{let{children:r}=e;return(0,a.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider border-b border-gray-600",children:r})},td:e=>{let{children:r}=e;return(0,a.jsx)("td",{className:"px-3 py-2 text-sm text-white border-b border-gray-600",children:r})},hr:()=>(0,a.jsx)("hr",{className:"my-4 border-gray-600"})},children:h})})})})}},95565:(e,r,t)=>{t.d(r,{AnalyticsSkeleton:()=>m,ConfigSelectorSkeleton:()=>i,MessageSkeleton:()=>o,MyModelsSkeleton:()=>l,O2:()=>n,RoutingSetupSkeleton:()=>d,vD:()=>c});var a=t(95155);t(11518),t(12115);let s=e=>{let{className:r="",variant:t="text",width:s="100%",height:o="1rem",lines:i=1}=e,n="animate-pulse bg-gray-200 rounded",l=()=>{switch(t){case"circular":return"rounded-full";case"rectangular":return"rounded-lg";default:return"rounded"}},d={width:"number"==typeof s?"".concat(s,"px"):s,height:"number"==typeof o?"".concat(o,"px"):o};return i>1?(0,a.jsx)("div",{className:"space-y-2 ".concat(r),children:Array.from({length:i}).map((e,r)=>(0,a.jsx)("div",{className:"".concat(n," ").concat(l()),style:{...d,width:r===i-1?"75%":d.width}},r))}):(0,a.jsx)("div",{className:"".concat(n," ").concat(l()," ").concat(r),style:d})},o=()=>(0,a.jsx)("div",{className:"space-y-6 py-8",children:Array.from({length:3}).map((e,r)=>(0,a.jsx)("div",{className:"flex ".concat(r%2==0?"justify-end":"justify-start"),children:(0,a.jsx)("div",{className:"max-w-3xl p-4 rounded-2xl ".concat(r%2==0?"bg-orange-50":"bg-white border border-gray-200"),children:(0,a.jsx)(s,{lines:3,height:"1rem"})})},r))}),i=()=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(s,{variant:"circular",width:32,height:32}),(0,a.jsx)(s,{width:"8rem",height:"1.5rem"})]}),n=()=>(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(s,{height:"2.5rem",width:"12rem",className:"mb-2"}),(0,a.jsx)(s,{height:"1.25rem",width:"20rem"})]}),(0,a.jsx)(s,{variant:"rectangular",height:"2.5rem",width:"8rem"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:4}).map((e,r)=>(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(s,{variant:"circular",width:40,height:40}),(0,a.jsx)(s,{height:"1rem",width:"3rem"})]}),(0,a.jsx)(s,{height:"2rem",width:"4rem",className:"mb-2"}),(0,a.jsx)(s,{height:"0.875rem",width:"6rem"})]},r))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)(s,{height:"1.5rem",width:"8rem",className:"mb-4"}),(0,a.jsx)(s,{variant:"rectangular",height:"20rem"})]}),(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)(s,{height:"1.5rem",width:"10rem",className:"mb-4"}),(0,a.jsx)(s,{variant:"rectangular",height:"20rem"})]})]})]}),l=()=>(0,a.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(s,{height:"2.5rem",width:"10rem",className:"mb-2"}),(0,a.jsx)(s,{height:"1.25rem",width:"18rem"})]}),(0,a.jsx)(s,{variant:"rectangular",height:"2.5rem",width:"10rem"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,r)=>(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(s,{height:"1.5rem",width:"8rem",className:"mb-2"}),(0,a.jsx)(s,{height:"1rem",width:"12rem"})]}),(0,a.jsx)(s,{variant:"circular",width:32,height:32})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(s,{height:"0.875rem",width:"4rem"}),(0,a.jsx)(s,{height:"0.875rem",width:"2rem"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(s,{height:"0.875rem",width:"5rem"}),(0,a.jsx)(s,{height:"0.875rem",width:"3rem"})]})]})]},r))})]}),d=()=>(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(s,{height:"3rem",width:"16rem",className:"mx-auto mb-4"}),(0,a.jsx)(s,{height:"1.25rem",width:"24rem",className:"mx-auto"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:Array.from({length:4}).map((e,r)=>(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(s,{variant:"circular",width:48,height:48,className:"mr-4"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(s,{height:"1.5rem",width:"10rem",className:"mb-2"}),(0,a.jsx)(s,{height:"1rem",width:"8rem"})]})]}),(0,a.jsx)(s,{lines:3,height:"0.875rem"})]},r))})]}),c=()=>(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(s,{height:"2.5rem",width:"8rem",className:"mb-2"}),(0,a.jsx)(s,{height:"1.25rem",width:"16rem"})]}),(0,a.jsx)(s,{variant:"rectangular",height:"2.5rem",width:"12rem"})]}),(0,a.jsx)("div",{className:"card p-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(s,{variant:"circular",width:64,height:64,className:"mx-auto mb-4"}),(0,a.jsx)(s,{height:"1.5rem",width:"12rem",className:"mx-auto mb-2"}),(0,a.jsx)(s,{height:"1rem",width:"20rem",className:"mx-auto"})]})}),(0,a.jsxs)("div",{className:"card",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsx)(s,{height:"1.5rem",width:"10rem"})}),(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:Array.from({length:3}).map((e,r)=>(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)(s,{height:"1.25rem",width:"12rem",className:"mb-2"}),(0,a.jsx)(s,{height:"1rem",width:"8rem"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(s,{height:"1.5rem",width:"4rem"}),(0,a.jsx)(s,{variant:"circular",width:32,height:32})]})]})},r))})]})]}),m=()=>(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(s,{height:"2.5rem",width:"9rem",className:"mb-2"}),(0,a.jsx)(s,{height:"1.25rem",width:"18rem"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(s,{variant:"rectangular",height:"2.5rem",width:"8rem"}),(0,a.jsx)(s,{variant:"rectangular",height:"2.5rem",width:"6rem"})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:Array.from({length:3}).map((e,r)=>(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)(s,{height:"1.25rem",width:"6rem",className:"mb-4"}),(0,a.jsx)(s,{height:"2.5rem",width:"5rem",className:"mb-2"}),(0,a.jsx)(s,{height:"1rem",width:"8rem"})]},r))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)(s,{height:"1.5rem",width:"12rem",className:"mb-6"}),(0,a.jsx)(s,{variant:"rectangular",height:"24rem"})]}),(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)(s,{height:"1.5rem",width:"10rem",className:"mb-6"}),(0,a.jsx)(s,{variant:"rectangular",height:"24rem"})]})]})]})},95803:(e,r,t)=>{t.d(r,{A:()=>i});var a=t(95155),s=t(12115),o=t(33654);function i(e){let{text:r,className:t="",size:i="sm",variant:n="default",title:l="Copy to clipboard"}=e,[d,c]=(0,s.useState)(!1),m=async()=>{try{await navigator.clipboard.writeText(r),c(!0),setTimeout(()=>c(!1),2e3)}catch(t){let e=document.createElement("textarea");e.value=r,document.body.appendChild(e),e.focus(),e.select();try{document.execCommand("copy"),c(!0),setTimeout(()=>c(!1),2e3)}catch(e){}document.body.removeChild(e)}},g={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"};return(0,a.jsx)("button",{onClick:m,className:"\n        ".concat({sm:"p-1.5",md:"p-2",lg:"p-2.5"}[i],"\n        ").concat({default:"text-gray-500 hover:text-gray-700 hover:bg-gray-100/80",code:"text-gray-300 hover:text-white hover:bg-gray-600/80",message:"text-gray-500 hover:text-gray-700 hover:bg-white/20"}[n],"\n        rounded transition-all duration-200 cursor-pointer\n        ").concat(d?"text-green-600":"","\n        ").concat(t,"\n      "),title:d?"Copied!":l,children:d?(0,a.jsx)(o.S,{className:"".concat(g[i]," stroke-2")}):(0,a.jsx)("svg",{className:"".concat(g[i]," stroke-2"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})})})}},99030:(e,r,t)=>{t.d(r,{default:()=>o});var a=t(12115),s=t(35695);function o(){let e=(0,s.usePathname)();return(0,a.useEffect)(()=>{"undefined"!=typeof document&&(document.title=(e=>{switch(e){case"/dashboard":return"Dashboard - RouKey";case"/playground":return"Playground - RouKey";case"/my-models":return"My Models - RouKey";case"/routing-setup":return"Routing Setup - RouKey";case"/logs":return"Logs - RouKey";case"/training":return"Prompt Engineering - RouKey";case"/analytics":return"Analytics - RouKey";case"/add-keys":return"Add Keys - RouKey";case"/features":return"Features - RouKey";case"/routing-strategies":return"Routing Strategies - RouKey";case"/contact":return"Contact - RouKey";case"/about":return"About - RouKey";case"/pricing":return"Pricing - RouKey";default:return"RouKey - AI Gateway"}})(e))},[e]),null}}}]);