"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8530],{38530:(e,t,n)=>{let r;n.d(t,{bq:()=>s,wt:()=>f,TW:()=>u,mD:()=>d,Lz:()=>g,YF:()=>D,sD:()=>c,Jt:()=>l,A5:()=>x,Nf:()=>i});var o=n(12115);let i="undefined"!=typeof document?o.useLayoutEffect:()=>{};function l(e){let t=(0,o.useRef)(null);return i(()=>{t.current=e},[e]),(0,o.useCallback)((...e)=>{let n=t.current;return null==n?void 0:n(...e)},[])}n(95269),"undefined"!=typeof window&&window.document&&window.document.createElement;let a=new Map;"undefined"!=typeof FinalizationRegistry&&new FinalizationRegistry(e=>{a.delete(e)});let u=e=>{var t;return null!=(t=null==e?void 0:e.ownerDocument)?t:document},d=e=>e&&"window"in e&&e.window===e?e:u(e).defaultView||window;function c(e,t){1;return!!t&&!!e&&e.contains(t)}let s=(e=document)=>{var t;1;return e.activeElement};function f(e){return 0,e.target}n(52596);let p=null;function w(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&((null==(t=window.navigator.userAgentData)?void 0:t.brands.some(t=>e.test(t.brand)))||e.test(window.navigator.userAgent))}function m(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&e.test((null==(t=window.navigator.userAgentData)?void 0:t.platform)||window.navigator.platform)}function b(e){let t=null;return()=>(null==t&&(t=e()),t)}let g=b(function(){return m(/^Mac/i)}),v=b(function(){return m(/^iPhone/i)}),y=b(function(){return m(/^iPad/i)||g()&&navigator.maxTouchPoints>1}),h=b(function(){return v()||y()});b(function(){return g()||h()});let E=b(function(){return w(/AppleWebKit/i)&&!L()}),L=b(function(){return w(/Chrome/i)}),A=b(function(){return w(/Android/i)}),T=b(function(){return w(/Firefox/i)}),k=(0,o.createContext)({isNative:!0,open:function(e,t){(function(e,t){if(e instanceof HTMLAnchorElement)t(e);else if(e.hasAttribute("data-href")){let n=document.createElement("a");n.href=e.getAttribute("data-href"),e.hasAttribute("data-target")&&(n.target=e.getAttribute("data-target")),e.hasAttribute("data-rel")&&(n.rel=e.getAttribute("data-rel")),e.hasAttribute("data-download")&&(n.download=e.getAttribute("data-download")),e.hasAttribute("data-ping")&&(n.ping=e.getAttribute("data-ping")),e.hasAttribute("data-referrer-policy")&&(n.referrerPolicy=e.getAttribute("data-referrer-policy")),e.appendChild(n),t(n),e.removeChild(n)}})(e,e=>K(e,t))},useHref:e=>e});function K(e,t,n=!0){var r,o;let{metaKey:i,ctrlKey:l,altKey:a,shiftKey:u}=t;T()&&(null==(o=window.event)||null==(r=o.type)?void 0:r.startsWith("key"))&&"_blank"===e.target&&(g()?i=!0:l=!0);let d=E()&&g()&&!y()&&1?new KeyboardEvent("keydown",{keyIdentifier:"Enter",metaKey:i,ctrlKey:l,altKey:a,shiftKey:u}):new MouseEvent("click",{metaKey:i,ctrlKey:l,altKey:a,shiftKey:u,bubbles:!0,cancelable:!0});if(K.isOpening=n,function(){if(null==p){p=!1;try{document.createElement("div").focus({get preventScroll(){return p=!0,!0}})}catch{}}return p}())e.focus({preventScroll:!0});else{let t=function(e){let t=e.parentNode,n=[],r=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==r;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&n.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return r instanceof HTMLElement&&n.push({element:r,scrollTop:r.scrollTop,scrollLeft:r.scrollLeft}),n}(e);e.focus(),function(e){for(let{element:t,scrollTop:n,scrollLeft:r}of e)t.scrollTop=n,t.scrollLeft=r}(t)}e.dispatchEvent(d),K.isOpening=!1}K.isOpening=!1;let C=new Map,M=new Set;function N(){if("undefined"==typeof window)return;function e(e){return"propertyName"in e}let t=n=>{if(!e(n)||!n.target)return;let r=C.get(n.target);if(r&&(r.delete(n.propertyName),0===r.size&&(n.target.removeEventListener("transitioncancel",t),C.delete(n.target)),0===C.size)){for(let e of M)e();M.clear()}};document.body.addEventListener("transitionrun",n=>{if(!e(n)||!n.target)return;let r=C.get(n.target);r||(r=new Set,C.set(n.target,r),n.target.addEventListener("transitioncancel",t,{once:!0})),r.add(n.propertyName)}),document.body.addEventListener("transitionend",t)}function x(){let e=(0,o.useRef)(new Map),t=(0,o.useCallback)((t,n,r,o)=>{let i=(null==o?void 0:o.once)?(...t)=>{e.current.delete(r),r(...t)}:r;e.current.set(r,{type:n,eventTarget:t,fn:i,options:o}),t.addEventListener(n,i,o)},[]),n=(0,o.useCallback)((t,n,r,o)=>{var i;let l=(null==(i=e.current.get(r))?void 0:i.fn)||r;t.removeEventListener(n,l,o),e.current.delete(r)},[]),r=(0,o.useCallback)(()=>{e.current.forEach((e,t)=>{n(e.eventTarget,e.type,t,e.options)})},[n]);return(0,o.useEffect)(()=>r,[r]),{addGlobalListener:t,removeGlobalListener:n,removeAllGlobalListeners:r}}"undefined"!=typeof document&&("loading"!==document.readyState?N():document.addEventListener("DOMContentLoaded",N));let z="undefined"!=typeof document&&window.visualViewport;function D(e){return 0===e.mozInputSource&&!!e.isTrusted||(A()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}n(47650);let H=["input:not([disabled]):not([type=hidden])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable^="false"])'];H.join(":not([hidden]),"),H.push('[tabindex]:not([tabindex="-1"]):not([disabled])'),H.join(':not([hidden]):not([tabindex="-1"]),')}}]);