{"name": "resend", "version": "4.6.0", "description": "Node.js library for the Resend API", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**"], "engines": {"node": ">=18"}, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts", "test": "jest", "test:watch": "jest --watch", "format:apply": "biome check --write .", "format:check": "biome format .", "format": "biome format --write .", "lint": "biome check .", "prepublishOnly": "pnpm run build"}, "repository": {"type": "git", "url": "git+https://github.com/resendlabs/resend-node.git"}, "author": "", "license": "MIT", "bugs": {"url": "https://github.com/resendlabs/resend-node/issues"}, "homepage": "https://github.com/resendlabs/resend-node#readme", "dependencies": {"@react-email/render": "1.1.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/jest": "29.5.14", "@types/node": "18.19.86", "@types/react": "19.1.2", "jest": "29.7.0", "jest-fetch-mock": "3.0.3", "ts-jest": "29.3.4", "ts-node": "10.9.2", "tsup": "7.2.0", "typescript": "5.8.3"}, "packageManager": "pnpm@10.11.1"}