(()=>{var e={};e.id=807,e.ids=[807],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12927:(e,t,r)=>{Promise.resolve().then(r.bind(r,88215))},18536:(e,t,r)=>{Promise.resolve().then(r.bind(r,35291))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37132:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(43210);let n=o.forwardRef(function({title:e,titleId:t,...r},n){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m8.25 4.5 7.5 7.5-7.5 7.5"}))})},47417:(e,t,r)=>{"use strict";r.d(t,{AnalyticsSkeleton:()=>l,ConfigSelectorSkeleton:()=>s,MessageSkeleton:()=>n,MyModelsSkeleton:()=>i,RoutingSetupSkeleton:()=>a});var o=r(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call LoadingSkeleton() from the server but LoadingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LoadingSkeleton"),(0,o.registerClientReference)(function(){throw Error("Attempted to call ChatHistorySkeleton() from the server but ChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ChatHistorySkeleton"),(0,o.registerClientReference)(function(){throw Error("Attempted to call EnhancedChatHistorySkeleton() from the server but EnhancedChatHistorySkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","EnhancedChatHistorySkeleton");let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call MessageSkeleton() from the server but MessageSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MessageSkeleton"),s=(0,o.registerClientReference)(function(){throw Error("Attempted to call ConfigSelectorSkeleton() from the server but ConfigSelectorSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","ConfigSelectorSkeleton");(0,o.registerClientReference)(function(){throw Error("Attempted to call DashboardSkeleton() from the server but DashboardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","DashboardSkeleton");let i=(0,o.registerClientReference)(function(){throw Error("Attempted to call MyModelsSkeleton() from the server but MyModelsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","MyModelsSkeleton"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call RoutingSetupSkeleton() from the server but RoutingSetupSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","RoutingSetupSkeleton");(0,o.registerClientReference)(function(){throw Error("Attempted to call TrainingSkeleton() from the server but TrainingSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","TrainingSkeleton");let l=(0,o.registerClientReference)(function(){throw Error("Attempted to call AnalyticsSkeleton() from the server but AnalyticsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","AnalyticsSkeleton");(0,o.registerClientReference)(function(){throw Error("Attempted to call PlaygroundSkeleton() from the server but PlaygroundSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","PlaygroundSkeleton"),(0,o.registerClientReference)(function(){throw Error("Attempted to call LogsSkeleton() from the server but LogsSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","LogsSkeleton"),(0,o.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\LoadingSkeleton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\components\\LoadingSkeleton.tsx","default")},49579:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(43210);let n=o.forwardRef(function({title:e,titleId:t,...r},n){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"}))})},50942:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(43210);let n=o.forwardRef(function({title:e,titleId:t,...r},n){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},54984:(e,t,r)=>{Promise.resolve().then(r.bind(r,47417))},55511:e=>{"use strict";e.exports=require("crypto")},55541:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\page.tsx","default")},55591:e=>{"use strict";e.exports=require("https")},60925:(e,t,r)=>{"use strict";r.d(t,{c:()=>s});var o=r(43210);let n={};function s(){let[e,t]=(0,o.useState)({}),r=(0,o.useRef)({}),s=(0,o.useCallback)(e=>{let t=n[e];return!!t&&!(Date.now()-t.timestamp>3e5)&&!t.isLoading},[]),i=(0,o.useCallback)(e=>{let t=n[e];return!t||t.isLoading?null:Date.now()-t.timestamp>3e5?(delete n[e],null):t.data},[]),a=(0,o.useCallback)(async(e,o="medium")=>{if(s(e))return i(e);if(n[e]?.isLoading)return null;r.current[e]&&r.current[e].abort();let a=new AbortController;r.current[e]=a,n[e]={data:{},timestamp:Date.now(),isLoading:!0},t(t=>({...t,[e]:"loading"}));try{"low"===o?await new Promise(e=>setTimeout(e,200)):"medium"===o&&await new Promise(e=>setTimeout(e,50));let[r,s,i]=await Promise.allSettled([fetch(`/api/custom-configs/${e}`,{signal:a.signal}),fetch(`/api/keys?custom_config_id=${e}`,{signal:a.signal}),fetch(`/api/complexity-assignments?custom_config_id=${e}`,{signal:a.signal})]),l=null,c=[],d="none",p={},u=[];"fulfilled"===r.status&&r.value.ok&&(d=(l=await r.value.json()).routing_strategy||"none",p=l.routing_strategy_params||{}),"fulfilled"===s.status&&s.value.ok&&(c=await s.value.json()),"fulfilled"===i.status&&i.value.ok&&(u=await i.value.json());let m={configDetails:l,apiKeys:c,routingStrategy:d,routingParams:p,complexityAssignments:u};return n[e]={data:m,timestamp:Date.now(),isLoading:!1},t(t=>({...t,[e]:"success"})),m}catch(r){if("AbortError"===r.name)return null;return delete n[e],t(t=>({...t,[e]:"error"})),null}finally{delete r.current[e]}},[s,i]),l=(0,o.useCallback)(e=>({onMouseEnter:()=>{s(e)||a(e,"high")}}),[a,s]),c=(0,o.useCallback)(e=>{delete n[e],t(t=>{let r={...t};return delete r[e],r})},[]),d=(0,o.useCallback)(()=>{Object.keys(n).forEach(e=>{delete n[e]}),t({})},[]);return{prefetchRoutingSetupData:a,getCachedData:i,isCached:s,createHoverPrefetch:l,clearCache:c,clearAllCache:d,getStatus:(0,o.useCallback)(t=>e[t]||"idle",[e]),getCacheInfo:(0,o.useCallback)(()=>({cachedConfigs:Object.keys(n),cacheSize:Object.keys(n).length,totalCacheAge:Object.values(n).reduce((e,t)=>e+(Date.now()-t.timestamp),0)/Object.keys(n).length}),[]),prefetchStatus:e}}},62392:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(43210);let n=o.forwardRef(function({title:e,titleId:t,...r},n){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z"}))})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71238:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var o=r(37413),n=r(47417);function s(){return(0,o.jsx)(n.RoutingSetupSkeleton,{})}},74075:e=>{"use strict";e.exports=require("zlib")},74461:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(43210);let n=o.forwardRef(function({title:e,titleId:t,...r},n){return o.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":t},r),e?o.createElement("title",{id:t},e):null,o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"}))})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82663:(e,t,r)=>{Promise.resolve().then(r.bind(r,55541))},83829:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var o=r(65239),n=r(48088),s=r(88170),i=r.n(s),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c={children:["",{children:["routing-setup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,55541)),"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,71238)),"C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\RoKey App\\rokey-app\\src\\app\\routing-setup\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/routing-setup/page",pathname:"/routing-setup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88215:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var o=r(60687),n=r(43210),s=r(85814),i=r.n(s),a=r(14566),l=r(62392),c=r(74461),d=r(50942),p=r(49579),u=r(37132),m=r(60925);function g(){let[e,t]=(0,n.useState)([]),[r,s]=(0,n.useState)(!0),[g,h]=(0,n.useState)(null),{createHoverPrefetch:f}=(0,m.c)();return(0,o.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,o.jsx)("div",{className:"border-b border-gray-800/50",children:(0,o.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:(0,o.jsx)("div",{className:"flex items-center justify-between",children:(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-2xl font-semibold text-white mb-2",children:"Advanced Routing Setup"}),(0,o.jsx)("p",{className:"text-sm text-gray-400 max-w-2xl",children:"Configure intelligent routing strategies for your API configurations with enterprise-grade precision"})]})})})}),(0,o.jsxs)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-8",children:[r&&(0,o.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"w-8 h-8 border-4 border-orange-500/20 border-t-orange-500 rounded-full animate-spin mx-auto mb-3"}),(0,o.jsx)("p",{className:"text-gray-400 text-sm",children:"Loading configurations..."})]})}),g&&(0,o.jsx)("div",{className:"bg-red-900/20 border border-red-500/30 rounded-xl p-4 mb-6 backdrop-blur-sm",children:(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)("div",{className:"w-6 h-6 bg-red-500/20 rounded-full flex items-center justify-center",children:(0,o.jsx)("svg",{className:"w-3 h-3 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"font-medium text-red-300 text-sm",children:"Error Loading Configurations"}),(0,o.jsx)("p",{className:"text-red-400 text-xs mt-0.5",children:g})]})]})}),!r&&!g&&0===e.length&&(0,o.jsx)("div",{className:"text-center py-12",children:(0,o.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-xl p-8 max-w-sm mx-auto",children:[(0,o.jsx)("div",{className:"w-12 h-12 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-orange-500/30",children:(0,o.jsx)(a.A,{className:"w-6 h-6 text-orange-400"})}),(0,o.jsx)("h3",{className:"text-h4 text-white mb-2",children:"No Configurations Found"}),(0,o.jsx)("p",{className:"text-body-sm text-gray-400 mb-4 leading-relaxed",children:"Create your first Custom API Configuration to start setting up intelligent routing strategies"}),(0,o.jsxs)(i(),{href:"/my-models",className:"btn-primary inline-flex items-center text-sm",children:[(0,o.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"Create Configuration"]})]})}),!r&&!g&&e.length>0&&(0,o.jsx)("div",{className:"grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:e.map((e,t)=>{let r=[{bg:"bg-gradient-to-br from-pink-500 to-rose-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-blue-500 to-blue-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-emerald-500 to-green-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-amber-500 to-orange-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-purple-500 to-violet-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-cyan-500 to-teal-600",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-indigo-500 to-blue-700",icon:"text-white",text:"text-white"},{bg:"bg-gradient-to-br from-red-500 to-pink-600",icon:"text-white",text:"text-white"}],n=r[t%r.length],s=(e=>{switch(e){case"intelligent_role":return l.A;case"complexity_round_robin":return c.A;case"strict_fallback":return d.A;case"auto_optimal":return p.A;default:return a.A}})(e.routing_strategy||"none");return(0,o.jsx)(i(),{href:`/routing-setup/${e.id}?from=routing-setup`,className:"group block transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl",...f(e.id),children:(0,o.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-2xl shadow-lg overflow-hidden h-80 hover:border-gray-700/50 transition-all duration-300",children:[(0,o.jsxs)("div",{className:`${n.bg} p-8 h-48 flex flex-col items-center justify-center relative`,children:[(0,o.jsx)("div",{className:"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,o.jsx)(s,{className:`w-10 h-10 ${n.icon}`})}),(0,o.jsx)("div",{className:"bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full",children:(0,o.jsx)("span",{className:`text-sm font-bold ${n.text}`,children:"none"!==e.routing_strategy&&e.routing_strategy?"intelligent_role"===e.routing_strategy?"Smart Role":"complexity_round_robin"===e.routing_strategy?"Complexity":"strict_fallback"===e.routing_strategy?"Fallback":e.routing_strategy:"Default"})}),(0,o.jsx)(u.A,{className:`w-6 h-6 ${n.text} absolute top-4 right-4 group-hover:translate-x-1 transition-transform duration-300`})]}),(0,o.jsxs)("div",{className:"p-6 h-32 flex flex-col justify-between bg-gray-900/70",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-bold text-white group-hover:text-gray-200 transition-colors duration-200 line-clamp-2 leading-tight",children:e.name}),(0,o.jsx)("p",{className:"text-sm text-gray-400 mt-2",children:"Advanced routing configuration with intelligent strategies"})]}),(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("span",{className:"text-xs text-gray-500 font-medium",children:["Created ",new Date(e.created_at).toLocaleDateString()]}),(0,o.jsx)("button",{className:"px-3 py-1 text-xs font-medium text-gray-300 bg-gray-800/50 rounded-md hover:bg-gray-700/50 transition-colors duration-200 border border-gray-700/50",children:"Configure"})]})]})]})},e.id)})})]})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4447,5449,4912],()=>r(83829));module.exports=o})();