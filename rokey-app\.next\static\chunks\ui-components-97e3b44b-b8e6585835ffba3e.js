"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6419],{8413:(e,s,a)=>{a.d(s,{A:()=>l});var r=a(95155),t=a(12115),i=a(60323);let d=new Map;function l(e){let{configId:s,onRetry:a,className:l="",disabled:n=!1}=e,[c,o]=(0,t.useState)(!1),[m,g]=(0,t.useState)([]),[u,x]=(0,t.useState)(!1),[h,b]=(0,t.useState)(!1),[v,p]=(0,t.useState)("bottom"),j=(0,t.useRef)(null),y=(0,t.useCallback)(async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(s){if(e){let e=d.get(s);if(e&&Date.now()-e.timestamp<3e5){g(e.keys),b(!0);return}}x(!0);try{let e=await fetch("/api/keys?custom_config_id=".concat(s));if(e.ok){let a=(await e.json()).filter(e=>"active"===e.status);d.set(s,{keys:a,timestamp:Date.now()}),g(a),b(!0)}}catch(e){}finally{x(!1)}}},[s]);(0,t.useEffect)(()=>{s&&!h&&y(!0)},[s,y,h]),(0,t.useEffect)(()=>{let e=e=>{j.current&&!j.current.contains(e.target)&&o(!1)};return c&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[c]);let N=e=>{o(!1),a(e)},f=(0,t.useCallback)(()=>{if(!j.current)return;let e=j.current.getBoundingClientRect(),s=window.innerHeight,a=e.top,r=s-e.bottom;r>=300||r>=a?p("bottom"):p("top")},[]);return(0,r.jsxs)("div",{className:"relative ".concat(l),ref:j,children:[(0,r.jsxs)("button",{onClick:()=>{c||0!==m.length||h||y(!0),c||f(),o(!c)},disabled:n,className:"\n          flex items-center space-x-1 p-1.5 rounded transition-all duration-200 cursor-pointer\n          ".concat(n?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:text-gray-700 hover:bg-white/20","\n        "),title:"Retry with different model",children:[(0,r.jsx)("svg",{className:"w-4 h-4 stroke-2 ".concat(u?"animate-spin":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,r.jsx)(i.D,{className:"w-3 h-3 stroke-2"})]}),c&&(0,r.jsx)("div",{className:"absolute w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-scale-in ".concat("top"===v?"bottom-full left-0 mb-1 origin-bottom-left":"top-full left-0 mt-1 origin-top-left"),children:(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 border-b border-gray-100",children:[(0,r.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"Retry Options"}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),y(!1)},disabled:u,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50",title:"Refresh available models",children:(0,r.jsx)("svg",{className:"w-3 h-3 ".concat(u?"animate-spin":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})]}),(0,r.jsxs)("button",{onClick:()=>N(),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-4 h-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,r.jsx)("span",{children:"Retry with same model"})]}),(m.length>0||u)&&(0,r.jsx)("div",{className:"border-t border-gray-100 my-1"}),u&&(0,r.jsxs)("div",{className:"px-3 py-2 text-sm text-gray-500 flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),(0,r.jsx)("span",{children:"Loading models..."})]}),m.map(e=>(0,r.jsxs)("button",{onClick:()=>N(e.id),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex flex-col disabled:opacity-50",disabled:u,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,r.jsx)("span",{className:"font-medium",children:e.label}),(0,r.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.provider})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-0.5",children:["Temperature: ",e.temperature]})]},e.id)),!u&&0===m.length&&h&&(0,r.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500",children:"No alternative models available"}),m.length>0&&!u&&(0,r.jsxs)("div",{className:"px-3 py-1 text-xs text-gray-400 border-t border-gray-100 flex items-center justify-between",children:[(0,r.jsxs)("span",{children:[m.length," model",1!==m.length?"s":""," available"]}),(()=>{let e=d.get(s);return e&&Date.now()-e.timestamp<3e5?(0,r.jsx)("span",{className:"text-green-500 text-xs",children:"●"}):null})()]})]})})]})}},14446:(e,s,a)=>{a.d(s,{Ay:()=>t,CE:()=>i});var r=a(95155);function t(){return(0,r.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"})}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]})}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"card p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"border border-gray-200 rounded-xl p-4 animate-pulse",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full animate-pulse"})]})]})},e))})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"card p-8 min-h-[600px]",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("div",{className:"h-7 bg-gray-200 rounded w-48 mb-3 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full animate-pulse"})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16 animate-pulse"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 animate-pulse"})]})]},e))})]}),(0,r.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,r.jsx)("div",{className:"h-5 bg-gray-200 rounded w-48 mb-4 animate-pulse"}),(0,r.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-4",children:[1,2,3,4,5].map(e=>(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mx-auto mb-2 animate-pulse"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse"})]},e))}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-40 animate-pulse"})})]})]})})]})]})})}function i(){return(0,r.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"})]})]})},e))}),(0,r.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-20 bg-gray-200 rounded animate-pulse"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]})]})]})})}a(12115)},38050:(e,s,a)=>{a.d(s,{default:()=>l});var r=a(12115),t=a(35695),i=a(21826),d=a(44042);function l(e){let{enableUserBehaviorTracking:s=!0,enableNavigationTracking:a=!0,enableInteractionTracking:l=!0}=e,n=(0,t.usePathname)(),c=(0,r.useRef)(""),o=(0,r.useRef)(0),{exportMetrics:m}=(0,d.D)("PerformanceTracker");return(0,r.useEffect)(()=>{if(!a)return;let e=c.current;e&&e!==n&&(i.zf.trackNavigation(e,n),performance.now(),o.current),c.current=n,o.current=performance.now()},[n,a]),(0,r.useEffect)(()=>{if(!l)return;let e=e=>{let s=e.target;if("mouseenter"===e.type&&"A"===s.tagName){let e=s.getAttribute("href");e&&e.startsWith("/")&&i.zf.schedulePrefetch(e)}if("click"===e.type&&("BUTTON"===s.tagName||s.closest("button"))){var a;let e=(null==(a=s.textContent)?void 0:a.trim())||"Unknown";e.toLowerCase().includes("get started")||e.toLowerCase().includes("sign up")?i.zf.schedulePrefetch("/auth/signup"):e.toLowerCase().includes("pricing")?i.zf.schedulePrefetch("/pricing"):e.toLowerCase().includes("features")&&i.zf.schedulePrefetch("/features")}};return document.addEventListener("mouseenter",e,!0),document.addEventListener("click",e,!0),()=>{document.removeEventListener("mouseenter",e,!0),document.removeEventListener("click",e,!0)}},[l]),(0,r.useEffect)(()=>{let e;if(!s)return;let a=!1,r=0,t=()=>{a||(a=!0,performance.now());let s=window.scrollY/(document.body.scrollHeight-window.innerHeight)*100;r=Math.max(r,s),clearTimeout(e),e=setTimeout(()=>{a=!1,performance.now(),r>80&&("/"===n?(i.zf.schedulePrefetch("/pricing"),i.zf.schedulePrefetch("/features")):"/features"===n&&i.zf.schedulePrefetch("/auth/signup")),r=0},150)},d=performance.now(),l=()=>{performance.now()-d>1e4&&("/"===n?i.zf.schedulePrefetch("/auth/signup"):"/pricing"===n&&i.zf.schedulePrefetch("/auth/signup"))};window.addEventListener("scroll",t,{passive:!0});let c=()=>{document.hidden&&l()};document.addEventListener("visibilitychange",c);let o=()=>{l()};return window.addEventListener("beforeunload",o),()=>{clearTimeout(e),window.removeEventListener("scroll",t),document.removeEventListener("visibilitychange",c),window.removeEventListener("beforeunload",o),l()}},[n,s,m]),(0,r.useEffect)(()=>{if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let s=e.getEntries();s[s.length-1].startTime});e.observe({entryTypes:["largest-contentful-paint"]});let s=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.processingStart&&e.startTime&&(e.processingStart,e.startTime)})});s.observe({entryTypes:["first-input"]});let a=0,r=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.hadRecentInput||(a+=e.value)})});return r.observe({entryTypes:["layout-shift"]}),()=>{e.disconnect(),s.disconnect(),r.disconnect()}}},[]),null}},69903:(e,s,a)=>{a.d(s,{A:()=>u});var r=a(95155),t=a(12115),i=a(35695),d=a(99323);let l=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"})]},e))}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3,4].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})]})]}),n=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"bg-white p-8 rounded-2xl border-2",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3 mx-auto"})]}),(0,r.jsx)("div",{className:"space-y-3 mb-8",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]},e))})]}),c=()=>(0,r.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,r.jsx)("div",{className:"h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6"}),(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsx)("div",{className:"py-20",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[1,2,3,4,5,6].map(e=>(0,r.jsx)("div",{className:"bg-white rounded-2xl p-8 border",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-xl"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mb-3"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-4"}),(0,r.jsx)("div",{className:"space-y-2",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"},e))})]})]})},e))})})]}),o=()=>(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]})]})}),m=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,r.jsx)("div",{className:"h-32 bg-gray-200 rounded mt-4"}),(0,r.jsx)("div",{className:"h-10 bg-gray-200 rounded mt-4"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,r.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,r.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})]}),g=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,r.jsx)("div",{className:"bg-white p-6 rounded-lg border",children:(0,r.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})})]});function u(e){let s,{targetRoute:a,children:u}=e,[x,h]=(0,t.useState)(!0),[b,v]=(0,t.useState)(!1),p=(0,i.usePathname)(),j=(0,t.useRef)(),{isPageCached:y}=(0,d.bu)()||{isPageCached:()=>!1};return((0,t.useEffect)(()=>(p===a&&(j.current=setTimeout(()=>{v(!0),setTimeout(()=>h(!1),100)},y(a)?50:200)),()=>{j.current&&clearTimeout(j.current)}),[p,a,y]),(0,t.useEffect)(()=>{h(!0),v(!1)},[a]),p!==a&&x||p===a&&x&&!b)?(0,r.jsx)("div",{className:"optimistic-loading-container",children:(s=a).startsWith("/dashboard")?(0,r.jsx)(l,{}):s.startsWith("/pricing")?(0,r.jsx)(n,{}):s.startsWith("/features")?(0,r.jsx)(c,{}):s.startsWith("/auth/")?(0,r.jsx)(o,{}):s.startsWith("/playground")?(0,r.jsx)(m,{}):(0,r.jsx)(g,{})}):(0,r.jsx)("div",{className:"transition-opacity duration-300 ".concat(b?"opacity-100":"opacity-0"),children:u})}},78817:(e,s,a)=>{a.d(s,{A:()=>n});var r=a(95155);a(12115);var t=a(89732),i=a(99323),d=a(95565);let l={"/dashboard":{title:"Dashboard",subtitle:"Loading overview & analytics...",icon:t.fA,color:"text-blue-500",bgColor:"bg-blue-50"},"/my-models":{title:"My Models",subtitle:"Loading API key management...",icon:t.RY,color:"text-green-500",bgColor:"bg-green-50"},"/playground":{title:"Playground",subtitle:"Loading model testing environment...",icon:t.cu,color:"text-orange-500",bgColor:"bg-orange-50"},"/routing-setup":{title:"Routing Setup",subtitle:"Loading routing configuration...",icon:t.sR,color:"text-purple-500",bgColor:"bg-purple-50"},"/logs":{title:"Logs",subtitle:"Loading request history...",icon:t.AQ,color:"text-gray-500",bgColor:"bg-gray-50"},"/training":{title:"Prompt Engineering",subtitle:"Loading custom prompts...",icon:t.tl,color:"text-indigo-500",bgColor:"bg-indigo-50"},"/analytics":{title:"Analytics",subtitle:"Loading advanced insights...",icon:t.r9,color:"text-pink-500",bgColor:"bg-pink-50"}};function n(e){let{targetRoute:s}=e,{clearNavigation:a}=(0,i.bu)()||{clearNavigation:()=>{}};if(!(s?l[s]:null))return(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,r.jsx)(t.cu,{className:"w-8 h-8 text-gray-400"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Loading..."}),(0,r.jsx)("p",{className:"text-gray-500",children:"Please wait while we load the page"})]})});let n=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-1 space-y-3",children:Array.from({length:5}).map((e,s)=>(0,r.jsx)("div",{className:"h-16 bg-gray-200 rounded-lg animate-pulse"},s))}),(0,r.jsx)("div",{className:"lg:col-span-3",children:(0,r.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg animate-pulse"})})]})]}),c=()=>(0,r.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"}),(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-16"})]})]}),(0,r.jsx)("div",{className:"space-y-3",children:Array.from({length:8}).map((e,s)=>(0,r.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"},s))})]});return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("button",{onClick:a,className:"absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",title:"Cancel loading",children:(0,r.jsx)(t.fK,{className:"w-5 h-5"})}),(()=>{switch(s){case"/dashboard":default:return(0,r.jsx)(d.O2,{});case"/my-models":return(0,r.jsx)(d.MyModelsSkeleton,{});case"/playground":return(0,r.jsx)(n,{});case"/routing-setup":return(0,r.jsx)(d.RoutingSetupSkeleton,{});case"/logs":return(0,r.jsx)(c,{});case"/training":return(0,r.jsx)(d.vD,{});case"/analytics":return(0,r.jsx)(d.AnalyticsSkeleton,{})}})()]})}}}]);