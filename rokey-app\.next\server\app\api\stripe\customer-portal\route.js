(()=>{var e={};e.id=7071,e.ids=[7071],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66511:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>P,routeModule:()=>I,serverHooks:()=>S,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var t={};s.r(t),s.d(t,{POST:()=>R});var o=s(96559),i=s(48088),u=s(37719),n=s(32190),a=s(64745),p=s(39398),c=s(94473);let E=new a.A(c.Lj.secretKey,{apiVersion:"2025-02-24.acacia"}),_=(0,p.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);async function R(e){try{let r=await e.json(),{userId:s}=r;if(!s)return n.NextResponse.json({error:"Missing userId"},{status:400});let{data:t,error:o}=await _.from("subscriptions").select("stripe_customer_id").eq("user_id",s).order("created_at",{ascending:!1}).limit(1).single(),i=null;if(t&&t.stripe_customer_id)i=t.stripe_customer_id;else{let{data:e,error:r}=await _.auth.admin.getUserById(s);if(r||!e.user?.email)return n.NextResponse.json({error:"No Stripe customer found for user"},{status:404});try{let r=await E.customers.list({email:e.user.email,limit:1});i=r.data.length>0?r.data[0].id:(await E.customers.create({email:e.user.email,metadata:{supabase_user_id:s}})).id}catch(e){return n.NextResponse.json({error:"Failed to create or find Stripe customer"},{status:500})}}if(!i)return n.NextResponse.json({error:"No Stripe customer found for user"},{status:404});let{returnUrl:u}=r,a=await E.billingPortal.sessions.create({customer:i,return_url:u||"https://roukey.online/billing"});return n.NextResponse.json({url:a.url})}catch(e){if(e instanceof a.A.errors.StripeError)return n.NextResponse.json({error:`Stripe error: ${e.message}`},{status:400});return n.NextResponse.json({error:"Internal server error"},{status:500})}}let I=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/stripe/customer-portal/route",pathname:"/api/stripe/customer-portal",filename:"route",bundlePath:"app/api/stripe/customer-portal/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\stripe\\customer-portal\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:S}=I;function P(){return(0,u.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94473:(e,r,s)=>{"use strict";s.d(r,{Dm:()=>o,Lj:()=>t,Zu:()=>i});let t={publishableKey:process.env.STRIPE_LIVE_PUBLISHABLE_KEY,secretKey:process.env.STRIPE_LIVE_SECRET_KEY,webhookSecret:process.env.STRIPE_LIVE_WEBHOOK_SECRET},o={FREE:process.env.STRIPE_LIVE_FREE_PRICE_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRICE_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID},i={FREE:process.env.STRIPE_LIVE_FREE_PRODUCT_ID,STARTER:process.env.STRIPE_LIVE_STARTER_PRODUCT_ID,PROFESSIONAL:process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID,ENTERPRISE:process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID};process.env.STRIPE_LIVE_FREE_PRICE_ID,process.env.STRIPE_LIVE_STARTER_PRICE_ID,process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID,process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID,t.publishableKey&&t.publishableKey.substring(0,20),t.secretKey&&t.secretKey.substring(0,20),t.webhookSecret&&t.webhookSecret.substring(0,15)},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,580,9398,4745],()=>s(66511));module.exports=t})();