(()=>{var e={};e.id=9370,e.ids=[1489,9370],e.modules={2507:(e,r,t)=>{"use strict";t.d(r,{H:()=>u,Q:()=>n,createSupabaseServerClientOnRequest:()=>a});var s=t(34386),i=t(39398),o=t(44999);async function a(){let e=await (0,o.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:r=>e.get(r)?.value,set(r,t,s){try{e.set({name:r,value:t,...s})}catch(e){}},remove(r,t){try{e.set({name:r,value:"",...t})}catch(e){}}}})}function n(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:r=>e.cookies.get(r)?.value,set(e,r,t){},remove(e,r){}}})}function u(){return(0,i.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=51906,e.exports=r},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89716:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>d,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>c});var i=t(96559),o=t(48088),a=t(37719),n=t(32190),u=t(2507);async function c(e){try{let r=(0,u.Q)(e),{data:{session:t},error:s}=await r.auth.getSession();if(s||!t?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{task:i,available_roles:o,user_config:a}=await e.json();if(!i||!o||!Array.isArray(o))return n.NextResponse.json({error:"Invalid request: task and available_roles are required"},{status:400});let c=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!c)return n.NextResponse.json({error:"Classification service unavailable"},{status:503});let p=`You are RouKey's intelligent role classifier for browser automation tasks.

Available Roles: ${o.join(", ")}

Analyze the task and determine which role(s) are most appropriate. Consider:
- Task complexity and requirements
- Role specializations and capabilities
- Multi-role coordination if needed

Respond with JSON: {"roles": ["role1", "role2"], "reasoning": "explanation"}

If multiple roles are needed, list them in order of importance.
If no specific role matches, use the first available role.`,l=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${c}`},body:JSON.stringify({model:"gemini-2.0-flash-lite",messages:[{role:"system",content:p},{role:"user",content:`Task: ${i}`}],temperature:.05,max_tokens:100})});if(!l.ok)return n.NextResponse.json({error:"Role classification failed"},{status:500});let h=await l.json(),d=h.choices?.[0]?.message?.content;if(!d)return n.NextResponse.json({error:"Invalid classification response"},{status:500});try{let e=JSON.parse(d),r=(e.roles||[]).filter(e=>o.includes(e));return 0===r.length&&o.length>0&&r.push(o[0]),n.NextResponse.json({roles:r,reasoning:e.reasoning||"Gemini-based role classification",classification_method:"gemini",available_roles:o,task_summary:i.substring(0,100)})}catch(e){return n.NextResponse.json({roles:o.length>0?[o[0]]:[],reasoning:"Fallback classification due to parsing error",classification_method:"fallback",available_roles:o,task_summary:i.substring(0,100)})}}catch(e){return n.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/orchestration/classify-roles/route",pathname:"/api/orchestration/classify-roles",filename:"route",bundlePath:"app/api/orchestration/classify-roles/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\orchestration\\classify-roles\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:h,serverHooks:d}=p;function m(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:h})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,9398,3410],()=>t(89716));module.exports=s})();